-- DB update 2021_04_26_00 -> 2021_04_27_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_26_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_26_00 2021_04_27_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1618750411311383100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1618750411311383100');
UPDATE `gameobject`SET `position_x`='-7406', `position_y`='-2756', `position_z`='12.064' WHERE `guid`=7327;
UPDATE `gameobject`SET `position_x`='-520', `position_y`='-4762', `position_z`='30.064' WHERE `guid`=12487;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
