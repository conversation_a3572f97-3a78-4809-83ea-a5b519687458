-- DB update 2021_11_08_04 -> 2021_11_08_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_08_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_08_04 2021_11_08_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1636233172792932800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1636233172792932800');

DELETE FROM `spell_dbc` WHERE `Id` IN (78142, 78951, 79342, 79579);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_08_05' WHERE sql_rev = '1636233172792932800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
