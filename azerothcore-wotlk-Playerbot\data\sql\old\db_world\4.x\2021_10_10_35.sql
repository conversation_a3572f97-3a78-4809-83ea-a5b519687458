-- DB update 2021_10_10_34 -> 2021_10_10_35
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_34';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_34 2021_10_10_35 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633784351138842832'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633784351138842832');

-- <PERSON>moves invalid Purple Lotus drops from various NPCs
DELETE FROM `creature_loot_template` WHERE `item` = 8831 AND `entry` NOT IN (12219, 6510, 6509, 6511, 6512, 12223, 12224, 12220, 5881, 8384, 13141, 13142, 5490, 7139, 14448, 1812, 6517, 6527, 6519, 6518, 5481, 5485, 7584, 7100, 7101, 11462, 13022);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_35' WHERE sql_rev = '1633784351138842832';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
