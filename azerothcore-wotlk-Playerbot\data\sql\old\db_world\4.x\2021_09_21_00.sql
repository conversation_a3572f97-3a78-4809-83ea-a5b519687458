-- DB update 2021_09_20_18 -> 2021_09_21_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_20_18';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_20_18 2021_09_21_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1631944525344217322'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1631944525344217322');

-- Deletes Plaguebloom from all NPC loot tables 
DELETE FROM `creature_loot_template` WHERE `item` = 13466 AND `comment` LIKE '%Plaguebloom%';

-- Remove loot from Crimson Bodyguard
UPDATE `creature_template` SET `lootid` = 0 WHERE `Entry` = 13118 AND `Name` = 'Crimson Bodyguard';

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_21_00' WHERE sql_rev = '1631944525344217322';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
