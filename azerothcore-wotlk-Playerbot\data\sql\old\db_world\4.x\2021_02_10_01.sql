-- DB update 2021_02_10_00 -> 2021_02_10_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_02_10_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_02_10_00 2021_02_10_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1612520952274785900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1612520952274785900');

-- Lower respawns to improve quests Find the Gems and Power Source & Find the Gems

UPDATE `gameobject` SET `spawntimesecs`=2 WHERE `guid` IN
(
40694, -- Shadowforge Cache
40695  -- Conspicuous Urn
);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
