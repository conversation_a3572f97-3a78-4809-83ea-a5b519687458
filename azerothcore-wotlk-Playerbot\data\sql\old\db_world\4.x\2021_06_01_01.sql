-- DB update 2021_06_01_00 -> 2021_06_01_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_01_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_01_00 2021_06_01_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1621846003001480200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1621846003001480200');

DELETE FROM `gossip_menu_option` WHERE `MenuID` IN (9594,9595) AND `OptionID` = 1;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_01_01' WHERE sql_rev = '1621846003001480200';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
