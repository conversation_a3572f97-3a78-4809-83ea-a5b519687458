-- DB update 2021_10_29_00 -> 2021_10_29_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_29_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_29_00 2021_10_29_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635252935230504905'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) <PERSON>LUES ('1635252935230504905');

-- <PERSON><PERSON>ves <PERSON><PERSON><PERSON> from 295 NPCs not listed below
DELETE FROM `creature_loot_template` WHERE `item` = 3818 AND `entry` NOT IN (764, 765, 766, 940, 1812, 2931, 4382, 4385, 4386, 4387, 5481, 5485, 5490, 6509, 6510, 6511, 6512, 6517, 6518, 6519, 6527, 7100, 7101, 7104, 7139, 7584, 8384, 11462, 12219, 12220, 12223, 12224, 12836, 13022, 13142, 14231, 14448);

-- Removes lootIDs from Samantha Swifthoof and Watcher Biggs
UPDATE `creature_template` SET `lootid` = 0 WHERE `Entry` IN (5476, 11748);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_29_01' WHERE sql_rev = '1635252935230504905';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
