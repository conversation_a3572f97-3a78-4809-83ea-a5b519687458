-- DB update 2019_03_22_02 -> 2019_03_22_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_03_22_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_03_22_02 2019_03_22_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1553133787666600978'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1553133787666600978');

<PERSON><PERSON><PERSON><PERSON> TABLE `game_graveyard_zone` TO `graveyard_zone`;
ALTER TABLE `graveyard_zone` CHANGE `id` `ID` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0';
ALTER TABLE `graveyard_zone` CHANGE `ghost_zone` `GhostZone` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0';
ALTER TABLE `graveyard_zone` CHANGE `faction` `Faction` SMALLINT(5) UNSIGNED NOT NULL DEFAULT '0';
ALTER TABLE `graveyard_zone` ADD COLUMN `Comment` TEXT AFTER `Faction`;

DELETE FROM command WHERE name = 'reload game_graveyard_zone';
INSERT INTO command(name, security, help) VALUES ('reload graveyard_zone', 3, 'Syntax: .reload graveyard_zone');

UPDATE `graveyard_zone` SET `Comment`='Redridge Mountains - Redridge Mountains' WHERE `ID`=2;
UPDATE `graveyard_zone` SET `Comment`='Duskwood, Darkshire - Duskwood' WHERE `ID`=3;
UPDATE `graveyard_zone` SET `Comment`='Westfall, Sentinel Hill GY - Westfall' WHERE `ID`=4;
UPDATE `graveyard_zone` SET `Comment`='Loch Modan, Thelsamar - Loch Modan' WHERE `ID`=6;
UPDATE `graveyard_zone` SET `Comment`='Wetlands, Crossroads GY' WHERE `ID`=7;
UPDATE `graveyard_zone` SET `Comment`='Badlands, Graveyard NE' WHERE `ID`=8;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, The Crossroads - Northern Barrens' WHERE `ID`=10;
UPDATE `graveyard_zone` SET `Comment`='Desolace, Ethel Rethor GY' WHERE `ID`=31;
UPDATE `graveyard_zone` SET `Comment`='Durotar, Razor Hill GY' WHERE `ID`=32;
UPDATE `graveyard_zone` SET `Comment`='Mulgore, Red Cloud Mesa GY - Mulgore' WHERE `ID`=34;
UPDATE `graveyard_zone` SET `Comment`='Darkshore, New Auberdine GY - Darkshore' WHERE `ID`=35;
UPDATE `graveyard_zone` SET `Comment`='Deadwind Pass, Morgan''s Plot' WHERE `ID`=36;
UPDATE `graveyard_zone` SET `Comment`='Silithus, Valor''s Rest - Silithus' WHERE `ID`=70;
UPDATE `graveyard_zone` SET `Comment`='Mulgore, Bloodhoof Village GY - Mulgore' WHERE `ID`=89;
UPDATE `graveyard_zone` SET `Comment`='Teldrassil, Darnassus GY' WHERE `ID`=90;
UPDATE `graveyard_zone` SET `Comment`='Teldrassil, Dolanaar GY - Teldrassil' WHERE `ID`=91;
UPDATE `graveyard_zone` SET `Comment`='Ashenvale, Astranaar GY - Ashenvale' WHERE `ID`=92;
UPDATE `graveyard_zone` SET `Comment`='Teldrassil, Aldrassil GY - Teldrassil' WHERE `ID`=93;
UPDATE `graveyard_zone` SET `Comment`='Tirisfal Glades, Deathknell - Tirisfal Glades' WHERE `ID`=94;
UPDATE `graveyard_zone` SET `Comment`='Tirisfal Glades, Undercity' WHERE `ID`=96;
UPDATE `graveyard_zone` SET `Comment`='Silverpine Forest, The Sepulcher - Silverpine Forest' WHERE `ID`=97;
UPDATE `graveyard_zone` SET `Comment`='Hillsbrad Foothills, Tarren Mill - Hillsbrad Foothills' WHERE `ID`=98;
UPDATE `graveyard_zone` SET `Comment`='Arathi Highlands, Eastern Road - Arathi Highlands' WHERE `ID`=99;
UPDATE `graveyard_zone` SET `Comment`='Dun Morogh, Anvilmar - Dun Morogh' WHERE `ID`=100;
UPDATE `graveyard_zone` SET `Comment`='Dun Morogh, Kharanos' WHERE `ID`=101;
UPDATE `graveyard_zone` SET `Comment`='Badlands, Kargath - Badlands' WHERE `ID`=103;
UPDATE `graveyard_zone` SET `Comment`='Redridge Mountains, Lakeshire' WHERE `ID`=104;
UPDATE `graveyard_zone` SET `Comment`='Elwynn Forest, Northshire - Elwynn Forest' WHERE `ID`=105;
UPDATE `graveyard_zone` SET `Comment`='Elwynn Forest, Goldshire - Elwynn Forest' WHERE `ID`=106;
UPDATE `graveyard_zone` SET `Comment`='Elwynn Forest, Stormwind' WHERE `ID`=107;
UPDATE `graveyard_zone` SET `Comment`='Swamp of Sorrows, Stonard GY' WHERE `ID`=108;
UPDATE `graveyard_zone` SET `Comment`='Stranglethorn Vale, Booty Bay GY - Stranglethorn Vale' WHERE `ID`=109;
UPDATE `graveyard_zone` SET `Comment`='Teldrassil, Rut''theran Village GY - Teldrassil' WHERE `ID`=129;
UPDATE `graveyard_zone` SET `Comment`='Hillsbrad Foothills, Southshore' WHERE `ID`=149;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Snowfall Graveyard (Mid) - Alterac Valley' WHERE `ID`=169;
UPDATE `graveyard_zone` SET `Comment`='Dustwallow Marsh, Theramore Isle GY - Dustwallow Marsh' WHERE `ID`=189;
UPDATE `graveyard_zone` SET `Comment`='Tanaris, Gadgetzan GY' WHERE `ID`=209;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, Camp Taurajo GY - Southern Barrens' WHERE `ID`=229;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, Ratchet' WHERE `ID`=249;
UPDATE `graveyard_zone` SET `Comment`='Tirisfal Glades, Brill - Tirisfal Glades' WHERE `ID`=289;
UPDATE `graveyard_zone` SET `Comment`='Feralas, New Feathermoon Stronghold GY (A) - Feralas' WHERE `ID`=309;
UPDATE `graveyard_zone` SET `Comment`='Feralas, Camp Mojache GY (H) - Feralas' WHERE `ID`=310;
UPDATE `graveyard_zone` SET `Comment`='Thousand Needles, Splithoof Heights GY (MOVED) - Thousand Needles' WHERE `ID`=329;
UPDATE `graveyard_zone` SET `Comment`='The Hinterlands, Aerie Peak - The Hinterlands' WHERE `ID`=349;
UPDATE `graveyard_zone` SET `Comment`='Azshara, Northern Azshara GY - Azshara' WHERE `ID`=369;
UPDATE `graveyard_zone` SET `Comment`='Blasted Lands, Dreadmaul Hold GY - Blasted Lands' WHERE `ID`=370;
UPDATE `graveyard_zone` SET `Comment`='Blasted Lands, Dreadmaul Hold GY - Swamp of Sorrows' WHERE `ID`=370;
UPDATE `graveyard_zone` SET `Comment`='Stranglethorn Vale, Northern Stranglethorn GY - Stranglethorn Vale' WHERE `ID`=389;
UPDATE `graveyard_zone` SET `Comment`='Stonetalon Mountains, Webwinder Path GY - Stonetalon Mountains' WHERE `ID`=409;
UPDATE `graveyard_zone` SET `Comment`='Felwood, Morlos''Aran - Felwood' WHERE `ID`=449;
UPDATE `graveyard_zone` SET `Comment`='Un''Goro Crater, The Marshlands - Un''Goro Crater' WHERE `ID`=450;
UPDATE `graveyard_zone` SET `Comment`='Darkshore, Twilight Vale GY' WHERE `ID`=469;
UPDATE `graveyard_zone` SET `Comment`='Wetlands, Baradin Bay GY - Wetlands' WHERE `ID`=489;
UPDATE `graveyard_zone` SET `Comment`='Western Plaguelands, Chillwind Camp - Western Plaguelands' WHERE `ID`=509;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Pestilent Scar - Eastern Plaguelands' WHERE `ID`=510;
UPDATE `graveyard_zone` SET `Comment`='Winterspring, Everlook GY - Winterspring' WHERE `ID`=511;
UPDATE `graveyard_zone` SET `Comment`='Ashenvale, Kargathia GY' WHERE `ID`=512;
UPDATE `graveyard_zone` SET `Comment`='Programmer Isle - Programmer Isle' WHERE `ID`=529;
UPDATE `graveyard_zone` SET `Comment`='Western Plaguelands, Bulwark' WHERE `ID`=569;
UPDATE `graveyard_zone` SET `Comment`='Azshara, (Overlooks) The Shattered Strand GY - Azshara' WHERE `ID`=609;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Horde Safe - Alterac Valley' WHERE `ID`=610;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Alliance Safe - Alterac Valley' WHERE `ID`=611;
UPDATE `graveyard_zone` SET `Comment`='TEST for GM Client Only - Do Not Bug - Tirisfal Glades' WHERE `ID`=629;
UPDATE `graveyard_zone` SET `Comment`='Azshara, Bitter Reaches GY - Azshara' WHERE `ID`=630;
UPDATE `graveyard_zone` SET `Comment`='Dustwallow Marsh, Brackenwall Village GY - Dustwallow Marsh' WHERE `ID`=631;
UPDATE `graveyard_zone` SET `Comment`='Moonglade GY - Moonglade' WHERE `ID`=633;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Darrowshire - Eastern Plaguelands' WHERE `ID`=634;
UPDATE `graveyard_zone` SET `Comment`='Felwood, Irontree Woods - Felwood' WHERE `ID`=635;
UPDATE `graveyard_zone` SET `Comment`='Searing Gorge, Thorium Point - Searing Gorge' WHERE `ID`=636;
UPDATE `graveyard_zone` SET `Comment`='Durotar, Sen''jin Village GY - Durotar' WHERE `ID`=649;
UPDATE `graveyard_zone` SET `Comment`='Programmer Isle, Bucklers Cemetery 2 - Programmer Isle' WHERE `ID`=669;
UPDATE `graveyard_zone` SET `Comment`='Programmer Isle, Bucklers Cemetery 1 - Programmer Isle' WHERE `ID`=670;
UPDATE `graveyard_zone` SET `Comment`='Programmer Isle, Bucklers Cemetery 3 - Programmer Isle' WHERE `ID`=671;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Stormpike Graveyard (Hi) - Alterac Valley' WHERE `ID`=689;
UPDATE `graveyard_zone` SET `Comment`='Durotar, Valley of Trials GY - Durotar' WHERE `ID`=709;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, PvP Alliance Choke Graveyard (A-choke) - Alterac Valley' WHERE `ID`=729;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, PvP Horde Choke Graveyard (H-choke) - Alterac Valley' WHERE `ID`=749;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Frostwolf Relief Hut (H-base) - Alterac Valley' WHERE `ID`=750;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Stormpike Aid Station (A-base) - Alterac Valley' WHERE `ID`=751;
UPDATE `graveyard_zone` SET `Comment`='Warsong Gulch - Alliance Enter Loc - Warsong Gulch' WHERE `ID`=769;
UPDATE `graveyard_zone` SET `Comment`='Warsong Gulch - Horde Enter Loc - Warsong Gulch' WHERE `ID`=770;
UPDATE `graveyard_zone` SET `Comment`='Warsong Gulch - Alliance Rez Loc - Warsong Gulch' WHERE `ID`=771;
UPDATE `graveyard_zone` SET `Comment`='Warsong Gulch - Horde Rez Loc - Warsong Gulch' WHERE `ID`=772;
UPDATE `graveyard_zone` SET `Comment`='The Hinterlands, The Overlook Cliffs - The Hinterlands' WHERE `ID`=789;
UPDATE `graveyard_zone` SET `Comment`='Warsong Gulch - Horde Exit Loc - Warsong Gulch' WHERE `ID`=809;
UPDATE `graveyard_zone` SET `Comment`='Warsong Gulch - Alliance Exit Loc - Warsong Gulch' WHERE `ID`=810;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Alliance Exit' WHERE `ID`=829;
UPDATE `graveyard_zone` SET `Comment`='Alterac Valley, Horde Exit - Alterac Valley' WHERE `ID`=830;
UPDATE `graveyard_zone` SET `Comment`='Feralas, Dire Maul Stonemaul Hold GY - Feralas' WHERE `ID`=849;
UPDATE `graveyard_zone` SET `Comment`='Durotar, Northern Durotar GY' WHERE `ID`=850;
UPDATE `graveyard_zone` SET `Comment`='Mulgore, Thunder Bluff GY' WHERE `ID`=851;
UPDATE `graveyard_zone` SET `Comment`='Dun Morogh, Gates of Ironforge - Dun Morogh' WHERE `ID`=852;
UPDATE `graveyard_zone` SET `Comment`='Elwynn Forest, Eastvale Logging Camp' WHERE `ID`=854;
UPDATE `graveyard_zone` SET `Comment`='Western Plaguelands, Caer Darrow - Western Plaguelands' WHERE `ID`=869;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Horde Entrance - Arathi Basin' WHERE `ID`=889;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Alliance Entrance - Arathi Basin' WHERE `ID`=890;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Horde Exit - Arathi Basin' WHERE `ID`=891;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Alliance Exit - Arathi Basin' WHERE `ID`=892;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Graveyard, H-Mid (Farm) - Arathi Basin' WHERE `ID`=893;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Graveyard, Mid (Blacksmith) - Arathi Basin' WHERE `ID`=894;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Graveyard, A-Mid (Stables) - Arathi Basin' WHERE `ID`=895;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Graveyard, ALT-N (Gold Mine) - Arathi Basin' WHERE `ID`=896;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Graveyard, ALT-S (Lumber Mill) - Arathi Basin' WHERE `ID`=897;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Graveyard, A-Base (Trollbane Hall) - Arathi Basin' WHERE `ID`=898;
UPDATE `graveyard_zone` SET `Comment`='Arathi Basin - Graveyard, H-Base (Defiler''s Den) - Arathi Basin' WHERE `ID`=899;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Blackwood Lake - Eastern Plaguelands' WHERE `ID`=909;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Blackwood Lake - Stratholme' WHERE `ID`=909;
UPDATE `graveyard_zone` SET `Comment`='Silithus, Cenarion Hold - Ruins of Ahn''Qiraj' WHERE `ID`=910;
UPDATE `graveyard_zone` SET `Comment`='Silithus, Cenarion Hold - Silithus' WHERE `ID`=910;
UPDATE `graveyard_zone` SET `Comment`='Duskwood, Ravenhill - Duskwood' WHERE `ID`=911;
UPDATE `graveyard_zone` SET `Comment`='Eversong Woods, Sunstrider Isle - Eversong Woods' WHERE `ID`=912;
UPDATE `graveyard_zone` SET `Comment`='Silithus, Scarab Wall (AQ Only)' WHERE `ID`=913;
UPDATE `graveyard_zone` SET `Comment`='Eversong Woods, Farstrider Lodge GY - Eversong Woods' WHERE `ID`=914;
UPDATE `graveyard_zone` SET `Comment`='Ghostlands, Tranquillien - Ghostlands' WHERE `ID`=915;
UPDATE `graveyard_zone` SET `Comment`='Ghostlands, Sanctum - Ghostlands' WHERE `ID`=916;
UPDATE `graveyard_zone` SET `Comment`='Ghostlands, Amani Pass' WHERE `ID`=917;
UPDATE `graveyard_zone` SET `Comment`='Azuremyst Isle, Ammen Vale' WHERE `ID`=918;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Thrallmar' WHERE `ID`=919;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Honor Hold' WHERE `ID`=920;
UPDATE `graveyard_zone` SET `Comment`='Eversong Woods, Silvermoon City' WHERE `ID`=921;
UPDATE `graveyard_zone` SET `Comment`='Azuremyst, Azure Watch GY - Azuremyst Isle' WHERE `ID`=923;
UPDATE `graveyard_zone` SET `Comment`='Azuremyst, Stillpine GY' WHERE `ID`=924;
UPDATE `graveyard_zone` SET `Comment`='Bloodmyst, Blood Watch GY - Bloodmyst Isle' WHERE `ID`=925;
UPDATE `graveyard_zone` SET `Comment`='Bloodmyst, Wilderness GY' WHERE `ID`=926;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Graveyard CG Tower - Eastern Plaguelands' WHERE `ID`=927;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Zabra''jin GY' WHERE `ID`=928;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, SE Graveyard - Nagrand' WHERE `ID`=930;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Temple - Hellfire Peninsula' WHERE `ID`=933;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Falcon Watch - Hellfire Peninsula' WHERE `ID`=934;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 001 - Twisting Nether' WHERE `ID`=942;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 002 - Twisting Nether' WHERE `ID`=943;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 003 - Twisting Nether' WHERE `ID`=944;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 005 - Twisting Nether' WHERE `ID`=945;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 006 - Twisting Nether' WHERE `ID`=946;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 007 - Twisting Nether' WHERE `ID`=947;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 008 - Twisting Nether' WHERE `ID`=948;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 009 - Twisting Nether' WHERE `ID`=949;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 010 - Twisting Nether' WHERE `ID`=950;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 011 - Twisting Nether' WHERE `ID`=951;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 012 - Twisting Nether' WHERE `ID`=952;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 013 - Twisting Nether' WHERE `ID`=953;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 014 - Twisting Nether' WHERE `ID`=954;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 015 - Twisting Nether' WHERE `ID`=955;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 016 - Twisting Nether' WHERE `ID`=956;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 017 - Twisting Nether' WHERE `ID`=957;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 018 - Twisting Nether' WHERE `ID`=958;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 019 - Twisting Nether' WHERE `ID`=959;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 020 - Twisting Nether' WHERE `ID`=960;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 021 - Twisting Nether' WHERE `ID`=961;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 022 - Twisting Nether' WHERE `ID`=962;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 023 - Twisting Nether' WHERE `ID`=963;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 024 - Twisting Nether' WHERE `ID`=964;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 025 - Twisting Nether' WHERE `ID`=965;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 026 - Twisting Nether' WHERE `ID`=966;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 027 - Twisting Nether' WHERE `ID`=967;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Corpse Location 028 - Twisting Nether' WHERE `ID`=968;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, PvP GY' WHERE `ID`=969;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Telredor GY' WHERE `ID`=970;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 006 - Twisting Nether' WHERE `ID`=972;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Cenarion GY - Zangarmarsh' WHERE `ID`=973;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 001 - Twisting Nether' WHERE `ID`=974;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 002 - Twisting Nether' WHERE `ID`=975;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 003 - Twisting Nether' WHERE `ID`=976;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 004 - Twisting Nether' WHERE `ID`=977;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 008 - Twisting Nether' WHERE `ID`=978;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 005 - Twisting Nether' WHERE `ID`=979;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 007 - Twisting Nether' WHERE `ID`=980;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 009 - Twisting Nether' WHERE `ID`=981;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 011 - Twisting Nether' WHERE `ID`=982;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 010 - Twisting Nether' WHERE `ID`=983;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 012 - Twisting Nether' WHERE `ID`=984;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 013 - Twisting Nether' WHERE `ID`=985;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 014 - Twisting Nether' WHERE `ID`=986;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 016 - Twisting Nether' WHERE `ID`=987;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 015 - Twisting Nether' WHERE `ID`=988;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 017 - Twisting Nether' WHERE `ID`=989;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 018 - Twisting Nether' WHERE `ID`=990;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Corpse Location 019 - Twisting Nether' WHERE `ID`=991;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Northwind Cleft - Nagrand' WHERE `ID`=992;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Halaa GY - Nagrand' WHERE `ID`=993;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Shattrath GY' WHERE `ID`=994;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Wilderness GY - Terokkar Forest' WHERE `ID`=995;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 001 - Twisting Nether' WHERE `ID`=999;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 002 - Twisting Nether' WHERE `ID`=1000;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 003 - Twisting Nether' WHERE `ID`=1001;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 004 - Twisting Nether' WHERE `ID`=1002;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 005 - Twisting Nether' WHERE `ID`=1003;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 006 - Twisting Nether' WHERE `ID`=1004;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 007 - Twisting Nether' WHERE `ID`=1005;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 008 - Twisting Nether' WHERE `ID`=1006;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 009 - Twisting Nether' WHERE `ID`=1007;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 010 - Twisting Nether' WHERE `ID`=1008;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 011 - Twisting Nether' WHERE `ID`=1009;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 012 - Twisting Nether' WHERE `ID`=1010;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 013 - Twisting Nether' WHERE `ID`=1011;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Corpse Location 014 - Twisting Nether' WHERE `ID`=1012;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 001 - Twisting Nether' WHERE `ID`=1013;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 002 - Twisting Nether' WHERE `ID`=1014;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 003 - Twisting Nether' WHERE `ID`=1015;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 004 - Twisting Nether' WHERE `ID`=1016;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 005 - Twisting Nether' WHERE `ID`=1017;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 006 - Twisting Nether' WHERE `ID`=1018;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 007 - Twisting Nether' WHERE `ID`=1019;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 008 - Twisting Nether' WHERE `ID`=1020;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 009 - Twisting Nether' WHERE `ID`=1021;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 010 - Twisting Nether' WHERE `ID`=1022;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 011 - Twisting Nether' WHERE `ID`=1023;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 001 - Twisting Nether' WHERE `ID`=1024;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 002 - Twisting Nether' WHERE `ID`=1025;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 003 - Twisting Nether' WHERE `ID`=1026;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 004 - Twisting Nether' WHERE `ID`=1027;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 016 - Twisting Nether' WHERE `ID`=1028;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 017 - Twisting Nether' WHERE `ID`=1029;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 018 - Twisting Nether' WHERE `ID`=1030;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 019 - Twisting Nether' WHERE `ID`=1031;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 020 - Twisting Nether' WHERE `ID`=1032;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 021 - Twisting Nether' WHERE `ID`=1033;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 022 - Twisting Nether' WHERE `ID`=1034;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Corpse Location 023 - Twisting Nether' WHERE `ID`=1035;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Portal Plateau - Nagrand' WHERE `ID`=1037;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, Elemental Plateau - Nagrand' WHERE `ID`=1038;
UPDATE `graveyard_zone` SET `Comment`='Nagrand, SW Graveyard - Nagrand' WHERE `ID`=1039;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Throne of Kil''Jaedan - Hellfire Peninsula' WHERE `ID`=1040;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Dark Portal - Hellfire Peninsula' WHERE `ID`=1041;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Bone Wastes GY' WHERE `ID`=1042;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Harborage GY - Zangarmarsh' WHERE `ID`=1043;
UPDATE `graveyard_zone` SET `Comment`='Zangarmarsh, Sporeggar GY - Zangarmarsh' WHERE `ID`=1044;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm, Stormspire GY - Netherstorm' WHERE `ID`=1045;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm, Area 52 GY - Netherstorm' WHERE `ID`=1046;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon Valley, Shadowmoon Village GY - Shadowmoon Valley' WHERE `ID`=1047;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon Valley, Wildhammer GY - Shadowmoon Valley' WHERE `ID`=1048;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Sylvanaar GY - Blade''s Edge Mountains' WHERE `ID`=1049;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Thunderlord GY - Blade''s Edge Mountains' WHERE `ID`=1050;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Skettis GY - Terokkar Forest' WHERE `ID`=1051;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 005 - Twisting Nether' WHERE `ID`=1052;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 006 - Twisting Nether' WHERE `ID`=1053;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 007 - Twisting Nether' WHERE `ID`=1054;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 008 - Twisting Nether' WHERE `ID`=1055;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 009 - Twisting Nether' WHERE `ID`=1056;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 010 - Twisting Nether' WHERE `ID`=1057;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 011 - Twisting Nether' WHERE `ID`=1058;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 012 - Twisting Nether' WHERE `ID`=1059;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 013 - Twisting Nether' WHERE `ID`=1060;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 014 - Twisting Nether' WHERE `ID`=1061;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 015 - Twisting Nether' WHERE `ID`=1062;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 016 - Twisting Nether' WHERE `ID`=1063;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 017 - Twisting Nether' WHERE `ID`=1064;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 018 - Twisting Nether' WHERE `ID`=1065;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 019 - Twisting Nether' WHERE `ID`=1066;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 020 - Twisting Nether' WHERE `ID`=1067;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 021 - Twisting Nether' WHERE `ID`=1068;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 022 - Twisting Nether' WHERE `ID`=1069;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon, Corpse Location 023 - Twisting Nether' WHERE `ID`=1070;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 000 - Twisting Nether' WHERE `ID`=1072;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 001 - Twisting Nether' WHERE `ID`=1073;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 002 - Twisting Nether' WHERE `ID`=1074;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 003 - Twisting Nether' WHERE `ID`=1075;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 004 - Twisting Nether' WHERE `ID`=1076;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 005 - Twisting Nether' WHERE `ID`=1077;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 006 - Twisting Nether' WHERE `ID`=1078;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 007 - Twisting Nether' WHERE `ID`=1079;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 008 - Twisting Nether' WHERE `ID`=1080;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 009 - Twisting Nether' WHERE `ID`=1081;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 010 - Twisting Nether' WHERE `ID`=1082;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 011 - Twisting Nether' WHERE `ID`=1083;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 012 - Twisting Nether' WHERE `ID`=1084;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 013 - Twisting Nether' WHERE `ID`=1085;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 014 - Twisting Nether' WHERE `ID`=1086;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 015 - Twisting Nether' WHERE `ID`=1087;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 016 - Twisting Nether' WHERE `ID`=1088;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 017 - Twisting Nether' WHERE `ID`=1089;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 018 - Twisting Nether' WHERE `ID`=1090;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 019 - Twisting Nether' WHERE `ID`=1091;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 020 - Twisting Nether' WHERE `ID`=1092;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 021 - Twisting Nether' WHERE `ID`=1093;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 022 - Twisting Nether' WHERE `ID`=1094;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 023 - Twisting Nether' WHERE `ID`=1095;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 024 - Twisting Nether' WHERE `ID`=1096;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 025 - Twisting Nether' WHERE `ID`=1097;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 026 - Twisting Nether' WHERE `ID`=1098;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 027 - Twisting Nether' WHERE `ID`=1099;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 028 - Twisting Nether' WHERE `ID`=1100;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 029 - Twisting Nether' WHERE `ID`=1101;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Corpse Location 030 - Twisting Nether' WHERE `ID`=1102;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 000 - Twisting Nether' WHERE `ID`=1134;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 001 - Twisting Nether' WHERE `ID`=1135;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 002 - Twisting Nether' WHERE `ID`=1136;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 003 - Twisting Nether' WHERE `ID`=1137;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 004 - Twisting Nether' WHERE `ID`=1138;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 005 - Twisting Nether' WHERE `ID`=1139;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 006 - Twisting Nether' WHERE `ID`=1140;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 007 - Twisting Nether' WHERE `ID`=1141;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 008 - Twisting Nether' WHERE `ID`=1142;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 009 - Twisting Nether' WHERE `ID`=1143;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 010 - Twisting Nether' WHERE `ID`=1144;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 011 - Twisting Nether' WHERE `ID`=1145;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 012 - Twisting Nether' WHERE `ID`=1146;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 013 - Twisting Nether' WHERE `ID`=1147;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 014 - Twisting Nether' WHERE `ID`=1148;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 015 - Twisting Nether' WHERE `ID`=1149;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 016 - Twisting Nether' WHERE `ID`=1150;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 017 - Twisting Nether' WHERE `ID`=1151;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 018 - Twisting Nether' WHERE `ID`=1152;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 019 - Twisting Nether' WHERE `ID`=1153;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 020 - Twisting Nether' WHERE `ID`=1154;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 021 - Twisting Nether' WHERE `ID`=1155;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 022 - Twisting Nether' WHERE `ID`=1156;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 023 - Twisting Nether' WHERE `ID`=1157;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 024 - Twisting Nether' WHERE `ID`=1158;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 025 - Twisting Nether' WHERE `ID`=1159;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 026 - Twisting Nether' WHERE `ID`=1160;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 027 - Twisting Nether' WHERE `ID`=1161;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 028 - Twisting Nether' WHERE `ID`=1162;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 029 - Twisting Nether' WHERE `ID`=1163;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 030 - Twisting Nether' WHERE `ID`=1164;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 031 - Twisting Nether' WHERE `ID`=1165;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 032 - Twisting Nether' WHERE `ID`=1166;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 033 - Twisting Nether' WHERE `ID`=1167;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 034 - Twisting Nether' WHERE `ID`=1168;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 035 - Twisting Nether' WHERE `ID`=1169;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 036 - Twisting Nether' WHERE `ID`=1170;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 037 - Twisting Nether' WHERE `ID`=1171;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 038 - Twisting Nether' WHERE `ID`=1172;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 039 - Twisting Nether' WHERE `ID`=1173;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 040 - Twisting Nether' WHERE `ID`=1174;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 041 - Twisting Nether' WHERE `ID`=1175;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 042 - Twisting Nether' WHERE `ID`=1176;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 043 - Twisting Nether' WHERE `ID`=1177;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 044 - Twisting Nether' WHERE `ID`=1178;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 045 - Twisting Nether' WHERE `ID`=1179;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 046 - Twisting Nether' WHERE `ID`=1180;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 047 - Twisting Nether' WHERE `ID`=1181;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 048 - Twisting Nether' WHERE `ID`=1182;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 049 - Twisting Nether' WHERE `ID`=1183;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 050 - Twisting Nether' WHERE `ID`=1184;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 051 - Twisting Nether' WHERE `ID`=1185;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 052 - Twisting Nether' WHERE `ID`=1186;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 053 - Twisting Nether' WHERE `ID`=1187;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 054 - Twisting Nether' WHERE `ID`=1188;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 055 - Twisting Nether' WHERE `ID`=1189;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 056 - Twisting Nether' WHERE `ID`=1190;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 057 - Twisting Nether' WHERE `ID`=1191;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 058 - Twisting Nether' WHERE `ID`=1192;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 059 - Twisting Nether' WHERE `ID`=1193;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 060 - Twisting Nether' WHERE `ID`=1194;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 061 - Twisting Nether' WHERE `ID`=1195;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 062 - Twisting Nether' WHERE `ID`=1196;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 063 - Twisting Nether' WHERE `ID`=1197;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 064 - Twisting Nether' WHERE `ID`=1198;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 065 - Twisting Nether' WHERE `ID`=1199;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 066 - Twisting Nether' WHERE `ID`=1200;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 067 - Twisting Nether' WHERE `ID`=1201;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 068 - Twisting Nether' WHERE `ID`=1202;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 069 - Twisting Nether' WHERE `ID`=1203;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 070 - Twisting Nether' WHERE `ID`=1204;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 071 - Twisting Nether' WHERE `ID`=1205;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 072 - Twisting Nether' WHERE `ID`=1206;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 073 - Twisting Nether' WHERE `ID`=1207;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 074 - Twisting Nether' WHERE `ID`=1208;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 075 - Twisting Nether' WHERE `ID`=1209;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 076 - Twisting Nether' WHERE `ID`=1210;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 077 - Twisting Nether' WHERE `ID`=1211;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 078 - Twisting Nether' WHERE `ID`=1212;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 079 - Twisting Nether' WHERE `ID`=1213;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 080 - Twisting Nether' WHERE `ID`=1214;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 081 - Twisting Nether' WHERE `ID`=1215;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 082 - Twisting Nether' WHERE `ID`=1216;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 083 - Twisting Nether' WHERE `ID`=1217;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 084 - Twisting Nether' WHERE `ID`=1218;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 085 - Twisting Nether' WHERE `ID`=1219;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 086 - Twisting Nether' WHERE `ID`=1220;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 087 - Twisting Nether' WHERE `ID`=1221;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 088 - Twisting Nether' WHERE `ID`=1222;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 089 - Twisting Nether' WHERE `ID`=1223;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 090 - Twisting Nether' WHERE `ID`=1224;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 091 - Twisting Nether' WHERE `ID`=1225;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 092 - Twisting Nether' WHERE `ID`=1226;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 093 - Twisting Nether' WHERE `ID`=1227;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 094 - Twisting Nether' WHERE `ID`=1228;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 095 - Twisting Nether' WHERE `ID`=1229;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 096 - Twisting Nether' WHERE `ID`=1230;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 097 - Twisting Nether' WHERE `ID`=1231;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 098 - Twisting Nether' WHERE `ID`=1232;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 099 - Twisting Nether' WHERE `ID`=1233;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 100 - Twisting Nether' WHERE `ID`=1234;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 101 - Twisting Nether' WHERE `ID`=1235;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 102 - Twisting Nether' WHERE `ID`=1236;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 103 - Twisting Nether' WHERE `ID`=1237;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 104 - Twisting Nether' WHERE `ID`=1238;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm - Corpse Catcher 105 - Twisting Nether' WHERE `ID`=1239;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Force Camps (Alliance) - Hellfire Peninsula' WHERE `ID`=1240;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Evergrove GY' WHERE `ID`=1241;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, North Ridge GY - Blade''s Edge Mountains' WHERE `ID`=1242;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, West Ridge GY - Blade''s Edge Mountains' WHERE `ID`=1243;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, East Ridge GY - Blade''s Edge Mountains' WHERE `ID`=1244;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm, Cosmowrench GY' WHERE `ID`=1247;
UPDATE `graveyard_zone` SET `Comment`='Hellfire Peninsula, Spinebreaker GY - Hellfire Peninsula' WHERE `ID`=1248;
UPDATE `graveyard_zone` SET `Comment`='Tanaris, CoT GY' WHERE `ID`=1249;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon Valley, Altar GY - Shadowmoon Valley' WHERE `ID`=1250;
UPDATE `graveyard_zone` SET `Comment`='Shadowmoon Valley, Sanctum GY - Shadowmoon Valley' WHERE `ID`=1251;
UPDATE `graveyard_zone` SET `Comment`='Netherstorm, Kirin''Var GY - Netherstorm' WHERE `ID`=1252;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Toshley GY - Blade''s Edge Mountains' WHERE `ID`=1253;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, Raven Wood GY - Blade''s Edge Mountains' WHERE `ID`=1254;
UPDATE `graveyard_zone` SET `Comment`='Blade''s Edge, NE Ridge GY - Blade''s Edge Mountains' WHERE `ID`=1255;
UPDATE `graveyard_zone` SET `Comment`='Silverpine Forest, South GY - Silverpine Forest' WHERE `ID`=1256;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Ogre GY - Terokkar Forest' WHERE `ID`=1257;
UPDATE `graveyard_zone` SET `Comment`='Black Temple, Alliance GY - Black Temple' WHERE `ID`=1261;
UPDATE `graveyard_zone` SET `Comment`='Black Temple, Horde GY - Black Temple' WHERE `ID`=1262;
UPDATE `graveyard_zone` SET `Comment`='Dustwallow Marsh, Tabetha''s GY - Dustwallow Marsh' WHERE `ID`=1264;
UPDATE `graveyard_zone` SET `Comment`='Dustwallow Marsh, Mudsprocket GY - Onyxia''s Lair' WHERE `ID`=1265;
UPDATE `graveyard_zone` SET `Comment`='Dustwallow Marsh, Mudsprocket GY - Dustwallow Marsh' WHERE `ID`=1265;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Northwest GY - Howling Fjord' WHERE `ID`=1266;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Tuskarr GY - Howling Fjord' WHERE `ID`=1267;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Island GY - Howling Fjord' WHERE `ID`=1268;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Central GY - Howling Fjord' WHERE `ID`=1269;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, North GY - Howling Fjord' WHERE `ID`=1270;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Vengance GY - Howling Fjord' WHERE `ID`=1271;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Southeast GY - Howling Fjord' WHERE `ID`=1272;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, South Beach GY - Howling Fjord' WHERE `ID`=1273;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, South GY - Howling Fjord' WHERE `ID`=1274;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Valgarde GY - Howling Fjord' WHERE `ID`=1275;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Northeast GY - Howling Fjord' WHERE `ID`=1276;
UPDATE `graveyard_zone` SET `Comment`='Un''Goro Crater, Central GY - Un''Goro Crater' WHERE `ID`=1277;
UPDATE `graveyard_zone` SET `Comment`='Un''Goro Crater, Marshal''s GY - Un''Goro Crater' WHERE `ID`=1278;
UPDATE `graveyard_zone` SET `Comment`='Stonetalon Mountains, Charred Vale GY - Stonetalon Mountains' WHERE `ID`=1279;
UPDATE `graveyard_zone` SET `Comment`='Stonetalon Mountains, Peak GY - Stonetalon Mountains' WHERE `ID`=1280;
UPDATE `graveyard_zone` SET `Comment`='Tanaris, Pirate GY - Tanaris' WHERE `ID`=1281;
UPDATE `graveyard_zone` SET `Comment`='Tanaris, Central GY - Tanaris' WHERE `ID`=1282;
UPDATE `graveyard_zone` SET `Comment`='Winterspring, South GY - Winterspring' WHERE `ID`=1283;
UPDATE `graveyard_zone` SET `Comment`='Winterspring, West GY - Winterspring' WHERE `ID`=1284;
UPDATE `graveyard_zone` SET `Comment`='Western Plaguelands, Central GY - Western Plaguelands' WHERE `ID`=1286;
UPDATE `graveyard_zone` SET `Comment`='Searing Gorge, SE GY - Searing Gorge' WHERE `ID`=1287;
UPDATE `graveyard_zone` SET `Comment`='Badlands, South GY - Badlands' WHERE `ID`=1288;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, South GY - Southern Barrens' WHERE `ID`=1289;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Tuskar GY - Borean Tundra' WHERE `ID`=1290;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Razorthorn Rise GY - Terokkar Forest' WHERE `ID`=1291;
UPDATE `graveyard_zone` SET `Comment`='Isle of Quel''Danas, Staging Area GY - Isle of Quel''Danas' WHERE `ID`=1292;
UPDATE `graveyard_zone` SET `Comment`='Isle of Quel''Danas, Staging Area GY - Magisters'' Terrace' WHERE `ID`=1293;
UPDATE `graveyard_zone` SET `Comment`='Isle of Quel''Danas, Staging Area GY - Sunwell Plateau' WHERE `ID`=1293;
UPDATE `graveyard_zone` SET `Comment`='Terokkar Forest, Lake Jorune GY - Terokkar Forest' WHERE `ID`=1298;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Vileprey GY - Grizzly Hills' WHERE `ID`=1300;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Amberpine GY - Grizzly Hills' WHERE `ID`=1301;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Westfall GY - Grizzly Hills' WHERE `ID`=1302;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Eastern GY - Grizzly Hills' WHERE `ID`=1303;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Northwestern GY - Grizzly Hills' WHERE `ID`=1304;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Southwestern GY - Grizzly Hills' WHERE `ID`=1305;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Central GY - Grizzly Hills' WHERE `ID`=1306;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Northeastern GY - Dragonblight' WHERE `ID`=1307;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Wintergarde GY - Naxxramas' WHERE `ID`=1308;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Wintergarde GY - Dragonblight' WHERE `ID`=1308;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Forsaken East GY - Naxxramas' WHERE `ID`=1309;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Agmar''s Hammer GY - Dragonblight' WHERE `ID`=1310;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Star''s Rest GY - Dragonblight' WHERE `ID`=1311;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Moa''ki Harbor GY - Dragonblight' WHERE `ID`=1312;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Borean Border GY - Dragonblight' WHERE `ID`=1313;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Wyrmrest GY - Dragonblight' WHERE `ID`=1314;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Wrathgate Horde GY - Dragonblight' WHERE `ID`=1315;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Wrathgate Alliance GY - Dragonblight' WHERE `ID`=1316;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Warsong Hold GY - Borean Tundra' WHERE `ID`=1317;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Riplash GY - Borean Tundra' WHERE `ID`=1318;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Coldarra GY' WHERE `ID`=1319;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Amber Ledge GY - Borean Tundra' WHERE `ID`=1320;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Fizzcrank GY - Borean Tundra' WHERE `ID`=1321;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Bor''Gorok GY - Borean Tundra' WHERE `ID`=1322;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Death''s Stand GY - Borean Tundra' WHERE `ID`=1323;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Taunka''le GY - Borean Tundra' WHERE `ID`=1324;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Coast of Echoes GY - Borean Tundra' WHERE `ID`=1325;
UPDATE `graveyard_zone` SET `Comment`='Borean Tundra, Valiance Keep GY - Borean Tundra' WHERE `ID`=1326;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Fortress (West) - Wintergrasp' WHERE `ID`=1328;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Siege Factory (Defense NE) - Wintergrasp' WHERE `ID`=1329;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Siege Factory (Defense NW) - Wintergrasp' WHERE `ID`=1330;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Horde Starting Area - Wintergrasp' WHERE `ID`=1331;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Alliance Starting Area - Wintergrasp' WHERE `ID`=1332;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Siege Factory (SE) - Wintergrasp' WHERE `ID`=1333;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Siege Factory (SW) - Wintergrasp' WHERE `ID`=1334;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, South GY - Sholazar Basin' WHERE `ID`=1336;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Utgarde GY - Utgarde Pinnacle' WHERE `ID`=1337;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, Nesingwary GY - Sholazar Basin' WHERE `ID`=1341;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, Central GY - Sholazar Basin' WHERE `ID`=1342;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, Northwest GY - Sholazar Basin' WHERE `ID`=1343;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, Northeast GY - Sholazar Basin' WHERE `ID`=1344;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, East GY - Sholazar Basin' WHERE `ID`=1345;
UPDATE `graveyard_zone` SET `Comment`='Zul''Drak, Western GY - Zul''Drak' WHERE `ID`=1352;
UPDATE `graveyard_zone` SET `Comment`='Zul''Drak, Northwestern GY - Zul''Drak' WHERE `ID`=1353;
UPDATE `graveyard_zone` SET `Comment`='Zul''Drak, Southwestern GY - Zul''Drak' WHERE `ID`=1354;
UPDATE `graveyard_zone` SET `Comment`='Zul''Drak, Southern GY - Zul''Drak' WHERE `ID`=1355;
UPDATE `graveyard_zone` SET `Comment`='Zul''Drak, Central GY - Zul''Drak' WHERE `ID`=1356;
UPDATE `graveyard_zone` SET `Comment`='Zul''Drak, Southeastern GY - Zul''Drak' WHERE `ID`=1357;
UPDATE `graveyard_zone` SET `Comment`='Zul''Drak, Gun''Drak GY - Zul''Drak' WHERE `ID`=1358;
UPDATE `graveyard_zone` SET `Comment`='Crystalsong Forest, Dalaran GY' WHERE `ID`=1359;
UPDATE `graveyard_zone` SET `Comment`='Ebon Hold GY - Chapter I - Plaguelands: The Scarlet Enclave' WHERE `ID`=1360;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Ebon Hold GY - Eastern Plaguelands' WHERE `ID`=1369;
UPDATE `graveyard_zone` SET `Comment`='Ebon Hold GY - Chapter II/III - Plaguelands: The Scarlet Enclave' WHERE `ID`=1370;
UPDATE `graveyard_zone` SET `Comment`='Ebon Hold GY - Chapter IV - Plaguelands: The Scarlet Enclave' WHERE `ID`=1371;
UPDATE `graveyard_zone` SET `Comment`='Un''Goro Crater, Shaper''s Terrace GY - Un''Goro Crater' WHERE `ID`=1372;
UPDATE `graveyard_zone` SET `Comment`='Howling Fjord, Utgarde 2 GY - Utgarde Keep' WHERE `ID`=1376;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, Stormwright GY - Sholazar Basin' WHERE `ID`=1379;
UPDATE `graveyard_zone` SET `Comment`='Crystalsong Forest, Alliance GY - Crystalsong Forest' WHERE `ID`=1380;
UPDATE `graveyard_zone` SET `Comment`='Icecrown, Argent Vanguard - Icecrown' WHERE `ID`=1381;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Valkyrion GY - The Storm Peaks' WHERE `ID`=1383;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Ulduar GY - The Storm Peaks' WHERE `ID`=1384;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Temple East GY - The Storm Peaks' WHERE `ID`=1385;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Temple West GY - The Storm Peaks' WHERE `ID`=1387;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Frostfield GY - The Storm Peaks' WHERE `ID`=1388;
UPDATE `graveyard_zone` SET `Comment`='Crystalsong Forest, West GY - Crystalsong Forest' WHERE `ID`=1391;
UPDATE `graveyard_zone` SET `Comment`='Crystalsong Forest, Horde GY - Crystalsong Forest' WHERE `ID`=1392;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Naxxramas GY - Dragonblight' WHERE `ID`=1393;
UPDATE `graveyard_zone` SET `Comment`='Dragonblight, Icemist GY - Dragonblight' WHERE `ID`=1394;
UPDATE `graveyard_zone` SET `Comment`='Icecrown Glacier, Quarry GY - Icecrown' WHERE `ID`=1395;
UPDATE `graveyard_zone` SET `Comment`='Icecrown Glacier, Vrykul Central GY - Icecrown' WHERE `ID`=1396;
UPDATE `graveyard_zone` SET `Comment`='Icecrown Glacier, Northeast Ice GY - Icecrown' WHERE `ID`=1397;
UPDATE `graveyard_zone` SET `Comment`='Grizzly Hills, Drak''tharon GY - Grizzly Hills' WHERE `ID`=1398;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Temple of the Makers GY - The Storm Peaks' WHERE `ID`=1400;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Snowdrift GY - The Storm Peaks' WHERE `ID`=1401;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Temple of Storms GY - The Storm Peaks' WHERE `ID`=1402;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, K3 GY - The Storm Peaks' WHERE `ID`=1403;
UPDATE `graveyard_zone` SET `Comment`='Sholazar Basin, Frenzyheart GY - Sholazar Basin' WHERE `ID`=1404;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands: Acherus - Eastern Plaguelands' WHERE `ID`=1405;
UPDATE `graveyard_zone` SET `Comment`='Icecrown Glacier, Jotunheim GY - Icecrown' WHERE `ID`=1407;
UPDATE `graveyard_zone` SET `Comment`='Storm Peaks, Foot Steppes GY - The Storm Peaks' WHERE `ID`=1408;
UPDATE `graveyard_zone` SET `Comment`='Undercity - Alliance - Wrath Gate - Tirisfal Glades' WHERE `ID`=1409;
UPDATE `graveyard_zone` SET `Comment`='Alterac Mountains - Central GY - Hillsbrad Foothills' WHERE `ID`=1411;
UPDATE `graveyard_zone` SET `Comment`='Winterspring, Wintersaber GY - Winterspring' WHERE `ID`=1416;
UPDATE `graveyard_zone` SET `Comment`='Winterspring, Crossroad GY - Winterspring' WHERE `ID`=1417;
UPDATE `graveyard_zone` SET `Comment`='Winterspring, Darkwhisper GY - Winterspring' WHERE `ID`=1418;
UPDATE `graveyard_zone` SET `Comment`='Azshara, Southern Azshara GY - Azshara' WHERE `ID`=1419;
UPDATE `graveyard_zone` SET `Comment`='Azshara, Bilgewater Harbor GY - Azshara' WHERE `ID`=1420;
UPDATE `graveyard_zone` SET `Comment`='Desolace, Ghost Walker Post GY - Desolace' WHERE `ID`=1421;
UPDATE `graveyard_zone` SET `Comment`='Desolace, Sar''theris Strand GY - Desolace' WHERE `ID`=1422;
UPDATE `graveyard_zone` SET `Comment`='Desolace, Mannoroc Coven GY - Desolace' WHERE `ID`=1423;
UPDATE `graveyard_zone` SET `Comment`='Desolace, Magram Village GY - Desolace' WHERE `ID`=1424;
UPDATE `graveyard_zone` SET `Comment`='Desolace, Roadside GY - Desolace' WHERE `ID`=1425;
UPDATE `graveyard_zone` SET `Comment`='Ashenvale, Shrine of Aessina GY - Ashenvale' WHERE `ID`=1426;
UPDATE `graveyard_zone` SET `Comment`='Ashenvale, Nightsong GY - Ashenvale' WHERE `ID`=1427;
UPDATE `graveyard_zone` SET `Comment`='Stonetalon Mountains, Windshear Crag GY - Stonetalon Mountains' WHERE `ID`=1428;
UPDATE `graveyard_zone` SET `Comment`='Stonetalon Mountains, Mirkfallon GY - Stonetalon Mountains' WHERE `ID`=1429;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, Forgotten Pools - Northern Barrens' WHERE `ID`=1430;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, North GY - Northern Barrens' WHERE `ID`=1431;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, Raptor Grounds - Southern Barrens' WHERE `ID`=1432;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, Central GY - Southern Barrens' WHERE `ID`=1433;
UPDATE `graveyard_zone` SET `Comment`='The Barrens, East GY - Northern Barrens' WHERE `ID`=1434;
UPDATE `graveyard_zone` SET `Comment`='Mulgore, Southeast GY - Mulgore' WHERE `ID`=1435;
UPDATE `graveyard_zone` SET `Comment`='Mulgore, Red Rocks GY - Mulgore' WHERE `ID`=1436;
UPDATE `graveyard_zone` SET `Comment`='Thousand Needles, Freewind Post GY (MOVED) - Thousand Needles' WHERE `ID`=1437;
UPDATE `graveyard_zone` SET `Comment`='Thousand Needles, Speed Barge GY (MOVED) - Thousand Needles' WHERE `ID`=1438;
UPDATE `graveyard_zone` SET `Comment`='Tanaris, Southwest GY - Tanaris' WHERE `ID`=1439;
UPDATE `graveyard_zone` SET `Comment`='Tanaris, Abyssal Sands GY - Tanaris' WHERE `ID`=1440;
UPDATE `graveyard_zone` SET `Comment`='Feralas, Ruins of Isildien GY - Feralas' WHERE `ID`=1441;
UPDATE `graveyard_zone` SET `Comment`='Feralas, Lower Wilds GY - Feralas' WHERE `ID`=1442;
UPDATE `graveyard_zone` SET `Comment`='Feralas, Twin Colossals GY - Feralas' WHERE `ID`=1443;
UPDATE `graveyard_zone` SET `Comment`='Silithus, Hive''Regal - Silithus' WHERE `ID`=1444;
UPDATE `graveyard_zone` SET `Comment`='Silithus, Twilight Base Camp - Silithus' WHERE `ID`=1445;
UPDATE `graveyard_zone` SET `Comment`='Arathi Highlands, Stromgarde - Arathi Highlands' WHERE `ID`=1446;
UPDATE `graveyard_zone` SET `Comment`='Hillsbrad Foothills, Thoradin''s Wall - Hillsbrad Foothills' WHERE `ID`=1447;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Northdale - Eastern Plaguelands' WHERE `ID`=1448;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, Stratholme - Eastern Plaguelands' WHERE `ID`=1449;
UPDATE `graveyard_zone` SET `Comment`='Eastern Plaguelands, West GY - Eastern Plaguelands' WHERE `ID`=1450;
UPDATE `graveyard_zone` SET `Comment`='Western Plaguelands, Hearthglen - Eastern Plaguelands' WHERE `ID`=1451;
UPDATE `graveyard_zone` SET `Comment`='Hillsbrad Foothills, Hillsbrad Fields GY - Hillsbrad Foothills' WHERE `ID`=1452;
UPDATE `graveyard_zone` SET `Comment`='The Hinterlands, Seradane - The Hinterlands' WHERE `ID`=1453;
UPDATE `graveyard_zone` SET `Comment`='The Hinterlands, Shadra''Alor - The Hinterlands' WHERE `ID`=1454;
UPDATE `graveyard_zone` SET `Comment`='Wetlands, Sundown Marsh GY - Wetlands' WHERE `ID`=1455;
UPDATE `graveyard_zone` SET `Comment`='Wetlands, South Road GY - Wetlands' WHERE `ID`=1456;
UPDATE `graveyard_zone` SET `Comment`='Wetlands, Raptor Ridge GY - Wetlands' WHERE `ID`=1457;
UPDATE `graveyard_zone` SET `Comment`='AAA - Arena (Dev Test) - Stranglethorn Vale' WHERE `ID`=1458;
UPDATE `graveyard_zone` SET `Comment`='Stranglethorn Vale, Central GY - Stranglethorn Vale' WHERE `ID`=1459;
UPDATE `graveyard_zone` SET `Comment`='Stranglethorn Vale, Savage Coast GY - Stranglethorn Vale' WHERE `ID`=1460;
UPDATE `graveyard_zone` SET `Comment`='Duskwood, Central GY - Duskwood' WHERE `ID`=1461;
UPDATE `graveyard_zone` SET `Comment`='Westfall, Dagger Hills GY - Westfall' WHERE `ID`=1462;
UPDATE `graveyard_zone` SET `Comment`='Westfall, Longshore - Westfall' WHERE `ID`=1463;
UPDATE `graveyard_zone` SET `Comment`='Blasted Lands, Dark Portal GY - Blasted Lands' WHERE `ID`=1464;
UPDATE `graveyard_zone` SET `Comment`='Swamp of Sorrows, Alliance Hub GY - Swamp of Sorrows' WHERE `ID`=1465;
UPDATE `graveyard_zone` SET `Comment`='Swamp of Sorrows, Splinterspear GY - Swamp of Sorrows' WHERE `ID`=1466;
UPDATE `graveyard_zone` SET `Comment`='Redridge Mountains, Stonewatch - Redridge Mountains' WHERE `ID`=1467;
UPDATE `graveyard_zone` SET `Comment`='Elwynn Forest, Tower of Azora - Elwynn Forest' WHERE `ID`=1468;
UPDATE `graveyard_zone` SET `Comment`='Burning Steppes, Blackrock Mountain - Blackrock Mountain' WHERE `ID`=1469;
UPDATE `graveyard_zone` SET `Comment`='Burning Steppes, East GY - Burning Steppes' WHERE `ID`=1470;
UPDATE `graveyard_zone` SET `Comment`='Dun Morogh, Iceflow Lake - Dun Morogh' WHERE `ID`=1471;
UPDATE `graveyard_zone` SET `Comment`='Dun Morogh, East Road - Dun Morogh' WHERE `ID`=1472;
UPDATE `graveyard_zone` SET `Comment`='Loch Modan, The Loch - Loch Modan' WHERE `ID`=1473;
UPDATE `graveyard_zone` SET `Comment`='Wintergrasp, Fortress Graveyard (Indoors) - Vault of Archavon' WHERE `ID`=1474;
UPDATE `graveyard_zone` SET `Comment`='Icecrown, Argent Tournament GY' WHERE `ID`=1478;
UPDATE `graveyard_zone` SET `Comment`='Icecrown Glacier, Citadel GY' WHERE `ID`=1682;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
