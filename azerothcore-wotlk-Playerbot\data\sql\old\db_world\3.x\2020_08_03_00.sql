-- DB update 2020_08_02_00 -> 2020_08_03_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_08_02_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_08_02_00 2020_08_03_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1591992052509403400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1591992052509403400');

DELETE FROM `playercreateinfo_item` WHERE `itemid` = 40582 AND `amount` = -1;

ALTER TABLE `playercreateinfo_item`
	CHANGE `amount` `amount` SMALLINT UNSIGNED NOT NULL DEFAULT 1,
	ADD `Note` VARCHAR(255) AFTER `amount`;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
