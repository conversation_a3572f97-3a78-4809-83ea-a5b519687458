-- DB update 2021_09_07_04 -> 2021_09_07_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_07_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_07_04 2021_09_07_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1630695496578966300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1630695496578966300');

UPDATE `spell_group` SET `special_flag`=0x1000 WHERE `spell_id`=6343;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_07_05' WHERE sql_rev = '1630695496578966300';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
