-- DB update 2021_12_09_00 -> 2021_12_09_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_09_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_09_00 2021_12_09_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1637978286218341866'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1637978286218341866');

DELETE FROM `gameobject_template_addon` WHERE `entry` IN (186648,187021,186672,186667);
INSERT INTO `gameobject_template_addon` (`entry`, `faction`, `flags`, `mingold`, `maxgold`) VALUES
(186648,94,0,0,0),
(187021,94,0,0,0),
(186672,94,0,0,0),
(186667,94,0,0,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_09_01' WHERE sql_rev = '1637978286218341866';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
