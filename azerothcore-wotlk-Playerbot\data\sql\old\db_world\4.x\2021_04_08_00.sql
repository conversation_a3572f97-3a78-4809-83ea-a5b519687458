-- DB update 2021_04_07_01 -> 2021_04_08_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_07_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_07_01 2021_04_08_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1617549311457174700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1617549311457174700');

<PERSON>TER TABLE `spell_dbc`
    CHANGE `Description_Lang_enUS` `Description_Lang_enUS` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_enGB` `Description_Lang_enGB` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_koKR` `Description_Lang_koKR` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_frFR` `Description_Lang_frFR` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_deDE` `Description_Lang_deDE` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_enCN` `Description_Lang_enCN` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_zhCN` `Description_Lang_zhCN` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_enTW` `Description_Lang_enTW` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_zhTW` `Description_Lang_zhTW` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_esES` `Description_Lang_esES` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_esMX` `Description_Lang_esMX` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_ruRU` `Description_Lang_ruRU` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_ptPT` `Description_Lang_ptPT` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_ptBR` `Description_Lang_ptBR` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_itIT` `Description_Lang_itIT` text(0) DEFAULT NULL,
    CHANGE `Description_Lang_Unk` `Description_Lang_Unk` text(0) DEFAULT NULL;

DELETE FROM `spell_dbc` WHERE `ID` IN (5308, 20658, 20660, 20661, 20662, 25234, 25236, 47470, 47471);
INSERT INTO `spell_dbc` VALUES
(5308, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 24, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 1','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 0.3, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(20658, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 32, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 2','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 0.6, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(20660, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 40, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 281, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 3','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 0.9, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(20661, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 48, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 403, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 4','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 1.2, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(20662, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 56, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 553, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 5','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 1.5, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(25234, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 65, 65, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 686, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 6','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 1.8, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(25236, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 70, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 864, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 7','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 2.1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(47470, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 73, 73, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 1141, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 8','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 3, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0),
(47471, 0, 0, 0, 327696, 134218240, 0, 263168, 513, 0, 0, 0, 327680, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 80, 80, 0, 1, 150, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 173555, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 1455, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 0, 1648, 0, 50, 'Execute','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Rank 9','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190, 'Attempt to finish off a wounded foe, causing ${$m1+$AP*0.2} damage and converting each extra point of rage into $*10;F1 additional damage (up to a maximum cost of 30 rage).  Only usable on enemies that have less than 20% health.','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712190,'','','','','','','','','', 0, 0, 0, 0, 0, 0, 0, 16712188, 0, 133, 1500, 0, 4, 536870912, 0, 0, 0, 2, 2, 0, 3.8, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
