-- DB update 2021_12_17_10 -> 2021_12_17_11
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_17_10';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_17_10 2021_12_17_11 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639437546334915264'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639437546334915264');

-- Pathing for Knucklerot Entry: 16246
SET @NPC := 82765;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=7218.1196,`position_y`=-6629.3047,`position_z`=57.32447 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,7218.1196,-6629.3047,57.32447,0,0,0,0,100,0),
(@PATH,2,7231.338,-6649.3203,47.811695,0,0,0,0,100,0),
(@PATH,3,7230.456,-6660.803,45.28435,0,0,0,0,100,0),
(@PATH,4,7239.4326,-6665.0493,43.377243,0,0,0,0,100,0),
(@PATH,5,7262.2397,-6659.4585,36.753952,0,0,0,0,100,0),
(@PATH,6,7266.249,-6649.5176,35.426315,0,0,0,0,100,0),
(@PATH,7,7254.9243,-6623.2344,32.39009,0,0,0,0,100,0),
(@PATH,8,7246.369,-6603.0894,30.381788,0,0,0,0,100,0),
(@PATH,9,7237.333,-6588.133,27.051235,0,0,0,0,100,0),
(@PATH,10,7221.332,-6576.1523,21.271461,0,0,0,0,100,0),
(@PATH,11,7208.1,-6556.9106,17.522804,0,0,0,0,100,0),
(@PATH,12,7190.5786,-6543.272,13.899645,0,0,0,0,100,0),
(@PATH,13,7171.0454,-6520.758,11.164112,0,0,0,0,100,0),
(@PATH,14,7148.6,-6500.493,10.721241,0,0,0,0,100,0),
(@PATH,15,7129.309,-6472.9683,12.4379015,0,0,0,0,100,0),
(@PATH,16,7119.624,-6448.0024,18.221363,0,0,0,0,100,0),
(@PATH,17,7116.664,-6428.121,24.726286,0,0,0,0,100,0),
(@PATH,18,7109.51,-6393.672,35.538235,0,0,0,0,100,0),
(@PATH,19,7095.8926,-6373.9375,41.25888,0,0,0,0,100,0),
(@PATH,20,7075.6963,-6350.8867,44.309013,0,0,0,0,100,0),
(@PATH,21,7059.8984,-6338.1333,45.060314,0,0,0,0,100,0),
(@PATH,22,7054.5864,-6314.6484,45.629,0,0,0,0,100,0),
(@PATH,23,7049.105,-6280.729,45.15278,0,0,0,0,100,0),
(@PATH,24,7047.887,-6254.881,46.195107,0,0,0,0,100,0),
(@PATH,25,7046.983,-6227.33,44.565548,0,0,0,0,100,0),
(@PATH,26,7047.6055,-6207.1245,42.058468,0,0,0,0,100,0),
(@PATH,27,7049.5835,-6187.9995,37.554325,0,0,0,0,100,0),
(@PATH,28,7051.144,-6171.618,32.93543,0,0,0,0,100,0),
(@PATH,29,7052.8545,-6146.7764,23.859083,0,0,0,0,100,0),
(@PATH,30,7053.5957,-6137.6416,20.872633,0,0,0,0,100,0),
(@PATH,31,7055.2734,-6133.294,19.905083,0,0,0,0,100,0),
(@PATH,32,7059.672,-6128.687,18.786186,0,0,0,0,100,0),
(@PATH,33,7071.697,-6117.3955,14.023144,0,0,0,0,100,0),
(@PATH,34,7093.6147,-6095.5493,6.574215,0,0,0,0,100,0),
(@PATH,35,7109.7246,-6082.735,4.520455,0,0,0,0,100,0),
(@PATH,36,7126.2793,-6075.2334,4.8707967,0,0,0,0,100,0),
(@PATH,37,7139.5767,-6067.7383,6.6660266,0,0,0,0,100,0),
(@PATH,38,7150.7285,-6058.123,8.560953,0,0,0,0,100,0),
(@PATH,39,7176.9634,-6039.1553,11.663287,0,0,0,0,100,0),
(@PATH,40,7190.601,-6028.1177,13.487218,0,0,0,0,100,0),
(@PATH,41,7212.005,-6016.477,15.9861355,0,0,0,0,100,0),
(@PATH,42,7222.8374,-6013.205,16.825857,0,0,0,0,100,0),
(@PATH,43,7237.5176,-6007.67,17.93024,0,0,0,0,100,0),
(@PATH,44,7261.5806,-6006.891,19.477848,0,0,0,0,100,0),
(@PATH,45,7277.553,-6007.6123,19.174519,0,0,0,0,100,0),
(@PATH,46,7315.783,-6012.2915,17.083754,0,0,0,0,100,0),
(@PATH,47,7329.646,-6016.048,17.00099,0,0,0,0,100,0),
(@PATH,48,7347.3066,-6020.059,14.224654,0,0,0,0,100,0),
(@PATH,49,7361.1743,-6021.5063,15.312545,0,0,0,0,100,0),
(@PATH,50,7374.0566,-6018.3325,15.768617,0,0,0,0,100,0),
(@PATH,51,7388.2017,-6013.831,13.977235,0,0,0,0,100,0),
(@PATH,52,7394.7065,-6017.738,14.771668,0,0,0,0,100,0),
(@PATH,53,7408.4287,-6022.6763,12.761997,0,0,0,0,100,0),
(@PATH,54,7425.6694,-6037.5864,9.673162,0,0,0,0,100,0),
(@PATH,55,7437.733,-6062.2974,11.87658,0,0,0,0,100,0),
(@PATH,56,7437.788,-6089.6313,15.724862,0,0,0,0,100,0),
(@PATH,57,7438.1,-6106.8755,16.700287,0,0,0,0,100,0),
(@PATH,58,7440.6006,-6124.9478,17.647064,0,0,0,0,100,0),
(@PATH,59,7441.397,-6146.6235,16.485638,0,0,0,0,100,0),
(@PATH,60,7441.646,-6163.8735,16.074993,0,0,0,0,100,0),
(@PATH,61,7438.871,-6170.299,18.454395,0,0,0,0,100,0),
(@PATH,62,7430.56,-6193.0127,20.277378,0,0,0,0,100,0),
(@PATH,63,7427.9473,-6217.1826,24.379581,0,0,0,0,100,0),
(@PATH,64,7429.4946,-6229.729,28.576847,0,0,0,0,100,0),
(@PATH,65,7434.894,-6245.916,29.252306,0,0,0,0,100,0),
(@PATH,66,7442.9673,-6269.803,28.677402,0,0,0,0,100,0),
(@PATH,67,7462.119,-6282.438,26.32999,0,0,0,0,100,0),
(@PATH,68,7480.26,-6280.065,19.964615,0,0,0,0,100,0),
(@PATH,69,7490.634,-6276.477,19.59645,0,0,0,0,100,0),
(@PATH,70,7526.979,-6257.7993,11.957291,0,0,0,0,100,0),
(@PATH,71,7543.932,-6245.7954,19.710392,0,0,0,0,100,0),
(@PATH,72,7554.3794,-6236.944,24.672062,0,0,0,0,100,0),
(@PATH,73,7560.5444,-6230.904,26.54689,0,0,0,0,100,0),
(@PATH,74,7603.0435,-6206.7344,22.56906,0,0,0,0,100,0),
(@PATH,75,7616.893,-6196.318,24.003525,0,0,0,0,100,0),
(@PATH,76,7629.8003,-6191.6763,23.44371,0,0,0,0,100,0),
(@PATH,77,7655.933,-6183.077,20.0597,0,0,0,0,100,0),
(@PATH,78,7676.711,-6178.3574,18.821676,0,0,0,0,100,0),
(@PATH,79,7688.25,-6176.118,18.731344,0,0,0,0,100,0),
(@PATH,80,7694.282,-6178.534,18.606344,0,0,0,0,100,0),
(@PATH,81,7700.558,-6189.822,18.464651,0,0,0,0,100,0),
(@PATH,82,7707.638,-6204.4863,19.123034,0,0,0,0,100,0),
(@PATH,83,7715.6304,-6214.595,19.623034,0,0,0,0,100,0),
(@PATH,84,7725.3774,-6231.32,20.123034,0,0,0,0,100,0),
(@PATH,85,7730.224,-6240.7036,20.516788,0,0,0,0,100,0),
(@PATH,86,7737.143,-6249.7085,20.213175,0,0,0,0,100,0),
(@PATH,87,7741.9707,-6254.7295,19.86271,0,0,0,0,100,0),
(@PATH,88,7751.5347,-6263.6587,19.607828,0,0,0,0,100,0),
(@PATH,89,7756.4785,-6264.9946,19.732828,0,0,0,0,100,0),
(@PATH,90,7776.2666,-6256.4766,18.457012,0,0,0,0,100,0),
(@PATH,91,7799.77,-6249.8687,17.128765,0,0,0,0,100,0),
(@PATH,92,7808.941,-6245.6724,19.656225,0,0,0,0,100,0),
(@PATH,93,7828.362,-6232.0547,21.24412,0,0,0,0,100,0),
(@PATH,94,7844.921,-6220.1235,17.537918,0,0,0,0,100,0),
(@PATH,95,7862.1025,-6207.33,19.446854,0,0,0,0,100,0),
(@PATH,96,7878.6743,-6194.4297,20.292835,0,0,0,0,100,0),
(@PATH,97,7883.5947,-6184.6772,19.891712,0,0,0,0,100,0),
(@PATH,98,7888.4834,-6169.678,19.417835,0,0,0,0,100,0),
(@PATH,99,7896.885,-6149.725,17.98382,0,0,0,0,100,0),
(@PATH,100,7905.166,-6128.5474,15.853258,0,0,0,0,100,0),
(@PATH,101,7923.791,-6121.918,15.615221,0,0,0,0,100,0),
(@PATH,102,7934.5,-6119.705,15.771505,0,0,0,0,100,0),
(@PATH,103,7923.791,-6121.918,15.615221,0,0,0,0,100,0),
(@PATH,104,7905.166,-6128.5474,15.853258,0,0,0,0,100,0),
(@PATH,105,7896.885,-6149.725,17.98382,0,0,0,0,100,0),
(@PATH,106,7888.4834,-6169.678,19.417835,0,0,0,0,100,0),
(@PATH,107,7883.5947,-6184.6772,19.891712,0,0,0,0,100,0),
(@PATH,108,7878.6743,-6194.4297,20.292835,0,0,0,0,100,0),
(@PATH,109,7862.1035,-6207.3496,19.359207,0,0,0,0,100,0),
(@PATH,110,7844.921,-6220.1235,17.537918,0,0,0,0,100,0),
(@PATH,111,7828.362,-6232.0547,21.24412,0,0,0,0,100,0),
(@PATH,112,7808.941,-6245.6724,19.656225,0,0,0,0,100,0),
(@PATH,113,7799.77,-6249.8687,17.128765,0,0,0,0,100,0),
(@PATH,114,7776.2666,-6256.4766,18.457012,0,0,0,0,100,0),
(@PATH,115,7756.4785,-6264.9946,19.732828,0,0,0,0,100,0),
(@PATH,116,7751.5347,-6263.6587,19.607828,0,0,0,0,100,0),
(@PATH,117,7741.9707,-6254.7295,19.86271,0,0,0,0,100,0),
(@PATH,118,7737.143,-6249.7085,20.213175,0,0,0,0,100,0),
(@PATH,119,7730.224,-6240.7036,20.516788,0,0,0,0,100,0),
(@PATH,120,7725.3774,-6231.32,20.123034,0,0,0,0,100,0),
(@PATH,121,7715.6304,-6214.595,19.623034,0,0,0,0,100,0),
(@PATH,122,7707.638,-6204.4863,19.123034,0,0,0,0,100,0),
(@PATH,123,7700.558,-6189.822,18.464651,0,0,0,0,100,0),
(@PATH,124,7694.282,-6178.534,18.606344,0,0,0,0,100,0),
(@PATH,125,7688.25,-6176.118,18.731344,0,0,0,0,100,0),
(@PATH,126,7676.711,-6178.3574,18.821676,0,0,0,0,100,0),
(@PATH,127,7655.933,-6183.077,20.0597,0,0,0,0,100,0),
(@PATH,128,7629.8003,-6191.6763,23.44371,0,0,0,0,100,0),
(@PATH,129,7616.893,-6196.318,24.003525,0,0,0,0,100,0),
(@PATH,130,7603.0435,-6206.7344,22.56906,0,0,0,0,100,0),
(@PATH,131,7560.5444,-6230.904,26.54689,0,0,0,0,100,0),
(@PATH,132,7554.3794,-6236.944,24.672062,0,0,0,0,100,0),
(@PATH,133,7543.932,-6245.7954,19.710392,0,0,0,0,100,0),
(@PATH,134,7526.979,-6257.7993,11.957291,0,0,0,0,100,0),
(@PATH,135,7490.634,-6276.477,19.59645,0,0,0,0,100,0),
(@PATH,136,7480.26,-6280.065,19.964615,0,0,0,0,100,0),
(@PATH,137,7462.119,-6282.438,26.32999,0,0,0,0,100,0),
(@PATH,138,7442.9673,-6269.803,28.677402,0,0,0,0,100,0),
(@PATH,139,7434.894,-6245.916,29.252306,0,0,0,0,100,0),
(@PATH,140,7429.4946,-6229.729,28.576847,0,0,0,0,100,0),
(@PATH,141,7427.9473,-6217.1826,24.379581,0,0,0,0,100,0),
(@PATH,142,7430.56,-6193.0127,20.277378,0,0,0,0,100,0),
(@PATH,143,7438.864,-6170.314,18.502735,0,0,0,0,100,0),
(@PATH,144,7441.6387,-6163.8887,16.203167,0,0,0,0,100,0),
(@PATH,145,7441.397,-6146.6235,16.485638,0,0,0,0,100,0),
(@PATH,146,7440.6006,-6124.9478,17.647064,0,0,0,0,100,0),
(@PATH,147,7438.1,-6106.8755,16.700287,0,0,0,0,100,0),
(@PATH,148,7437.788,-6089.6313,15.724862,0,0,0,0,100,0),
(@PATH,149,7437.733,-6062.2974,11.87658,0,0,0,0,100,0),
(@PATH,150,7425.6694,-6037.5864,9.673162,0,0,0,0,100,0),
(@PATH,151,7408.4287,-6022.6763,12.761997,0,0,0,0,100,0),
(@PATH,152,7394.7065,-6017.738,14.771668,0,0,0,0,100,0),
(@PATH,153,7388.2017,-6013.831,13.977235,0,0,0,0,100,0),
(@PATH,154,7374.0566,-6018.3325,15.768617,0,0,0,0,100,0),
(@PATH,155,7361.1743,-6021.5063,15.312545,0,0,0,0,100,0),
(@PATH,156,7347.3066,-6020.059,14.224654,0,0,0,0,100,0),
(@PATH,157,7329.746,-6016.086,16.996107,0,0,0,0,100,0),
(@PATH,158,7315.783,-6012.2915,17.083754,0,0,0,0,100,0),
(@PATH,159,7277.553,-6007.6123,19.174519,0,0,0,0,100,0),
(@PATH,160,7261.5806,-6006.891,19.477848,0,0,0,0,100,0),
(@PATH,161,7237.5176,-6007.67,17.93024,0,0,0,0,100,0),
(@PATH,162,7222.8374,-6013.205,16.825857,0,0,0,0,100,0),
(@PATH,163,7212.005,-6016.477,15.9861355,0,0,0,0,100,0),
(@PATH,164,7190.601,-6028.1177,13.487218,0,0,0,0,100,0),
(@PATH,165,7176.9634,-6039.1553,11.663287,0,0,0,0,100,0),
(@PATH,166,7150.7285,-6058.123,8.560953,0,0,0,0,100,0),
(@PATH,167,7139.5767,-6067.7383,6.6660266,0,0,0,0,100,0),
(@PATH,168,7126.2793,-6075.2334,4.8707967,0,0,0,0,100,0),
(@PATH,169,7109.7246,-6082.735,4.520455,0,0,0,0,100,0),
(@PATH,170,7093.6147,-6095.5493,6.574215,0,0,0,0,100,0),
(@PATH,171,7071.697,-6117.3955,14.023144,0,0,0,0,100,0),
(@PATH,172,7059.672,-6128.687,18.786186,0,0,0,0,100,0),
(@PATH,173,7055.2734,-6133.294,19.905083,0,0,0,0,100,0),
(@PATH,174,7053.5957,-6137.6416,20.872633,0,0,0,0,100,0),
(@PATH,175,7052.8545,-6146.7764,23.859083,0,0,0,0,100,0),
(@PATH,176,7051.144,-6171.618,32.93543,0,0,0,0,100,0),
(@PATH,177,7049.5835,-6187.9995,37.554325,0,0,0,0,100,0),
(@PATH,178,7047.6055,-6207.1245,42.058468,0,0,0,0,100,0),
(@PATH,179,7046.983,-6227.33,44.565548,0,0,0,0,100,0),
(@PATH,180,7047.887,-6254.881,46.195107,0,0,0,0,100,0),
(@PATH,181,7049.105,-6280.729,45.15278,0,0,0,0,100,0),
(@PATH,182,7054.5864,-6314.6484,45.629,0,0,0,0,100,0),
(@PATH,183,7059.8984,-6338.1333,45.060314,0,0,0,0,100,0),
(@PATH,184,7075.6963,-6350.8867,44.309013,0,0,0,0,100,0),
(@PATH,185,7095.8477,-6373.864,41.21762,0,0,0,0,100,0),
(@PATH,186,7109.51,-6393.672,35.538235,0,0,0,0,100,0),
(@PATH,187,7116.664,-6428.121,24.726286,0,0,0,0,100,0),
(@PATH,188,7119.624,-6448.0024,18.221363,0,0,0,0,100,0),
(@PATH,189,7129.309,-6472.9683,12.4379015,0,0,0,0,100,0),
(@PATH,190,7148.6,-6500.493,10.721241,0,0,0,0,100,0),
(@PATH,191,7171.0454,-6520.758,11.164112,0,0,0,0,100,0),
(@PATH,192,7190.5786,-6543.272,13.899645,0,0,0,0,100,0),
(@PATH,193,7208.1,-6556.9106,17.522804,0,0,0,0,100,0),
(@PATH,194,7221.332,-6576.1523,21.271461,0,0,0,0,100,0),
(@PATH,195,7237.333,-6588.133,27.051235,0,0,0,0,100,0),
(@PATH,196,7246.369,-6603.0894,30.381788,0,0,0,0,100,0),
(@PATH,197,7254.9243,-6623.2344,32.39009,0,0,0,0,100,0),
(@PATH,198,7266.249,-6649.5176,35.426315,0,0,0,0,100,0),
(@PATH,199,7262.2397,-6659.4585,36.753952,0,0,0,0,100,0),
(@PATH,200,7239.4326,-6665.0493,43.377243,0,0,0,0,100,0),
(@PATH,201,7230.456,-6660.803,45.28435,0,0,0,0,100,0),
(@PATH,202,7231.386,-6649.385,47.880177,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_17_11' WHERE sql_rev = '1639437546334915264';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
