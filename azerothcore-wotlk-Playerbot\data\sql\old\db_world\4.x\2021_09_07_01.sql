-- DB update 2021_09_07_00 -> 2021_09_07_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_07_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_07_00 2021_09_07_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1630670252547733100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1630670252547733100');

UPDATE `quest_template` SET `RewardMoney`=74000, `RewardBonusMoney`=0 WHERE `id` IN (11731,11922,11657,11923);
UPDATE `quest_template` SET `RewardMoney`=7400, `RewardBonusMoney`=0 WHERE `id` IN (11921,11926,11924,11925);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_07_01' WHERE sql_rev = '1630670252547733100';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
