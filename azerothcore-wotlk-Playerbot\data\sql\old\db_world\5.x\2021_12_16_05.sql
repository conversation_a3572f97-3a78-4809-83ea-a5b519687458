-- DB update 2021_12_16_04 -> 2021_12_16_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_16_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_16_04 2021_12_16_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639624033697103469'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639624033697103469');

-- Delete Reported Missing game_event_creature
DELETE FROM `game_event_creature` WHERE `guid` IN ( 85635, 85636, 85701, 85702, 85703, 85704, 85705, 85706, 85707, 85708, 85709, 85710, 
85711, 86165, 86849, 86850, 94513, 94586, 94696, 94715, 94716, 94717, 94724, 94726, 94733, 152261, 152262, 152263, 152264, 152265, 152266, 
152267, 152268, 152269, 152270, 152271, 152272, 152273, 152274, 152275, 152276, 200002, 202863, 207170, 240400, 240401, 240402, 240403, 
240404, 240405, 240406, 240407, 240408, 240409, 240410, 240411, 240412, 240421, 240422, 240423, 240424, 240425, 240426, 240436, 240437, 
240438, 240439, 240440, 240441, 240442, 240443, 240444, 240445, 240446, 240447, 240448, 240449, 240450, 240451, 240452, 240453, 240454, 
240465, 240466, 240467, 240468, 240470, 240471, 240472, 240473, 240474, 240475, 240476, 240477, 240478, 240479, 240480, 240481, 240482, 
240483, 240484, 240485, 240486, 240498, 240499, 240500, 240501, 240502, 240503, 240504, 240505, 240506, 240507, 240508, 240509, 240510, 
240511, 240512, 240513, 240514, 240515, 240516, 240517, 240527, 240528, 240529, 240530, 240537, 240538, 240539, 240540, 240541, 240542, 
240543, 240544, 240545, 240546, 240547, 240548, 240549, 240550, 240551, 240552, 240557, 240558, 240559, 240560, 240561, 240562, 240563, 
240564, 240565, 240566, 240567, 240568, 240569, 240570, 240571, 240572, 240573, 240574, 240575, 240576, 240587, 240588, 240589, 240590, 
240591, 240592, 240593, 240594, 240595, 240596, 240597, 240598, 240599, 240600, 240601, 240602, 240603, 240604, 240605, 240606, 240620, 
240621, 240622, 240623, 240624, 240625, 240626, 240627, 240628, 240629, 240630, 240631, 240632, 240633, 240634, 240635, 240636, 240637, 
240638, 240639, 244399, 244500, 244510 );

-- Delete Reported Missing game_event_gameobject
DELETE FROM `game_event_gameobject` WHERE `guid` IN ( 36, 38, 40, 6559,  6613,  6613,  9729,  11140, 17834, 17857, 18015, 21018, 21498, 
31641, 31645, 31668, 31669, 31671, 31672, 50552, 50553, 50554, 50574, 50582, 50642, 50643, 50644, 50645, 50646, 50647, 50648, 50649, 50675, 
50676, 50677, 50678, 50679, 50680, 50681, 50682, 50683, 50684, 50685, 50686, 50687, 50688, 50689, 50690, 50691, 50692, 50693, 50694, 50695, 
50696, 50810, 50828, 50940, 50957, 50972, 50973, 50974, 50975, 50976, 50977, 50978, 50979, 50981, 50984, 50985, 50986, 50987, 50988, 54315, 
54316, 70547, 70594, 150706, 150857, 150868, 150995, 151015 );

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_16_05' WHERE sql_rev = '1639624033697103469';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
