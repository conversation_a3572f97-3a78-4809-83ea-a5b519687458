-- DB update 2021_08_22_00 -> 2021_08_22_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_22_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_22_00 2021_08_22_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1629238721796812800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1629238721796812800');

-- Set the waypoint to the NPC
UPDATE `creature_template_addon` SET `path_id` = 402450 WHERE (`entry` = 10268);

-- Remove C++ script, add SAI and Waypoint based movement
UPDATE `creature_template` SET `AIName` = 'SmartAI', `MovementType` = 2, `ScriptName` = '' WHERE (`entry` = 10268);

DELETE FROM `smart_scripts` WHERE (`source_type` = 0 AND `entryorguid` = 10268);
INSERT INTO `smart_scripts` (`entryorguid`, `source_type`, `id`, `link`, `event_type`, `event_phase_mask`, `event_chance`, `event_flags`, `event_param1`, `event_param2`, `event_param3`, `event_param4`, `event_param5`, `action_type`, `action_param1`, `action_param2`, `action_param3`, `action_param4`, `action_param5`, `action_param6`, `target_type`, `target_param1`, `target_param2`, `target_param3`, `target_param4`, `target_x`, `target_y`, `target_z`, `target_o`, `comment`) VALUES
(10268, 0, 0, 0, 0, 0, 100, 0, 17000, 20000, 8000, 10000, 0, 11, 16495, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Gizrul the Slavener - In Combat - Cast \'Fatal Bite\''),
(10268, 0, 1, 0, 0, 0, 100, 0, 10000, 12000, 8000, 10000, 0, 11, 16128, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Gizrul the Slavener - In Combat - Cast \'Infected Bite\''),
(10268, 0, 2, 0, 2, 0, 100, 1, 0, 40, 300, 300, 0, 11, 8269, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 'Gizrul the Slavener - Between 0-40% Health - Cast \'Frenzy\' (No Repeat)');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_22_01' WHERE sql_rev = '1629238721796812800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
