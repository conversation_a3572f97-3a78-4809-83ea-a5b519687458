-- DB update 2021_10_20_05 -> 2021_10_20_06
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_20_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_20_05 2021_10_20_06 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1634415389857402800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1634415389857402800');

DELETE FROM `conditions` WHERE `SourceTypeOrReferenceId`=19 AND `SourceEntry` IN (12135,11131,12139,11219);
INSERT INTO `conditions` VALUES
(19,0,12135,0,0,8,0,11360,0,0,0,0,0,'','"Let the Fires Come" available if "Fire Brigade Practice" rewarded'),
(19,0,12135,0,1,8,0,11439,0,0,0,0,0,'','"Let the Fires Come" available if "Fire Brigade Practice" rewarded'),
(19,0,12135,0,2,8,0,11440,0,0,0,0,0,'','"Let the Fires Come" available if "Fire Brigade Practice" rewarded'),
(19,0,11131,0,0,8,0,11360,0,0,0,0,0,'','"Stop the Fires" available if "Fire Brigade Practice" rewarded'),
(19,0,11131,0,1,8,0,11439,0,0,0,0,0,'','"Stop the Fires" available if "Fire Brigade Practice" rewarded'),
(19,0,11131,0,2,8,0,11440,0,0,0,0,0,'','"Stop the Fires" available if "Fire Brigade Practice" rewarded'),
(19,0,12139,0,0,8,0,11361,0,0,0,0,0,'','"Let the Fires Come" available if "Fire Training" rewarded'),
(19,0,12139,0,0,8,0,11449,0,0,0,0,0,'','"Let the Fires Come" available if "Fire Training" rewarded'),
(19,0,12139,0,0,8,0,11450,0,0,0,0,0,'','"Let the Fires Come" available if "Fire Training" rewarded'),
(19,0,11219,0,0,8,0,11361,0,0,0,0,0,'','"Stop the Fires" available if "Fire Training" rewarded'),
(19,0,11219,0,0,8,0,11449,0,0,0,0,0,'','"Stop the Fires" available if "Fire Training" rewarded'),
(19,0,11219,0,0,8,0,11450,0,0,0,0,0,'','"Stop the Fires" available if "Fire Training" rewarded');

-- 
SET @CGUID       := 86582;
SET @OGUID       := 66913;
SET @Event       := 12;

DELETE FROM `creature` WHERE `guid` BETWEEN @CGUID AND @CGUID+8;
INSERT INTO `creature` (`guid`, `id`, `map`, `spawnMask`, `phaseMask`,  `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `wander_distance`, `MovementType`) VALUES
(@CGUID+0, 23537, 530, 1, 1, 9229.309570, -6780.560059, 27.053900, 1.082100, 120, 0, 0),
(@CGUID+1, 23537, 530, 1, 1, 9224.150391, -6777.709961, 28.385300, 0.663223, 120, 0, 0),
(@CGUID+2, 23537, 530, 1, 1, 9224.700195, -6771.000000, 27.259501, 0.069812, 120, 0, 0),
(@CGUID+3, 23537, 530, 1, 1, 9225.459961, -6765.229980, 26.058001, 5.794490, 120, 0, 0),
(@CGUID+4, 23537, 530, 1, 1, -4210.676758, -12291.706055, 1.587878, 0.044762, 120, 0, 0),
(@CGUID+5, 23537, 1, 1, 1, 284.369965, -4571.27002, 35.80556, 3.033128, 120, 0, 0),
(@CGUID+6, 23537, 1, 1, 1, 292.79895, -4544.667969, 38.915844, 3.640020, 120, 0, 0),
(@CGUID+7, 23537, 0, 1, 1, 2229.418945, 473.572235, 39.926392, 6.121745, 120, 0, 0),
(@CGUID+8, 23537, 0, 1, 1, -9304.201172, 29.874376, 71.198364, 2.833013, 120, 0, 0);

DELETE FROM `gameobject` WHERE `guid` BETWEEN @OGUID AND @OGUID+16;
INSERT INTO `gameobject` (`guid`, `id`, `map`,`zoneId`,`areaId`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`,`VerifiedBuild`) VALUES
(@OGUID+0,186720,530,0,0,1,1,-4210.676758, -12291.706055, 1.587878, 0.044762,0,0,0,0,180,100,1,0),
(@OGUID+1,186234,530,0,0,1,1,-4164.268066, -12279.269531, -0.751072, 2.769312,0,0,0,0,180,100,1,0),  
(@OGUID+2,186615,530,0,0,1,1,-4166.730957, -12277.448242, -0.415201, 5.697277,0,0,0,0,180,100,1,0), 
(@OGUID+3,186614,530,0,0,1,1,-4166.223633, -12280.923828, -0.506239, 0.554489,0,0,0,0,180,100,1,0), 
(@OGUID+4,186615,0,0,0,1,1,-5750.119629, -485.775787, 396.965454, 2.553856,0,0,0,0,180,100,1,0),
(@OGUID+5,186720,1,0,0,1,1,284.369965, -4571.27002, 35.80556, 3.033128,0,0,0.936673,0.350206,180,100,1,0), 
(@OGUID+6,186720,1,0,0,1,1,292.79895, -4544.667969, 38.915844, 3.640020,0,0,0.936673,0.350206,180,100,1,0), 
(@OGUID+7,186615,1,0,0,1,1,241.153595, -4563.961426, 14.254396, 0.721713,0,0,0,0,180,100,1,0),
(@OGUID+8,186615,530,0,0,1,1,9236.260742, -6772.439453, 24.756542, 2.243101,0,0,0,0,180,100,1,0),
(@OGUID+9,186720,0,0,0,1,1,2229.418945, 473.572235, 39.926392, 6.121745,0,0,0,0,180,100,1,0),
(@OGUID+10,186234,0,0,0,1,1,2275.884766, 503.988312, 35.200726, 3.272932,0,0,0,0,180,100,1,0),
(@OGUID+11,186615,0,0,0,1,1,2275.95459, 501.79629, 34.74936, 1.721770,0,0,0,0,180,100,1,0),
(@OGUID+12,186720,0,0,0,1,1, -9304.201172, 29.874376, 71.198364, 2.833013,0,0,0.994522,0.104528,180,100,1,0),
(@OGUID+13,186615,0,0,0,1,1, -9376.37109, 54.368896, 60.69603, 5.279530,0,0,0,0,180,100,1,0),
(@OGUID+14,186234,0,0,0,1,1,-9433.50488, 60.31855, 56.57888, 5.952971,0,0,0,0,180,100,1,0),
(@OGUID+15,186615,0,0,0,1,1,-9436.19238,  60.993786, 56.47847, 6.132042,0,0,0,0,180,100,1,0),
(@OGUID+16,186615,530,0,0,1,1,9509.071289, -6810.86084, 16.49318, 5.904098,0,0,0,0,180,100,1,0);

DELETE FROM `creature_addon` WHERE `guid`=10299;
DELETE FROM `gameobject_addon` WHERE `guid` IN (43067, 43078, 43079, 43045, 43046, 43052, 43053, 43054);

DELETE FROM `game_event_creature` WHERE `guid` BETWEEN @CGUID+0 AND @CGUID+8 AND `eventEntry`=@Event;
INSERT INTO `game_event_creature` SELECT @Event, creature.guid FROM `creature` WHERE creature.guid BETWEEN @CGUID+0 AND @CGUID+8;

DELETE FROM `game_event_gameobject` WHERE `guid` BETWEEN @OGUID+0 AND @OGUID+16 AND `eventEntry`=@Event;
INSERT INTO `game_event_gameobject` SELECT @Event, gameobject.guid FROM `gameobject` WHERE gameobject.guid BETWEEN @OGUID+0 AND @OGUID+16;

--
SET @OGUID:=78023;
DELETE FROM `game_event_gameobject` WHERE `guid` BETWEEN @OGUID+0 AND @OGUID+385 AND `eventEntry`=12;
DELETE FROM `gameobject` WHERE `guid` BETWEEN @OGUID+0 AND @OGUID+385;
INSERT INTO `gameobject` (`guid`, `id`, `map`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`) VALUES
(@OGUID+0,180406, 530, 1, 1, 10006.97, -7223.957, 38.252, 3.839725, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+1,180410, 530, 1, 1, 10017.58, -7218.51, 32.9932, 2.303831, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+2,180411, 530, 1, 1, 10001.91, -7204.366, 43.59731, 4.76475, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: -1)
(@OGUID+3,180410, 530, 1, 1, 9963.57, -7252.031, 33.84737, 2.268925, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+4,180410, 530, 1, 1, 9961.773, -7253.525, 33.84737, 2.321287, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+5,180410, 530, 1, 1, 9965.425, -7250.459, 34.04182, 2.321287, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+6,180410, 530, 1, 1, 9966.247, -7242.492, 33.60432, 3.874631, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+7,180410, 530, 1, 1, 10024.43, -7212.82, 32.86126, 2.234018, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+8,180410, 530, 1, 1, 10020.63, -7200.405, 33.09042, 4.101525, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+9,180405, 530, 1, 1, 9823.148, -7386.113, 20.45557, 0.9948372, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+10,180407, 530, 1, 1, 9812.965, -7409.957, 13.62506, 0.4886912, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+11,180427, 530, 1, 1, 9682.321, -7432.618, 23.67524, 5.637414, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: -1)
(@OGUID+12,180426, 530, 1, 1, 9685.572, -7441.069, 23.97384, 5.026549, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: -1)
(@OGUID+13,180426, 530, 1, 1, 9686.853, -7440.715, 20.77245, 6.108654, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: -1)
(@OGUID+14,180427, 530, 1, 1, 9686.702, -7436.893, 20.72384, 1.780234, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: -1)
(@OGUID+15,180426, 530, 1, 1, 9684.29, -7437.083, 23.16829, 1.97222, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: -1)
(@OGUID+16,180427, 530, 1, 1, 9682.661, -7437.406, 19.14746, 6.056293, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: -1)
(@OGUID+17,180427, 530, 1, 1, 9682.217, -7440.542, 23.70301, 0.2617982, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: -1)
(@OGUID+18,180426, 530, 1, 1, 9681.567, -7440.406, 21.67523, 2.35619, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: -1)
(@OGUID+19,180427, 530, 1, 1, 9688.92, -7439.088, 24.64051, 5.846854, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: -1)
(@OGUID+20,180415, 530, 1, 1, 9726.541, -7459.687, 14.03802, 2.513274, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+21,180415, 530, 1, 1, 9646.93, -7446.186, 14.02778, 1.274088, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+22,180415, 530, 1, 1, 9763.853, -7467.264, 13.57424, 2.18166, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+23,180415, 530, 1, 1, 9721.928, -7473.809, 14.03804, 5.497789, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+24,180415, 530, 1, 1, 9763.872, -7469.173, 13.57424, 1.954769, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+25,180415, 530, 1, 1, 9642.902, -7441.372, 14.02778, 4.485497, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+26,180415, 530, 1, 1, 9706.262, -7464.713, 14.03802, 3.019413, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+27,180415, 530, 1, 1, 9722.172, -7455.784, 14.03802, 5.061456, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+28,180415, 530, 1, 1, 9637.544, -7439.558, 14.02778, 2.129301, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+29,180415, 530, 1, 1, 9708.006, -7469.922, 14.03803, 1.047198, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+30,180415, 530, 1, 1, 9717.246, -7454.905, 14.03803, 0.4886912, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+31,180415, 530, 1, 1, 9707.984, -7459.522, 14.03802, 0.4886912, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+32,180415, 530, 1, 1, 9726.428, -7470.03, 14.03803, 5.567601, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+33,180415, 530, 1, 1, 9763.653, -7472.03, 13.57424, 6.19592, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+34,180415, 530, 1, 1, 9712.377, -7473.811, 14.03804, 5.550147, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+35,180415, 530, 1, 1, 9728.004, -7464.864, 14.03802, 1.012289, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+36,180415, 530, 1, 1, 9712.409, -7455.581, 14.03802, 0.3316107, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+37,180415, 530, 1, 1, 9717.181, -7474.624, 14.03804, 4.293513, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+38,180415, 530, 1, 1, 9660.289, -7459.098, 14.02777, 2.82743, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+39,180415, 530, 1, 1, 9661.99, -7464.522, 14.02777, 0.8377575, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+40,180415, 530, 1, 1, 9655.606, -7455.169, 14.02777, 2.111848, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+41,180415, 530, 1, 1, 9649.94, -7453.708, 14.02778, 1.710422, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+42,180415, 530, 1, 1, 9641.782, -7259.7, 14.81578, 5.689774, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+43,180406, 530, 1, 1, 9607.743, -7398.093, 13.61317, 0.3141584, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+44,180415, 530, 1, 1, 9628.13, -7445.979, 14.02779, 0.5235979, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+45,180415, 530, 1, 1, 9632.113, -7441.426, 14.02779, 1.850049, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+46,180415, 530, 1, 1, 9660.25, -7470.04, 14.02776, 0.4188786, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+47,180415, 530, 1, 1, 9638.744, -7460.21, 15.98692, 4.782203, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+48,180415, 530, 1, 1, 9641.474, -7463.021, 16.38364, 0.122173, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+49,180415, 530, 1, 1, 9640.314, -7461.316, 17.30185, 6.056293, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+50,180415, 530, 1, 1, 9638.427, -7461.016, 18.32743, 0.8901166, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+51,180415, 530, 1, 1, 9638.209, -7462.663, 18.66362, 0.087266, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+52,180415, 530, 1, 1, 9637.09, -7461.913, 18.66316, 1.780234, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+53,180415, 530, 1, 1, 9640.428, -7464.382, 15.98692, 3.647741, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+54,180415, 530, 1, 1, 9641.477, -7465.749, 15.98692, 0.087266, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+55,180415, 530, 1, 1, 9639.198, -7461.872, 15.98692, 2.897245, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+56,180415, 530, 1, 1, 9624.851, -7453.41, 14.02778, 3.228859, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+57,180415, 530, 1, 1, 9637.354, -7461.244, 15.98692, 3.455756, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+58,180415, 530, 1, 1, 9639.954, -7462.725, 16.84003, 1.186823, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+59,180415, 530, 1, 1, 9635.964, -7460.174, 15.97606, 3.804818, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+60,180415, 530, 1, 1, 9635.571, -7461.838, 15.98692, 1.745327, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+61,180415, 530, 1, 1, 9639.138, -7462.302, 17.97781, 6.161013, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+62,180415, 530, 1, 1, 9635.984, -7466.34, 18.39254, 4.136433, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+63,180415, 530, 1, 1, 9633.069, -7464.283, 15.98692, 3.47321, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+64,180415, 530, 1, 1, 9634.456, -7463.351, 15.98692, 1.06465, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+65,180415, 530, 1, 1, 9635.389, -7467.68, 17.8932, 2.897245, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+66,180415, 530, 1, 1, 9637.663, -7466.748, 18.66317, 1.343901, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+67,180415, 530, 1, 1, 9633.908, -7461.739, 15.98692, 0.9075702, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+68,180415, 530, 1, 1, 9634.506, -7465.276, 16.39715, 2.740162, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+69,180415, 530, 1, 1, 9637.328, -7468.691, 15.98692, 4.520406, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+70,180415, 530, 1, 1, 9638.302, -7467.272, 15.98692, 1.483528, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+71,180415, 530, 1, 1, 9635.129, -7466.257, 17.33418, 2.321287, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+72,180415, 530, 1, 1, 9649.725, -7475.164, 14.02777, 4.677484, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+73,180415, 530, 1, 1, 9634.771, -7467.749, 15.98692, 5.044002, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+74,180415, 530, 1, 1, 9639.864, -7467.848, 15.98692, 0.8726639, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+75,180415, 530, 1, 1, 9636.4, -7467.287, 15.98692, 1.710422, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+76,180415, 530, 1, 1, 9639.832, -7466.174, 15.98692, 3.159062, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+77,180415, 530, 1, 1, 9655.462, -7473.856, 14.02776, 3.787367, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+78,180415, 530, 1, 1, 9633.779, -7466.766, 16.85439, 0.5410506, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+79,180415, 530, 1, 1, 9654.685, -7494.923, 20.20687, 4.171338, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+80,180415, 530, 1, 1, 9642.694, -7487.412, 14.02775, 1.53589, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+81,180415, 530, 1, 1, 9708.024, -7495.3, 20.21482, 2.268925, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+82,180405, 530, 1, 1, 9677.399, -7499.985, 15.73777, 0.2094394, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+83,180407, 530, 1, 1, 9687.479, -7494.583, 15.76028, 3.630291, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+84,180411, 530, 1, 1, 9694.27, -7498.087, 19.95394, 4.71239, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: -1)
(@OGUID+85,180415, 530, 1, 1, 9714.893, -7495.471, 20.20084, 0.1745321, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+86,180406, 530, 1, 1, 9689.191, -7499.704, 15.73832, 0.2617982, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+87,180415, 530, 1, 1, 9705.337, -7495.149, 20.22054, 2.408554, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+88,180406, 530, 1, 1, 9679.796, -7494.525, 15.75649, 3.38594, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+89,180415, 530, 1, 1, 9646.641, -7482.605, 14.02776, 5.98648, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+90,180411, 530, 1, 1, 9672.331, -7497.908, 19.90336, 5.515242, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: -1)
(@OGUID+91,180415, 530, 1, 1, 9619.048, -7454.828, 14.02777, 3.124123, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+92,180415, 530, 1, 1, 9711.266, -7495.455, 20.2082, 1.692969, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+93,180415, 530, 1, 1, 9658.174, -7494.944, 20.21395, 0.8726639, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+94,180407, 530, 1, 1, 9697.96, -7500.003, 15.73456, 5.93412, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+95,180415, 530, 1, 1, 9661.191, -7494.833, 20.22025, 0.1919852, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+96,180415, 530, 1, 1, 9614.421, -7458.695, 14.02777, 0.7504908, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+97,180411, 530, 1, 1, 9683.252, -7498, 21.73868, 5.253442, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: -1)
(@OGUID+98,180415, 530, 1, 1, 9612.596, -7464.159, 14.02776, 2.460913, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+99,180415, 530, 1, 1, 9624.603, -7474.983, 14.02777, 3.717554, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+100,180415, 530, 1, 1, 9718.226, -7495.425, 20.1941, 5.602507, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+101,180415, 530, 1, 1, 9651.492, -7494.754, 20.20044, 5.375615, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+102,180415, 530, 1, 1, 9708.641, -7508.83, 20.19345, 0.3665176, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+103,180415, 530, 1, 1, 9614.337, -7469.526, 14.02776, 2.600535, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+104,180415, 530, 1, 1, 9767.092, -7472.754, 13.57029, 0.802851, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+105,180415, 530, 1, 1, 9627.683, -7482.578, 14.02777, 1.361356, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+106,180415, 530, 1, 1, 9618.941, -7473.685, 14.02776, 0.7853968, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+107,180415, 530, 1, 1, 9648.458, -7494.752, 20.19428, 4.34587, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+108,180415, 530, 1, 1, 9631.637, -7487.333, 14.02776, 3.630291, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+109,180415, 530, 1, 1, 9657.274, -7508.259, 20.19345, 0, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+110,180415, 530, 1, 1, 9637.185, -7488.979, 14.02775, 4.590216, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+111,180407, 530, 1, 1, 9668.626, -7499.798, 15.73456, 0.8552105, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+112,180415, 530, 1, 1, 9708.441, -7511.002, 20.19343, 2.094393, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+113,180415, 530, 1, 1, 9658.278, -7510.662, 20.19343, 2.775069, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+114,180415, 530, 1, 1, 9767.192, -7475.003, 13.54758, 0.3665176, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+115,180415, 530, 1, 1, 9747.169, -7504.403, 13.82745, 2.565632, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+116,180415, 530, 1, 1, 9708.247, -7520.514, 20.1934, 5.009095, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+117,180411, 530, 1, 1, 9768.593, -7495.143, 22.07407, 4.276057, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: -1)
(@OGUID+118,180415, 530, 1, 1, 9770.302, -7477.873, 13.54952, 6.178466, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+119,180415, 530, 1, 1, 9708.587, -7514.249, 20.19342, 3.839725, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+120,180405, 530, 1, 1, 9771.71, -7476.252, 13.54952, 1.553341, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+121,180405, 530, 1, 1, 9765.729, -7484.294, 13.51967, 5.061456, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+122,180415, 530, 1, 1, 9745.084, -7507.751, 13.84984, 1.256636, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+123,180415, 530, 1, 1, 9708.395, -7517.227, 20.19341, 0.8377575, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+124,180406, 530, 1, 1, 9766.075, -7491.438, 13.51901, 4.939284, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+125,180415, 530, 1, 1, 9767.161, -7488.73, 13.5172, 2.321287, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+126,180415, 530, 1, 1, 9767.326, -7477.913, 13.5208, 2.391098, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+127,180415, 530, 1, 1, 9770.485, -7489.105, 13.54949, 5.777041, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+128,180415, 530, 1, 1, 9770.472, -7486.432, 13.54949, 3.42085, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+129,180415, 530, 1, 1, 9766.888, -7486.758, 13.51768, 3.351047, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+130,180411, 530, 1, 1, 9768.688, -7480.606, 22.21311, 2.286379, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: -1)
(@OGUID+131,180415, 530, 1, 1, 9770.341, -7492.48, 13.5495, 5.078908, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+132,180415, 530, 1, 1, 9770.315, -7483.281, 13.5495, 5.462882, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+133,180407, 530, 1, 1, 9806.146, -7483.15, 13.54675, 5.113817, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+134,180405, 530, 1, 1, 9828.603, -7428.656, 13.619, 3.281239, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+135,180405, 530, 1, 1, 9771.549, -7499.522, 13.54952, 2.268925, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+136,180415, 530, 1, 1, 9770.2, -7497.835, 13.54952, 1.117009, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+137,180415, 530, 1, 1, 9767.045, -7497.897, 13.52364, 2.949595, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+138,180415, 530, 1, 1, 9763.943, -7503.117, 13.57326, 5.393069, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+139,180415, 530, 1, 1, 9760.557, -7504.637, 13.84737, 2.670348, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+140,180407, 530, 1, 1, 9822.736, -7460.87, 14.96318, 4.433136, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+141,180415, 530, 1, 1, 9767.044, -7502.682, 13.56838, 4.904376, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+142,180415, 530, 1, 1, 9828.7, -7444.441, 15.47772, 3.560473, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+143,180406, 530, 1, 1, 9806.036, -7492.882, 13.54704, 4.991644, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+144,180472, 530, 1, 1, 9828.494, -7448.282, 18.26859, 5.009095, 0, 0, 0, 1, 120, 255, 1), -- 180472 (Area: -1)
(@OGUID+145,180472, 530, 1, 1, 9834.405, -7383.108, 18.10079, 0.383971, 0, 0, 0, 1, 120, 255, 1), -- 180472 (Area: -1)
(@OGUID+146,180406, 530, 1, 1, 9850.404, -7388.42, 13.64399, 0.5759573, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+147,180415, 530, 1, 1, 9864.481, -7406.846, 17.2591, 4.537859, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+148,180415, 530, 1, 1, 9873.646, -7397.968, 17.17783, 6.073746, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+149,180415, 530, 1, 1, 9871.325, -7404.424, 17.25784, 1.117009, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+150,180407, 530, 1, 1, 9864.585, -7372.301, 20.45062, 4.694937, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+151,180472, 530, 1, 1, 9874.94, -7450.112, 18.36826, 6.248279, 0, 0, 0, 1, 120, 255, 1), -- 180472 (Area: -1)
(@OGUID+152,180406, 530, 1, 1, 9876.843, -7387.032, 20.45033, 0.3665176, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+153,180407, 530, 1, 1, 9912.188, -7409.072, 13.64037, 1.500983, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+154,180405, 530, 1, 1, 9899.083, -7405.358, 13.6274, 5.044002, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+155,180415, 530, 1, 1, 9841.817, -7508.129, 19.64095, 3.926996, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+156,180415, 530, 1, 1, 9849.061, -7502.075, -4.007756, 1.762782, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+157,180415, 530, 1, 1, 9852.741, -7502.268, -4.003982, 4.380776, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+158,180415, 530, 1, 1, 9847.087, -7501.374, 19.64914, 4.537859, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+159,180415, 530, 1, 1, 9855.832, -7499.758, 14.95692, 3.47321, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+160,180415, 530, 1, 1, 9844.303, -7503.149, 19.64698, 5.061456, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+161,180415, 530, 1, 1, 9850.985, -7500.915, 19.68542, 3.822273, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+162,180415, 530, 1, 1, 9850.927, -7498.934, 14.93043, 1.483528, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+163,180415, 530, 1, 1, 9841.513, -7507.542, -4.000395, 1.099556, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+164,180405, 530, 1, 1, 9881.052, -7461.776, 18.23401, 3.211419, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+165,180415, 530, 1, 1, 9855.014, -7501.309, 19.67005, 0.1919852, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+166,180415, 530, 1, 1, 9845.86, -7499.851, 14.9349, 4.71239, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+167,180415, 530, 1, 1, 9863.115, -7510.865, -3.999226, 2.146753, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+168,180415, 530, 1, 1, 9836.048, -7510.821, -4.0007, 0.2792516, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+169,180415, 530, 1, 1, 9857.706, -7503.155, 19.66912, 4.34587, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+170,180407, 530, 1, 1, 9911.933, -7454.624, 3.859614, 4.66003, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+171,180415, 530, 1, 1, 9861.13, -7509.801, -3.999039, 1.675514, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+172,180415, 530, 1, 1, 9860.067, -7508.15, 19.66097, 2.321287, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+173,180415, 530, 1, 1, 9838.981, -7510.788, -3.999198, 2.18166, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+174,180415, 530, 1, 1, 9860.134, -7507.262, -3.999874, 1.378809, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+175,180415, 530, 1, 1, 9841.044, -7509.531, -3.99918, 0.8377575, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+176,180415, 530, 1, 1, 9866.301, -7510.897, -4.000865, 5.427975, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+177,180415, 530, 1, 1, 9850.412, -7576.229, 20.30321, 2.82743, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+178,180415, 530, 1, 1, 9859.636, -7571.133, 20.36898, 2.775069, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+179,180415, 530, 1, 1, 9840.057, -7565.67, 20.272, 1.396262, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+180,180415, 530, 1, 1, 9855.566, -7556.837, 20.22193, 6.056293, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+181,180415, 530, 1, 1, 9855.622, -7574.95, 20.33961, 5.689774, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+182,180415, 530, 1, 1, 9841.286, -7570.885, 20.31774, 1.919862, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+183,180415, 530, 1, 1, 9850.589, -7555.328, 20.28959, 3.944446, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+184,180415, 530, 1, 1, 9860.757, -7565.826, 20.21995, 1.221729, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+185,180415, 530, 1, 1, 9845.252, -7556.721, 20.27281, 1.029743, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+186,180415, 530, 1, 1, 9859.213, -7560.637, 20.16981, 6.03884, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+187,180415, 530, 1, 1, 9845.12, -7574.828, 20.32867, 5.270896, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+188,180415, 530, 1, 1, 9841.471, -7560.367, 20.30399, 0.4712385, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+189,180415, 530, 1, 1, 9708.164, -7523.533, 20.19339, 1.797689, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+190,180415, 530, 1, 1, 9708.63, -7525.708, 20.19339, 5.462882, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+191,180415, 530, 1, 1, 9689.424, -7526.101, 18.17952, 0, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+192,180415, 530, 1, 1, 9698.255, -7529.269, 18.1807, 1.186823, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+193,180415, 530, 1, 1, 9699.27, -7531.121, 18.17952, 1.570796, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+194,180415, 530, 1, 1, 9688.258, -7524.131, 18.17952, 1.466076, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+195,180415, 530, 1, 1, 9696.238, -7528.111, 18.18006, 1.518436, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+196,180415, 530, 1, 1, 9699.441, -7533.089, 18.18734, 0.9599299, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+197,180415, 530, 1, 1, 9686.209, -7523.117, 18.17953, 0.4188786, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+198,180415, 530, 1, 1, 9680.044, -7523.145, 18.17953, 1.989672, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+199,180415, 530, 1, 1, 9689.915, -7528.086, 18.18014, 3.543024, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+200,180415, 530, 1, 1, 9677.983, -7524.141, 18.17953, 4.677484, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+201,180415, 530, 1, 1, 9676.573, -7530.896, 18.18807, 3.490667, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+202,180415, 530, 1, 1, 9669.972, -7528.087, 18.18037, 3.194002, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+203,180415, 530, 1, 1, 9658.169, -7523.246, 20.19338, 3.38594, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+204,180415, 530, 1, 1, 9666.942, -7531.015, 18.18179, 3.298687, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+205,180415, 530, 1, 1, 9658.161, -7520.05, 20.19339, 5.602507, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+206,180415, 530, 1, 1, 9676.96, -7525.968, 18.17952, 3.787367, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+207,180415, 530, 1, 1, 9690.021, -7531.793, 18.19084, 5.113817, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+208,180415, 530, 1, 1, 9658.127, -7516.909, 20.19341, 0.157079, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+209,180415, 530, 1, 1, 9687.82, -7536.056, 18.20918, 1.186823, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+210,180415, 530, 1, 1, 9677.461, -7536.019, 18.20867, 3.47321, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+211,180415, 530, 1, 1, 9676.623, -7527.953, 18.17995, 5.759588, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+212,180415, 530, 1, 1, 9658.238, -7513.847, 20.19342, 5.724681, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+213,180415, 530, 1, 1, 9668.019, -7529.09, 18.17951, 4.502952, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+214,180415, 530, 1, 1, 9657.903, -7525.752, 20.19338, 4.729844, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+215,180415, 530, 1, 1, 9666.733, -7532.866, 18.1876, 0.6283169, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+216,180415, 530, 1, 1, 9555.557, -7451.346, 15.48449, 4.799657, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+217,180415, 530, 1, 1, 9566.941, -7457.089, 15.52037, 6.230826, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+218,180415, 530, 1, 1, 9567.046, -7447.51, 15.51974, 2.513274, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+219,180415, 530, 1, 1, 9566.97, -7453.825, 15.51949, 1.605702, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+220,180415, 530, 1, 1, 9566.997, -7451.406, 15.51957, 0.3665176, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+221,180415, 530, 1, 1, 9568.402, -7426.109, 19.47399, 4.101525, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+222,180415, 530, 1, 1, 9567.152, -7445.854, 15.52034, 0.6108634, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+223,180472, 530, 1, 1, 9563.636, -7502.269, 21.49481, 4.607672, 0, 0, 0, 1, 120, 255, 1), -- 180472 (Area: -1)
(@OGUID+224,180415, 530, 1, 1, 9566.976, -7449.656, 15.51938, 2.33874, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+225,180415, 530, 1, 1, 9566.942, -7455.471, 15.51938, 2.373644, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+226,180405, 530, 1, 1, 9563.622, -7483.452, 15.5327, 4.276057, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+227,180415, 530, 1, 1, 9567.161, -7443.97, 15.52034, 0.2268925, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+228,180406, 530, 1, 1, 9562.59, -7505.374, 16.25697, 4.782203, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+229,180415, 530, 1, 1, 9567.133, -7442.481, 15.5201, 5.951575, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+230,180415, 530, 1, 1, 9565.785, -7425.961, 19.47643, 1.448622, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+231,180410, 530, 1, 1, 9540.223, -7450.809, 17.51837, 0.01745246, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+232,180415, 530, 1, 1, 9567.209, -7426.12, 19.47489, 6.178466, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+233,180415, 530, 1, 1, 9541.864, -7425.773, 19.47651, 0.5410506, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+234,180415, 530, 1, 1, 9563.859, -7424.643, 19.47689, 3.857183, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+235,180410, 530, 1, 1, 9540.258, -7448.014, 17.50896, 0.05235888, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+236,180472, 530, 1, 1, 9564.246, -7412.431, 27.42245, 4.450591, 0, 0, 0, 1, 120, 255, 1), -- 180472 (Area: -1)
(@OGUID+237,180410, 530, 1, 1, 9550.316, -7412.965, 20.95277, 4.782203, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+238,180415, 530, 1, 1, 9544.101, -7423.859, 19.47693, 4.310966, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+239,180415, 530, 1, 1, 9563.4, -7423.909, 19.47697, 5.201083, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+240,180410, 530, 1, 1, 9540.216, -7453.866, 17.47302, 0.03490625, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+241,180415, 530, 1, 1, 9543.036, -7424.986, 19.47662, 3.717554, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+242,180415, 530, 1, 1, 9564.668, -7425.304, 19.47664, 0.5061446, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+243,180407, 530, 1, 1, 9567.768, -7397.515, 16.85042, 4.991644, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+244,180406, 530, 1, 1, 9567.915, -7416.937, 19.47392, 5.166176, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+245,180410, 530, 1, 1, 9553.402, -7413.04, 20.98712, 4.694937, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+246,180407, 530, 1, 1, 9543.043, -7483.666, 15.53269, 2.565632, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+247,180415, 530, 1, 1, 9540.23, -7425.866, 19.4752, 0.4712385, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+248,180472, 530, 1, 1, 9519.833, -7433.035, 19.3605, 0.2094394, 0, 0, 0, 1, 120, 255, 1), -- 180472 (Area: -1)
(@OGUID+249,180415, 530, 1, 1, 9539.083, -7425.975, 19.47459, 4.398232, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+250,180407, 530, 1, 1, 9540.185, -7417.852, 19.4752, 2.111848, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+251,180405, 530, 1, 1, 9557.853, -7400.035, 16.83773, 2.670348, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+252,180410, 530, 1, 1, 9543.699, -7412.968, 20.87036, 4.729844, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+253,180406, 530, 1, 1, 9508.441, -7424.596, 14.1965, 3.68265, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+254,180410, 530, 1, 1, 9538.562, -7415.213, 20.94702, 0, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+255,180405, 530, 1, 1, 9523.479, -7423.124, 14.25087, 4.625124, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+256,180410, 530, 1, 1, 9540.664, -7412.908, 20.89148, 4.747296, 0, 0, 0, 1, 120, 255, 1), -- 180410 (Area: -1)
(@OGUID+257,180406, 530, 1, 1, 9539.319, -7392.822, 16.82288, 1.186823, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+258,180472, 530, 1, 1, 9554.567, -7389.99, 21.68028, 0.6632232, 0, 0, 0, 1, 120, 255, 1), -- 180472 (Area: -1)
(@OGUID+259,180415, 530, 1, 1, 9487.291, -7422.018, 14.81501, 4.625124, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+260,180415, 530, 1, 1, 9484.362, -7445.353, 14.90302, 3.996807, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+261,180415, 530, 1, 1, 9480.708, -7432.739, 14.90301, 2.408554, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+262,180415, 530, 1, 1, 9476.451, -7449.176, 14.90303, 0.6632232, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+263,180407, 530, 1, 1, 9494.879, -7428.358, 17.09135, 3.281239, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+264,180415, 530, 1, 1, 9492.825, -7419.987, 14.81502, 2.164206, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+265,180415, 530, 1, 1, 9476.508, -7431.844, 14.90302, 1.483528, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+266,180415, 530, 1, 1, 9467.375, -7440.646, 14.90301, 2.932139, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+267,180415, 530, 1, 1, 9472.466, -7432.785, 14.90301, 4.747296, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+268,180415, 530, 1, 1, 9482.682, -7420.492, 14.81501, 2.809975, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+269,180415, 530, 1, 1, 9484.56, -7436.282, 14.90301, 3.246347, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+270,180415, 530, 1, 1, 9468.686, -7445.147, 14.90302, 0.4014249, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+271,180415, 530, 1, 1, 9496.979, -7414.939, 14.81503, 1.413715, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+272,180415, 530, 1, 1, 9472.42, -7448.443, 14.90303, 3.543024, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+273,180415, 530, 1, 1, 9485.846, -7440.649, 14.90301, 0.2967052, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+274,180415, 530, 1, 1, 9497.751, -7409.99, 14.81504, 4.71239, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+275,180415, 530, 1, 1, 9480.601, -7448.55, 14.90303, 4.049168, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+276,180415, 530, 1, 1, 9497.879, -7397.243, 14.81503, 5.044002, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+277,180415, 530, 1, 1, 9468.706, -7436.143, 14.90301, 4.834563, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+278,180415, 530, 1, 1, 9497.823, -7404.051, 14.81503, 5.061456, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: -1)
(@OGUID+279,180405, 530, 1, 1, 9728.203, -7077.669, 16.74163, 5.497789, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+280,180405, 530, 1, 1, 9731.667, -7093.845, 16.69393, 4.834563, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+281,180406, 530, 1, 1, 9649.085, -7055.952, 18.9854, 6.143561, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: -1)
(@OGUID+282,180405, 530, 1, 1, 9630.405, -7055.748, 18.99824, 0.6806767, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: -1)
(@OGUID+283,180407, 530, 1, 1, 9625.018, -7048.128, 16.5235, 2.35619, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+284,180407, 530, 1, 1, 9452.534, -7117.212, 17.60469, 1.239183, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: -1)
(@OGUID+285, 180415, 530, 1, 1, 9471.1, -6788.781, 18.13373, 0.4014249, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+286, 180415, 530, 1, 1, 9476.353, -6788.941, 18.10409, 0.9424766, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+287, 180405, 530, 1, 1, 9477.764, -6788.119, 16.49356, 2.722713, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+288, 180415, 530, 1, 1, 9478.772, -6793.347, 18.11179, 4.101525, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+289, 180415, 530, 1, 1, 9540.375, -6792.719, 18.12548, 5.305802, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+290, 180406, 530, 1, 1, 9543.698, -6778.754, 15.11356, 5.654869, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: 0)
(@OGUID+291, 180415, 530, 1, 1, 9543.889, -6783.125, 17.3731, 0.1919852, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+292, 180415, 530, 1, 1, 9544.979, -6778.125, 16.53427, 2.042035, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+293, 180415, 530, 1, 1, 9543.481, -6796.776, 18.1165, 3.595379, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+294, 180405, 530, 1, 1, 9542.096, -6797.444, 16.47561, 5.794494, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+295, 180415, 530, 1, 1, 9541.747, -6787.827, 17.90674, 5.358162, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+296, 180415, 530, 1, 1, 8744.743, -6707.795, 71.16882, 6.178466, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+297, 180407, 530, 1, 1, 8752.958, -6701.513, 70.30769, 0.8552105, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: 0)
(@OGUID+298, 180415, 530, 1, 1, 8732.847, -6664.828, 71.75481, 5.759588, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+299, 180415, 530, 1, 1, 8761.782, -6686.872, 71.60171, 2.984498, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+300, 180407, 530, 1, 1, 8727.983, -6662.12, 70.34041, 0.9948372, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: 0)
(@OGUID+301, 180415, 530, 1, 1, 8748.609, -6711.054, 71.06712, 2.111848, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+302, 180415, 530, 1, 1, 8759.109, -6691.253, 71.28538, 2.495818, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+303, 180406, 530, 1, 1, 8749.692, -6699.78, 69.26237, 5.637414, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: 0)
(@OGUID+304, 180415, 530, 1, 1, 8739.994, -6664.781, 71.07558, 4.276057, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+305, 180415, 530, 1, 1, 8757.932, -6701.396, 71.16756, 1.954769, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+306, 180405, 530, 1, 1, 8723.28, -6666.225, 70.24129, 1.134463, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+307, 180415, 530, 1, 1, 8753.414, -6709.49, 71.1883, 0.5585039, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+308, 180415, 530, 1, 1, 8726.561, -6687.083, 72.62592, 3.228859, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+309, 180405, 530, 1, 1, 8753.743, -6699.585, 70.3749, 4.363324, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+310, 180415, 530, 1, 1, 8723.233, -6681.452, 71.84742, 0.6981314, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+311, 180405, 530, 1, 1, 8719.521, -6656.928, 93.42024, 0.9948372, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+312, 180415, 530, 1, 1, 8700.916, -6674.799, 72.0319, 1.518436, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 3462)
(@OGUID+313, 180415, 530, 1, 1, 8706.488, -6662.007, 71.92754, 5.777041, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 3462)
(@OGUID+314, 180406, 530, 1, 1, 8705.386, -6687.072, 70.4631, 2.408554, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: 3462)
(@OGUID+315, 180411, 530, 1, 1, 8717.235, -6634.217, 81.3668, 2.844883, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: 3462)
(@OGUID+316, 180415, 530, 1, 1, 8702.579, -6665.299, 71.93767, 1.151916, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 3462)
(@OGUID+317, 180415, 530, 1, 1, 8701.252, -6679.795, 72.46958, 0.9773831, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 3462)
(@OGUID+318, 180407, 530, 1, 1, 8706.709, -6692.784, 70.40144, 2.146753, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: 3462)
(@OGUID+319, 180411, 530, 1, 1, 8692.95, -6653.304, 81.65059, 3.298687, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: 3462)
(@OGUID+320, 180415, 530, 1, 1, 8698.946, -6684.249, 72.75284, 3.263772, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 3462)
(@OGUID+321, 180411, 530, 1, 1, 8690.379, -6624.208, 81.74379, 5.323256, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: 3462)
(@OGUID+322, 180407, 530, 1, 1, 8698.128, -6633.019, 82.70164, 4.171338, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: 3462)
(@OGUID+323, 180406, 530, 1, 1, 8704.135, -6639.711, 82.70164, 1.134463, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: 3462)
(@OGUID+324, 180411, 530, 1, 1, 8714.333, -6651.519, 81.38808, 1.97222, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: 3462)
(@OGUID+325, 180405, 530, 1, 1, 8679.956, -6613.042, 93.42129, 5.759588, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 3462)
(@OGUID+326, 180405, 530, 1, 1, 8683.743, -6621.233, 70.36587, 4.06662, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 3462)
(@OGUID+327, 180406, 530, 1, 1, 8688.724, -6616.967, 70.36587, 3.892087, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: 3462)
(@OGUID+328, 180415, 530, 1, 1, 7605.088, -6811.049, 84.1159, 0.3141584, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+329, 180415, 530, 1, 1, 7612.11, -6804.234, 81.90377, 4.991644, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+330, 180415, 530, 1, 1, 7612.021, -6828.266, 83.9107, 5.550147, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+331, 180415, 530, 1, 1, 7566.673, -6821.13, 88.05801, 4.415683, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+332, 180427, 530, 1, 1, 7573.918, -6803.229, 94.8674, 5.445428, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+333, 180415, 530, 1, 1, 7567.127, -6817.865, 88.0573, 2.076939, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+334, 180427, 530, 1, 1, 7569.024, -6809.578, 94.50038, 5.602507, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+335, 180426, 530, 1, 1, 7569.082, -6825.991, 89.69709, 3.508117, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+336, 180415, 530, 1, 1, 7602.116, -6828.639, 86.01514, 3.735006, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+337, 180427, 530, 1, 1, 7574.768, -6814.038, 95.28167, 1.623156, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+338, 180405, 530, 1, 1, 7577.568, -6858.068, 93.35548, 3.019413, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+339, 180426, 530, 1, 1, 7572.743, -6822.458, 90.08892, 2.007128, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+340, 180407, 530, 1, 1, 7576.379, -6780.385, 87.40021, 2.216565, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: 0)
(@OGUID+341, 180426, 530, 1, 1, 7572.007, -6815.111, 93.45477, 3.054327, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+342, 180415, 530, 1, 1, 7567.2, -6819.601, 88.05785, 3.717554, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+343, 180427, 530, 1, 1, 7589.011, -6833.851, 98.97574, 2.792518, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+344, 180407, 530, 1, 1, 7572.278, -6819.3, 86.66614, 1.919862, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: 0)
(@OGUID+345, 180426, 530, 1, 1, 7571.846, -6841.222, 98.84716, 0.6283169, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+346, 180415, 530, 1, 1, 7562.185, -6823.288, 88.05971, 5.445428, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+347, 180427, 530, 1, 1, 7567.376, -6835.135, 98.69683, 5.777041, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+348, 180415, 530, 1, 1, 7561.724, -6814.664, 88.05759, 0.087266, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+349, 180415, 530, 1, 1, 7565.117, -6815.148, 88.05692, 2.44346, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+350, 180415, 530, 1, 1, 7558.944, -6816.728, 88.05735, 0.802851, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+351, 180405, 530, 1, 1, 7566.199, -6800.06, 87.48344, 1.570796, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+352, 180415, 530, 1, 1, 7563.428, -6814.641, 88.05914, 4.747296, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+353, 180415, 530, 1, 1, 7559.307, -6821.544, 88.05991, 0.5934101, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+354, 180415, 530, 1, 1, 7558.512, -6820.009, 88.05814, 6.248279, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+355, 180426, 530, 1, 1, 7560.972, -6804.747, 97.29667, 0.4886912, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+356, 180415, 530, 1, 1, 7565.508, -6822.448, 88.05878, 2.879789, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+357, 180426, 530, 1, 1, 7567.499, -6812.465, 101.9166, 0.4886912, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+358, 180415, 530, 1, 1, 7566.346, -6816.298, 88.0579, 6.108654, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+359, 180407, 530, 1, 1, 7571.595, -6855.143, 93.3475, 2.967041, 0, 0, 0, 1, 120, 255, 1), -- 180407 (Area: 0)
(@OGUID+360, 180415, 530, 1, 1, 7564.033, -6823.213, 88.05829, 2.583081, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+361, 180415, 530, 1, 1, 7560.621, -6822.766, 88.05952, 5.777041, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+362, 180426, 530, 1, 1, 7564.719, -6828.118, 96.17546, 0.9948372, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+363, 180426, 530, 1, 1, 7560.467, -6812.441, 96.04751, 1.919862, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+364, 180415, 530, 1, 1, 7560.125, -6815.4, 88.05717, 5.515242, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+365, 180426, 530, 1, 1, 7547.692, -6814.104, 93.27422, 5.846854, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+366, 180406, 530, 1, 1, 7552.977, -6801.014, 87.09279, 4.991644, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: 0)
(@OGUID+367, 180426, 530, 1, 1, 7547.643, -6825.288, 94.40193, 0.8901166, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+368, 180427, 530, 1, 1, 7558.28, -6809.564, 94.68667, 5.270896, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+369, 180415, 530, 1, 1, 7546.278, -6830.885, 88.81131, 4.625124, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+370, 180426, 530, 1, 1, 7557.047, -6828.95, 95.44633, 2.286379, 0, 0, 0, 1, 120, 255, 1), -- 180426 (Area: 0)
(@OGUID+371, 180415, 530, 1, 1, 7555.831, -6833.354, 89.27995, 5.323256, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+372, 180415, 530, 1, 1, 7558.429, -6818.413, 88.05863, 3.019413, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 0)
(@OGUID+373, 180405, 530, 1, 1, 7552.512, -6816.896, 86.9488, 4.956738, 0, 0, 0, 1, 120, 255, 1), -- 180405 (Area: 0)
(@OGUID+374, 180411, 530, 1, 1, 7557.601, -6889.191, 103.978, 4.520406, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: 0)
(@OGUID+375, 180427, 530, 1, 1, 7542.735, -6808.821, 93.34179, 5.532695, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+376, 180427, 530, 1, 1, 7538.969, -6832.844, 96.30994, 2.932139, 0, 0, 0, 1, 120, 255, 1), -- 180427 (Area: 0)
(@OGUID+377, 180411, 530, 1, 1, 7552.211, -6766.903, 96.95532, 1.97222, 0, 0, 0, 1, 120, 255, 1), -- 180411 (Area: 0)
(@OGUID+378, 180406, 530, 1, 1, 7512.574, -6856.683, 84.62782, 4.206246, 0, 0, 0, 1, 120, 255, 1), -- 180406 (Area: 3488)
(@OGUID+379, 180415, 530, 1, 1, 7506.477, -6809.167, 81.56933, 5.881761, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 3488)
(@OGUID+380, 180415, 530, 1, 1, 7516.309, -6806.774, 83.16227, 1.343901, 0, 0, 0, 1, 120, 255, 1), -- 180415 (Area: 3488)
(@OGUID+381, 186720, 530, 1, 1, 9225.462, -6765.234, 26.05804, 5.794494, 0, 0, 0, 1, 120, 255, 1), -- 186720 (Area: 0)
(@OGUID+382, 186720, 530, 1, 1, 9229.307, -6780.561, 27.05395, 1.082103, 0, 0, 0, 1, 120, 255, 1), -- 186720 (Area: 0)
(@OGUID+383, 186720, 530, 1, 1, 9224.697, -6771.005, 27.25945, 0.06981169, 0, 0, 0, 1, 120, 255, 1), -- 186720 (Area: 0)
(@OGUID+384, 186720, 530, 1, 1, 9224.153, -6777.715, 28.38528, 0.6632232, 0, 0, 0, 1, 120, 255, 1), -- 186720 (Area: 0)
(@OGUID+385, 186234, 530, 1, 1, 9235.062, -6770.263, 24.79453, 2.792518, 0, 0, 0, 1, 120, 255, 1); -- 186234 (Area: 0)

INSERT INTO `game_event_gameobject` (`eventEntry`, `guid`) VALUES
(12, @OGUID+0),
(12, @OGUID+1),
(12, @OGUID+2),
(12, @OGUID+3),
(12, @OGUID+4),
(12, @OGUID+5),
(12, @OGUID+6),
(12, @OGUID+7),
(12, @OGUID+8),
(12, @OGUID+9),
(12, @OGUID+10),
(12, @OGUID+11),
(12, @OGUID+12),
(12, @OGUID+13),
(12, @OGUID+14),
(12, @OGUID+15),
(12, @OGUID+16),
(12, @OGUID+17),
(12, @OGUID+18),
(12, @OGUID+19),
(12, @OGUID+20),
(12, @OGUID+21),
(12, @OGUID+22),
(12, @OGUID+23),
(12, @OGUID+24),
(12, @OGUID+25),
(12, @OGUID+26),
(12, @OGUID+27),
(12, @OGUID+28),
(12, @OGUID+29),
(12, @OGUID+30),
(12, @OGUID+31),
(12, @OGUID+32),
(12, @OGUID+33),
(12, @OGUID+34),
(12, @OGUID+35),
(12, @OGUID+36),
(12, @OGUID+37),
(12, @OGUID+38),
(12, @OGUID+39),
(12, @OGUID+40),
(12, @OGUID+41),
(12, @OGUID+42),
(12, @OGUID+43),
(12, @OGUID+44),
(12, @OGUID+45),
(12, @OGUID+46),
(12, @OGUID+47),
(12, @OGUID+48),
(12, @OGUID+49),
(12, @OGUID+50),
(12, @OGUID+51),
(12, @OGUID+52),
(12, @OGUID+53),
(12, @OGUID+54),
(12, @OGUID+55),
(12, @OGUID+56),
(12, @OGUID+57),
(12, @OGUID+58),
(12, @OGUID+59),
(12, @OGUID+60),
(12, @OGUID+61),
(12, @OGUID+62),
(12, @OGUID+63),
(12, @OGUID+64),
(12, @OGUID+65),
(12, @OGUID+66),
(12, @OGUID+67),
(12, @OGUID+68),
(12, @OGUID+69),
(12, @OGUID+70),
(12, @OGUID+71),
(12, @OGUID+72),
(12, @OGUID+73),
(12, @OGUID+74),
(12, @OGUID+75),
(12, @OGUID+76),
(12, @OGUID+77),
(12, @OGUID+78),
(12, @OGUID+79),
(12, @OGUID+80),
(12, @OGUID+81),
(12, @OGUID+82),
(12, @OGUID+83),
(12, @OGUID+84),
(12, @OGUID+85),
(12, @OGUID+86),
(12, @OGUID+87),
(12, @OGUID+88),
(12, @OGUID+89),
(12, @OGUID+90),
(12, @OGUID+91),
(12, @OGUID+92),
(12, @OGUID+93),
(12, @OGUID+94),
(12, @OGUID+95),
(12, @OGUID+96),
(12, @OGUID+97),
(12, @OGUID+98),
(12, @OGUID+99),
(12, @OGUID+100),
(12, @OGUID+101),
(12, @OGUID+102),
(12, @OGUID+103),
(12, @OGUID+104),
(12, @OGUID+105),
(12, @OGUID+106),
(12, @OGUID+107),
(12, @OGUID+108),
(12, @OGUID+109),
(12, @OGUID+110),
(12, @OGUID+111),
(12, @OGUID+112),
(12, @OGUID+113),
(12, @OGUID+114),
(12, @OGUID+115),
(12, @OGUID+116),
(12, @OGUID+117),
(12, @OGUID+118),
(12, @OGUID+119),
(12, @OGUID+120),
(12, @OGUID+121),
(12, @OGUID+122),
(12, @OGUID+123),
(12, @OGUID+124),
(12, @OGUID+125),
(12, @OGUID+126),
(12, @OGUID+127),
(12, @OGUID+128),
(12, @OGUID+129),
(12, @OGUID+130),
(12, @OGUID+131),
(12, @OGUID+132),
(12, @OGUID+133),
(12, @OGUID+134),
(12, @OGUID+135),
(12, @OGUID+136),
(12, @OGUID+137),
(12, @OGUID+138),
(12, @OGUID+139),
(12, @OGUID+140),
(12, @OGUID+141),
(12, @OGUID+142),
(12, @OGUID+143),
(12, @OGUID+144),
(12, @OGUID+145),
(12, @OGUID+146),
(12, @OGUID+147),
(12, @OGUID+148),
(12, @OGUID+149),
(12, @OGUID+150),
(12, @OGUID+151),
(12, @OGUID+152),
(12, @OGUID+153),
(12, @OGUID+154),
(12, @OGUID+155),
(12, @OGUID+156),
(12, @OGUID+157),
(12, @OGUID+158),
(12, @OGUID+159),
(12, @OGUID+160),
(12, @OGUID+161),
(12, @OGUID+162),
(12, @OGUID+163),
(12, @OGUID+164),
(12, @OGUID+165),
(12, @OGUID+166),
(12, @OGUID+167),
(12, @OGUID+168),
(12, @OGUID+169),
(12, @OGUID+170),
(12, @OGUID+171),
(12, @OGUID+172),
(12, @OGUID+173),
(12, @OGUID+174),
(12, @OGUID+175),
(12, @OGUID+176),
(12, @OGUID+177),
(12, @OGUID+178),
(12, @OGUID+179),
(12, @OGUID+180),
(12, @OGUID+181),
(12, @OGUID+182),
(12, @OGUID+183),
(12, @OGUID+184),
(12, @OGUID+185),
(12, @OGUID+186),
(12, @OGUID+187),
(12, @OGUID+188),
(12, @OGUID+189),
(12, @OGUID+190),
(12, @OGUID+191),
(12, @OGUID+192),
(12, @OGUID+193),
(12, @OGUID+194),
(12, @OGUID+195),
(12, @OGUID+196),
(12, @OGUID+197),
(12, @OGUID+198),
(12, @OGUID+199),
(12, @OGUID+200),
(12, @OGUID+201),
(12, @OGUID+202),
(12, @OGUID+203),
(12, @OGUID+204),
(12, @OGUID+205),
(12, @OGUID+206),
(12, @OGUID+207),
(12, @OGUID+208),
(12, @OGUID+209),
(12, @OGUID+210),
(12, @OGUID+211),
(12, @OGUID+212),
(12, @OGUID+213),
(12, @OGUID+214),
(12, @OGUID+215),
(12, @OGUID+216),
(12, @OGUID+217),
(12, @OGUID+218),
(12, @OGUID+219),
(12, @OGUID+220),
(12, @OGUID+221),
(12, @OGUID+222),
(12, @OGUID+223),
(12, @OGUID+224),
(12, @OGUID+225),
(12, @OGUID+226),
(12, @OGUID+227),
(12, @OGUID+228),
(12, @OGUID+229),
(12, @OGUID+230),
(12, @OGUID+231),
(12, @OGUID+232),
(12, @OGUID+233),
(12, @OGUID+234),
(12, @OGUID+235),
(12, @OGUID+236),
(12, @OGUID+237),
(12, @OGUID+238),
(12, @OGUID+239),
(12, @OGUID+240),
(12, @OGUID+241),
(12, @OGUID+242),
(12, @OGUID+243),
(12, @OGUID+244),
(12, @OGUID+245),
(12, @OGUID+246),
(12, @OGUID+247),
(12, @OGUID+248),
(12, @OGUID+249),
(12, @OGUID+250),
(12, @OGUID+251),
(12, @OGUID+252),
(12, @OGUID+253),
(12, @OGUID+254),
(12, @OGUID+255),
(12, @OGUID+256),
(12, @OGUID+257),
(12, @OGUID+258),
(12, @OGUID+259),
(12, @OGUID+260),
(12, @OGUID+261),
(12, @OGUID+262),
(12, @OGUID+263),
(12, @OGUID+264),
(12, @OGUID+265),
(12, @OGUID+266),
(12, @OGUID+267),
(12, @OGUID+268),
(12, @OGUID+269),
(12, @OGUID+270),
(12, @OGUID+271),
(12, @OGUID+272),
(12, @OGUID+273),
(12, @OGUID+274),
(12, @OGUID+275),
(12, @OGUID+276),
(12, @OGUID+277),
(12, @OGUID+278),
(12, @OGUID+279),
(12, @OGUID+280),
(12, @OGUID+281),
(12, @OGUID+282),
(12, @OGUID+283),
(12, @OGUID+284),
(12, @OGUID+285),
(12, @OGUID+286),
(12, @OGUID+287),
(12, @OGUID+288),
(12, @OGUID+289),
(12, @OGUID+290),
(12, @OGUID+291),
(12, @OGUID+292),
(12, @OGUID+293),
(12, @OGUID+294),
(12, @OGUID+295),
(12, @OGUID+296),
(12, @OGUID+297),
(12, @OGUID+298),
(12, @OGUID+299),
(12, @OGUID+300),
(12, @OGUID+301),
(12, @OGUID+302),
(12, @OGUID+303),
(12, @OGUID+304),
(12, @OGUID+305),
(12, @OGUID+306),
(12, @OGUID+307),
(12, @OGUID+308),
(12, @OGUID+309),
(12, @OGUID+310),
(12, @OGUID+311),
(12, @OGUID+312),
(12, @OGUID+313),
(12, @OGUID+314),
(12, @OGUID+315),
(12, @OGUID+316),
(12, @OGUID+317),
(12, @OGUID+318),
(12, @OGUID+319),
(12, @OGUID+320),
(12, @OGUID+321),
(12, @OGUID+322),
(12, @OGUID+323),
(12, @OGUID+324),
(12, @OGUID+325),
(12, @OGUID+326),
(12, @OGUID+327),
(12, @OGUID+328),
(12, @OGUID+329),
(12, @OGUID+330),
(12, @OGUID+331),
(12, @OGUID+332),
(12, @OGUID+333),
(12, @OGUID+334),
(12, @OGUID+335),
(12, @OGUID+336),
(12, @OGUID+337),
(12, @OGUID+338),
(12, @OGUID+339),
(12, @OGUID+340),
(12, @OGUID+341),
(12, @OGUID+342),
(12, @OGUID+343),
(12, @OGUID+344),
(12, @OGUID+345),
(12, @OGUID+346),
(12, @OGUID+347),
(12, @OGUID+348),
(12, @OGUID+349),
(12, @OGUID+350),
(12, @OGUID+351),
(12, @OGUID+352),
(12, @OGUID+353),
(12, @OGUID+354),
(12, @OGUID+355),
(12, @OGUID+356),
(12, @OGUID+357),
(12, @OGUID+358),
(12, @OGUID+359),
(12, @OGUID+360),
(12, @OGUID+361),
(12, @OGUID+362),
(12, @OGUID+363),
(12, @OGUID+364),
(12, @OGUID+365),
(12, @OGUID+366),
(12, @OGUID+367),
(12, @OGUID+368),
(12, @OGUID+369),
(12, @OGUID+370),
(12, @OGUID+371),
(12, @OGUID+372),
(12, @OGUID+373),
(12, @OGUID+374),
(12, @OGUID+375),
(12, @OGUID+376),
(12, @OGUID+377),
(12, @OGUID+378),
(12, @OGUID+379),
(12, @OGUID+380),
(12, @OGUID+381),
(12, @OGUID+382),
(12, @OGUID+383),
(12, @OGUID+384),
(12, @OGUID+385);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_20_06' WHERE sql_rev = '1634415389857402800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
