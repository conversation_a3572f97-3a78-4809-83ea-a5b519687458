-- DB update 2021_11_05_04 -> 2021_11_05_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_05_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_05_04 2021_11_05_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635874451387473500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635874451387473500');

DELETE FROM `quest_poi` WHERE `QuestID`=8311 AND `id`>0;
INSERT INTO `quest_poi` (`QuestID`, `id`, `ObjectiveIndex`, `MapID`, `WorldMapAreaId`, `Floor`, `Priority`, `Flags`, `VerifiedBuild`) VALUES
(8311,1,4,0,301,0,0,3,12340),
(8311,2,5,0,341,0,0,3,12340),
(8311,3,6,0,341,0,0,3,12340),
(8311,4,7,1,381,0,0,3,12340);

DELETE FROM `quest_poi_points` WHERE `QuestID`=8311 AND `Idx1`>0;
INSERT INTO `quest_poi_points` (`QuestID`, `Idx1`, `Idx2`, `X`, `Y`, `VerifiedBuild`) VALUES
(8311,1,0,-8869,670,12340),
(8311,2,0,-4594,-1003,12340),
(8311,3,0,-4845,-860,12340),
(8311,4,0,10124,2227,12340);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_05_05' WHERE sql_rev = '1635874451387473500';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
