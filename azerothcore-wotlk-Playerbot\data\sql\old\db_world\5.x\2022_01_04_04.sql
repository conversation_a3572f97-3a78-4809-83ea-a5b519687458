-- DB update 2022_01_04_03 -> 2022_01_04_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_04_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_04_03 2022_01_04_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1640754933928340200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1640754933928340200');
/* Shadowglen Creature Table improvements */
DELETE FROM `creature` WHERE `guid` IN (46573, 46586, 46715, 46714, 46462, 46461, 46449, 46459, 46438, 47905, 47907, 47922, 47883, 47925, 47660, 47880, 47921, 47900, 47928, 47915, 47926, 47752, 47863, 47911, 47876, 49612, 49617, 49622, 49613, 49614, 49615, 49616, 49621, 49569, 49564, 49611, 49568, 49623, 49570, 49562, 49572, 49561, 49571, 49560, 49563, 49567, 46935, 46928, 46931, 46934, 46926, 46927, 46936, 46920, 46933, 46917, 46916, 46919, 46915, 46932, 46918, 46924, 46912, 46913, 46922, 46921, 49647, 49635, 49627, 49629, 49632, 49630, 49631, 49634, 49637, 49644, 49642, 49638, 49641, 46939, 46944, 46943, 46941, 46938, 46937, 46940, 46957, 46942, 46949, 46955, 46947, 46948, 49566, 46923, 49610, 46929, 46930, 49565, 46925, 49620, 49628, 49648, 46945, 49633, 46952, 49636, 49646, 49626, 46953, 49640, 46954, 49643, 46946, 49639, 49645, 46951, 46950, 47266, 47018, 47021, 47023, 47038, 47062, 47054, 47055, 47010, 47012, 47208, 47037, 47249, 47061, 47060, 47016, 47003, 47017, 47002, 47001, 47262, 47000, 47263, 47057, 46999, 46998, 47056, 47268, 47269, 46997, 46958, 46981, 47271, 47006, 47270, 47053, 46974, 47004, 47029, 47009, 46971, 46972, 47008, 47052, 47039, 47030, 47031, 47267, 47350, 47283, 47290, 47312, 47288, 47289, 47286, 47284, 47281, 47287, 47282, 47285, 47310, 47280, 47342, 47334, 47344, 47314, 47346, 47336, 47329, 47322, 47343, 47338, 47341, 47335, 47345, 47339, 47319, 47340, 47330, 47316, 47315, 47331, 47326, 47321, 47337, 47332, 47333, 47324, 47320, 47325, 47327, 47317, 47328);
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `wander_distance`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`) VALUES
/* WILDLIFE */ 
/* Toads Entry 1420 */
(46573, 1420, 1, 0, 0, 1, 1, 0, 0, 10515.612, 892.85626, 1309.6267, 3.24027252197265625, 360, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10515.612 892.85626 1309.6267
(46586, 1420, 1, 0, 0, 1, 1, 0, 0, 10534.172, 868.0371, 1309.553, 4.163003921508789062, 360, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10534.172 868.0371 1309.553 
(46715, 1420, 1, 0, 0, 1, 1, 0, 0, 10561.927, 909.9647, 1311.021, 5.05486297607421875, 360, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10561.927 909.9647 1311.021 
(46714, 1420, 1, 0, 0, 1, 1, 0, 0, 10591.926, 833.1082, 1309.3878, 4.977931976318359375, 360, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10591.926 833.1082 1309.3878 
/* Deer Entry 883 */
(46462, 883, 1, 0, 0, 1, 1, 0, 0, 10648.584, 754.1246, 1318.5656, 5.753081321716308593, 360, 30, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10648.584 754.1246 1318.5656
(46461, 883, 1, 0, 0, 1, 1, 0, 0, 10617.471, 648.7571, 1328.219, 2.271554470062255859, 360, 30, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10617.471 648.7571 1328.219
(46449, 883, 1, 0, 0, 1, 1, 0, 0, 10514.419, 982.477, 1318.1084, 0.610427618026733398, 360, 30, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10514.419 982.477 1318.1084
(46459, 883, 1, 0, 0, 1, 1, 0, 0, 10400.079, 666.67175, 1326.2286, 4.77298736572265625, 360, 30, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10400.079 666.67175 1326.2286
(46438, 883, 1, 0, 0, 1, 1, 0, 0, 10314.586, 979.14874, 1335.1, 2.399487972259521484, 360, 30, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10314.586 979.14874 1335.1
/* Rabbit Entry 721 */
(47905, 721, 1, 0, 0, 1, 1, 0, 0, 10727.169, 781.13934, 1329.061, 5.183627605438232421, 360, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10727.169 781.13934 1329.061
(47907, 721, 1, 0, 0, 1, 1, 0, 0, 10515.402, 777.91724, 1316.6826, 2.414243698120117187, 360, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10515.402 777.91724 1316.6826
(47922, 721, 1, 0, 0, 1, 1, 0, 0, 10711.573, 889.9894, 1326.3293, 4.625797271728515625, 360, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10711.573 889.9894 1326.3293
(47883, 721, 1, 0, 0, 1, 1, 0, 0, 10685.008, 687.5368, 1331.5573, 3.193583488464355468, 360, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10685.008 687.5368 1331.5573
(47925, 721, 1, 0, 0, 1, 1, 0, 0, 10476.606, 693.0233, 1320.6428, 2.798938274383544921, 360, 2.911, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10476.606 693.0233 1320.6428
(47660, 721, 1, 0, 0, 1, 1, 0, 0, 10611.273, 714.32965, 1322.2097, 5.654691219329833984, 360, 2.848, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10611.273 714.32965 1322.2097
(47880, 721, 1, 0, 0, 1, 1, 0, 0, 10410.403, 1030.4722, 1333.3378, 3.652793407440185546, 360, 2.905, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10410.403 1030.4722 1333.3378
(47921, 721, 1, 0, 0, 1, 1, 0, 0, 10410.746, 817.53125, 1319.1262, 5.821177482604980468, 360, 2.636, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10410.746 817.53125 1319.1262
(47900, 721, 1, 0, 0, 1, 1, 0, 0, 10418.708, 521.8265, 1332.1619, 0.174203023314476013, 360, 2.711, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10418.708 521.8265 1332.1619
(47928, 721, 1, 0, 0, 1, 1, 0, 0, 10348.217, 821.25977, 1325.02, 3.569943428039550781, 360, 23494, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10348.217 821.25977 1325.02
(47915, 721, 1, 0, 0, 1, 1, 0, 0, 10266.343, 790.2104, 1345.4912, 3.597279548645019531, 360, 2.898718, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10266.343 790.2104 1345.4912
(47926, 721, 1, 0, 0, 1, 1, 0, 0, 10268.179, 848.2243, 1341.4581, 5.799013137817382812, 360, 2.004067, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10268.179 848.2243 1341.4581 ITS THE CUTE LITTLE BUNNY INSIDE THE TREE OMG
(47752, 721, 1, 0, 0, 1, 1, 0, 0, 10280.863, 1001.8224, 1343.0934, 4.969374656677246093, 360, 2.784632, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10280.863 1001.8224 1343.0934
(47863, 721, 1, 0, 0, 1, 1, 0, 0, 10345.33, 999.2444, 1334.4905, 1.297797083854675292, 360, 2.784919, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10345.33 999.2444 1334.4905
(47911, 721, 1, 0, 0, 1, 1, 0, 0, 10514.205, 941.73285, 1316.2262, 1.97318124771118164, 360, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10514.205 941.73285 1316.2262
(47876, 721, 1, 0, 0, 1, 1, 0, 0, 10564.13, 579.1275, 1340.2653, 2.518269300460815429, 360, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10564.13 579.1275 1340.2653
/* COMBAT MOBS */
/* Young Nightsabers Entry 2031 */
(49612, 2031, 1, 0, 0, 1, 1, 0, 0, 10271.938, 816.8682, 1341.4053, 5.654866695404052734, 180, 25.44487, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10271.938 816.8682 1341.4053
(49617, 2031, 1, 0, 0, 1, 1, 0, 0, 10351.3, 683.01324, 1327.9269, 3.351032257080078125, 180, 23.29791, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10351.3 683.01324 1327.9269
(49622, 2031, 1, 0, 0, 1, 1, 0, 0, 10320.974, 880.1531, 1331.7047, 3.455751895904541015, 180, 22.38227, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10320.974 880.1531 1331.7047
(49613, 2031, 1, 0, 0, 1, 1, 0, 0, 10280.857, 857.1169, 1339.1458, 1.413716673851013183, 180, 22.99973, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10280.857 857.1169 1339.1458
(49614, 2031, 1, 0, 0, 1, 1, 0, 0, 10281.604, 749.8841, 1338.6992, 4.118977069854736328, 180, 21.31903, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10281.604 749.8841 1338.6992
(49615, 2031, 1, 0, 0, 1, 1, 0, 0, 10299.705, 733.2233, 1332.6569, 0, 180, 17.21947, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10299.705 733.2233 1332.6569
(49616, 2031, 1, 0, 0, 1, 1, 0, 0, 10312.582, 728.23047, 1331.3386, 3.420845270156860351, 180, 25.82285, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10312.582 728.23047 1331.3386
(49621, 2031, 1, 0, 0, 1, 1, 0, 0, 10333.52, 666.81525, 1331.7272, 6.038839340209960937, 180, 17.76859, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10333.52 666.81525 1331.7272
(49569, 2031, 1, 0, 0, 1, 1, 0, 0, 10315.867, 683.88696, 1332.4817, 3.40339207649230957, 180, 26.24141, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10315.867 683.88696 1332.4817
(49564, 2031, 1, 0, 0, 1, 1, 0, 0, 10366.742, 666.8535, 1329.1953, 4.799655437469482421, 180, 19.79353, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10366.742 666.8535 1329.1953
(49611, 2031, 1, 0, 0, 1, 1, 0, 0, 10350.286, 651.2524, 1329.2548, 3.944444179534912109, 180, 19.95662, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10350.286 651.2524 1329.2548
(49568, 2031, 1, 0, 0, 1, 1, 0, 0, 10384.266, 652.5614, 1326.2896, 3.106686115264892578, 180, 20.83298, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10384.266 652.5614 1326.2896
(49623, 2031, 1, 0, 0, 1, 1, 0, 0, 10417.382, 681.8238, 1325.0664, 4.956735134124755859, 180, 26.25918, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10417.382 681.8238 1325.0664
(49570, 2031, 1, 0, 0, 1, 1, 0, 0, 10433.427, 666.6122, 1325.0164, 3.228859186172485351, 180, 25.08154, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10433.427 666.6122 1325.0164
(49562, 2031, 1, 0, 0, 1, 1, 0, 0, 10483.739, 681.75653, 1320.3365, 0.750491559505462646, 180, 24.86354, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10483.739 681.75653 1320.3365
(49572, 2031, 1, 0, 0, 1, 1, 0, 0, 10299.942, 666.6502, 1336.742, 2.792526721954345703, 180, 1734831, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10299.942 666.6502 1336.742
(49561, 2031, 1, 0, 0, 1, 1, 0, 0, 10446.576, 535.4616, 1339.0592, 5.410520553588867187, 180, 25.08067, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10446.576 535.4616 1339.0592
(49571, 2031, 1, 0, 0, 1, 1, 0, 0, 10415.95, 618.2155, 1322.1969, 0.261799395084381103, 180, 20.0623, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10415.95 618.2155 1322.1969
(49560, 2031, 1, 0, 0, 1, 1, 0, 0, 10397.339, 585.24457, 1329.3262, 3.054326057434082031, 180, 22.79945, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10397.339 585.24457 1329.3262
(49563, 2031, 1, 0, 0, 1, 1, 0, 0, 10416.483, 551.8123, 1331.1406, 5.340707302093505859, 180, 25.08067, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10416.483 551.8123 1331.1406
(49567, 2031, 1, 0, 0, 1, 1, 0, 0, 10305.284, 919.7031, 1333.9556, 5.654866695404052734, 180, 20, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10305.284 919.7031 1333.9556
/* Young Thistleboars Entry 1984 */
(46935, 1984, 1, 0, 0, 1, 1, 0, 0, 10266.817, 833.2274, 1339.7814, 1.675516128540039062, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10266.817 833.2274 1339.7814
(46928, 1984, 1, 0, 0, 1, 1, 0, 0, 10280.182, 916.58234, 1338.4318, 5.777040004730224609, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10280.182 916.58234 1338.4318
(46931, 1984, 1, 0, 0, 1, 1, 0, 0, 10283.104, 714.8585, 1339.18, 1.570796370506286621, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10283.104 714.8585 1339.18
(46934, 1984, 1, 0, 0, 1, 1, 0, 0, 10290.128, 785.113, 1336.1807, 3.385938644409179687, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10290.128 785.113 1336.1807
(46926, 1984, 1, 0, 0, 1, 1, 0, 0, 10296.911, 887.39355, 1333.0632, 4.468042850494384765, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10296.911 887.39355 1333.0632
(46927, 1984, 1, 0, 0, 1, 1, 0, 0, 10317.479, 951.83575, 1331.3418, 5.934119224548339843, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10317.479 951.83575 1331.3418
(46936, 1984, 1, 0, 0, 1, 1, 0, 0, 10318.389, 807.35895, 1328.0408, 4.764749050140380859, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10318.389 807.35895 1328.0408
(46920, 1984, 1, 0, 0, 1, 1, 0, 0, 10350.841, 982.39215, 1332.5795, 3.001966238021850585, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10350.841 982.39215 1332.5795
(46933, 1984, 1, 0, 0, 1, 1, 0, 0, 10357.427, 785.5423, 1323.7747, 5.602506637573242187, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10357.427 785.5423 1323.7747
(46917, 1984, 1, 0, 0, 1, 1, 0, 0, 10383.036, 1013.9316, 1335.1426, 0.122173048555850982, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10383.036 1013.9316 1335.1426
(46916, 1984, 1, 0, 0, 1, 1, 0, 0, 10383.638, 981.5667, 1327.3972, 0.157079637050628662, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10383.638 981.5667 1327.3972
(46919, 1984, 1, 0, 0, 1, 1, 0, 0, 10383.798, 614.8129, 1327.4352, 3.228859186172485351, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10383.798 614.8129 1327.4352
(46915, 1984, 1, 0, 0, 1, 1, 0, 0, 10385.005, 873.50964, 1324.7062, 0.645771801471710205, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10385.005 873.50964 1324.7062
(46932, 1984, 1, 0, 0, 1, 1, 0, 0, 10400.088, 666.6272, 1326.2334, 5.899212837219238281, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10400.088 666.6272 1326.2334
(46918, 1984, 1, 0, 0, 1, 1, 0, 0, 10400.21, 599.59393, 1327.439, 2.426007747650146484, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10400.21 599.59393 1327.439
(46924, 1984, 1, 0, 0, 1, 1, 0, 0, 10415.058, 948.6526, 1320.6753, 2.478367567062377929, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10415.058 948.6526 1320.6753
(46912, 1984, 1, 0, 0, 1, 1, 0, 0, 10415.428, 917.7987, 1319.566, 1.396263360977172851, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10415.428 917.7987 1319.566
(46913, 1984, 1, 0, 0, 1, 1, 0, 0, 10415.708, 976.8835, 1323.835, 3.298672199249267578, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10415.708 976.8835 1323.835
(46922, 1984, 1, 0, 0, 1, 1, 0, 0, 10416.655, 650.2708, 1323.3453, 0.069813169538974761, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10416.655 650.2708 1323.3453
(46921, 1984, 1, 0, 0, 1, 1, 0, 0, 10442.111, 956.43054, 1320.5726, 6.021385669708251953, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10442.111 956.43054 1320.5726
/* Mangy Nightsaber Entry 2032 */
/* Mangy Nightsaber 49647 did not exist and was not re-added here */
(49635, 2032, 1, 0, 0, 1, 1, 0, 0, 10511.255, 716.0282, 1317.209, 2.513274192810058593, 180, 20.19677, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10511.255 716.0282 1317.209
(49627, 2032, 1, 0, 0, 1, 1, 0, 0, 10514.981, 585.23285, 1338.9989, 2.042035102844238281, 180, 20.26815, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10514.981 585.23285 1338.9989
(49629, 2032, 1, 0, 0, 1, 1, 0, 0, 10550.723, 648.24133, 1325.6317, 5.79449319839477539, 180, 26.60139, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10550.723 648.24133 1325.6317
(49632, 2032, 1, 0, 0, 1, 1, 0, 0, 10549.13, 623.0011, 1330.7285, 3.769911050796508789, 180, 26.60139, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10549.13 623.0011 1330.7285
(49630, 2032, 1, 0, 0, 1, 1, 0, 0, 10516.551, 648.4095, 1324.9072, 5.654866695404052734, 180, 26, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10516.551 648.4095 1324.9072
(49631, 2032, 1, 0, 0, 1, 1, 0, 0, 10582.714, 650.6452, 1326.579, 0.872664630413055419, 180, 25.12836, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10582.714 650.6452 1326.579
(49634, 2032, 1, 0, 0, 1, 1, 0, 0, 10583.3, 618.9361, 1331.8383, 0.418879032135009765, 180, 19, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10583.3 618.9361 1331.8383
(49637, 2032, 1, 0, 0, 1, 1, 0, 0, 10614.814, 815.3429, 1310.2959, 5.026548385620117187, 180, 25.53074, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10614.814 815.3429 1310.2959
(49644, 2032, 1, 0, 0, 1, 1, 0, 0, 10616.244, 616.87286, 1336.3732, 5.410520553588867187, 180, 24.62528, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10616.244 616.87286 1336.3732
(49642, 2032, 1, 0, 0, 1, 1, 0, 0, 10649.615, 649.6035, 1334.2993, 4.886921882629394531, 180, 28.5359, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10649.615 649.6035 1334.2993
(49638, 2032, 1, 0, 0, 1, 1, 0, 0, 10765.028, 784.63324, 1332.2982, 4.15388345718383789, 180, 19, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10765.028 784.63324 1332.2982
(49641, 2032, 1, 0, 0, 1, 1, 0, 0, 10649.418, 715.8143, 1325.7777, 1.972222089767456054, 180, 19, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10649.418 715.8143 1325.7777
/* Thistle Boar Entry 1985 */
(46939, 1985, 1, 0, 0, 1, 1, 0, 0, 10485.291, 983.4388, 1321.1436, 0.907571196556091308, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10485.291 983.4388 1321.1436
(46944, 1985, 1, 0, 0, 1, 1, 0, 0, 10500.377, 753.67316, 1314.0103, 5.497786998748779296, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10500.377 753.67316 1314.0103
(46943, 1985, 1, 0, 0, 1, 1, 0, 0, 10516.56, 682.7782, 1319.9613, 0.715584993362426757, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10516.56 682.7782 1319.9613
(46941, 1985, 1, 0, 0, 1, 1, 0, 0, 10517.706, 949.63165, 1316.3641, 1.832595705986022949, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10517.706 949.63165 1316.3641
(46938, 1985, 1, 0, 0, 1, 1, 0, 0, 10549.423, 951.1659, 1315.958, 4.886921882629394531, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10549.423 951.1659 1315.958
(46937, 1985, 1, 0, 0, 1, 1, 0, 0, 10550.505, 981.1451, 1318.2482, 2.373647689819335937, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10550.505 981.1451 1318.2482
(46940, 1985, 1, 0, 0, 1, 1, 0, 0, 10581.247, 948.8594, 1316.2583, 1.65806281566619873, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10581.247 948.8594 1316.2583
(46957, 1985, 1, 0, 0, 1, 1, 0, 0, 10613.903, 647.68835, 1328.521, 1.780235767364501953, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10613.903 647.68835 1328.521
(46942, 1985, 1, 0, 0, 1, 1, 0, 0, 10616.272, 916.5843, 1316.502, 2.932153224945068359, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10616.272 916.5843 1316.502
(46949, 1985, 1, 0, 0, 1, 1, 0, 0, 10648.681, 782.7717, 1318.0311, 3.473205089569091796, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10648.681 782.7717 1318.0311
(46955, 1985, 1, 0, 0, 1, 1, 0, 0, 10648.777, 682.27563, 1329.4344, 1.169370532035827636, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10648.777 682.27563 1329.4344
(46947, 1985, 1, 0, 0, 1, 1, 0, 0, 10648.795, 849.7055, 1315.5149, 5.707226753234863281, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10648.795 849.7055 1315.5149
(46948, 1985, 1, 0, 0, 1, 1, 0, 0, 10751.183, 751.4058, 1328.1476, 5.777040004730224609, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10751.183 751.4058 1328.1476
/* Young Nightsaber/Young Thistleboar spawns -- ALL of these have two ids as they should switch back and forth between the mob types */
(49566, 2031, 1, 0, 0, 1, 1, 0, 0, 10245.866, 844.3586, 1343.9066, 0.506145477294921875, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10245.866 844.3586 1343.9066
(46923, 1984, 1, 0, 0, 1, 1, 0, 0, 10266.559, 733.7458, 1343.4877, 1.762782573699951171, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10266.559 733.7458 1343.4877
(49610, 2031, 1, 0, 0, 1, 1, 0, 0, 10316.083, 649.7789, 1331.6206, 5.427973747253417968, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10316.083 649.7789 1331.6206
(46929, 1984, 1, 0, 0, 1, 1, 0, 0, 10366.403, 699.85376, 1326.7089, 4.97418832778930664, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10366.403 699.85376 1326.7089
(46930, 1984, 1, 0, 0, 1, 1, 0, 0, 10382.43, 682.1378, 1325.6403, 4.502949237823486328, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10382.43 682.1378 1325.6403
(49565, 2031, 1, 0, 0, 1, 1, 0, 0, 10400.095, 633.01605, 1325.8828, 6.17846536636352539, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10400.095 633.01605 1325.8828
(46925, 1984, 1, 0, 0, 1, 1, 0, 0, 10484.313, 881.80414, 1312.1876, 3.700098037719726562, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10484.313 881.80414 1312.1876
(49620, 2031, 1, 0, 0, 1, 1, 0, 0, 10510.209, 869.6787, 1313.3438, 4.468042850494384765, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10510.209 869.6787 1313.3438
/* Mangy Nightsaber/Thistle Boar spawns -- ALL of these have two ids */
(49628, 2032, 1, 0, 0, 1, 1, 0, 0, 10516.052, 617.9318, 1331.6506, 3.822271108627319335, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10516.052 617.9318 1331.6506
(49648, 2032, 1, 0, 0, 1, 1, 0, 0, 10533.324, 633.5844, 1327.3145, 2.844886541366577148, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10533.324 633.5844 1327.3145
(46945, 1985, 1, 0, 0, 1, 1, 0, 0, 10552.625, 782.24304, 1313.4692, 0.890117883682250976, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10552.625 782.24304 1313.4692
(49633, 2032, 1, 0, 0, 1, 1, 0, 0, 10568.464, 633.21594, 1326.7195, 3.455751895904541015, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10568.464 633.21594 1326.7195
(46952, 1985, 1, 0, 0, 1, 1, 0, 0, 10582.159, 784.1131, 1310.5391, 4.310963153839111328, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10582.159 784.1131 1310.5391
(49636, 2032, 1, 0, 0, 1, 1, 0, 0, 10599.876, 799.4928, 1309.868, 5.742133140563964843, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10599.876 799.4928 1309.868
(49646, 2032, 1, 0, 0, 1, 1, 0, 0, 10615.404, 750.7155, 1317.5345, 1.535889744758605957, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10615.404 750.7155 1317.5345
(49626, 2032, 1, 0, 0, 1, 1, 0, 0, 10617.052, 784.78595, 1314.0431, 2.58308720588684082, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10617.052 784.78595 1314.0431
(46953, 1985, 1, 0, 0, 1, 1, 0, 0, 10618.01, 684.5653, 1326.204, 0.994837641716003417, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10618.01 684.5653 1326.204
(49640, 2032, 1, 0, 0, 1, 1, 0, 0, 10633.322, 732.6758, 1322.9066, 2.635447263717651367, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10633.322 732.6758 1322.9066
(46954, 1985, 1, 0, 0, 1, 1, 0, 0, 10633.33, 699.4627, 1325.9407, 6.091198921203613281, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10633.33 699.4627 1325.9407
(49643, 2032, 1, 0, 0, 1, 1, 0, 0, 10633.621, 666.5614, 1329.6592, 2.809980154037475585, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10633.621 666.5614 1329.6592
(46946, 1985, 1, 0, 0, 1, 1, 0, 0, 10649.84, 749.3575, 1319.7266, 1.710422635078430175, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10649.84 749.3575 1319.7266
(49639, 2032, 1, 0, 0, 1, 1, 0, 0, 10666.771, 766.71136, 1320.6018, 3.909537553787231445, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10666.771 766.71136 1320.6018
(49645, 2032, 1, 0, 0, 1, 1, 0, 0, 10666.881, 733.63776, 1323.4856, 3.787364482879638671, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10666.881 733.63776 1323.4856
(46951, 1985, 1, 0, 0, 1, 1, 0, 0, 10677.708, 749.8698, 1322.5094, 2.251474618911743164, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10677.708 749.8698 1322.5094
(46950, 1985, 1, 0, 0, 1, 1, 0, 0, 10682.271, 718.951, 1326.6854, 5.98647928237915039, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10682.271 718.951 1326.6854
/* Webwood Spiders Entry 1986 */
/* 47266 did not exist and is not re-added here */
/* 47018 did not exist and is not re-added here */
/* 47021 did not exist and is not re-added here */
/* 47023 did not exist and is not re-added here */
(47038, 1986, 1, 0, 0, 1, 1, 0, 0, 10666.519, 933.33875, 1322.0034, 2.237140178680419921, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10666.519 933.33875 1322.0034
(47062, 1986, 1, 0, 0, 1, 1, 0, 0, 10681.086, 916.2959, 1322.7343, 5.341568470001220703, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10681.086 916.2959 1322.7343
(47054, 1986, 1, 0, 0, 1, 1, 0, 0, 10681.857, 882.40375, 1320.5514, 3.932816028594970703, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10681.857 882.40375 1320.5514
(47055, 1986, 1, 0, 0, 1, 1, 0, 0, 10704.617, 944.4545, 1328.3015, 4.671853065490722656, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10704.617 944.4545 1328.3015
(47010, 1986, 1, 0, 0, 1, 1, 0, 0, 10715.091, 849.3726, 1325.8514, 0.742432296276092529, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10715.091 849.3726 1325.8514
(47012, 1986, 1, 0, 0, 1, 1, 0, 0, 10722.477, 877.2881, 1328.2606, 5.82608795166015625, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10722.477 877.2881 1328.2606
(47208, 1986, 1, 0, 0, 1, 1, 0, 0, 10731.819, 950.6483, 1333.217, 0.691456913948059082, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10731.819 950.6483 1333.217
(47037, 1986, 1, 0, 0, 1, 1, 0, 0, 10735.101, 900.9295, 1332.0488, 1.262176036834716796, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10735.101 900.9295 1332.0488
(47249, 1986, 1, 0, 0, 1, 1, 0, 0, 10753.574, 917.36957, 1338.4082, 0.60208207368850708, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10753.574 917.36957 1338.4082
(47061, 1986, 1, 0, 0, 1, 1, 0, 0, 10768.658, 923.96704, 1338.8344, 0.715287506580352783, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10768.658 923.96704 1338.8344
(47060, 1986, 1, 0, 0, 1, 1, 0, 0, 10786.26, 933.2063, 1337.399, 0.92502450942993164, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10786.26 933.2063 1337.399
(47016, 1986, 1, 0, 0, 1, 1, 0, 0, 10802.613, 944.37, 1336.0028, 3.475992679595947265, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10802.613 944.37 1336.0028
(47003, 1986, 1, 0, 0, 1, 1, 0, 0, 10823.141, 900.42706, 1335.8253, 3.303171157836914062, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10823.141 900.42706 1335.8253
(47017, 1986, 1, 0, 0, 1, 1, 0, 0, 10833.636, 934.5781, 1335.8376, 0.404480248689651489, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10833.636 934.5781 1335.8376
(47002, 1986, 1, 0, 0, 1, 1, 0, 0, 10835.615, 900.36694, 1334.823, 6.167264461517333984, 180, 4, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10835.615 900.36694 1334.823
(47001, 1986, 1, 0, 0, 1, 1, 0, 0, 10840.277, 885.4483, 1333.4894, 3.285114288330078125, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10840.277 885.4483 1333.4894
(47262, 1986, 1, 0, 0, 1, 1, 0, 0, 10843.158, 957.24945, 1335.4874, 2.405657052993774414, 180, 4.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10843.158 957.24945 1335.4874
(47000, 1986, 1, 0, 0, 1, 1, 0, 0, 10858.989, 858.99866, 1328.28, 5.654866695404052734, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10858.989 858.99866 1328.28
(47263, 1986, 1, 0, 0, 1, 1, 0, 0, 10864.862, 979.12384, 1336.5565, 0.973445236682891845, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10864.862 979.12384 1336.5565
(47057, 1986, 1, 0, 0, 1, 1, 0, 0, 10866.691, 917.2933, 1331.1963, 1.735110044479370117, 180, 4, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10866.691 917.2933 1331.1963
(46999, 1986, 1, 0, 0, 1, 1, 0, 0, 10868.057, 866.56116, 1326.7775, 1.342463016510009765, 180, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10868.057 866.56116 1326.7775
(46998, 1986, 1, 0, 0, 1, 1, 0, 0, 10882.504, 874.8653, 1325.4072, 4.981355667114257812, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10882.504 874.8653 1325.4072
(47056, 1986, 1, 0, 0, 1, 1, 0, 0, 10889.384, 917.5865, 1326.6378, 3.529083013534545898, 180, 2.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10889.384 917.5865 1326.6378
(47268, 1986, 1, 0, 0, 1, 1, 0, 0, 10893.004, 986.80664, 1337.1378, 5.216555118560791015, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10893.004 986.80664 1337.1378
(47269, 1986, 1, 0, 0, 1, 1, 0, 0, 10897.854, 974.71277, 1337.6536, 1.645332217216491699, 180, 4, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10897.854 974.71277 1337.6536
(46997, 1986, 1, 0, 0, 1, 1, 0, 0, 10899.094, 898.2902, 1323.6565, 3.498100996017456054, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10899.094 898.2902 1323.6565
(46958, 1986, 1, 0, 0, 1, 1, 0, 0, 10899.946, 878.31335, 1329.9327, 5.081437110900878906, 180, 4, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10899.946 878.31335 1329.9327
(46981, 1986, 1, 0, 0, 1, 1, 0, 0, 10908.563, 857.28625, 1328.9635, 3.51712656021118164, 180, 4.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10908.563 857.28625 1328.9635
(47271, 1986, 1, 0, 0, 1, 1, 0, 0, 10911.826, 969.3532, 1338.9346, 0.074718475341796875, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10911.826 969.3532 1338.9346
(47006, 1986, 1, 0, 0, 1, 1, 0, 0, 10915.727, 867.573, 1329.3433, 0.649777233600616455, 180, 4, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10915.727 867.573 1329.3433 significant relocation, it was an extra where it was, but a missing spider elsewhere was found.
(47270, 1986, 1, 0, 0, 1, 1, 0, 0, 10916.435, 957.58575, 1339.6991, 2.680749893188476562, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10916.435 957.58575 1339.6991
(47053, 1986, 1, 0, 0, 1, 1, 0, 0, 10922.044, 938.8875, 1322.2385, 0.228215917944908142, 180, 4, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10922.044 938.8875 1322.2385
(46974, 1986, 1, 0, 0, 1, 1, 0, 0, 10931.627, 864.9782, 1332.0298, 2.859531641006469726, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10931.627 864.9782 1332.0298
(47004, 1986, 1, 0, 0, 1, 1, 0, 0, 10934.576, 948.4298, 1321.892, 0.822031021118164062, 180, 5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10934.576 948.4298 1321.892
(47029, 1986, 1, 0, 0, 1, 1, 0, 0, 10941.859, 965.10504, 1327.1233, 5.16617441177368164, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10941.859 965.10504 1327.1233
(47009, 1986, 1, 0, 0, 1, 1, 0, 0, 10942.695, 914.79865, 1339.9248, 1.898581504821777343, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10942.695 914.79865 1339.9248
(46971, 1986, 1, 0, 0, 1, 1, 0, 0, 10948.623, 892.98126, 1337.5388, 0.760018706321716308, 180, 4, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10948.623 892.98126 1337.5388
(46972, 1986, 1, 0, 0, 1, 1, 0, 0, 10949.452, 867.8744, 1333.9869, 2.995260000228881835, 180, 3.5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10949.452 867.8744 1333.9869
(47008, 1986, 1, 0, 0, 1, 1, 0, 0, 10952.849, 939.5933, 1340.6506, 0.489440500736236572, 180, 3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10952.849 939.5933 1340.6506
(47052, 1986, 1, 0, 0, 1, 1, 0, 0, 10963.469, 925.5204, 1340.4236, 1.96779489517211914, 180, 4.7, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10963.469 925.5204 1340.4236
(47039, 1986, 1, 0, 0, 1, 1, 0, 0, 10973.123, 938.04926, 1337.9688, 3.923380613327026367, 180, 3.6, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10973.123 938.04926 1337.9688
(47030, 1986, 1, 0, 0, 1, 1, 0, 0, 10979.2, 977.51996, 1335.3134, 3.837030649185180664, 180, 4.3, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10979.2 977.51996 1335.3134
(47031, 1986, 1, 0, 0, 1, 1, 0, 0, 10990.74, 944.18933, 1336.1697, 6.03648233413696289, 180, 4.9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10990.74 944.18933 1336.1697
(47267, 1986, 1, 0, 0, 1, 1, 0, 0, 10718.58, 816.39734, 1327.0068, 5.064973354339599609, 180, 24.48583, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10718.58 816.39734 1327.0068
/* Githyiss the Vile Entry 1994 */
(47350, 1994, 1, 0, 0, 1, 1, 0, 0, 10938.402, 930.2512, 1340.9491, 0.162309497594833374, 90, 4.522563, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10938.402 930.2512 1340.9491
/* Grell Entry 1988 */
(47283, 1988, 1, 0, 0, 1, 1, 0, 0, 10254.234, 980.44904, 1342.792, 1.274090290069580078, 180, 4.8, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10254.234 980.44904 1342.792
(47290, 1988, 1, 0, 0, 1, 1, 0, 0, 10261.25, 952.9198, 1342.2471, 5.096361160278320312, 180, 5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10261.25 952.9198 1342.2471
(47312, 1988, 1, 0, 0, 1, 1, 0, 0, 10433.251, 966.4555, 1320.6614, 3.525565147399902343, 180, 27.790, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10433.251 966.4555 1320.6614
(47288, 1988, 1, 0, 0, 1, 1, 0, 0, 10271.706, 970.67035, 1341.1482, 1.972222089767456054, 180, 1, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10271.706 970.67035 1341.1482
(47289, 1988, 1, 0, 0, 1, 1, 0, 0, 10271.204, 964.8377, 1340.8528, 2.39110112190246582, 180, 0, 0, 1, 0, 2, 0, 0, 0, '', 0), -- .go xyz 10271.204 964.8377 1340.8528 NOTE THIS MOB HAS A PATH INCLUDED LATER IN THIS REVISION
(47286, 1988, 1, 0, 0, 1, 1, 0, 0, 10267.069, 964.64386, 1340.9333, 0.506145477294921875, 180, 0, 0, 1, 0, 0, 0, 0, 0, '', 0), -- .go xyz 10267.069 964.64386 1340.9333 (stay movetype)
(47284, 1988, 1, 0, 0, 1, 1, 0, 0, 10265.174, 967.29767, 1340.8788, 6.056292533874511718, 180, 0, 0, 1, 0, 0, 0, 0, 0, '', 0), -- .go xyz 10265.174 967.29767 1340.8788 (stay movetype)
(47281, 1988, 1, 0, 0, 1, 1, 0, 0, 10300.125, 966.756, 1336.4978, 5.026548385620117187, 180, 8.77, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10300.125 966.756 1336.4978
(47287, 1988, 1, 0, 0, 1, 1, 0, 0, 10271.893, 981.595, 1342.053, 1.692969322204589843, 180, 4.595, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10271.893 981.595 1342.053
(47282, 1988, 1, 0, 0, 1, 1, 0, 0, 10281.907, 947.99445, 1337.7051, 3.351032257080078125, 180, 9.769, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10281.907 947.99445 1337.7051
(47285, 1988, 1, 0, 0, 1, 1, 0, 0, 10283.576, 971.9565, 1339.1772, 1.239183783531188964, 180, 4.875, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10283.576 971.9565 1339.1772
(47310, 1988, 1, 0, 0, 1, 1, 0, 0, 10265.903, 943.0347, 1342.206, 3.455751895904541015, 180, 4.85, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10265.903 943.0347 1342.206
(47280, 1988, 1, 0, 0, 1, 1, 0, 0, 10284.647, 684.559, 1336.8057, 2.408554315567016601, 180, 26.175, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10284.647 684.559 1336.8057
/* Grellkin Entry 1989 */
(47342, 1989, 1, 0, 0, 1, 1, 0, 0, 10358.002, 1031.7273, 1340.2966, 2.478367567062377929, 180, 4.88, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10358.002 1031.7273 1340.2966 Begin Area 04 Mobs
(47334, 1989, 1, 0, 0, 1, 1, 0, 0, 10331.079, 1036.7262, 1339.4031, 5.8817596435546875, 180, 0, 0, 1, 0, 0, 0, 0, 0, '', 0), -- .go xyz 10331.079 1036.7262 1339.4031 (stay movetype)
(47344, 1989, 1, 0, 0, 1, 1, 0, 0, 10338.604, 1040.6471, 1339.4031, 4.729842185974121093, 180, 0, 0, 1, 0, 0, 0, 0, 0, '', 0), -- .go xyz 10338.604 1040.6471 1339.4031 (stay movetype)
(47314, 1989, 1, 0, 0, 1, 1, 0, 0, 10482.639, 1065.87, 1327.5127, 0.418879032135009765, 180, 0.816, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10482.639 1065.87 1327.5127
(47346, 1989, 1, 0, 0, 1, 1, 0, 0, 10333.449, 1018.8153, 1337.8043, 0.767944872379302978, 180, 9.865, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10333.449 1018.8153 1337.8043
(47336, 1989, 1, 0, 0, 1, 1, 0, 0, 10310.946, 1036.2836, 1343.1344, 1.93731546401977539, 180, 5.08, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10310.946 1036.2836 1343.1344
(47329, 1989, 1, 0, 0, 1, 1, 0, 0, 10493.197, 1044.005, 1326.464, 3.59537816047668457, 180, 0.9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10493.197 1044.005 1326.464
(47322, 1989, 1, 0, 0, 1, 1, 0, 0, 10533.27, 966.5156, 1316.9325, 5.777040004730224609, 180, 23.92, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10533.27 966.5156 1316.9325
(47343, 1989, 1, 0, 0, 1, 1, 0, 0, 10352.508, 1044.3253, 1342.7543, 2.670353651046752929, 180, 0.81, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10352.508 1044.3253 1342.7543
(47338, 1989, 1, 0, 0, 1, 1, 0, 0, 10331.617, 1030.6943, 1339.1439, 1.884955525398254394, 180, 0.9905, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10331.617 1030.6943 1339.1439
(47341, 1989, 1, 0, 0, 1, 1, 0, 0, 10344.033, 1036.6569, 1339.4775, 2.530727386474609375, 180, 0.9241, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10344.033 1036.6569 1339.4775
(47335, 1989, 1, 0, 0, 1, 1, 0, 0, 10316.063, 1018.7052, 1337.8849, 4.834561824798583984, 180, 9.669, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10316.063 1018.7052 1337.8849
(47345, 1989, 1, 0, 0, 1, 1, 0, 0, 10349.598, 1018.1509, 1336.6907, 0.92502450942993164, 180, 9.865, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10349.598 1018.1509 1336.6907
(47339, 1989, 1, 0, 0, 1, 1, 0, 0, 10336.676, 1032.8141, 1339.4031, 2.321287870407104492, 180, 0, 0, 1, 0, 2, 0, 0, 0, '', 0), -- .go xyz 10336.676 1032.8141 1339.4031 NOTE THIS MOB HAS A PATH INCLUDED LATER
(47319, 1989, 1, 0, 0, 1, 1, 0, 0, 10507.471, 1064.3892, 1325.983, 6.161012172698974609, 180, 0.7688, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10507.471 1064.3892 1325.983 Begin Area 12 Mobs
(47340, 1989, 1, 0, 0, 1, 1, 0, 0, 10517.359, 1014.5809, 1318.3701, 1.343903541564941406, 180, 9.48765, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10517.359 1014.5809 1318.3701
(47330, 1989, 1, 0, 0, 1, 1, 0, 0, 10529.228, 1033.2657, 1322.6095, 0.209439516067504882, 180, 8.797559, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10529.228 1033.2657 1322.6095
(47316, 1989, 1, 0, 0, 1, 1, 0, 0, 10600.643, 633.2474, 1329.6177, 0.977384388446807861, 180, 24.013, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10600.643 633.2474 1329.6177
(47315, 1989, 1, 0, 0, 1, 1, 0, 0, 10651.342, 684.23114, 1329.2515, 0.383972436189651489, 180, 30.28282, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10651.342 684.23114 1329.2515
(47331, 1989, 1, 0, 0, 1, 1, 0, 0, 10516.406, 1048.017, 1322.9946, 3.368485450744628906, 180, 9.428166, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10516.406 1048.017 1322.9946 
(47326, 1989, 1, 0, 0, 1, 1, 0, 0, 10500.292, 1053.153, 1325.5538, 2.600540637969970703, 180, 0, 0, 1, 0, 2, 0, 0, 0, '', 0), -- .go xyz 10500.292 1053.153 1325.5538 NOTE THIS MOB HAS A PATH INCLUDED LATER
(47321, 1989, 1, 0, 0, 1, 1, 0, 0, 10501.355, 1056.5448, 1325.5538, 4.084070205688476562, 180, 0.8825122, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10501.355 1056.5448 1325.5538
(47337, 1989, 1, 0, 0, 1, 1, 0, 0, 10681.228, 848.6761, 1322.0049, 3.281219005584716796, 180, 24, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10681.228 848.6761 1322.0049
(47332, 1989, 1, 0, 0, 1, 1, 0, 0, 10633.619, 933.41626, 1320.6321, 1.902408838272094726, 180, 25, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10633.619 933.41626 1320.6321
(47333, 1989, 1, 0, 0, 1, 1, 0, 0, 10632.88, 766.91473, 1319.0658, 4.101523876190185546, 180, 25, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10632.88 766.91473 1319.0658
(47324, 1989, 1, 0, 0, 1, 1, 0, 0, 10531.55, 1065.1317, 1321.4423, 1.954768776893615722, 180, 8.797559, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10531.55 1065.1317 1321.4423
(47320, 1989, 1, 0, 0, 1, 1, 0, 0, 10500.098, 666.6133, 1322.3407, 6.161012172698974609, 300, 24, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10500.098 666.6133 1322.3407
(47325, 1989, 1, 0, 0, 1, 1, 0, 0, 10496.867, 1058.3293, 1325.5538, 5.044001579284667968, 180, 0, 0, 1, 0, 0, 0, 0, 0, '', 0), -- .go xyz 10496.867 1058.3293 1325.5538 (stay movetype)
(47327, 1989, 1, 0, 0, 1, 1, 0, 0, 10496.637, 1052.5563, 1325.5538, 0.907571196556091308, 180, 0, 0, 1, 0, 0, 0, 0, 0, '', 0), -- .go xyz 10496.637 1052.5563 1325.5538 (stay movetype)
(47317, 1989, 1, 0, 0, 1, 1, 0, 0, 10426.051, 546.92017, 1333.475, 2.338741064071655273, 180, 24, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10426.051 546.92017 1333.475
(47328, 1989, 1, 0, 0, 1, 1, 0, 0, 10485.975, 1018.1689, 1325.7792, 1.832595705986022949, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0); -- .go xyz 10485.975 1018.1689 1325.7792

-- Pathing for 47326 Grellkin in the northmost Grellkin camp in Shadowglen that has 2 point pathing for whatever reason Entry: 1989
-- 0x2044A0002001F140000047000041F061 .go xyz 10507.13 1048.6158 1324.6445
SET @NPC := 47326;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=10507.13,`position_y`=1048.6158,`position_z`=1324.6445 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,10507.13,1048.6158,1324.6445,0,11000,0,0,100,0),
(@PATH,2,10500.348,1053.144,1325.5411,0,31000,0,0,100,0);
-- Pathing for 47339 Grellkin in the southmost Grellkin camp in Shadowglen that has 2 point pathing for whatever reason Entry: 1989
-- 0x2056B4002001F1400000150000BD2881 .go xyz 10336.16 1032.9716 1339.371
SET @NPC := 47339;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=10336.16,`position_y`=1032.9716,`position_z`=1339.371 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,10336.16,1032.9716,1339.371,0,25000,0,0,100,0),
(@PATH,2,10337.761,1025.8269,1338.2256,0,26000,0,0,100,0);
-- Pathing for Grell in the only Grell camp in Shadowglen that has 2 point pathing for whatever reason  Entry: 1988
-- 0x2056B4002001F10000001500003D394B .go xyz 10273.941 963.12164 1340.2665
SET @NPC := 47289;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=10273.941,`position_y`=963.12164,`position_z`=1340.2665 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,10273.941,963.12164,1340.2665,0,13000,0,0,100,0),
(@PATH,2,10271.291,964.8906,1340.8572,0,25000,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_04_04' WHERE sql_rev = '1640754933928340200';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
