-- DB update 2022_01_03_19 -> 2022_01_03_20
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_03_19';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_03_19 2022_01_03_20 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639519249292727400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639519249292727400');

-- creature
DELETE FROM `creature` WHERE `guid` IN (86249,86258,86264,86265,86716,86898,90490,94518,94553,94555,94603,94604,94634,94635,94637,94649,94693,94694,94699,94700,
94704,94710,94711,94738,94747,94753,94760,94761,94762,94765,94773,94786,94796,94803,94805,94808,94814,94831,94839,94840,94862,94862,94862,94863,94863,94863,94920,
94920,94921,94921,94922,94923,94924,94925,94926,94927,94928,94929,94930,94930,94930,94930,94931,94931,94931,94931,94932,94932,94932,94932,94933,94933,94933,94933,
94934,94934,94934,94935,94935,94935,94936,94936,94936,94937,94937,94937,94950,94956,202858,202859,202860,202861,248565,248566,248567,248568,248569,248570,248571,
248573,248574,248576,248577,248578,248579,248580,248583);

DELETE FROM `game_event_creature` WHERE `guid` IN (86249,86258,86264,86265,86716,86898,90490,94518,94553,94555,94603,94604,94634,94635,94637,94649,94693,94694,
94699,94700,94704,94710,94711,94738,94747,94753,94760,94761,94762,94765,94773,94786,94796,94803,94805,94808,94814,94831,94839,94840,94862,94862,94862,94863,94863,
94863,94920,94920,94921,94921,94922,94923,94924,94925,94926,94927,94928,94929,94930,94930,94930,94930,94931,94931,94931,94931,94932,94932,94932,94932,94933,94933,
94933,94933,94934,94934,94934,94935,94935,94935,94936,94936,94936,94937,94937,94937,94950,94956,202858,202859,202860,202861,248565,248566,248567,248568,248569,
248570,248571,248573,248574,248576,248577,248578,248579,248580,248583);

DELETE FROM `creature_addon` WHERE `guid` IN (86249,86258,86264,86265,86716,86898,90490,94518,94553,94555,94603,94604,94634,94635,94637,94649,94693,94694,94699,94700,
94704,94710,94711,94738,94747,94753,94760,94761,94762,94765,94773,94786,94796,94803,94805,94808,94814,94831,94839,94840,94862,94862,94862,94863,94863,94863,94920,
94920,94921,94921,94922,94923,94924,94925,94926,94927,94928,94929,94930,94930,94930,94930,94931,94931,94931,94931,94932,94932,94932,94932,94933,94933,94933,94933,
94934,94934,94934,94935,94935,94935,94936,94936,94936,94937,94937,94937,94950,94956,202858,202859,202860,202861,248565,248566,248567,248568,248569,248570,248571,
248573,248574,248576,248577,248578,248579,248580,248583);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_03_20' WHERE sql_rev = '1639519249292727400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
