-- DB update 2021_09_03_02 -> 2021_09_03_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_03_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_03_02 2021_09_03_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1630690395841002600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1630690395841002600');

-- Fix merge of #7707
-- Adds movements for Stonewind Trackers
UPDATE `creature` SET `MovementType` = 1, `wander_distance` = 5 WHERE `id` = 16316 AND `guid` IN (82666, 82668);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_03_03' WHERE sql_rev = '1630690395841002600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
