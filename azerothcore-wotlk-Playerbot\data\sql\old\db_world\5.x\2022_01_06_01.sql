-- DB update 2022_01_06_00 -> 2022_01_06_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_06_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_06_00 2022_01_06_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641422836099367029'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641422836099367029');
-- Fixes issue: https://github.com/azerothcore/azerothcore-wotlk/issues/4136

-- Siltfin Oracle Corrected Script For Orcales 17190 where they would cast lightning shield in combat which is incorrect, tested on retail
SET @ENTRY := 17191;
SET @SOURCETYPE := 0;

DELETE FROM `smart_scripts` WHERE `entryorguid`=@ENTRY AND `source_type`=@SOURCETYPE;
UPDATE creature_template SET AIName="SmartAI" WHERE entry=@ENTRY LIMIT 1;
INSERT INTO `smart_scripts` (`entryorguid`,`source_type`,`id`,`link`,`event_type`,`event_phase_mask`,`event_chance`,`event_flags`,`event_param1`,`event_param2`,`event_param3`,`event_param4`,`action_type`,`action_param1`,`action_param2`,`action_param3`,`action_param4`,`action_param5`,`action_param6`,`target_type`,`target_param1`,`target_param2`,`target_param3`,`target_x`,`target_y`,`target_z`,`target_o`,`comment`) VALUES 
(@ENTRY,@SOURCETYPE,0,1,1,0,100,1,0,0,0,0,21,0,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Out of Combat - Disable Combat Movement (No Repeat)"),
(@ENTRY,@SOURCETYPE,1,0,61,0,100,0,0,0,0,0,22,0,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Out of Combat - Set Event Phase 0 (No Repeat)"),
(@ENTRY,@SOURCETYPE,2,0,1,0,100,0,0,0,605000,605000,11,12550,1,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Out of Combat - Cast 'Lightning Shield' (No Repeat)"),
(@ENTRY,@SOURCETYPE,3,4,4,0,100,1,0,0,0,0,11,9739,0,0,0,0,0,2,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - On Aggro - Cast 'Wrath' (No Repeat)"),
(@ENTRY,@SOURCETYPE,4,0,61,0,100,0,0,0,0,0,23,1,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - On Aggro - Increment Phase By 1 (No Repeat)"),
(@ENTRY,@SOURCETYPE,5,0,9,1,100,0,0,40,3400,4800,11,9739,0,0,0,0,0,2,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Within 0-40 Range - Cast 'Wrath' (No Repeat)"),
(@ENTRY,@SOURCETYPE,6,7,3,1,100,1,0,7,0,0,21,1,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Between 0-7% Mana - Enable Combat Movement (Phase 1) (No Repeat)"),
(@ENTRY,@SOURCETYPE,7,0,61,1,100,0,0,0,0,0,23,1,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Between 0-7% Mana - Increment Phase By 1 (Phase 1) (No Repeat)"),
(@ENTRY,@SOURCETYPE,8,0,9,1,100,1,35,80,0,0,21,1,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Within 35-80 Range - Enable Combat Movement (Phase 1) (No Repeat)"),
(@ENTRY,@SOURCETYPE,9,0,9,1,100,1,5,15,0,0,21,0,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Within 5-15 Range - Disable Combat Movement (Phase 1) (No Repeat)"),
(@ENTRY,@SOURCETYPE,10,0,9,1,100,1,0,5,0,0,21,1,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Within 0-5 Range - Enable Combat Movement (Phase 1) (No Repeat)"),
(@ENTRY,@SOURCETYPE,11,0,3,2,100,0,15,100,100,100,23,0,1,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Between 15-100% Mana - Decrement Phase By 1 (Phase 1) (No Repeat)"),
(@ENTRY,@SOURCETYPE,12,13,2,0,100,1,0,15,0,0,22,3,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Between 0-15% Health - Set Event Phase 3 (No Repeat)"),
(@ENTRY,@SOURCETYPE,13,14,61,0,100,0,0,0,0,0,21,1,0,0,0,0,0,1,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Between 0-15% Health - Enable Combat Movement (No Repeat)"),
(@ENTRY,@SOURCETYPE,14,0,61,0,100,0,0,0,0,0,25,1,0,0,0,0,0,0,0,0,0,0.0,0.0,0.0,0.0,"Siltfin Oracle - Between 0-15% Health - Flee For Assist (No Repeat)");
-- This fixes the entire murloc overpop issue and adds waypoints to the missing ones
DELETE FROM `creature` WHERE `id`='17192';
DELETE FROM `creature` WHERE `id`='17191';
DELETE FROM `creature` WHERE `id`='17190';
-- Azuremyst Isle Murloc Spawn Correction
INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(60815, 17190, 530, 0, 0, 1, 1, 0, 0, -3418.7344, -11851.264, -13.1580305, 5.732042312622070312, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60816, 17191, 530, 0, 0, 1, 1, 0, 0, -3456.1323, -11847.735, -11.387332, 1.711444258689880371, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60817, 17192, 530, 0, 0, 1, 1, 0, 0, -3509.9038, -11856.772, -0.23409465, 3.333652734756469726, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60818, 17190, 530, 0, 0, 1, 1, 0, 0, -3418.388, -11906.669, 0.5362283, 5.327297210693359375, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60819, 17191, 530, 0, 0, 1, 1, 0, 0, -3447.114, -11812.999, -14.579041, 0.279252678155899047, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60820, 17190, 530, 0, 0, 1, 1, 0, 0, -3479.6594, -11873.479, 0.51927835, 0.044982723891735076, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60821, 17190, 530, 0, 0, 1, 1, 0, 0, -3480.5273, -11816.888, -5.5090046, 4.998753547668457031, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60822, 17190, 530, 0, 0, 1, 1, 0, 0, -3508.683, -11873.045, 0.9488133, 3.711103439331054687, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60823, 17191, 530, 0, 0, 1, 1, 0, 0, -3414.6814, -11881.868, -1.9741567, 1.940909028053283691, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60824, 17191, 530, 0, 0, 1, 1, 0, 0, -3482.1824, -11851.981, -3.5120018, 4.693110466003417968, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60825, 17190, 530, 0, 0, 1, 1, 0, 0, -3447.0583, -11880.729, -0.6653257, 4.630099773406982421, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60826, 17191, 530, 0, 0, 1, 1, 0, 0, -3385.7063, -11852.628, -11.661822, 0.175547182559967041, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60827, 17190, 530, 0, 0, 1, 1, 0, 0, -3349.188, -11850.748, -6.7036333, 5.400006294250488281, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60828, 17191, 530, 0, 0, 1, 1, 0, 0, -3384.3682, -11877.396, -2.5841198, 4.056396007537841796, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60829, 17192, 530, 0, 0, 1, 1, 0, 0, -3387.4993, -11913.941, 2.3895292, 3.002491950988769531, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60830, 17190, 530, 0, 0, 1, 1, 0, 0, -3283.5479, -11849.478, -6.3345637, 3.834458112716674804, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60831, 17190, 530, 0, 0, 1, 1, 0, 0, -3347.9106, -11874.783, 0.90404034, 5.980024337768554687, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60832, 17192, 530, 0, 0, 1, 1, 0, 0, -3311.9468, -11876.064, 1.0249523, 5.917231082916259765, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60833, 17191, 530, 0, 0, 1, 1, 0, 0, -3317.9648, -11849.428, -5.6661654, 2.480176925659179687, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60834, 17190, 530, 0, 0, 1, 1, 0, 0, -3250.885, -11849.2, -6.2724185, 3.617093801498413085, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60835, 17191, 530, 0, 0, 1, 1, 0, 0, -3282.3862, -11876.091, 0.6628773, 2.436619758605957031, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60836, 17190, 530, 0, 0, 1, 1, 0, 0, -3246.7244, -11879.32, 0.53758234, 3.889608383178710937, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60837, 17190, 530, 0, 0, 1, 1, 0, 0, -3180.962, -11876.614, -2.5694907, 3.93802499771118164, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60838, 17191, 530, 0, 0, 1, 1, 0, 0, -3185.7952, -11850.562, -11.835139, 6.23506021499633789, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60839, 17190, 530, 0, 0, 1, 1, 0, 0, -3215.2356, -11850.744, -8.318182, 2.504043817520141601, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60840, 17192, 530, 0, 0, 1, 1, 0, 0, -3204.5115, -11887.962, 1.3484204, 3.67181396484375, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60841, 17191, 530, 0, 0, 1, 1, 0, 0, -3149.9995, -11881.737, -4.5278587, 3.279867172241210937, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60842, 17190, 530, 0, 0, 1, 1, 0, 0, -3121.5337, -11883.218, -10.033356, 5.83039093017578125, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60843, 17191, 530, 0, 0, 1, 1, 0, 0, -3083.1177, -11948.621, -4.8925085, 5.963871002197265625, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60844, 17192, 530, 0, 0, 1, 1, 0, 0, -3121.371, -11914.906, -0.047368668, 4.471675872802734375, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60845, 17190, 530, 0, 0, 1, 1, 0, 0, -3147.534, -11909.957, 1.7172734, 1.019341349601745605, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60846, 17191, 530, 0, 0, 1, 1, 0, 0, -3109.468, -11944.301, 1.3611784, 4.029753684997558593, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60847, 17190, 530, 0, 0, 1, 1, 0, 0, -3085.8335, -11916, -12.106035, 0.524696588516235351, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60848, 17192, 530, 0, 0, 1, 1, 0, 0, -3103.0347, -11960.887, 1.2907534, 4.892964839935302734, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0),
(60849, 17192, 530, 0, 0, 1, 1, 0, 0, -3507.0442, -11719.272, -2.3212037, 4.633449554443359375, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0),
(60850, 17191, 530, 0, 0, 1, 1, 0, 0, -3475.5908, -11744.083, -11.263855, 0.034218687564134597, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60851, 17191, 530, 0, 0, 1, 1, 0, 0, -3445.6099, -11717.692, -12.640131, 4.099737167358398437, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60852, 17191, 530, 0, 0, 1, 1, 0, 0, -3451.366, -11783.213, -11.717131, 0.016028549522161483, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60853, 17190, 530, 0, 0, 1, 1, 0, 0, -3449.7366, -11749.387, -15.975848, 5.947178840637207031, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0);
-- Silvermyst Isle Murloc Spawn Correction
INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(60854, 17190, 530, 0, 0, 1, 1, 0, 0, -4782.0522, -10885.83, -1.9032596, 5.721250534057617187, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60856, 17191, 530, 0, 0, 1, 1, 0, 0, -4780.7427, -10947.197, 2.0806522, 0.963193178176879882, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60858, 17190, 530, 0, 0, 1, 1, 0, 0, -4750.2812, -10916.691, -4.0878963, 4.093502044677734375, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60859, 17190, 530, 0, 0, 1, 1, 0, 0, -4848.485, -10880.066, 1.5396113, 3.835026741027832031, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60860, 17192, 530, 0, 0, 1, 1, 0, 0, -4778.426, -10918.231, 1.1470184, 0.303351521492004394, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60861, 17191, 530, 0, 0, 1, 1, 0, 0, -4815.167, -10916.244, 3.2374144, 6.246098995208740234, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60862, 17191, 530, 0, 0, 1, 1, 0, 0, -4816.705, -10882.181, 1.3823434, 1.453345298767089843, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60863, 17190, 530, 0, 0, 1, 1, 0, 0, -4884.4507, -10876.484, 0.89848834, 2.572053909301757812, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60864, 17190, 530, 0, 0, 1, 1, 0, 0, -4916.4434, -10815.736, 0.12851134, 4.188790321350097656, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60865, 17191, 530, 0, 0, 1, 1, 0, 0, -4885.069, -10789.235, -7.5229855, 5.109590530395507812, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60866, 17191, 530, 0, 0, 1, 1, 0, 0, -4882.716, -10813.426, 3.8298893, 0.072389766573905944, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60867, 17192, 530, 0, 0, 1, 1, 0, 0, -4916.4175, -10847.909, 2.2527323, 3.577924966812133789, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60868, 17191, 530, 0, 0, 1, 1, 0, 0, -4916.2856, -10878.55, 1.6243434, 0.987139821052551269, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60869, 17191, 530, 0, 0, 1, 1, 0, 0, -4881.9375, -10849.54, -0.37858066, 1.384037613868713378, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60870, 17190, 530, 0, 0, 1, 1, 0, 0, -4950.81, -10846.213, 2.3041203, 1.48352980613708496, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60871, 17190, 530, 0, 0, 1, 1, 0, 0, -4949.591, -10813.83, 1.9970464, 1.88459479808807373, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60872, 17190, 530, 0, 0, 1, 1, 0, 0, -4916.4023, -10782.341, -2.5666227, 5.699655055999755859, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60873, 17191, 530, 0, 0, 1, 1, 0, 0, -4944.8433, -10780.778, 0.8070873, 1.985079765319824218, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60874, 17192, 530, 0, 0, 1, 1, 0, 0, -4972.7026, -10787.582, 4.0531445, 3.650714874267578125, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60875, 17190, 530, 0, 0, 1, 1, 0, 0, -5012.6875, -10752.829, -1.8142476, 2.912671566009521484, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60876, 17191, 530, 0, 0, 1, 1, 0, 0, -5014.7197, -10782.382, 1.0995353, 3.78670501708984375, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60877, 17190, 530, 0, 0, 1, 1, 0, 0, -4981.4985, -10755.423, 0.14584734, 0.838271677494049072, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60878, 17192, 530, 0, 0, 1, 1, 0, 0, -5077.753, -10777.782, 0.9097403, 5.257307052612304687, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60879, 17190, 530, 0, 0, 1, 1, 0, 0, -5050.1504, -10754.573, -2.1774678, 3.009273052215576171, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60880, 17191, 530, 0, 0, 1, 1, 0, 0, -5084.356, -10782.681, 1.3913144, 1.313789725303649902, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60881, 17191, 530, 0, 0, 1, 1, 0, 0, -5050.065, -10783.608, 0.70146734, 4.281090736389160156, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60882, 17191, 530, 0, 0, 1, 1, 0, 0, -5149.8066, -10754.19, -0.8922967, 1.360486507415771484, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60883, 17191, 530, 0, 0, 1, 1, 0, 0, -5082.3066, -10755.057, -1.2758986, 5.392621040344238281, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60884, 17190, 530, 0, 0, 1, 1, 0, 0, -5119.077, -10780.689, 1.6282684, 6.048854827880859375, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60885, 17191, 530, 0, 0, 1, 1, 0, 0, -5117.627, -10754.137, 0.34845033, 2.586098909378051757, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60886, 17192, 530, 0, 0, 1, 1, 0, 0, -5152.6533, -10782.051, 0.56522334, 2.030295848846435546, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60887, 17190, 530, 0, 0, 1, 1, 0, 0, -5180.472, -10781.96, -0.7218827, 6.108652114868164062, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60888, 17191, 530, 0, 0, 1, 1, 0, 0, -5179.2207, -10754.452, -5.7092085, 2.342447757720947265, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60889, 17190, 530, 0, 0, 1, 1, 0, 0, -5218.4097, -10814.118, 1.7652323, 4.179326057434082031, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60891, 17191, 530, 0, 0, 1, 1, 0, 0, -5216.8667, -10784.105, -1.0252436, 3.174775600433349609, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60892, 17190, 530, 0, 0, 1, 1, 0, 0, -5253.382, -10846.761, 1.4306344, 6.230739116668701171, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60894, 17192, 530, 0, 0, 1, 1, 0, 0, -5289.4277, -10851.064, -0.8234407, 4.722154617309570312, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60895, 17190, 530, 0, 0, 1, 1, 0, 0, -5313.278, -10848.15, -1.4534626, 5.594808578491210937, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60898, 17191, 530, 0, 0, 1, 1, 0, 0, -5281.528, -10821.938, -7.1264277, 1.467707157135009765, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60899, 17190, 530, 0, 0, 1, 1, 0, 0, -5285.053, -10877.956, 1.4539274, 5.279401779174804687, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60900, 17191, 530, 0, 0, 1, 1, 0, 0, -5316.7944, -10883.229, 2.9844863, 0.562620699405670166, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60901, 17190, 530, 0, 0, 1, 1, 0, 0, -5321.817, -10949.605, -0.12932667, 1.964986801147460937, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60902, 17191, 530, 0, 0, 1, 1, 0, 0, -5345.443, -10944.845, -2.4428368, 5.557324886322021484, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60903, 17190, 530, 0, 0, 1, 1, 0, 0, -5345.604, -10915.708, -0.6951377, 3.472287893295288085, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60904, 17190, 530, 0, 0, 1, 1, 0, 0, -5348.8247, -10881.534, -1.4308957, 0.935154259204864501, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60905, 17192, 530, 0, 0, 1, 1, 0, 0, -5326.346, -10924.387, -0.061333664, 3.229621648788452148, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60906, 17190, 530, 0, 0, 1, 1, 0, 0, -5350.43, -11013.511, 0.35168836, 0.324238121509552001, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60907, 17190, 530, 0, 0, 1, 1, 0, 0, -5350.531, -10987.597, -1.6087946, 4.186498641967773437, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60908, 17190, 530, 0, 0, 1, 1, 0, 0, -5419.6704, -10952.906, -21.553621, 0.472459375858306884, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60909, 17192, 530, 0, 0, 1, 1, 0, 0, -5426.0225, -11008.705, -23.93589, 3.582702398300170898, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60910, 17191, 530, 0, 0, 1, 1, 0, 0, -5415.7188, -10981.389, -21.267178, 0.531979978084564208, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60911, 17190, 530, 0, 0, 1, 1, 0, 0, -5450.788, -10978.354, -38.85598, 0.367103815078735351, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60912, 17191, 530, 0, 0, 1, 1, 0, 0, -5450.012, -10954.308, -38.900898, 1.450325965881347656, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60913, 17190, 530, 0, 0, 1, 1, 0, 0, -5451.4165, -11014.778, -32.491295, 1.298519492149353027, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60914, 17191, 530, 0, 0, 1, 1, 0, 0, -5476.1006, -10987.348, -153.56447, 2.300004959106445312, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60915, 17191, 530, 0, 0, 1, 1, 0, 0, -5478.5923, -11013.903, -99.875465, 3.014854192733764648, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60916, 17190, 530, 0, 0, 1, 1, 0, 0, -5478.1235, -10953.604, -151.27324, 3.179136276245117187, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60917, 17192, 530, 0, 0, 1, 1, 0, 0, -4773.0347, -10936.996, 0.79550934, 2.093307971954345703, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(60918, 17192, 530, 0, 0, 1, 1, 0, 0, -5422.1284, -10983.814, -25.117788, 0.890117883682250976, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0);
-- Pathing for Siltfin Hunter Entry: 17192
SET @NPC := 60848;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-3507.3623,`position_y`=-11723.294,`position_z`=-2.4598432 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-3507.3623,-11723.294,-2.4598432,0,0,0,0,100,0),
(@PATH,2,-3484.9644,-11730.426,-9.596426,0,0,0,0,100,0),
(@PATH,3,-3460.9214,-11754.151,-13.45673,0,0,0,0,100,0),
(@PATH,4,-3453.2812,-11787.156,-10.818729,0,0,0,0,100,0),
(@PATH,5,-3461.5327,-11808.388,-10.687053,0,0,0,0,100,0),
(@PATH,6,-3483.0698,-11821.035,-5.677581,0,0,0,0,100,0),
(@PATH,7,-3505.5654,-11830.038,-0.4040861,0,0,0,0,100,0),
(@PATH,8,-3530.954,-11848.568,0.6330228,0,0,0,0,100,0),
(@PATH,9,-3522.1147,-11873.662,2.3754025,0,0,0,0,100,0),
(@PATH,10,-3491.854,-11881.409,1.0961723,0,0,0,0,100,0),
(@PATH,11,-3473.4165,-11878.115,0.6796684,0,0,0,0,100,0),
(@PATH,12,-3441.9653,-11885.802,-0.57041025,0,0,0,0,100,0),
(@PATH,13,-3416.75,-11893.61,-0.24430943,0,0,0,0,100,0),
(@PATH,14,-3394.9019,-11901.724,0.656273,0,0,0,0,100,0),
(@PATH,15,-3373.8406,-11883.355,0.14870358,0,0,0,0,100,0),
(@PATH,16,-3345.6877,-11879.197,1.0803907,0,0,0,0,100,0),
(@PATH,17,-3322.915,-11882.277,1.1478192,0,0,0,0,100,0),
(@PATH,18,-3281.5635,-11880.732,1.0384169,0,0,0,0,100,0),
(@PATH,19,-3255.14,-11875.51,0.24752569,0,0,0,0,100,0),
(@PATH,20,-3281.5635,-11880.732,1.0384169,0,0,0,0,100,0),
(@PATH,21,-3322.915,-11882.277,1.1478192,0,0,0,0,100,0),
(@PATH,22,-3345.6877,-11879.197,1.0803907,0,0,0,0,100,0),
(@PATH,23,-3373.8406,-11883.355,0.14870358,0,0,0,0,100,0),
(@PATH,24,-3394.9019,-11901.724,0.656273,0,0,0,0,100,0),
(@PATH,25,-3416.75,-11893.61,-0.24430943,0,0,0,0,100,0),
(@PATH,26,-3441.9653,-11885.802,-0.57041025,0,0,0,0,100,0),
(@PATH,27,-3473.4165,-11878.115,0.6796684,0,0,0,0,100,0),
(@PATH,28,-3491.854,-11881.409,1.0961723,0,0,0,0,100,0),
(@PATH,29,-3522.1147,-11873.662,2.3754025,0,0,0,0,100,0),
(@PATH,30,-3530.954,-11848.568,0.6330228,0,0,0,0,100,0),
(@PATH,31,-3505.5654,-11830.038,-0.4040861,0,0,0,0,100,0),
(@PATH,32,-3483.0698,-11821.035,-5.677581,0,0,0,0,100,0),
(@PATH,33,-3461.5327,-11808.388,-10.687053,0,0,0,0,100,0),
(@PATH,34,-3453.2812,-11787.156,-10.818729,0,0,0,0,100,0),
(@PATH,35,-3460.9214,-11754.151,-13.45673,0,0,0,0,100,0),
(@PATH,36,-3484.9644,-11730.426,-9.596426,0,0,0,0,100,0);
-- Pathing for Siltfin Hunter Entry: 17192
SET @NPC := 60849;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-3102.4048,`position_y`=-11964.337,`position_z`=1.3131504 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-3102.4048,-11964.337,1.3131504,0,0,0,0,100,0),
(@PATH,2,-3110.902,-11939.24,1.2054844,0,0,0,0,100,0),
(@PATH,3,-3125.7214,-11924.45,1.9875517,0,0,0,0,100,0),
(@PATH,4,-3140.2388,-11907.929,0.8125229,0,0,0,0,100,0),
(@PATH,5,-3165.4905,-11901.601,1.3979721,0,0,0,0,100,0),
(@PATH,6,-3181.0986,-11898.499,1.1609921,0,0,0,0,100,0),
(@PATH,7,-3203.952,-11889.292,1.4012604,0,0,0,0,100,0),
(@PATH,8,-3221.1853,-11889.556,1.4532623,0,0,0,0,100,0),
(@PATH,9,-3237.999,-11887.988,1.001432,0,0,0,0,100,0),
(@PATH,10,-3254.9707,-11879.298,0.57711554,0,0,0,0,100,0),
(@PATH,11,-3283.4229,-11880.534,1.0144911,0,0,0,0,100,0),
(@PATH,12,-3313.9695,-11882.954,1.4261395,0,0,0,0,100,0),
(@PATH,13,-3344.1895,-11871.624,0.73761725,0,0,0,0,100,0),
(@PATH,14,-3372.4,-11878.497,-0.52878666,0,0,0,0,100,0),
(@PATH,15,-3344.1895,-11871.624,0.73761725,0,0,0,0,100,0),
(@PATH,16,-3313.9695,-11882.954,1.4261395,0,0,0,0,100,0),
(@PATH,17,-3283.4229,-11880.534,1.0144911,0,0,0,0,100,0),
(@PATH,18,-3254.9707,-11879.298,0.57711554,0,0,0,0,100,0),
(@PATH,19,-3237.999,-11887.988,1.001432,0,0,0,0,100,0),
(@PATH,20,-3221.1853,-11889.556,1.4532623,0,0,0,0,100,0),
(@PATH,21,-3203.952,-11889.292,1.4012604,0,0,0,0,100,0),
(@PATH,22,-3181.0986,-11898.499,1.1609921,0,0,0,0,100,0),
(@PATH,23,-3165.4905,-11901.601,1.3979721,0,0,0,0,100,0),
(@PATH,24,-3140.2388,-11907.929,0.8125229,0,0,0,0,100,0),
(@PATH,25,-3125.7214,-11924.45,1.9875517,0,0,0,0,100,0),
(@PATH,26,-3110.902,-11939.24,1.2054844,0,0,0,0,100,0);
-- Pathing for Murgurgula Entry: 17475
SET @NPC := 62989;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-3532.4504,`position_y`=-11839.493,`position_z`=0.24215364 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-3532.4504,-11839.493,0.24215364,0,2000,0,0,100,0),
(@PATH,2,-3528.9163,-11864.7,2.2262845,0,0,0,0,100,0),
(@PATH,3,-3502.587,-11883.379,2.1981564,0,0,0,0,100,0),
(@PATH,4,-3473.328,-11883.536,0.692852,0,0,0,0,100,0),
(@PATH,5,-3451.3577,-11905.594,1.3231692,0,0,0,0,100,0),
(@PATH,6,-3424.0977,-11921.437,2.2423608,0,0,0,0,100,0),
(@PATH,7,-3401.3726,-11920.06,2.6000268,0,0,0,0,100,0),
(@PATH,8,-3390.2778,-11925,3.9492416,0,0,0,0,100,0),
(@PATH,9,-3376.302,-11918.646,3.7055893,0,0,0,0,100,0),
(@PATH,10,-3360.1824,-11893.064,1.5261915,0,0,0,0,100,0),
(@PATH,11,-3343.4473,-11883.011,1.0803907,0,0,0,0,100,0),
(@PATH,12,-3303.1553,-11886.411,1.7049481,0,0,0,0,100,0),
(@PATH,13,-3271.4805,-11885.552,1.366786,0,0,0,0,100,0),
(@PATH,14,-3226.0237,-11891.944,1.2486725,0,0,0,0,100,0),
(@PATH,15,-3201.6091,-11891.711,1.4415436,0,0,0,0,100,0),
(@PATH,16,-3164.0845,-11911.024,2.2790756,0,0,0,0,100,0),
(@PATH,17,-3140.2844,-11913.65,1.4692612,0,0,0,0,100,0),
(@PATH,18,-3113.9927,-11939.892,1.654459,0,2000,0,0,100,0),
(@PATH,19,-3140.2844,-11913.65,1.4692612,0,0,0,0,100,0),
(@PATH,20,-3164.0845,-11911.024,2.2790756,0,0,0,0,100,0),
(@PATH,21,-3201.6091,-11891.711,1.4415436,0,0,0,0,100,0),
(@PATH,22,-3226.0237,-11891.944,1.2486725,0,0,0,0,100,0),
(@PATH,23,-3271.4805,-11885.552,1.366786,0,0,0,0,100,0),
(@PATH,24,-3303.1553,-11886.411,1.7049481,0,0,0,0,100,0),
(@PATH,25,-3343.4473,-11883.011,1.0803907,0,0,0,0,100,0),
(@PATH,26,-3360.1824,-11893.064,1.5261915,0,0,0,0,100,0),
(@PATH,27,-3376.302,-11918.646,3.7055893,0,0,0,0,100,0),
(@PATH,28,-3390.2778,-11925,3.9492416,0,0,0,0,100,0),
(@PATH,29,-3401.3726,-11920.06,2.6000268,0,0,0,0,100,0),
(@PATH,30,-3424.0977,-11921.437,2.2423608,0,0,0,0,100,0),
(@PATH,31,-3451.3577,-11905.594,1.3231692,0,0,0,0,100,0),
(@PATH,32,-3473.328,-11883.536,0.692852,0,0,0,0,100,0),
(@PATH,33,-3502.587,-11883.379,2.1981564,0,0,0,0,100,0),
(@PATH,34,-3528.9163,-11864.7,2.2262845,0,0,0,0,100,0);
-- Pathing for Siltfin Hunter Entry: 17192
SET @NPC := 60917;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-4777.058,`position_y`=-10930.01,`position_z`=0.9688175 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-4777.058,-10930.01,0.9688175,0,0,0,0,100,0),
(@PATH,2,-4797.7446,-10903.832,2.4763858,0,0,0,0,100,0),
(@PATH,3,-4818.099,-10886.483,2.694218,0,0,0,0,100,0),
(@PATH,4,-4843.6704,-10883.837,2.5746179,0,0,0,0,100,0),
(@PATH,5,-4874.6016,-10870.769,0.6556537,0,0,0,0,100,0),
(@PATH,6,-4900.4756,-10848.133,1.2946562,0,0,0,0,100,0),
(@PATH,7,-4907.662,-10832.577,0.7674937,0,0,0,0,100,0),
(@PATH,8,-4927.9697,-10817.761,1.2577281,0,0,0,0,100,0),
(@PATH,9,-4964.0767,-10794.24,3.4587717,0,0,0,0,100,0),
(@PATH,10,-4976.0903,-10788.668,4.0629096,0,0,0,0,100,0),
(@PATH,11,-5013.5767,-10785.397,1.5928441,0,0,0,0,100,0),
(@PATH,12,-4976.0903,-10788.668,4.0629096,0,0,0,0,100,0),
(@PATH,13,-4964.0767,-10794.24,3.4587717,0,0,0,0,100,0),
(@PATH,14,-4928.047,-10817.672,1.3138804,0,0,0,0,100,0),
(@PATH,15,-4907.662,-10832.577,0.7674937,0,0,0,0,100,0),
(@PATH,16,-4900.5234,-10848.016,1.3312773,0,0,0,0,100,0),
(@PATH,17,-4874.6016,-10870.769,0.6556537,0,0,0,0,100,0),
(@PATH,18,-4843.6704,-10883.837,2.5746179,0,0,0,0,100,0),
(@PATH,19,-4818.099,-10886.483,2.694218,0,0,0,0,100,0),
(@PATH,20,-4797.7446,-10903.832,2.4763858,0,0,0,0,100,0);
-- Pathing for Siltfin Hunter Entry: 17192
SET @NPC := 60918;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-5423.1123,`position_y`=-10979.838,`position_z`=-4.759457 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-5423.1123,-10979.838,-4.759457,0,0,0,0,100,0),
(@PATH,2,-5398.987,-10970.419,-3.200484,0,0,0,0,100,0),
(@PATH,3,-5361.497,-10969.956,-2.6465244,0,0,0,0,100,0),
(@PATH,4,-5335.377,-10957.705,-1.714967,0,0,0,0,100,0),
(@PATH,5,-5329.1,-10917.822,0.24141645,0,0,0,0,100,0),
(@PATH,6,-5311.691,-10881.454,3.248715,0,0,0,0,100,0),
(@PATH,7,-5285.8315,-10871.046,1.495104,0,0,0,0,100,0),
(@PATH,8,-5269.934,-10846.989,0.25116873,0,0,0,0,100,0),
(@PATH,9,-5231.2373,-10825.448,1.3859887,0,0,0,0,100,0),
(@PATH,10,-5203.1104,-10797.082,0.89881325,0,0,0,0,100,0),
(@PATH,11,-5160.0103,-10791.009,0.84628844,0,0,0,0,100,0),
(@PATH,12,-5131.122,-10785.068,2.580556,0,0,0,0,100,0),
(@PATH,13,-5084.096,-10787.871,1.2942367,0,0,0,0,100,0),
(@PATH,14,-5037.4937,-10781.24,0.13910496,0,0,0,0,100,0),
(@PATH,15,-5084.096,-10787.871,1.2942367,0,0,0,0,100,0),
(@PATH,16,-5131.122,-10785.068,2.580556,0,0,0,0,100,0),
(@PATH,17,-5160.0103,-10791.009,0.84628844,0,0,0,0,100,0),
(@PATH,18,-5203.1104,-10797.082,0.89881325,0,0,0,0,100,0),
(@PATH,19,-5231.2373,-10825.448,1.3859887,0,0,0,0,100,0),
(@PATH,20,-5269.934,-10846.989,0.25116873,0,0,0,0,100,0),
(@PATH,21,-5285.8315,-10871.046,1.495104,0,0,0,0,100,0),
(@PATH,22,-5311.691,-10881.454,3.248715,0,0,0,0,100,0),
(@PATH,23,-5329.1,-10917.822,0.24141645,0,0,0,0,100,0),
(@PATH,24,-5335.377,-10957.705,-1.714967,0,0,0,0,100,0),
(@PATH,25,-5361.497,-10969.956,-2.6465244,0,0,0,0,100,0),
(@PATH,26,-5398.987,-10970.419,-3.200484,0,0,0,0,100,0);
-- Gameobject spawn missing Water Barel
DELETE FROM `gameobject` WHERE `guid`=268970;
INSERT INTO `gameobject` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`, `VerifiedBuild`) VALUES
(268970, 3658, 530, 3524, 3575, 1, 1, -3518.95654296875, -11840.4130859375, -0.04765300080180168, 1.570795774459838867, 0, 0, 0.707106590270996093, 0.707106947898864746, 300, 255, 1, 0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_06_01' WHERE sql_rev = '1641422836099367029';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
