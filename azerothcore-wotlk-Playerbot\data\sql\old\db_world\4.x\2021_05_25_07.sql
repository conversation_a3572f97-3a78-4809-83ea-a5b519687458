-- DB update 2021_05_25_06 -> 2021_05_25_07
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_25_06';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_25_06 2021_05_25_07 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1621301896942678800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1621301896942678800');

UPDATE `item_template_locale` SET `name` = 'Sangrita brillante perfecta' WHERE (`ID` = 41433) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Sangrita sutil perfecta' WHERE (`ID` = 41439) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Sangrita fracturada perfecta' WHERE (`ID` = 41436) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Sangrita rúnica' WHERE (`ID` = 39911) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme luminoso perfecto' WHERE (`ID` = 41494) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme grabado perfecto' WHERE (`ID` = 41488) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme destellante perfecto' WHERE (`ID` = 41491) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme iluminado perfecto' WHERE (`ID` = 41493) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: citrino enorme iluminado' WHERE (`ID` = 41565) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme velado perfecto' WHERE (`ID` = 41502) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme durable perfecto' WHERE (`ID` = 41486) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: citrino enorme durable' WHERE (`ID` = 41563) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme maligno perfecto' WHERE (`ID` = 41429) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme prístino' WHERE (`ID` = 39961) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme potenciado perfecto' WHERE (`ID` = 41487) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: citrino enorme potenciado' WHERE (`ID` = 41564) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme completo perfecto' WHERE (`ID` = 41501) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme de luz trémula' WHERE (`ID` = 39965) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme de luz trémula perfecto' WHERE (`ID` = 41490) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme de precisión' WHERE (`ID` = 39966) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Citrino enorme de precisión perfecto' WHERE (`ID` = 41482) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de sol luminoso perfecto' WHERE (`ID` = 41444) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de sol rígido' WHERE (`ID` = 39915) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de sol rígido perfecto' WHERE (`ID` = 41447) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro intemporal perfecto' WHERE (`ID` = 41479) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro vívido' WHERE (`ID` = 39975) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro vívido perfecto' WHERE (`ID` = 41481) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: jade oscuro vívido' WHERE (`ID` = 41567) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro duradero perfecto' WHERE (`ID` = 41464) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro de vidente' WHERE (`ID` = 39979) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro de vidente perfecto' WHERE (`ID` = 41473) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: jade oscuro de vidente' WHERE (`ID` = 41568) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro brillante perfecto' WHERE (`ID` = 41475) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro intrincado perfecto' WHERE (`ID` = 41467) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro deslumbrante perfecto' WHERE (`ID` = 41463) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro luminiscente perfecto' WHERE (`ID` = 41469) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: jade oscuro opaco' WHERE (`ID` = 41573) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro opaco perfecto' WHERE (`ID` = 41471) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: jade oscuro tenso' WHERE (`ID` = 41570) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Jade oscuro tenso perfecto' WHERE (`ID` = 41478) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Calcedonia brillante perfecta' WHERE (`ID` = 41442) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Calcedonia lustrosa perfecta' WHERE (`ID` = 41440) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras enjudioso perfecto' WHERE (`ID` = 41456) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras resplandeciente perfecto' WHERE (`ID` = 41452) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras equilibrado perfecto' WHERE (`ID` = 41450) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras regio perfecto' WHERE (`ID` = 41458) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras de defensor' WHERE (`ID` = 39939) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras de defensor perfecto' WHERE (`ID` = 41451) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: cristal de sombras de defensor' WHERE (`ID` = 41574) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras de tenuidad perfecto' WHERE (`ID` = 41462) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras real perfecto' WHERE (`ID` = 41459) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Cristal de Sombras imbuido perfecto' WHERE (`ID` = 41454) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí escarlata brillante' WHERE (`ID` = 39999) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí escarlata fracturado' WHERE (`ID` = 40002) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: rubí escarlata fracturado' WHERE (`ID` = 41817) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí escarlata rúnico' WHERE (`ID` = 39998) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: rubí escarlata rúnico' WHERE (`ID` = 41718) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí escarlata sutil' WHERE (`ID` = 40000) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: rubí escarlata sutil' WHERE (`ID` = 41719) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: topacio monarca grabado' WHERE (`ID` = 41777) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Topacio monarca prístino' WHERE (`ID` = 40053) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Topacio monarca de luz trémula' WHERE (`ID` = 40057) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Topacio monarca de precisión' WHERE (`ID` = 40058) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: brillo del otoño grueso' WHERE (`ID` = 41791) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Brillo del otoño grueso' WHERE (`ID` = 40015) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: esmeralda del bosque intemporal' WHERE (`ID` = 41795) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Esmeralda del bosque vívida' WHERE (`ID` = 40088) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: esmeralda del bosque vívida' WHERE (`ID` = 41698) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: esmeralda del bosque hendida' WHERE (`ID` = 41724) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: esmeralda del bosque brillante' WHERE (`ID` = 41782) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: esmeralda del bosque tensa' WHERE (`ID` = 41736) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: esmeralda del bosque opaca' WHERE (`ID` = 41739) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ópalo crepuscular de tenuidad' WHERE (`ID` = 40024) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ópalo crepuscular resplandeciente' WHERE (`ID` = 40025) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ópalo crepuscular de tenuidad' WHERE (`ID` = 41785) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ópalo crepuscular real' WHERE (`ID` = 40027) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ópalo crepuscular equilibrado' WHERE (`ID` = 40029) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ópalo crepuscular imbuido' WHERE (`ID` = 40030) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ópalo crepuscular imbuido' WHERE (`ID` = 41796) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ópalo crepuscular regio' WHERE (`ID` = 40031) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ópalo crepuscular enjundioso' WHERE (`ID` = 40033) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí cárdeno brillante' WHERE (`ID` = 40114) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: rubí cárdeno brillante' WHERE (`ID` = 46919) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí cárdeno fracturado' WHERE (`ID` = 40117) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí cárdeno rúnico' WHERE (`ID` = 40113) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Rubí cárdeno sutil' WHERE (`ID` = 40115) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ametrino completo' WHERE (`ID` = 40159) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ametrino completo' WHERE (`ID` = 47023) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ametrino de luz trémula' WHERE (`ID` = 40161) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ametrino de precisión' WHERE (`ID` = 40162) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ametrino grabado' WHERE (`ID` = 40143) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ametrino grabado' WHERE (`ID` = 46953) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ametrino prístino' WHERE (`ID` = 40157) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ametrino prístino' WHERE (`ID` = 46956) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ámbar del rey grueso' WHERE (`ID` = 40126) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ámbar del rey grueso' WHERE (`ID` = 46931) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ámbar del rey rígido' WHERE (`ID` = 40125) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ámbar del rey rígido' WHERE (`ID` = 46928) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ojo de Zul de vidente' WHERE (`ID` = 40170) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ojo de Zul de vidente' WHERE (`ID` = 46903) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ojo de Zul duradero' WHERE (`ID` = 46897) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: ojo de Zul intrincado' WHERE (`ID` = 46910) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Ojo de Zul vívido' WHERE (`ID` = 40166) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Circón majestuoso luciente' WHERE (`ID` = 40121) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: circón majestuoso luciente' WHERE (`ID` = 46927) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Piedra de terror de defensor' WHERE (`ID` = 40139) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: piedra de terror de defensor' WHERE (`ID` = 46941) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Boceto: piedra de terror resplandeciente' WHERE (`ID` = 46936) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Lágrima de pesadilla' WHERE (`ID` = 49110) AND `locale` IN('esMX','esES');

UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una esmeralda del bosque intemporal.' WHERE (`ID` = 41795) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un citrino enorme iluminado.' WHERE (`ID` = 41565) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un citrino enorme durable.' WHERE (`ID` = 41563) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un citrino enorme potenciado.' WHERE (`ID` = 41564) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un jade oscuro vívido.' WHERE (`ID` = 41567) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un jade oscuro de vidente.' WHERE (`ID` = 41568) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un jade oscuro opaco.' WHERE (`ID` = 41573) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un jade oscuro tenso.' WHERE (`ID` = 41570) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un cristal de Sombras de defensor.' WHERE (`ID` = 41574) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un rubí escarlata fracturado.' WHERE (`ID` = 41817) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un rubí escarlata rúnico.' WHERE (`ID` = 41718) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un rubí escarlata sutil.' WHERE (`ID` = 41719) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un topacio monarca grabado.' WHERE (`ID` = 41777) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un topacio monarca de precisión.' WHERE (`ID` = 41818) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un brillo del otoño grueso.' WHERE (`ID` = 41791) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una esmeralda del bosque vívida.' WHERE (`ID` = 41698) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una esmeralda del bosque hendida.' WHERE (`ID` = 41724) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una esmeralda del bosque brillante.' WHERE (`ID` = 41782) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una esmeralda del bosque tensa.' WHERE (`ID` = 41736) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una esmeralda del bosque opaca.' WHERE (`ID` = 41739) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ópalo crepuscular de tenuidad.' WHERE (`ID` = 41785) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ópalo crepuscular imbuido.' WHERE (`ID` = 41796) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un rubí cárdeno brillante.' WHERE (`ID` = 46919) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ametrino completo.' WHERE (`ID` = 47023) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ametrino grabado.' WHERE (`ID` = 46953) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ametrino prístino.' WHERE (`ID` = 46956) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ámbar del rey grueso.' WHERE (`ID` = 46931) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ámbar del rey luminoso.' WHERE (`ID` = 46930) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ámbar del rey rígido.' WHERE (`ID` = 46928) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ojo de Zul de vidente.' WHERE (`ID` = 46903) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ojo de Zul duradero.' WHERE (`ID` = 46897) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un ojo de Zul intrincado.' WHERE (`ID` = 46910) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar un circón majestuoso luciente.' WHERE (`ID` = 46927) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una piedra de terror de defensor.' WHERE (`ID` = 46941) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una piedra de terror resplandeciente.' WHERE (`ID` = 46936) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Te enseña a tallar una lágrima de pesadilla.' WHERE (`ID` = 49112) AND `locale` IN('esMX','esES');

UPDATE `item_template_locale` SET `name` = 'Glifo de Arañazo' WHERE (`ID` = 40903) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Aspecto de la víbora' WHERE (`ID` = 42901) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Añublo profano' WHERE (`ID` = 45803) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Bendición de sabiduría' WHERE (`ID` = 43366) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Cólera vengativa' WHERE (`ID` = 41107) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Deflagración Arcana' WHERE (`ID` = 42734) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Deflagración de cadáver' WHERE (`ID` = 43671) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de descomposición presurosa' WHERE (`ID` = 50077) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Eliminar maldición' WHERE (`ID` = 42753) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Espíritu redentor' WHERE (`ID` = 42417) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Explosión de Fuego' WHERE (`ID` = 42740) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de fuerza poseída' WHERE (`ID` = 43354) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Gema de maná' WHERE (`ID` = 42750) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de halcón' WHERE (`ID` = 42909) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de insultos barbáricos' WHERE (`ID` = 43420) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Maldición de agonía' WHERE (`ID` = 42456) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Ola de sanación inferior' WHERE (`ID` = 41535) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Protección divina' WHERE (`ID` = 41096) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Regeneración iracunda' WHERE (`ID` = 45794) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Rejuvenecimiento rápido' WHERE (`ID` = 50125) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Supresión de dolor' WHERE (`ID` = 45760) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Tótem de cólera' WHERE (`ID` = 45776) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Tótem Marea de maná' WHERE (`ID` = 41538) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo de Última carga' WHERE (`ID` = 43426) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `name` = 'Glifo del necrófago' WHERE (`ID` = 43549) AND `locale` IN('esMX','esES');

UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 40708) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 40709) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 40710) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 50458) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 50463) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 35104) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 50464) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 35105) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 35106) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 27947) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 25645) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 51501) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 39728) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 47665) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 47666) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33843) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 47667) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 51507) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33078) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 31031) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 51513) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 45114) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 30023) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 22345) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 32330) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 40267) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 27984) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 28248) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 24413) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42593) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42594) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42595) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42596) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42597) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42598) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42599) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42601) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42602) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 28523) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42603) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42604) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42606) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42607) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42608) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 42609) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 45169) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 22395) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 22396) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 40322) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 46978) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33939) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33940) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33941) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 27544) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 23199) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33951) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 23200) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33952) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33953) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 28066) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 27815) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 28357) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 37575) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 45255) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 29389) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 38361) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 23005) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 38367) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 38368) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33505) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33506) AND `locale` IN('esMX','esES');
UPDATE `item_template_locale` SET `Description` = 'Cuenta como un tótem de aire, tierra, fuego y agua.' WHERE (`ID` = 33507) AND `locale` IN('esMX','esES');

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
