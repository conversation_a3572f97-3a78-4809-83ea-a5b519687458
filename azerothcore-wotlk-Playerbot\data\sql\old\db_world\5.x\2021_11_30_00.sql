-- DB update 2021_11_29_06 -> 2021_11_30_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_29_06';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_29_06 2021_11_30_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1638183413923029400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1638183413923029400');

-- Duskwood Zone 10
-- SELECT * FROM gameobject WHERE position_x >= -11263.675781 AND position_x <= -9950.916992 AND position_y <= 590.750916 AND position_y >= -1718.122681 AND id = 1732; (vmangos)
-- SELECT * FROM gameobject WHERE zone_id = 10 AND id = 1732; (Sniffs)
-- SELECT DISTINCT gameobject.id ,gameobject.position_x, gameobject.position_y, gameobject.position_z, gameobject.orientation, gameobject.rotation0, gameobject.rotation1, gameobject.rotation2, gameobject.rotation3 FROM gameobject WHERE zone_id = 10 AND id = 1735;

-- @TODO: Delete pool_template, pool_gameobject and gameobject for Duskwood.
-- Which Gameobjects to delete in Duskwood found with query: 
--      SELECT gameobject.guid, pool_pool.description, pool_gameobject.description, pool_gameobject.guid
--      FROM `gameobject`
--      INNER JOIN `pool_gameobject` ON pool_gameobject.guid = gameobject.guid
--      INNER JOIN `pool_template` ON pool_gameobject.pool_entry = pool_template.entry
--      INNER JOIN `pool_pool` ON pool_template.entry = pool_pool.pool_id
--      WHERE pool_pool.description LIKE 'Spawn Point%Duskwood%' OR pool_pool.description LIKE '%Master Mineral Pool - Duskwood%';

SET @FREE_POOL_TEMPLATE_ENTRY = 500;

DELETE FROM `pool_template` WHERE `entry` >= @FREE_POOL_TEMPLATE_ENTRY AND `entry` <= @FREE_POOL_TEMPLATE_ENTRY + 3;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(@FREE_POOL_TEMPLATE_ENTRY + 0, 10, 'Tin Veins in Duskwood 28 objects total'),
(@FREE_POOL_TEMPLATE_ENTRY + 1, 2, 'Silver Veins in Duskwood 4 objects total'),
(@FREE_POOL_TEMPLATE_ENTRY + 2, 5, 'Gold Veins in Duskwood 7 objects total'),
(@FREE_POOL_TEMPLATE_ENTRY + 3, 4, 'Iron Deposits in Duskwood 17 objects total');

SET @FREE_GAMEOBJECT_ENTRY = 10230;

DELETE FROM `pool_gameobject` WHERE `pool_entry` >= @FREE_POOL_TEMPLATE_ENTRY AND `pool_entry` <= @FREE_POOL_TEMPLATE_ENTRY + 3;
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES 
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 0, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY, 0, 'Tin Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 1, 0, 'Silver Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 1, 0, 'Silver Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 1, 0, 'Silver Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 1, 0, 'Silver Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 2, 0, 'Gold Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 2, 0, 'Gold Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 2, 0, 'Gold Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 2, 0, 'Gold Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 2, 0, 'Gold Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 2, 0, 'Gold Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 2, 0, 'Gold Vein'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit'),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, @FREE_POOL_TEMPLATE_ENTRY + 3, 0, 'Iron Deposit');

-- Free for at least 330 objects
SET @FREE_GAMEOBJECT_ENTRY = 10230;

-- Clear gameobjects from azerothcore master
-- These can't go between ticks or it doesn't run
DELETE `gameobject`
FROM `gameobject`
INNER JOIN `pool_gameobject` ON pool_gameobject.guid = gameobject.guid
INNER JOIN `pool_template` ON pool_gameobject.pool_entry = pool_template.entry
INNER JOIN `pool_pool` ON pool_template.entry = pool_pool.pool_id
WHERE pool_pool.description LIKE 'Spawn Point%Duskwood%' OR pool_pool.description LIKE '%Master Mineral Pool - Duskwood%';

DELETE `pool_gameobject`
FROM `pool_gameobject`
INNER JOIN `pool_template` ON pool_gameobject.pool_entry = pool_template.entry
INNER JOIN `pool_pool` ON pool_template.entry = pool_pool.pool_id
WHERE pool_pool.description LIKE 'Spawn Point%Duskwood%' OR pool_pool.description LIKE '%Master Mineral Pool - Duskwood%';

DELETE `pool_template`
FROM `pool_template`
INNER JOIN `pool_pool` ON pool_template.entry = pool_pool.pool_id
WHERE pool_pool.description LIKE 'Spawn Point%Duskwood%' OR pool_pool.description LIKE '%Master Mineral Pool - Duskwood%';

DELETE `pool_pool`
FROM `pool_pool`
WHERE pool_pool.description LIKE 'Spawn Point%Duskwood%' OR pool_pool.description LIKE '%Master Mineral Pool - Duskwood%';

-- 57 Gameobjects to be inserted
DELETE FROM `gameobject` WHERE `guid` >= @FREE_GAMEOBJECT_ENTRY AND `guid` <= @FREE_GAMEOBJECT_ENTRY + 57;
INSERT INTO `gameobject` (`guid`, `id`, `map`, `zoneId`, `areaId`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`) VALUES 
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 0, 1732, 0, 10, 0, -1008.33, 55.5346, 0.0349062, 0, 0, 0.0174522, 0.999848, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -1506.51, 95.3776, 4.15388, 0, 0, -0.874619, 0.48481, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, 188.423, 36.8217, 4.32842, 0, 0, -0.829037, 0.559194, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -513.184, 41.4328, 0.506145, 0, 0, 0.25038, 0.968148, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -783.827, 49.1737, 3.38594, 0, 0, -0.992546, 0.12187, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -729.183, 46.4459, 3.4383, 0, 0, -0.989016, 0.147811, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -533.286, 39.3664, 3.82227, 0, 0, -0.942641, 0.333808, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -696.347, 54.7799, 1.58825, 0, 0, 0.71325, 0.70091, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, 12.5553, 44.3524, 4.46804, 0, 0, -0.788011, 0.615662, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -135.736, 39.7418, 1.46608, 0, 0, 0.66913, 0.743145, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -166.128, 11.467, 2.96704, 0, 0, 0.996194, 0.087165, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -124.765, 9.87703, 5.93412, 0, 0, -0.173648, 0.984808, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -542.823, 34.96, 5.32326, 0, 0, -0.461748, 0.887011, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -833.442, 77.4169, 4.45059, 0, 0, -0.793353, 0.608762, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -1153.09, 46.036, 0.174532, 0, 0, 0.0871553, 0.996195, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -1458.44, 91.035, 3.50812, 0, 0, -0.983254, 0.182238, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -120.787, 62.3164, 4.45059, 0, 0, -0.793353, 0.608762, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -999.958, 47.3942, 0.331611, 0, 0, 0.165047, 0.986286, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -1089.97, 63.3644, 3.99681, 0, 0, -0.909961, 0.414694, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -587.625, 64.6095, 2.89724, 0, 0, 0.992546, 0.12187, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -985.37, 68.1491, 5.96903, 0, 0, -0.156434, 0.987688, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -424.663, 59.9856, 5.89921, 0, 0, -0.190808, 0.981627, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -551.013, 70.454, 4.88692, 0, 0, -0.642787, 0.766045, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -1020.57, 53.7499, 1.50098, 0, 0, 0.681998, 0.731354, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -1399.99, 71.7657, 4.99164, 0, 0, -0.601814, 0.798636, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -1157.22, 55.1905, 1.09956, 0, 0, 0.522498, 0.852641, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -756.179, 62.4482, 4.17134, 0, 0, -0.870356, 0.492424, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1732, 0, 10, 0, -288.859, 47.7796, 5.93412, 0, 0, -0.173648, 0.984808, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1733, 0, 10, 0, -424.663, 59.9856, 5.89921, 0, 0, -0.190808, 0.981627, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1733, 0, 10, 0, -1375.07, 63.6493, 3.68265, 0, 0, -0.96363, 0.267241, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1733, 0, 10, 0, -1008.33, 55.5346, 0.0349062, 0, 0, 0.0174522, 0.999848, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1733, 0, 10, 0, -513.184, 41.4328, 0.506145, 0, 0, 0.25038, 0.968148, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1734, 0, 10, 1098, -10415.6, -1253.79, 52.5802, 3.49067, 0, 0, -0.984807, 0.173652, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1734, 0, 10, 42, -10362.5, -1404.4, 81.8085, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1734, 0, 10, 93, -11183.7, -135.158, 8.11372, 2.09439, 0, 0, 0.866025, 0.500001, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1734, 0, 10, 242, -10559.5, -724.451, 76.4627, 5.49779, 0, 0, -0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1734, 0, 10, 241, -11010, -989.044, 69.0113, 4.93928, 0, 0, -0.622514, 0.782609, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1734, 0, 10, 242, -10662.4, -894.27, 58.8167, 0.994837, 0, 0, 0.477158, 0.878817, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1734, 0, 10, 1098, -10301.6, -1467.02, 90.2184, 5.91667, 0, 0, -0.182235, 0.983255, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, 113.076, 34.8874, 1.81514, 0, 0, 0.788011, 0.615662, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -894.27, 58.8167, 0.994837, 0, 0, 0.477158, 0.878817, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -989.044, 69.0113, 4.93928, 0, 0, -0.622514, 0.782609, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -1404.4, 81.8085, 1.22173, 0, 0, 0.573576, 0.819152, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -1358.39, 83.4799, 2.30383, 0, 0, 0.913545, 0.406738, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -1291.58, 46.9721, 5.06146, 0, 0, -0.573576, 0.819152, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -1155.72, 42.4535, 0.331611, 0, 0, 0.165047, 0.986286, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -1467.02, 90.2184, 5.91667, 0, 0, -0.182235, 0.983255, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -908.818, 68.4815, 2.65289, 0, 0, 0.970295, 0.241925, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -53.2851, 18.2922, 4.85202, 0, 0, -0.656058, 0.75471, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -937.882, 66.9419, 1.6057, 0, 0, 0.719339, 0.694659, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -748.918, 72.097, 5.18363, 0, 0, -0.522498, 0.852641, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -1253.79, 52.5802, 3.49067, 0, 0, -0.984807, 0.173652, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, 134.72, 35.5812, 2.54818, 0, 0, 0.956305, 0.292372, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -1312.74, 63.4724, 5.86431, 0, 0, -0.207912, 0.978148, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -866.69, 73.6912, 2.51327, 0, 0, 0.951056, 0.309017, 0, 900, 100, 1),
(@FREE_GAMEOBJECT_ENTRY := @FREE_GAMEOBJECT_ENTRY + 1, 1735, 0, 10, 0, -138.255, 14.2671, 5.51524, 0, 0, -0.374606, 0.927184, 0, 900, 100, 1);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_30_00' WHERE sql_rev = '1638183413923029400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
