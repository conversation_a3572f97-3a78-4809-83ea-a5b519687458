-- DB update 2019_07_10_02 -> 2019_07_11_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_07_10_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_07_10_02 2019_07_11_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1561966083359604300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1561966083359604300');

DELETE FROM `creature_questender` WHERE `id`=15350 AND `quest` IN (8367,13476);
INSERT INTO `creature_questender` (`id`, `quest`) VALUES 
(15350, 8367),
(15350, 13476);

DELETE FROM `creature_queststarter` WHERE `id`=15351 AND `quest` IN (8371,13478);
INSERT INTO `creature_queststarter` (`id`, `quest`) VALUES 
(15351, 8371),
(15351, 13478);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
