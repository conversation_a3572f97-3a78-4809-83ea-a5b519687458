-- DB update 2021_06_22_01 -> 2021_06_23_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_22_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_22_01 2021_06_23_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1623851652869107640'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1623851652869107640');

-- Reduce Plans: Copper Chain Vest drop rate for Siltfin Murloc and Mistbat
UPDATE `creature_loot_template` SET `Chance` = 0.3 WHERE `entry` IN (17190, 16353) AND `item` = 3609;



--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_23_00' WHERE sql_rev = '1623851652869107640';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
