-- DB update 2021_12_17_03 -> 2021_12_17_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_17_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_17_03 2021_12_17_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639267185578233100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639267185578233100');

/* Disable LOS on Unworthy Initiate Chains
*/

DELETE FROM `disables` WHERE `sourceType`=0 AND `entry` IN (54612);
INSERT INTO `disables` (`sourceType`, `entry`, `flags`, `params_0`, `params_1`, `comment`) VALUES
(0, 54612, 64, '', '', 'Chained Peasant (Chest) LOS');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_17_04' WHERE sql_rev = '1639267185578233100';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
