-- DB update 2021_09_14_01 -> 2021_09_14_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_14_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_14_01 2021_09_14_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1620443572177894000'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1620443572177894000');

DELETE FROM `spell_custom_attr` WHERE `spell_id` IN (60988,55550,42772,59685);
INSERT INTO `spell_custom_attr` (`spell_id`, `attributes`) VALUES
(60988, 0x00080000),
(55550, 0x00080000),
(42772, 0x00080000),
(59685, 0x00080000);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_14_02' WHERE sql_rev = '1620443572177894000';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
