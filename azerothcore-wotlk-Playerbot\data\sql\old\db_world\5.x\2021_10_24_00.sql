-- DB update 2021_10_23_01 -> 2021_10_24_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_23_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_23_01 2021_10_24_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1634412327773100900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1634412327773100900');

UPDATE `quest_template_addon` SET `ExclusiveGroup`=12135 WHERE `id`=12135;
UPDATE `quest_template_addon` SET `ExclusiveGroup`=12135 WHERE `id`=11131;
UPDATE `quest_template_addon` SET `ExclusiveGroup`=12139 WHERE `id`=12139;
UPDATE `quest_template_addon` SET `ExclusiveGroup`=12139 WHERE `id`=11219;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_24_00' WHERE sql_rev = '1634412327773100900';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
