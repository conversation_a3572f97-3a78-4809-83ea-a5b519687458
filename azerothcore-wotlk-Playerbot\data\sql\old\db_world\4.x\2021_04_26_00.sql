-- DB update 2021_04_25_15 -> 2021_04_26_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_25_15';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_25_15 2021_04_26_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1618745790841763500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('16187457908417635012312301');
DELETE FROM `gameobject` WHERE `guid` IN (2135397, 2135398, 2135389, 2135391, 2135392, 2135393, 2135395, 2135396, 2135378, 2135379, 2135385, 2135387, 2135386, 2135384, 2135388);
INSERT INTO `gameobject` VALUES
(2135397, 3240, 1, 0, 0, 1, 1, -694.417, -5674.32, 6.26285, 1.17032, -0, -0, -0.552334, -0.833623, 900, 100, 1, '', 0),
(2135398, 3240, 1, 0, 0, 1, 1, -1293.67, -5135, 2.13708, 1.17032, -0, -0, -0.552334, -0.833623, 900, 100, 1, '', 0),
(2135389, 3240, 1, 0, 0, 1, 1, -1032.82, -5515.7, 8.19592, 2.56263, -0, -0, -0.958391, -0.285457, 900, 100, 1, '', 0),
(2135391, 3240, 1, 0, 0, 1, 1, -657.967, -5551.35, 3.92631, 0.913286, -0, -0, -0.440938, -0.897538, 900, 100, 1, '', 0),
(2135392, 3240, 1, 0, 0, 1, 1, -1025.77, -5558, 5.53497, 1.86755, -0, -0, -0.80387, -0.594805, 900, 100, 1, '', 0),
(2135393, 3240, 1, 0, 0, 1, 1, -1254.89, -5579.15, 7.22951, 1.7521, -0, -0, -0.768216, -0.640191, 900, 100, 1, '', 0),
(2135395, 3240, 1, 0, 0, 1, 1, -825.151, -5593.89, 4.17995, 5.45467, -0, -0, -0.40251, 0.915416, 900, 100, 1, '', 0),
(2135396, 3240, 1, 0, 0, 1, 1, -701.054, -5612.3, 28.268, 1.19389, -0, -0, -0.562117, -0.827058, 900, 100, 1, '', 0),
(2135378, 3240, 1, 0, 0, 1, 1, -1142.09, -5103.27, 4.41125, 4.54915, -0, -0, -0.762402, 0.647104, 900, 100, 1, '', 0),
(2135379, 3240, 1, 0, 0, 1, 1, -969.367, -5182.59, 1.80058, 1.82394, -0, -0, -0.790712, -0.612188, 900, 100, 1, '', 0),
(2135385, 3240, 1, 0, 0, 1, 1, -813.653, -5316.14, 3.31857, 4.71632, -0, -0, -0.705714, 0.708497, 900, 100, 1, '', 0),
(2135387, 3240, 1, 0, 0, 1, 1, -1251.37, -5341.21, 5.36343, 4.04087, -0, -0, -0.900604, 0.43464, 900, 100, 1, '', 0),
(2135386, 3240, 1, 0, 0, 1, 1, -1140.34, -5337.26, 5.67458, 4.04087, -0, -0, -0.900604, 0.43464, 900, 100, 1, '', 0),
(2135384, 3240, 1, 0, 0, 1, 1, -1518.92, -5304.11, 7.73214, 3.61284, -0, -0, -0.972369, 0.233449, 900, 100, 1, '', 0),
(2135388, 3240, 1, 0, 0, 1, 1, -1327.5, -5496.98, 5.54005, 2.56263, -0, -0, -0.958391, -0.285457, 900, 0, 1, '', 0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
