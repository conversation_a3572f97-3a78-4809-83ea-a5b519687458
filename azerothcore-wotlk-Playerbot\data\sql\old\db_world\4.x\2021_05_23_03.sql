-- DB update 2021_05_23_02 -> 2021_05_23_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_23_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_23_02 2021_05_23_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1621246317595402000'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1621246317595402000');

UPDATE `gossip_menu_option` SET `OptionBroadcastTextID` = 23112 WHERE `MenuID` = 9546 AND `OptionID` = 1; 
UPDATE `gossip_menu_option` SET `OptionBroadcastTextID` = 26697 WHERE `MenuID` = 9546 AND `OptionID` = 2; 

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
