-- DB update 2021_06_11_03 -> 2021_06_11_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_11_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_11_03 2021_06_11_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1622733528401617288'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1622733528401617288');

UPDATE `creature` SET `wander_distance` = 5, `MovementType` = 1
WHERE `guid` IN (9720, 9761, 9981, 10226, 31701, 31706, 31708, 31710);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_11_04' WHERE sql_rev = '1622733528401617288';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
