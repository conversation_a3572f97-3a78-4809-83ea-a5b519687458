-- DB update 2021_07_07_22 -> 2021_07_07_23
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_07_22';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_07_22 2021_07_07_23 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1625085335146756200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1625085335146756200');

UPDATE `creature_template` SET `InhabitType` = 1 WHERE `entry` IN (37955,38434,38435,38436,37501,38197,37502,38198,37232,38362);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_07_23' WHERE sql_rev = '1625085335146756200';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
