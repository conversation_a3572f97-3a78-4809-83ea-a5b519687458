-- DB update 2021_11_03_02 -> 2021_11_03_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_03_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_03_02 2021_11_03_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635649346761519900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635649346761519900');

DELETE FROM `areatrigger_scripts` WHERE `entry` = 3957;
INSERT INTO `areatrigger_scripts` (`entry`, `ScriptName`) VALUES
(3957, 'at_zulgurub_entrance_speech');

UPDATE `creature_text` SET `TextRange` = 3 WHERE `CreatureID` = 14834 AND `GroupID` = 3 AND `ID` = 0;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_03_03' WHERE sql_rev = '1635649346761519900';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
