-- DB update 2021_08_10_01 -> 2021_08_10_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_10_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_10_01 2021_08_10_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1628302161194713320'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1628302161194713320');

-- Disables <PERSON>fen<PERSON> wandering
UPDATE `creature` SET `MovementType` = 0, `wander_distance` = 0 WHERE `id` = 472 AND `guid` IN (81122, 134000, 134001, 134002);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_10_02' WHERE sql_rev = '1628302161194713320';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
