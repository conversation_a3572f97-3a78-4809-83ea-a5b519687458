-- DB update 2022_01_05_01 -> 2022_01_05_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_05_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_05_01 2022_01_05_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641388762527715027'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641388762527715027');

UPDATE `creature_template` SET `ScriptName`='npc_cameron' WHERE `entry`=805;

DELETE FROM `game_event` WHERE `eventEntry`=74;
INSERT INTO `game_event` (`eventEntry`, `start_time`, `end_time`, `occurence`, `length`, `holiday`, `holidayStage`, `description`, `world_event`, `announce`) VALUES
(74, '2022-01-01 07:00:00', '2030-01-01 08:00:00', 1440, 60, 0, 0, 'Childeren of Goldshire', 0, 2);

UPDATE `creature_template` SET `ScriptName`='npc_cameron' WHERE `entry`=805;

DELETE FROM `creature_formations` WHERE `leaderGUID`=79638;
INSERT INTO `creature_formations` (`leaderGUID`, `memberGUID`, `dist`, `angle`, `groupAI`) VALUES
(79638, 79638, 0, 0,   515), -- Cameron
(79638, 79639, 3, 230, 515), -- John
(79638, 79640, 3, 180, 515), -- Dana
(79638, 79641, 3, 130, 515), -- Jose
(79638, 79642, 3, 310, 515), -- Lisa
(79638, 79643, 3, 50,  515); -- Aaron

-- STORMWIND_PATH
SET @ENTRY := 80500;
DELETE FROM `waypoint_data` WHERE `id`=@ENTRY;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES
(@ENTRY,1 ,-9372.823,-66.29554,69.201859, 0, 0, 1, 0, 100, 0),
(@ENTRY,2 ,-9375.251,-70.71793,69.201691, 0, 0, 1, 0, 100, 0),
(@ENTRY,3 ,-9377.161,-72.19738,69.198997, 0, 0, 1, 0, 100, 0),
(@ENTRY,4 ,-9382.018,-76.88008,69.164696, 0, 0, 1, 0, 100, 0),
(@ENTRY,5 ,-9385.052,-76.36288,69.202148, 0, 0, 1, 0, 100, 0),
(@ENTRY,6 ,-9392.218,-71.91143,64.437691, 0, 0, 1, 0, 100, 0),
(@ENTRY,7 ,-9390.718,-68.80339,64.437691, 0, 0, 1, 0, 100, 0),
(@ENTRY,8 ,-9380.336,-78.55399,64.437691, 0, 0, 1, 0, 100, 0),
(@ENTRY,9 ,-9383.135,-84.98008,63.336449, 0, 0, 1, 0, 100, 0),
(@ENTRY,10,-9376.598,-89.94442,63.722805, 0, 0, 1, 0, 100, 0),
(@ENTRY,11,-9372.031,-87.89227,64.235367, 0, 0, 1, 0, 100, 0),
(@ENTRY,12,-9357.252,-67.5625,65.31458, 0, 0, 1, 0, 100, 0), -- 07:00:21
(@ENTRY,13,-9357.252,-67.5625,65.31458, 0, 0, 1, 0, 100, 0), -- 07:00:21
(@ENTRY,14,-9356.818,-59.81693,66.47333, 0, 0, 1, 0, 100, 0), -- 07:00:23
(@ENTRY,15,-9359.703,-40.74677,64.90179, 0, 0, 1, 0, 100, 0), -- 07:00:24
(@ENTRY,16,-9366.912,-26.63774,63.58042, 0, 0, 1, 0, 100, 0), -- 07:00:27
(@ENTRY,17,-9376.815,-16.4907,62.47466, 0, 0, 1, 0, 100, 0), -- 07:00:29
(@ENTRY,18,-9390.682,0.2398973,60.84456, 0, 0, 1, 0, 100, 0), -- 07:00:30
(@ENTRY,19,-9402.53,14.60529,59.79248, 0, 0, 1, 0, 100, 0), -- 07:00:34
(@ENTRY,20,-9419.141,34.55973,57.84264, 0, 0, 1, 0, 100, 0), -- 07:00:35
(@ENTRY,21,-9426.896,43.84751,57.06255, 0, 0, 1, 0, 100, 0), -- 07:00:40
(@ENTRY,22,-9433.867,53.78213,56.78226, 0, 0, 1, 0, 100, 0), -- 07:00:41
(@ENTRY,23,-9438.548,61.02147,56.39376, 0, 0, 1, 0, 100, 0), -- 07:00:42
(@ENTRY,24,-9436.482,68.04466,56.42494, 0, 0, 1, 0, 100, 0), -- 07:00:44
(@ENTRY,25,-9415.27,87.07191,57.30513, 0, 0, 1, 0, 100, 0), -- 07:00:45
(@ENTRY,26,-9396.235,98.29691,59.15154, 0, 0, 1, 0, 100, 0), -- 07:00:47
(@ENTRY,27,-9375.271,106.7702,60.70432, 0, 0, 1, 0, 100, 0), -- 07:00:51
(@ENTRY,28,-9364.256,110.7547,61.50436, 0, 0, 1, 0, 100, 0), -- 07:00:53
(@ENTRY,29,-9344.307,120.2394,63.43427, 0, 0, 1, 0, 100, 0), -- 07:00:55
(@ENTRY,30,-9332.738,126.8489,63.9777, 0, 0, 1, 0, 100, 0), -- 07:00:57
(@ENTRY,31,-9308.697,137.7139,65.57498, 0, 0, 1, 0, 100, 0), -- 07:00:59
(@ENTRY,32,-9292.605,144.2936,66.53963, 0, 0, 1, 0, 100, 0), -- 07:01:03
(@ENTRY,33,-9271.355,152.9366,67.37766, 0, 0, 1, 0, 100, 0), -- 07:01:04
(@ENTRY,34,-9258.908,158.3268,67.75345, 0, 0, 1, 0, 100, 0), -- 07:01:07
(@ENTRY,35,-9258.107,158.9872,67.80977, 0, 0, 1, 0, 100, 0), -- 07:01:08
(@ENTRY,36,-9245.959,170.1512,68.25052, 0, 0, 1, 0, 100, 0), -- 07:01:10
(@ENTRY,37,-9219.314,201.358,69.06726, 0, 0, 1, 0, 100, 0), -- 07:01:13
(@ENTRY,38,-9208.215,212.5414,70.81392, 0, 0, 1, 0, 100, 0), -- 07:01:17
(@ENTRY,39,-9198.92,222.5978,71.63802, 0, 0, 1, 0, 100, 0), -- 07:01:18
(@ENTRY,40, -9194.827, 227.36, 71.91199, 0, 0, 1, 0, 100, 0), -- 07:01:20
(@ENTRY,41,-9187.375,241.8835,72.91473, 0, 0, 1, 0, 100, 0), -- 07:01:21
(@ENTRY,42,-9184.045,254.7417,73.77821, 0, 0, 1, 0, 100, 0), -- 07:01:23
(@ENTRY,43,-9182.289,274.1739,75.36765, 0, 0, 1, 0, 100, 0), -- 07:01:25
(@ENTRY,44,-9181.092,289.6617,77.20319, 0, 0, 1, 0, 100, 0), -- 07:01:26
(@ENTRY,45,-9180.633,300.7958,78.49542, 0, 0, 1, 0, 100, 0), -- 07:01:29
(@ENTRY,46,-9178.008,326.7234,81.67275, 0, 0, 1, 0, 100, 0), -- 07:01:31
(@ENTRY,47,-9174.055,336.8582,84.08626, 0, 0, 1, 0, 100, 0), -- 07:01:34
(@ENTRY,48,-9163.633,351.4775,87.70863, 0, 0, 1, 0, 100, 0), -- 07:01:35
(@ENTRY,49,-9153.682,362.516,90.23949, 0, 0, 1, 0, 100, 0), -- 07:01:37
(@ENTRY,50,-9148.218,368.3586,90.66177, 0, 0, 1, 0, 100, 0), -- 07:01:40
(@ENTRY,51,-9125.46,390.1248,91.81776, 0, 0, 1, 0, 100, 0), -- 07:01:43
(@ENTRY,52,-9115.926,397.5776,92.71513, 0, 0, 1, 0, 100, 0), -- 07:01:45
(@ENTRY,53,-9095.435,413.1418,92.21481, 0, 0, 1, 0, 100, 0), -- 07:01:46
(@ENTRY,54,-9088.044,418.7169,92.44129, 0, 0, 1, 0, 100, 0), -- 07:01:49
(@ENTRY,55,-9058.086,439.64,93.14257, 0, 0, 1, 0, 100, 0), -- 07:01:52
(@ENTRY,56,-9045.07,450.092,93.28775, 0, 0, 1, 0, 100, 0), -- 07:01:54
(@ENTRY,57,-9040.674,453.6961,93.05584, 0, 0, 0, 0, 100, 0);

-- GOLDSHIRE_PATH
SET @ENTRY := 80501;
DELETE FROM `waypoint_data` WHERE `id`=@ENTRY;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES
(@ENTRY, 1  ,-9057.086,442.696,93.05582, 0, 0, 1, 0, 100, 0), -- 07:13:02
(@ENTRY, 2  ,-9074.805,431.4119,93.05582, 0, 0, 1, 0, 100, 0), -- 07:13:03
(@ENTRY, 3  ,-9077.455,426.727,92.54478, 0, 0, 1, 0, 100, 0), -- 07:13:05
(@ENTRY, 4  ,-9130.008,384.1526,91.08342, 0, 0, 1, 0, 100, 0), -- 07:13:11
(@ENTRY, 5  ,-9138.167,378.1005,90.83872, 0, 0, 1, 0, 100, 0), -- 07:13:14
(@ENTRY, 6  ,-9170.719,340.1506,85.00079, 0, 0, 1, 0, 100, 0), -- 07:13:17
(@ENTRY, 7  ,-9177.553,324.4485,81.63397, 0, 0, 1, 0, 100, 0), -- 07:13:22
(@ENTRY, 8  ,-9179.79,302.5313,78.90688, 0, 0, 1, 0, 100, 0), -- 07:13:24
(@ENTRY, 9  ,-9180.076,284.7124,76.62807, 0, 0, 1, 0, 100, 0), -- 07:13:26
(@ENTRY, 10 ,-9180.527,273.4619,75.51992, 0, 0, 1, 0, 100, 0), -- 07:13:29
(@ENTRY, 11 ,-9181.712,261.4136,74.55137, 0, 0, 1, 0, 100, 0), -- 07:13:30
(@ENTRY, 12 ,-9187.871,242.3035,72.95731, 0, 0, 1, 0, 100, 0), -- 07:13:32
(@ENTRY, 13 ,-9199.146,222.4604,71.7192, 0, 0, 1, 0, 100, 0), -- 07:13:35
(@ENTRY, 14 ,-9217.201,203.2024,69.32413, 0, 0, 1, 0, 100, 0), -- 07:13:37
(@ENTRY, 15 ,-9230.155,189.5526,68.19696, 0, 0, 1, 0, 100, 0), -- 07:13:41
(@ENTRY, 16 ,-9241.491,176.5851,67.95673, 0, 0, 1, 0, 100, 0), -- 07:13:45
(@ENTRY, 17 ,-9263.844,156.9561,67.57113, 0, 0, 1, 0, 100, 0), -- 07:13:46
(@ENTRY, 18 ,-9267.183,155.5535,67.38669, 0, 0, 1, 0, 100, 0), -- 07:13:48
(@ENTRY, 19 ,-9276.685,152.7653,67.1469, 0, 0, 1, 0, 100, 0), -- 07:13:50
(@ENTRY, 20 ,-9299.891,144.6541,66.28534, 0, 0, 1, 0, 100, 0), -- 07:13:52
(@ENTRY, 21 ,-9319.277,135.1606,65.03548, 0, 0, 1, 0, 100, 0), -- 07:13:53
(@ENTRY, 22 ,-9332.896,126.1193,63.87636, 0, 0, 1, 0, 100, 0), -- 07:13:57
(@ENTRY, 23 ,-9352.381,116.5327,62.6504, 0, 0, 1, 0, 100, 0), -- 07:13:58
(@ENTRY, 24 ,-9372.848,108.2433,60.93748, 0, 0, 1, 0, 100, 0), -- 07:14:02
(@ENTRY, 25 ,-9390.894,102.341,59.62895, 0, 0, 1, 0, 100, 0), -- 07:14:04
(@ENTRY, 26 ,-9395.941,100.5215,59.13963, 0, 0, 1, 0, 100, 0), -- 07:14:07
(@ENTRY, 27 ,-9409.482,92.83659,58.09391, 0, 0, 1, 0, 100, 0), -- 07:14:08
(@ENTRY, 28 ,-9417.838,86.17823,57.23439, 0, 0, 1, 0, 100, 0), -- 07:14:09
(@ENTRY, 29 ,-9433.703,74.28316,56.51966, 0, 0, 1, 0, 100, 0), -- 07:14:10
(@ENTRY, 30 ,-9435.221,73.19769,56.34148, 0, 0, 1, 0, 100, 0), -- 07:14:13
(@ENTRY, 31 ,-9448.541,67.65511,56.51829, 0, 0, 1, 0, 100, 0), -- 07:14:14
(@ENTRY, 32 ,-9460.035,63.37641,55.895, 0, 0, 0, 0, 100, 0);

-- WOODS_PATH
SET @ENTRY := 80502;
DELETE FROM `waypoint_data` WHERE `id`=@ENTRY;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES
(@ENTRY, 1 ,-9445.364,58.4987,55.97727, 0, 0, 1, 0, 100, 0), -- 07:31:27
(@ENTRY, 2 ,-9445.032,58.55785,56.2136, 0, 0, 1, 0, 100, 0), -- 07:31:28
(@ENTRY, 3 ,-9427.596,41.84236,57.21891, 0, 0, 1, 0, 100, 0), -- 07:31:30
(@ENTRY, 4 ,-9410.779,22.67539,58.65345, 0, 0, 1, 0, 100, 0), -- 07:31:32
(@ENTRY, 5 ,-9391.792,0.02795124,60.67725, 0, 0, 1, 0, 100, 0), -- 07:31:34
(@ENTRY, 6 ,-9377.568,-14.63209,62.37714, 0, 0, 1, 0, 100, 0), -- 07:31:38
(@ENTRY, 7 ,-9363.139,-29.04746,63.92186, 0, 0, 1, 0, 100, 0), -- 07:31:41
(@ENTRY, 8 ,-9348.428,-41.40288,65.24854, 0, 0, 1, 0, 100, 0), -- 07:31:43
(@ENTRY, 9 ,-9333.074,-51.61682,66.22433, 0, 0, 1, 0, 100, 0), -- 07:31:46
(@ENTRY, 10,-9324.543,-55.41918,66.51912, 0, 0, 1, 0, 100, 0), -- 07:31:49
(@ENTRY, 11,-9306.514,-57.01295,66.98401, 0, 0, 1, 0, 100, 0), -- 07:31:51
(@ENTRY, 12,-9288.65,-65.67791,67.9245, 0, 0, 1, 0, 100, 0), -- 07:31:53
(@ENTRY, 13,-9266.111,-81.1689,69.09925, 0, 0, 1, 0, 100, 0), -- 07:31:55
(@ENTRY, 14,-9246.279,-94.43002,70.69906, 0, 0, 1, 0, 100, 0), -- 07:31:57
(@ENTRY, 15,-9241.527,-97.61461,70.86122, 0, 0, 1, 0, 100, 0), -- 07:32:01
(@ENTRY, 16,-9228.587,-103.1802,71.24693, 0, 0, 1, 0, 100, 0), -- 07:32:02
(@ENTRY, 17,-9202.936,-110.5503,71.32005, 0, 0, 1, 0, 100, 0), -- 07:32:06
(@ENTRY, 18,-9189.19,-111.8865,71.32235, 0, 0, 1, 0, 100, 0), -- 07:32:07
(@ENTRY, 19,-9164.87,-117.8934,73.10243, 0, 0, 1, 0, 100, 0), -- 07:32:10
(@ENTRY, 20,-9164.276,-118.5964,73.10841, 0, 0, 1, 0, 100, 0), -- 07:32:11
(@ENTRY, 21,-9157.389,-130.045,74.83447, 0, 0, 1, 0, 100, 0), -- 07:32:12
(@ENTRY, 22,-9155.88,-137.6466,74.73592, 0, 0, 0, 0, 100, 0);

-- HOUSE_PATH
SET @ENTRY := 80503;
DELETE FROM `waypoint_data` WHERE `id`=@ENTRY;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES
(@ENTRY, 1 ,-9158.411,-126.9952,74.33096, 0, 0, 0, 0, 100, 0), -- 07:38:14
(@ENTRY, 2 ,-9161.936,-122.2632,73.48005, 0, 0, 0, 0, 100, 0), -- 07:38:18
(@ENTRY, 3 ,-9171.188,-116.403,72.13133, 0, 0, 0, 0, 100, 0), -- 07:38:23
(@ENTRY, 4 ,-9175.792,-115.2302,71.66595, 0, 0, 0, 0, 100, 0), -- 07:38:26
(@ENTRY, 5 ,-9188.126,-113.6762,71.1235, 0, 0, 0, 0, 100, 0), -- 07:38:31
(@ENTRY, 6 ,-9198.314,-111.6536,71.19713, 0, 0, 0, 0, 100, 0), -- 07:38:36
(@ENTRY, 7 ,-9212.777,-108.1362,71.49892, 0, 0, 0, 0, 100, 0), -- 07:38:41
(@ENTRY, 8 ,-9221.962,-106.0956,71.29646, 0, 0, 0, 0, 100, 0), -- 07:38:46
(@ENTRY, 9 ,-9238.102,-98.25057,71.02353, 0, 0, 0, 0, 100, 0), -- 07:38:52
(@ENTRY, 10,-9255.748,-87.4807,70.21791, 0, 0, 0, 0, 100, 0), -- 07:38:55
(@ENTRY, 11,-9262.821,-82.41038,69.39944, 0, 0, 0, 0, 100, 0), -- 07:39:01
(@ENTRY, 12,-9267.083,-79.06253,68.88945, 0, 0, 0, 0, 100, 0), -- 07:39:06
(@ENTRY, 13,-9277.369,-71.58946,68.45795, 0, 0, 0, 0, 100, 0), -- 07:39:11
(@ENTRY, 14,-9294.043,-62.51098,67.73149, 0, 0, 0, 0, 100, 0), -- 07:39:15
(@ENTRY, 15,-9296.369,-61.36803,67.39666, 0, 0, 0, 0, 100, 0), -- 07:39:20
(@ENTRY, 16,-9306.391,-58.70887,67.12717, 0, 0, 0, 0, 100, 0), -- 07:39:23
(@ENTRY, 17,-9314.822,-57.41774,66.82509, 0, 0, 0, 0, 100, 0), -- 07:39:27
(@ENTRY, 18,-9336.649,-53.8809,66.1555, 0, 0, 0, 0, 100, 0), -- 07:39:31
(@ENTRY, 19,-9337.359,-53.52038,65.92425, 0, 0, 0, 0, 100, 0), -- 07:39:37
(@ENTRY, 20,-9350.479,-55.02436,66.34923, 0, 0, 0, 0, 100, 0), -- 07:39:40
(@ENTRY, 21,-9351.158,-55.39683,66.35679, 0, 0, 0, 0, 100, 0), -- 07:39:43
(@ENTRY, 22,-9359.373,-69.76035,64.45229, 0, 0, 0, 0, 100, 0), -- 07:39:44
(@ENTRY, 23,-9360.618,-71.72406,64.24545, 0, 0, 0, 0, 100, 0), -- 07:39:50
(@ENTRY, 24,-9366.256,-80.96321,64.52115, 0, 0, 0, 0, 100, 0), -- 07:39:54
(@ENTRY, 25,-9372.031,-87.89227,64.235367, 0, 0, 0, 0, 100, 0),
(@ENTRY, 26,-9376.598,-89.94442,63.722805, 0, 0, 0, 0, 100, 0),
(@ENTRY, 27,-9383.135,-84.98008,63.336449, 0, 0, 0, 0, 100, 0),
(@ENTRY, 28,-9380.336,-78.55399,64.437691, 0, 0, 0, 0, 100, 0),
(@ENTRY, 29,-9390.718,-68.80339,64.437691, 0, 0, 0, 0, 100, 0),
(@ENTRY, 30,-9392.218,-71.91143,64.437691, 0, 0, 0, 0, 100, 0),
(@ENTRY, 31,-9385.052,-76.36288,69.202148, 0, 0, 0, 0, 100, 0),
(@ENTRY, 32,-9382.018,-76.88008,69.164696, 0, 0, 0, 0, 100, 0),
(@ENTRY, 33,-9377.161,-72.19738,69.198997, 0, 0, 0, 0, 100, 0),
(@ENTRY, 34,-9375.251,-70.71793,69.201691, 0, 0, 0, 0, 100, 0),
(@ENTRY, 35,-9372.823,-66.29554,69.201859, 0, 0, 0, 0, 100, 0);

-- Lisa run away waypoints
SET @ENTRY := 80700;
DELETE FROM `waypoint_data` WHERE `id`=@ENTRY;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES
(@ENTRY, 1,-9154.618,-134.9246,75.17611, 0, 0, 1, 0, 100, 0), -- 07:32:15
(@ENTRY, 2,-9155.719,-132.458,75.17039, 0, 0, 1, 0, 100, 0), -- 07:32:15
(@ENTRY, 3,-9340.689,-89.09771,66.49249, 0, 0, 1, 0, 100, 0), -- 07:32:27
(@ENTRY, 4,-9353.076,-86.58789,65.68958, 0, 0, 0, 0, 100, 0); -- 07:33:47

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_05_02' WHERE sql_rev = '1641388762527715027';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
