-- DB update 2019_10_26_00 -> 2019_10_27_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_10_26_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_10_26_00 2019_10_27_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1571215774635756412'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1571215774635756412');

-- Chilltusk: Remove usage of spell "First Aid" during waypoint movement
UPDATE `waypoint_data` SET `action` = 0 WHERE `id` = 1074120;
DELETE FROM `waypoint_scripts` WHERE `id` IN (1046,1047);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
