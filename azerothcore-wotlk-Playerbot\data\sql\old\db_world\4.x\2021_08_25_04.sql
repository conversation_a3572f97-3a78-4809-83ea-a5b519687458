-- DB update 2021_08_25_03 -> 2021_08_25_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_25_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_25_03 2021_08_25_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1629391445774422568'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1629391445774422568');

-- Change the respawntime of the Ammo Crate to 15 s
UPDATE `gameobject` SET `spawntimesecs` = 15 WHERE (`id` = 176785) AND (`guid` IN (10663));

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_25_04' WHERE sql_rev = '1629391445774422568';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
