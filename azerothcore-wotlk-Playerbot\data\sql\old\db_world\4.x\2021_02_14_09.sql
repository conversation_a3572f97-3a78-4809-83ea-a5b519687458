-- DB update 2021_02_14_08 -> 2021_02_14_09
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_02_14_08';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_02_14_08 2021_02_14_09 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1613268991019765900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1613268991019765900');

/* Correctly set Amplify Magic Ranks 4 thru 7 as positive buffs.
*/
DELETE FROM `spell_custom_attr` WHERE `spell_id` IN (43017, 33946, 27130, 10170);
INSERT INTO `spell_custom_attr` (`spell_id`, `attributes`) VALUES 
(43017, 100663296),
(33946, 100663296),
(27130, 100663296),
(10170, 100663296);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
