-- DB update 2020_09_13_00 -> 2020_09_14_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_09_13_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_09_13_00 2020_09_14_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1598879518158481300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1598879518158481300');
/*
 * General: Build Update
 * Update by Knindza | <www.azerothcore.org>
 * Copyright (C) <www.shadowburn.net> & <www.lichbane.com>
*/

/* Content 3.2.2 */ 
SET @Build := 10505;

UPDATE `creature_template` SET `VerifiedBuild` = @Build WHERE `entry` IN (34383, 34382, 34653, 34710, 34744, 34675, 34708, 34644, 34654, 34676, 34711, 34478, 34481, 34479, 34677, 34678, 34679, 34712, 34713, 34714, 34768, 34476, 35254, 34482, 34435, 35261, 34480, 34484, 35260, 35256, 34477, 34483, 36479, 36506);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
