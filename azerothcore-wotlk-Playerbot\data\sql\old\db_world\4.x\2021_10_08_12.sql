-- DB update 2021_10_08_11 -> 2021_10_08_12
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_08_11';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_08_11 2021_10_08_12 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633177696846115469'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633177696846115469');

-- Change <PERSON><PERSON>'s Embrace drop chance from Ras Frostwhisper to 0.36% (x2 = 0.72% total)
UPDATE `reference_loot_template` SET `Chance` = 0.36 WHERE `Entry` = 35030 AND `Item` = 13314;


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_08_12' WHERE sql_rev = '1633177696846115469';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
