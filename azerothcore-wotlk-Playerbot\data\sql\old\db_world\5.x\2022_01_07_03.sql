-- DB update 2022_01_07_02 -> 2022_01_07_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_07_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_07_02 2022_01_07_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639254662548269400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639254662548269400');

UPDATE `game_event` SET `holiday`=375, `length`=4320, `holidayStage`=1 WHERE `eventEntry`=71;

DELETE FROM `game_event` WHERE `eventEntry`=77;
INSERT INTO `game_event` VALUES
(77,'2016-11-06 02:01:00','2030-12-31 07:00:00',131040,4320,376,1,'Darkmoon Faire Building (Terokkar Forest)',0,2);

DELETE FROM `creature` WHERE `guid` IN (2000000,2000001,2000002,2000003,2000004,2000005,2000006,2000007);
INSERT INTO `creature` (`guid`, `id`, `map`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `wander_distance`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`) VALUES
(2000000, 14849, 530, 1, 1, 0, 1, -2250.29, 5245.24, -9.85045, 5.03122, 300, 0, 0, 699, 0, 0),
(2000001, 14849, 530, 1, 1, 0, 1, -2257.19, 5234.82, -9.89205, 0.06712, 300, 0, 0, 699, 0, 0),
(2000002, 14849, 530, 1, 1, 0, 1, -2258.29, 5215.98, -9.90446, 1.4558, 300, 0, 0, 699, 0, 0),
(2000003, 14849, 530, 1, 1, 0, 1, -2247.18, 5219.25, -9.90328, 1.04846, 300, 0, 0, 699, 0, 0),
(2000004, 14849, 530, 1, 1, 0, 1, -2273.24, 5210.8, -9.89171, 2.57128, 300, 0, 0, 699, 0, 0),
(2000005, 14849, 530, 1, 1, 0, 1, -2264.64, 5202.15, -9.9045, 3.226, 300, 0, 0, 699, 0, 0),
(2000006, 14849, 530, 1, 1, 0, 1, -2233.39, 5223.75, -9.91236, 2.31678, 300, 0, 0, 699, 0, 0),
(2000007, 14849, 530, 1, 1, 0, 1, -2281.47, 5212.95, -9.86383, 5.90235, 300, 0, 0, 699, 0, 0);

DELETE FROM `creature_addon` WHERE guid IN (2000000,2000001,2000002,2000003,2000004,2000005,2000006,2000007);
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `auras`) VALUES
(2000000, 0, 0, 0, 4097, 69, ""),
(2000001, 0, 0, 0, 4097, 234, ""),
(2000002, 0, 0, 0, 4097, 234, ""),
(2000003, 0, 0, 0, 4097, 234, ""),
(2000004, 0, 0, 0, 4097, 234, ""),
(2000005, 0, 0, 0, 4097, 234, ""),
(2000006, 0, 0, 0, 4097, 69, ""),
(2000007, 0, 0, 0, 4097, 69, "");
 
DELETE FROM `game_event_creature` WHERE `guid` IN (2000000,2000001,2000002,2000003,2000004,2000005,2000006,2000007) AND `eventEntry`=77;
INSERT INTO `game_event_creature` (`guid`,`eventEntry`) VALUES
(2000000,77),
(2000001,77),
(2000002,77),
(2000003,77),
(2000004,77),
(2000005,77),
(2000006,77),
(2000007,77);

DELETE FROM `creature_equip_template` WHERE `CreatureID` IN (14849);
INSERT INTO `creature_equip_template` (`CreatureID`, `ID`, `ItemID1`) VALUES (14849, 1, 2493);

DELETE FROM `gameobject` WHERE `guid` IN (5777,5782,5784,5826,5833,5843,5846,5865,5869,5882,5900,5909,5915,5919,5929,5941,5978,5994,6007,6009,6020,6029,6035,6044,6047,6048,6055,6057,6060,6062,6064,6073,6078,6079,6081,6084,6086,6087,6092,6093,6096,6102,6104,6106,6108,6112,6115,6119,6123,6132,6136,6146,6148,6149,6154,6161,6162,6164,6169,6173,6179,6181,6183,6191,6196,6197); 
INSERT INTO `gameobject` (`guid`, `id`, `map`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`) VALUES
(5777, 180005, 530, 1, 1, -2288.83, 5202.74, -7.31422, 2.53072, 0, 0, 0.953716, 0.300709, 180, 255, 1),
(5782, 180005, 530, 1, 1, -2298.22, 5237.68, -7.31973, 1.39626, 0, 0, 0.642786, 0.766046, 180, 255, 1),
(5784, 180005, 530, 1, 1, -2261.59, 5256.38, -7.32221, -0.069812, 0, 0, -0.0348989, 0.999391, 180, 255, 1),
(5826, 180005, 530, 1, 1, -2236.05, 5231.09, -7.63396, -1.27409, 0, 0, -0.594823, 0.803857, 180, 255, 1),
(5833, 180045, 530, 1, 1, -2277.49, 5228.89, -9.97543, -1.88495, 0, 0, -0.809015, 0.587788, 180, 255, 1),
(5843, 180045, 530, 1, 1, -2268.45, 5238.19, -9.97545, -2.68781, 0, 0, -0.97437, 0.22495, 180, 255, 1),
(5846, 180045, 530, 1, 1, -2261.48, 5193.23, -10.0657, -0.139624, 0, 0, -0.0697553, 0.997564, 180, 255, 1),
(5865, 186253, 530, 1, 1, -2237.73, 5186.31, -11.069, -1.02974, 0, 0, -0.492422, 0.870357, 180, 255, 1),
(5869, 179968, 530, 1, 1, -2252.03, 5245.33, -9.97546, -2.61799, 0, 0, -0.965925, 0.258821, 180, 255, 1),
(5882, 179968, 530, 1, 1, -2252.87, 5244.89, -9.9754, 0.523598, 0, 0, 0.258819, 0.965926, 180, 255, 1),
(5900, 179968, 530, 1, 1, -2253.22, 5243.05, -9.97547, -0.122173, 0, 0, -0.0610485, 0.998135, 180, 255, 1),
(5909, 179968, 530, 1, 1, -2252.57, 5244.43, -9.34352, 1.0821, 0, 0, 0.515036, 0.857168, 180, 255, 1),
(5915, 179968, 530, 1, 1, -2252.35, 5243.58, -9.9755, 0.296705, 0, 0, 0.147809, 0.989016, 180, 255, 1),
(5919, 179968, 530, 1, 1, -2253.72, 5244.38, -9.97543, -2.61799, 0, 0, -0.965925, 0.258821, 180, 255, 1),
(5929, 179968, 530, 1, 1, -2251.35, 5244.16, -9.97551, 0.523598, 0, 0, 0.258819, 0.965926, 180, 255, 1),
(5941, 179969, 530, 1, 1, -2236.44, 5220.29, -9.97543, -0.052359, 0, 0, -0.0261765, 0.999657, 180, 255, 1),
(5978, 179969, 530, 1, 1, -2232.89, 5211.41, -8.76615, 1.95477, 0, 0, 0.829038, 0.559192, 180, 255, 1),
(5994, 179969, 530, 1, 1, -2237.54, 5221, -9.97539, 1.39626, 0, 0, 0.642786, 0.766046, 180, 255, 1),
(6007, 179969, 530, 1, 1, -2235, 5214.41, -8.74052, 0.506145, 0, 0, 0.25038, 0.968148, 180, 255, 1),
(6009, 179969, 530, 1, 1, -2278.9, 5205.07, -8.39948, 1.29154, 0, 0, 0.601814, 0.798637, 180, 255, 1),
(6020, 179970, 530, 1, 1, -2276.49, 5205.49, -9.79441, -0.226892, 0, 0, -0.113203, 0.993572, 180, 255, 1),
(6029, 179970, 530, 1, 1, -2235.23, 5208.37, -10.0184, 0.349065, 0, 0, 0.173648, 0.984808, 180, 255, 1),
(6035, 179970, 530, 1, 1, -2234.24, 5210.23, -8.74533, -2.23402, 0, 0, -0.898794, 0.438372, 180, 255, 1),
(6044, 179970, 530, 1, 1, -2233.87, 5208.08, -10.0347, 1.39626, 0, 0, 0.642786, 0.766046, 180, 255, 1),
(6047, 179972, 530, 1, 1, -2234.8, 5214.42, -9.97589, 1.74533, 0, 0, 0.766045, 0.642787, 180, 255, 1),
(6048, 179972, 530, 1, 1, -2232.85, 5211.37, -10.0027, -1.0472, 0, 0, -0.500001, 0.866025, 180, 255, 1),
(6055, 179972, 530, 1, 1, -2233.39, 5209.66, -10.0232, -1.74533, 0, 0, -0.766045, 0.642787, 180, 255, 1),
(6057, 179972, 530, 1, 1, -2277.98, 5204.65, -9.64532, -1.72787, 0, 0, -0.760404, 0.64945, 180, 255, 1),
(6060, 179972, 530, 1, 1, -2279.44, 5205.24, -9.6256, 3.03684, 0, 0, 0.998629, 0.0523524, 180, 255, 1),
(6062, 179972, 530, 1, 1, -2234.86, 5210.77, -9.98861, 0.122173, 0, 0, 0.0610485, 0.998135, 180, 255, 1),
(6064, 179973, 530, 1, 1, -2234.81, 5216.3, -9.97498, 0, 0, 0, 0, 1, 180, 255, 1),
(6073, 179973, 530, 1, 1, -2233.96, 5218.32, -9.97322, 2.58308, 0, 0, 0.961261, 0.275641, 180, 255, 1),
(6078, 179977, 530, 1, 1, -2276.51, 5205.54, -9.10804, 1.76278, 0, 0, 0.771624, 0.636079, 180, 255, 1),
(6079, 179977, 530, 1, 1, -2234.85, 5214.47, -8.10517, 1.46608, 0, 0, 0.669132, 0.743144, 180, 255, 1),
(6081, 179977, 530, 1, 1, -2249.82, 5242.96, -8.99624, -2.23402, 0, 0, -0.898794, 0.438372, 180, 255, 1),
(6084, 180006, 530, 1, 1, -2280.79, 5246.01, -9.97544, -0.802851, 0, 0, -0.390731, 0.920505, 180, 255, 1),
(6086, 180007, 530, 1, 1, -2258.13, 5217.4, -9.98775, 2.47837, 0, 0, 0.945519, 0.325567, 180, 255, 1),
(6087, 180007, 530, 1, 1, -2274.65, 5211.7, -9.98778, -2.426, 0, 0, -0.936671, 0.350211, 180, 255, 1),
(6092, 180007, 530, 1, 1, -2246.35, 5220.69, -9.98459, 1.67551, 0, 0, 0.743143, 0.669133, 180, 255, 1),
(6093, 180007, 530, 1, 1, -2255.7, 5234.92, -9.97533, -3.07177, 0, 0, -0.999391, 0.0349043, 180, 255, 1),
(6096, 180007, 530, 1, 1, -2266.15, 5202.02, -9.98819, -1.6057, 0, 0, -0.719339, 0.694659, 180, 255, 1),
(6102, 180043, 530, 1, 1, -2269.51, 5197.71, -9.80712, -2.9845, 0, 0, -0.996917, 0.0784656, 180, 255, 1),
(6104, 180043, 530, 1, 1, -2281.53, 5243.79, -9.97545, 2.63544, 0, 0, 0.968147, 0.250383, 180, 255, 1),
(6106, 180043, 530, 1, 1, -2230.55, 5172.72, -13.4639, -0.994837, 0, 0, -0.477158, 0.878817, 180, 255, 1),
(6108, 180043, 530, 1, 1, -2220.71, 5176.08, -13.4164, 0.366518, 0, 0, 0.182235, 0.983255, 180, 255, 1),
(6112, 180043, 530, 1, 1, -2212.66, 5161.79, -17.595, 2.70526, 0, 0, 0.976296, 0.21644, 180, 255, 1),
(6115, 180043, 530, 1, 1, -2225.82, 5160.27, -17.2795, -0.087266, 0, 0, -0.0436192, 0.999048, 180, 255, 1),
(6119, 180043, 530, 1, 1, -2271.57, 5232.05, -9.9754, 2.37364, 0, 0, 0.927182, 0.37461, 180, 255, 1),
(6123, 180046, 530, 1, 1, -2265.91, 5235.78, -9.97537, -1.02974, 0, 0, -0.492422, 0.870357, 180, 255, 1),
(6132, 180046, 530, 1, 1, -2262.24, 5196.83, -10.0033, 1.29154, 0, 0, 0.601814, 0.798637, 180, 255, 1),
(6136, 180046, 530, 1, 1, -2274.82, 5227.6, -9.97544, -0.314158, 0, 0, -0.156434, 0.987688, 180, 255, 1),
(6146, 180046, 530, 1, 1, -2267.73, 5234.87, -9.97531, -1.0821, 0, 0, -0.515036, 0.857168, 180, 255, 1),
(6148, 180047, 530, 1, 1, -2261.18, 5196.14, -10.0059, -1.76278, 0, 0, -0.771624, 0.636079, 180, 255, 1),
(6149, 180047, 530, 1, 1, -2274.59, 5228.82, -9.97548, -2.32129, 0, 0, -0.91706, 0.398748, 180, 255, 1),
(6154, 180048, 530, 1, 1, -2261.29, 5196.36, -9.49793, -2.16421, 0, 0, -0.882948, 0.469471, 180, 255, 1),
(6161, 180049, 530, 1, 1, -2274.5, 5228.78, -9.4546, 0.767944, 0, 0, 0.374606, 0.927184, 180, 255, 1),
(6162, 180050, 530, 1, 1, -2261.12, 5196.34, -9.48182, -1.76278, 0, 0, -0.771624, 0.636079, 180, 255, 1),
(6164, 180051, 530, 1, 1, -2261.09, 5196.13, -9.47695, -2.60053, 0, 0, -0.963629, 0.267244, 180, 255, 1),
(6169, 180052, 530, 1, 1, -2273.22, 5232.94, -9.97547, -1.67551, 0, 0, -0.743143, 0.669133, 180, 255, 1),
(6173, 179967, 530, 1, 1, -2249.83, 5242.91, -9.97542, 0.331611, 0, 0, 0.165047, 0.986286, 180, 255, 1),
(6179, 179967, 530, 1, 1, -2280.81, 5205.82, -9.6751, -0.331611, 0, 0, -0.165047, 0.986286, 180, 255, 1),
(6181, 179967, 530, 1, 1, -2248.54, 5243.33, -9.97544, -1.29154, 0, 0, -0.601814, 0.798637, 180, 255, 1),
(6183, 179967, 530, 1, 1, -2235.97, 5217.44, -9.97761, -3.10665, 0, 0, -0.999847, 0.0174704, 180, 255, 1),
(6191, 179967, 530, 1, 1, -2237.76, 5219.87, -9.97718, -1.91986, 0, 0, -0.819151, 0.573577, 180, 255, 1),
(6196, 179967, 530, 1, 1, -2279.89, 5207.03, -9.77578, 2.75761, 0, 0, 0.981626, 0.190814, 180, 255, 1),
(6197, 179967, 530, 1, 1, -2249.41, 5244, -9.9754, -0.349065, 0, 0, -0.173648, 0.984808, 180, 255, 1);

DELETE FROM `game_event_gameobject` WHERE `guid` IN (5777,5782,5784,5826,5833,5843,5846,5865,5869,5882,5900,5909,5915,5919,5929,5941,5978,5994,6007,6009,6020,6029,6035,6044,6047,6048,6055,6057,6060,6062,6064,6073,6078,6079,6081,6084,6086,6087,6092,6093,6096,6102,6104,6106,6108,6112,6115,6119,6123,6132,6136,6146,6148,6149,6154,6161,6162,6164,6169,6173,6179,6181,6183,6191,6196,6197) AND `eventEntry`=77;
INSERT INTO `game_event_gameobject` (`guid`,`eventEntry`) VALUES
(5777,77),(5782,77),(5784,77),(5826,77),
(5833,77),(5843,77),(5846,77),(5865,77),
(5869,77),(5882,77),(5900,77),(5909,77),
(5915,77),(5919,77),(5929,77),(5941,77),
(5978,77),(5994,77),(6007,77),(6009,77),
(6020,77),(6029,77),(6035,77),(6044,77),
(6047,77),(6048,77),(6055,77),(6057,77),
(6060,77),(6062,77),(6064,77),(6073,77),
(6078,77),(6079,77),(6081,77),(6084,77),
(6086,77),(6087,77),(6092,77),(6093,77),
(6096,77),(6102,77),(6104,77),(6106,77),
(6108,77),(6112,77),(6115,77),(6119,77),
(6123,77),(6132,77),(6136,77),(6146,77),
(6148,77),(6149,77),(6154,77),(6161,77),
(6162,77),(6164,77),(6169,77),(6173,77),
(6179,77),(6181,77),(6183,77),(6191,77),
(6196,77),(6197,77);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_07_03' WHERE sql_rev = '1639254662548269400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
