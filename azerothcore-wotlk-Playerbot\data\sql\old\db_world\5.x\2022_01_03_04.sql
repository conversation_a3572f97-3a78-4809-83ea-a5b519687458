-- DB update 2022_01_03_03 -> 2022_01_03_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_03_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_03_03 2022_01_03_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1640722015363303857'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1640722015363303857');

-- Pathing for Jason Lemieux Entry: 3544.
SET @NPC := 15530;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-34.749947,`position_y`=-819.71674,`position_z`=57.503563 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-34.749947,-819.71674,57.503563,0,0,0,0,100,0),
(@PATH,2,-27.762045,-823.3263,57.5211,0,0,0,0,100,0),
(@PATH,3,-19.321018,-828.0194,57.945904,0,0,0,0,100,0),
(@PATH,4,-13.153754,-833.6922,59.063225,0,0,0,0,100,0),
(@PATH,5,-7.333822,-840.6851,60.13427,0,0,0,0,100,0),
(@PATH,6,-6.921767,-850.10223,60.190178,0,0,0,0,100,0),
(@PATH,7,-10.89757,-859.86414,59.871086,0,0,0,0,100,0),
(@PATH,8,-17.626682,-871.1272,59.232723,0,0,0,0,100,0),
(@PATH,9,-27.371637,-875.08356,57.58868,0,0,0,0,100,0),
(@PATH,10,-42.14247,-878.5745,55.797447,0,0,0,0,100,0),
(@PATH,11,-50.91808,-880.2924,55.98519,0,0,0,0,100,0),
(@PATH,12,-58.377335,-881.7529,57.482506,0,0,0,0,100,0),
(@PATH,13,-68.54937,-885.6777,56.405117,0,0,0,0,100,0),
(@PATH,14,-76.27729,-887.2549,55.45541,0,0,0,0,100,0),
(@PATH,15,-83.80317,-888.91534,55.263027,0,0,0,0,100,0),
(@PATH,16,-89.02469,-890.85706,55.263027,0,0,0,0,100,0),
(@PATH,17,-96.63482,-896.34247,55.17172,0,0,0,0,100,0),
(@PATH,18,-97.24723,-900.06433,55.430775,0,0,0,0,100,0),
(@PATH,19,-96.16038,-907.76746,55.28258,0,0,0,0,100,0),
(@PATH,20,-88.19287,-917.0272,55.180775,0,0,0,0,100,0),
(@PATH,21,-81.71425,-914.9733,55.180775,0,0,0,0,100,0),
(@PATH,22,-72.97906,-915.29407,55.73595,0,0,0,0,100,0),
(@PATH,23,-60.614857,-918.9371,55.820114,0,0,0,0,100,0),
(@PATH,24,-49.8846,-926.0623,55.19731,0,0,0,0,100,0),
(@PATH,25,-40.704155,-925.94977,54.523727,0,0,0,0,100,0),
(@PATH,26,-34.092396,-916.3973,54.897263,0,0,0,0,100,0),
(@PATH,27,-30.830784,-905.1478,55.89356,0,0,0,0,100,0),
(@PATH,28,-27.899035,-900.1659,55.94898,0,0,0,0,100,0),
(@PATH,29,-27.359213,-895.0418,56.133114,0,0,0,0,100,0),
(@PATH,30,-36.323353,-884.0202,56.167076,0,0,0,0,100,0),
(@PATH,31,-45.04031,-873.287,55.667076,0,0,0,0,100,0),
(@PATH,32,-51.541775,-861.87036,55.775265,0,0,0,0,100,0),
(@PATH,33,-52.6779,-847.5198,56.387325,0,0,0,0,100,0),
(@PATH,34,-51.70307,-839.7983,56.49548,0,0,0,0,100,0),
(@PATH,35,-47.32796,-830.12775,57.01162,0,0,0,0,100,0),
(@PATH,36,-41.50027,-824.35236,57.378563,0,0,0,0,100,0);

-- Pathing for Tarren Mill Deathguard Entry: 2405
SET @NPC := 15544; -- 15545
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-25.886068,`position_y`=-898.6489,`position_z`=56.008114 WHERE `guid`=@NPC;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=0,`position_x`=-27.664907,`position_y`=-898.89697,`position_z`=56.052258 WHERE `guid`=15545;
DELETE FROM `creature_addon` WHERE `guid` IN (@NPC,15545);
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id` IN (@PATH,155450);
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-25.886068,-898.6489,56.008114,0,0,0,0,100,0),
(@PATH,2,-30.908691,-887.16473,56.258114,0,0,0,0,100,0),
(@PATH,3,-41.13303,-876.28656,55.917076,0,0,0,0,100,0),
(@PATH,4,-51.34885,-866.22687,55.637325,0,0,0,0,100,0),
(@PATH,5,-53.72868,-855.4186,56.137325,0,0,0,0,100,0),
(@PATH,6,-52.107857,-838.1363,56.512325,0,0,0,0,100,0),
(@PATH,7,-45.029133,-827.61414,57.16372,0,0,0,0,100,0),
(@PATH,8,-34.071888,-819.03253,57.60244,0,0,0,0,100,0),
(@PATH,9,-22.551378,-809.7012,58.02598,0,0,0,0,100,0),
(@PATH,10,-17.284288,-801.41705,58.913433,0,0,0,0,100,0),
(@PATH,11,-16.238607,-789.4188,59.59491,0,0,0,0,100,0),
(@PATH,12,-19.13596,-778.89746,60.262634,0,0,0,0,100,0),
(@PATH,13,-22.513672,-766.3535,61.437855,0,0,0,0,100,0),
(@PATH,14,-22.733887,-754.7103,63.306995,0,0,0,0,100,0),
(@PATH,15,-20.244087,-743.15186,65.331406,0,0,0,0,100,0),
(@PATH,16,-18.791775,-729.40576,67.89773,0,0,0,0,100,0),
(@PATH,17,-19.882107,-719.29596,69.378685,0,0,0,0,100,0),
(@PATH,18,-18.791775,-729.40576,67.89773,0,0,0,0,100,0),
(@PATH,19,-20.244087,-743.15186,65.331406,0,0,0,0,100,0),
(@PATH,20,-22.733887,-754.7103,63.306995,0,0,0,0,100,0),
(@PATH,21,-22.549263,-766.2366,61.42687,0,0,0,0,100,0),
(@PATH,22,-19.13596,-778.89746,60.262634,0,0,0,0,100,0),
(@PATH,23,-16.238607,-789.4188,59.59491,0,0,0,0,100,0),
(@PATH,24,-17.284288,-801.41705,58.913433,0,0,0,0,100,0),
(@PATH,25,-22.551378,-809.7012,58.02598,0,0,0,0,100,0),
(@PATH,26,-34.071888,-819.03253,57.60244,0,0,0,0,100,0),
(@PATH,27,-45.029133,-827.61414,57.16372,0,0,0,0,100,0),
(@PATH,28,-52.107857,-838.1363,56.512325,0,0,0,0,100,0),
(@PATH,29,-53.72868,-855.4186,56.137325,0,0,0,0,100,0),
(@PATH,30,-51.34885,-866.22687,55.637325,0,0,0,0,100,0),
(@PATH,31,-41.13303,-876.28656,55.917076,0,0,0,0,100,0),
(@PATH,32,-30.908691,-887.16473,56.258114,0,0,0,0,100,0);
-- Formation
DELETE FROM `creature_formations` WHERE `leaderGUID`=15544;
INSERT INTO `creature_formations` (`leaderGUID`,`memberGUID`,`dist`,`angle`,`groupAI`,`point_1`,`point_2`) VALUES
(15544,15545,1,90,514,0,0),
(15544,15544,0,0,2,0,0);

UPDATE `creature` SET `wander_distance`=1,`MovementType`=1 WHERE `guid`=15302;
UPDATE `creature` SET `wander_distance`=1,`MovementType`=1 WHERE `guid`=15542;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_03_04' WHERE sql_rev = '1640722015363303857';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
