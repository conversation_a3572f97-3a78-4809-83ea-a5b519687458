-- DB update 2021_10_10_27 -> 2021_10_10_28
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_27';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_27 2021_10_10_28 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633622346247698400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633622346247698400');

UPDATE `gameobject` SET `orientation`=6.0, `rotation0`=0, `rotation1`=0, `rotation2`=0.607782, `rotation3`=0.794104 WHERE `guid`=31669;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_28' WHERE sql_rev = '1633622346247698400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
