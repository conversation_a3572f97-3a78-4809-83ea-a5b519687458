-- DB update 2021_10_08_00 -> 2021_10_08_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_08_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_08_00 2021_10_08_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1632590909434812800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1632590909434812800');

UPDATE `creature` SET `position_x`=2424.75, `position_y`=-1669.55, `position_z`=139.06 WHERE `guid`=34193;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_08_01' WHERE sql_rev = '1632590909434812800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
