-- DB update 2021_06_18_00 -> 2021_06_18_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_18_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_18_00 2021_06_18_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1622643754073506200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1622643754073506200');

SET @THORIUM_ORE = 10620;
SET @BC_GEMS = 13001;
UPDATE `prospecting_loot_template`
SET `GroupId` = 0
WHERE `ENTRY` = @THORIUM_ORE AND `Reference` = @BC_GEMS;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_18_01' WHERE sql_rev = '1622643754073506200';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
