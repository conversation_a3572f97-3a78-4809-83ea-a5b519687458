-- DB update 2021_07_07_14 -> 2021_07_07_15
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_07_14';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_07_14 2021_07_07_15 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1625489162833644602'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1625489162833644602');

-- Add gossip to Maggran Earthbinder
DELETE FROM `gossip_menu` WHERE `MenuID` = 50008;
INSERT INTO `gossip_menu` (`MenuID`, `TextID`) VALUES
(50008, 5443);
UPDATE `creature_template` SET `gossip_menu_id` = 50008 WHERE `entry` = 11860;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_07_15' WHERE sql_rev = '1625489162833644602';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
