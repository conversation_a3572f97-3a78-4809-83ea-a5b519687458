-- DB update 2022_01_10_03 -> 2022_01_11_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_10_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_10_03 2022_01_11_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641645833659060700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641645833659060700');

DELETE FROM `game_event_creature` WHERE `guid` IN (3565,3566);
INSERT INTO `game_event_creature` VALUES
(52,3565),
(52,3566);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_11_00' WHERE sql_rev = '1641645833659060700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
