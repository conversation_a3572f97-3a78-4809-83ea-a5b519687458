-- DB update 2021_04_27_01 -> 2021_04_29_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_27_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_27_01 2021_04_29_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1619295401445296600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1619295401445296600');

UPDATE `creature_template` SET `mechanic_immune_mask`=`mechanic_immune_mask`|
1| -- MECHANIC_CHARM
16| -- MECHANIC_FEAR
64| -- MECHANIC_ROOT
512| -- MECHANIC_SLEEP
4096| -- MECHANIC_FREEZE
65536| -- MECHANIC_POLYMORPH
536870912 -- MECHANIC_SAPPED
WHERE `entry` IN (34701, 34705, 34657);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
