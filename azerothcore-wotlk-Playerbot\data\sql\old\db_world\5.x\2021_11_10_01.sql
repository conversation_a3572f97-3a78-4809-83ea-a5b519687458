-- DB update 2021_11_10_00 -> 2021_11_10_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_10_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_10_00 2021_11_10_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1636478862897449300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1636478862897449300');

DELETE FROM `creature_formations` WHERE `leaderguid`=137859;
INSERT INTO `creature_formations` VALUES
(137859,137859,0,0,2,0,0),
(137859,137954,5,135,513,0,0),
(137859,137865,5,45,513,0,0),
(137859,137958,5,315,513,0,0),
(137859,137952,5,225,513,0,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_10_01' WHERE sql_rev = '1636478862897449300';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
