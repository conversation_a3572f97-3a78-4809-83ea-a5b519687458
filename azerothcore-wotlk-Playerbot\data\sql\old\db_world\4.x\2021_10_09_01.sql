-- DB update 2021_10_09_00 -> 2021_10_09_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_09_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_09_00 2021_10_09_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633115287601081900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633115287601081900');

DELETE FROM `gameobject` WHERE `guid` IN (30362,29868,30875,31969,29867,29870,31393,31968,30879,31966,31388,31967,34047,35776,35779,35787,35781,35778,35784,
35774,35788);
DELETE FROM `game_event_gameobject` WHERE `guid` IN (30362,29868,30875,31969,29867,29870,31393,31968,30879,31966,31388,31967,34047,35776,35779,35787,35781,35778,35784,
35774,35788);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_09_01' WHERE sql_rev = '1633115287601081900';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
