-- DB update 2019_05_11_02 -> 2019_05_12_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_05_11_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_05_11_02 2019_05_12_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1557057861901733700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1557057861901733700');

-- Returning the Favor (9931)
UPDATE `conditions` SET `ConditionValue1` = 18064 WHERE `SourceTypeOrReferenceId` = 17 AND `SourceEntry` = 32314 AND `ConditionTypeOrReference` = 29 AND `ElseGroup` = 1;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
