-- DB update 2021_08_18_00 -> 2021_08_18_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_18_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_18_00 2021_08_18_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1629097518726008500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1629097518726008500');

-- Remove Text
DELETE FROM `npc_text` WHERE `ID` = 16644;

-- Remove Gossip Menu
DELETE FROM `gossip_menu` WHERE `MenuID` = 11876;

-- Update "Winkey" Template
UPDATE `creature_template` SET `npcflag` = 0 WHERE (`entry` = 7770);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_18_01' WHERE sql_rev = '1629097518726008500';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
