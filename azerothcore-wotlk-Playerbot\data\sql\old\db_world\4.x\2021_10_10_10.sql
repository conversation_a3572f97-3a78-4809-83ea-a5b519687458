-- DB update 2021_10_10_09 -> 2021_10_10_10
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_09';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_09 2021_10_10_10 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633343204449526100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633343204449526100');

UPDATE `gameobject` SET `id`=180400 WHERE `guid` IN (151625,151626,151627,151628,151629);

DELETE FROM `gameobject` WHERE `guid` IN (6548,6553,6555,6557,6559,6561,6562,6563,6565,6567,6568,6573,6580,6585,6586,6587,6589,6591,6593,
6594,6595,6596,6598,6601,6612,6613,6614,6615,6616,6618,6619,6620,6621);
INSERT INTO `gameobject` VALUES
(6548,180397,530,0,0,1,1,-2047,5349.84,-41.0873,4.24115,0,0,-0.852641,0.522496,300,255,1,'',0),
(6553,180397,530,0,0,1,1,-2032.1,5221.72,-40.7506,1.51844,0,0,-0.688356,-0.725373,300,255,1,'',0),
(6555,180397,530,0,0,1,1,-1941.28,5409.95,-12.4698,3.15907,0,0,-0.999962,0.0087363,300,255,1,'',0),
(6557,180397,530,0,0,1,1,-1882.33,5509.25,-12.5392,1.93731,0,0,-0.824125,-0.566409,300,255,1,'',0),
(6559,180397,530,0,0,1,1,-1848.87,5349.6,-12.5184,4.86947,0,0,-0.649449,0.760405,300,255,1,'',0),
(6561,180397,530,0,0,1,1,-1836.9,5146.54,-42.9014,2.77507,0,0,-0.983255,-0.182237,300,255,1,'',0),
(6562,180397,530,0,0,1,1,-1814.73,5433.6,-12.4561,3.29869,0,0,-0.996917,0.0784657,300,255,1,'',0),
(6563,180397,530,0,0,1,1,-1786.12,5453.28,-12.5254,0.261798,0,0,-0.130526,-0.991445,300,255,1,'',0),
(6565,180397,530,0,0,1,1,-1783.08,5253.05,-40.3064,5.44543,0,0,-0.406736,0.913546,300,255,1,'',0),
(6567,180397,530,0,0,1,1,-1679.59,5503.09,-40.4978,0.837757,0,0,-0.406736,-0.913546,300,255,1,'',0),
(6568,180397,530,0,0,1,1,-1669.58,5412.65,-40.5241,0.0174525,0,0,-0.00872607,-0.999962,300,255,1,'',0),
(6573,180397,530,0,0,1,1,-1647.68,5217.71,-42.5536,3.07177,0,0,-0.999391,-0.0349043,300,255,1,'',0),
(6580,180397,571,0,0,1,1,5664.43,791.297,653.83,5.61996,0,0,-0.325567,0.945519,300,255,1,'',0),
(6585,180397,571,0,0,1,1,5674.48,808.294,654.311,4.50296,0,0,-0.777144,0.629323,300,255,1,'',0),
(6586,180397,1,0,0,1,1,9719.91,2545.86,1335.68,5.3058,0,0,-0.469471,0.882948,300,255,1,'',0),
(6587,180397,1,0,0,1,1,9963.39,2490.04,1316.1,0,0,0,0,1,300,255,1,'',0),
(6589,180397,1,0,0,1,1,9966.02,2180.19,1328.12,3.73501,0,0,-0.956305,0.292372,300,255,1,'',0),
(6591,180397,1,0,0,1,1,9985.58,2342.43,1330.79,3.19186,0,0,-0.999684,0.0251295,300,255,1,'',0),
(6593,180397,530,0,0,1,1,-3827.9,-11728.4,-106.771,2.18166,0,0,-0.887011,-0.461749,300,255,1,'',0),
(6594,180397,530,0,0,1,1,-3841.83,-11419,-132.059,3.82228,0,0,-0.942641,0.333809,300,255,1,'',0),
(6595,180397,530,0,0,1,1,-3909.87,-11632.8,-138.08,1.62316,0,0,-0.725376,-0.688353,300,255,1,'',0),
(6596,180397,530,0,0,1,1,-3947.58,-11696.7,-138.757,3.52558,0,0,-0.981626,0.190814,300,255,1,'',0),
(6598,180397,530,0,0,1,1,-4161.83,-11451.8,-131.011,0.506145,0,0,-0.25038,-0.968148,300,255,1,'',0),
(6601,180397,0,0,0,1,1,-4675.39,-985.716,501.659,1.37881,0,0,-0.636078,-0.771625,300,255,1,'',0),
(6612,180397,0,0,0,1,1,-4833.68,-1172.16,502.195,1.25664,0,0,-0.587786,-0.809016,300,255,1,'',0),
(6613,180397,0,0,0,1,1,-4917.24,-844.055,501.661,3.82228,0,0,-0.942641,0.333809,300,255,1,'',0),
(6614,180397,0,0,0,1,1,-5035.67,-921.683,501.659,0,0,0,0,1,300,255,1,'',0),
(6615,180397,0,0,0,1,1,-5039.29,-1259.13,505.3,0.715585,0,0,-0.350207,-0.936672,300,255,1,'',0),
(6616,180397,0,0,0,1,1,-8386.36,288.661,120.885,3.81389,0,0,-0.944032,0.329855,300,255,1,'',0),
(6618,180397,0,0,0,1,1,-8404.29,577.574,92.069,5.28835,0,0,-0.477158,0.878817,300,255,1,'',0),
(6619,180397,0,0,0,1,1,-8842.51,654.82,97.2985,3.80482,0,0,-0.945519,0.325567,300,255,1,'',0),
(6620,180397,0,0,0,1,1,-8847.17,593.815,93.454,2.42601,0,0,-0.936673,-0.350206,300,255,1,'',0),
(6621,180397,0,0,0,1,1,-8870.55,545.077,106.284,5.06146,0,0,-0.573576,0.819152,300,255,1,'',0);

DELETE FROM `game_event_gameobject` WHERE `eventEntry`=19 AND `guid`IN (6548,6553,6555,6557,6559,6561,6562,6563,6565,6567,6568,6573,6580,6585,6586,6587,6589,6591,6593,
6594,6595,6596,6598,6601,6612,6613,6614,6615,6616,6618,6619,6620,6621);
INSERT INTO `game_event_gameobject` VALUES
(19,6548),
(19,6553),
(19,6555),
(19,6557),
(19,6559),
(19,6561),
(19,6562),
(19,6563),
(19,6565),
(19,6567),
(19,6568),
(19,6573),
(19,6580),
(19,6585),
(19,6586),
(19,6587),
(19,6589),
(19,6591),
(19,6593),
(19,6594),
(19,6595),
(19,6596),
(19,6598),
(19,6601),
(19,6612),
(19,6613),
(19,6614),
(19,6615),
(19,6616),
(19,6618),
(19,6619),
(19,6620),
(19,6621);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_10' WHERE sql_rev = '1633343204449526100';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
