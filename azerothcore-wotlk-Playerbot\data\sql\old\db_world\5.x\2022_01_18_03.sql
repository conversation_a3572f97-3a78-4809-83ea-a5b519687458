-- DB update 2022_01_18_02 -> 2022_01_18_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_18_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_18_02 2022_01_18_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1642319541117378200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1642319541117378200');
DELETE FROM `creature` WHERE `guid` IN (49566, 46923, 49610, 46929, 46930, 49565, 46925, 49620, 49628, 49648, 46945, 49633, 46952, 49636, 49646, 49626, 46953, 49640, 46954, 49643, 46946, 49639, 49645, 46951, 46950);
INSERT INTO `creature` (`guid`,`id1`,`id2`,`id3`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
/* Young Nightsaber/Young Thistleboar spawns -- ALL of these have two ids as they should switch back and forth between the mob types */
(49566, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10245.866, 844.3586, 1343.9066, 0.506145477294921875, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10245.866 844.3586 1343.9066
(46923, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10266.559, 733.7458, 1343.4877, 1.762782573699951171, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10266.559 733.7458 1343.4877
(49610, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10316.083, 649.7789, 1331.6206, 5.427973747253417968, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10316.083 649.7789 1331.6206
(46929, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10366.403, 699.85376, 1326.7089, 4.97418832778930664, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10366.403 699.85376 1326.7089
(46930, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10382.43, 682.1378, 1325.6403, 4.502949237823486328, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10382.43 682.1378 1325.6403
(49565, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10400.095, 633.01605, 1325.8828, 6.17846536636352539, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10400.095 633.01605 1325.8828
(46925, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10484.313, 881.80414, 1312.1876, 3.700098037719726562, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10484.313 881.80414 1312.1876
(49620, 1984, 2031, 0, 1, 0, 0, 1, 1, 0, 10510.209, 869.6787, 1313.3438, 4.468042850494384765, 180, 9, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10510.209 869.6787 1313.3438
/* Mangy Nightsaber/Thistle Boar spawns -- ALL of these have two ids */
(49628, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10516.052, 617.9318, 1331.6506, 3.822271108627319335, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10516.052 617.9318 1331.6506
(49648, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10533.324, 633.5844, 1327.3145, 2.844886541366577148, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10533.324 633.5844 1327.3145
(46945, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10552.625, 782.24304, 1313.4692, 0.890117883682250976, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10552.625 782.24304 1313.4692
(49633, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10568.464, 633.21594, 1326.7195, 3.455751895904541015, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10568.464 633.21594 1326.7195
(46952, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10582.159, 784.1131, 1310.5391, 4.310963153839111328, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10582.159 784.1131 1310.5391
(49636, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10599.876, 799.4928, 1309.868, 5.742133140563964843, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10599.876 799.4928 1309.868
(49646, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10615.404, 750.7155, 1317.5345, 1.535889744758605957, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10615.404 750.7155 1317.5345
(49626, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10617.052, 784.78595, 1314.0431, 2.58308720588684082, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10617.052 784.78595 1314.0431
(46953, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10618.01, 684.5653, 1326.204, 0.994837641716003417, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10618.01 684.5653 1326.204
(49640, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10633.322, 732.6758, 1322.9066, 2.635447263717651367, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10633.322 732.6758 1322.9066
(46954, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10633.33, 699.4627, 1325.9407, 6.091198921203613281, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10633.33 699.4627 1325.9407
(49643, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10633.621, 666.5614, 1329.6592, 2.809980154037475585, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10633.621 666.5614 1329.6592
(46946, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10649.84, 749.3575, 1319.7266, 1.710422635078430175, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10649.84 749.3575 1319.7266
(49639, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10666.771, 766.71136, 1320.6018, 3.909537553787231445, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10666.771 766.71136 1320.6018
(49645, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10666.881, 733.63776, 1323.4856, 3.787364482879638671, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10666.881 733.63776 1323.4856
(46951, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10677.708, 749.8698, 1322.5094, 2.251474618911743164, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 10677.708 749.8698 1322.5094
(46950, 1985, 2032, 0, 1, 0, 0, 1, 1, 0, 10682.271, 718.951, 1326.6854, 5.98647928237915039, 180, 14, 0, 1, 0, 1, 0, 0, 0, '', 0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_18_03' WHERE sql_rev = '1642319541117378200';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
