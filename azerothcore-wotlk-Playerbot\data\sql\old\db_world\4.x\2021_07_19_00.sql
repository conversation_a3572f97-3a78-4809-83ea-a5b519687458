-- DB update 2021_07_18_00 -> 2021_07_19_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_18_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_18_00 2021_07_19_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1626201929766251800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1626201929766251800');

-- Moved <PERSON><PERSON><PERSON> to the ground
UPDATE `gameobject` SET `position_x` = -6859.7, `position_y` = -3335.35, `position_z` = 243.2 WHERE `id` = 2046 AND `guid` = 8985;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_19_00' WHERE sql_rev = '1626201929766251800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
