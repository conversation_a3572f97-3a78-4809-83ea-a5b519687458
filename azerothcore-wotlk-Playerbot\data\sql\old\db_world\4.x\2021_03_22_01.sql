-- DB update 2021_03_22_00 -> 2021_03_22_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_22_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_22_00 2021_03_22_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1616258651093218025'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1616258651093218025');

DELETE FROM `gameobject_addon` WHERE `guid` IN (5141, 5193, 5205, 5382, 5398, 5405, 5425);
INSERT INTO `gameobject_addon` (`guid`, `invisibilityType`, `invisibilityValue`) VALUES 
(5141, 0, 0),
(5193, 0, 0),
(5205, 0, 0),
(5382, 0, 0),
(5398, 0, 0),
(5405, 0, 0),
(5425, 0, 0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
