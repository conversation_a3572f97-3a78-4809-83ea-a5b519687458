-- DB update 2021_09_20_11 -> 2021_09_20_12
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_20_11';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_20_11 2021_09_20_12 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1631782025388292932'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1631782025388292932');

UPDATE `creature` SET `movementtype`= 1, `wander_distance` = 3 WHERE `GUID` = 32332 AND `id` = 4619;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_20_12' WHERE sql_rev = '1631782025388292932';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
