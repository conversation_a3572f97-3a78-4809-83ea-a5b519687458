-- DB update 2021_09_07_12 -> 2021_09_08_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_07_12';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_07_12 2021_09_08_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1630944576847563700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1630944576847563700');

DELETE FROM `gameobject` WHERE `id`=180397;
INSERT INTO `gameobject` VALUES
(6586,180397,1,0,0,1,1,9719.91,2545.86,1335.68,5.3058,0,0,-0.469471,0.882948,300,255,1,'',0),
(6587,180397,1,0,0,1,1,9963.39,2490.04,1316.1,0,0,0,0,1,300,255,1,'',0),
(6589,180397,1,0,0,1,1,9966.02,2180.19,1328.12,3.73501,0,0,-0.956305,0.292372,300,255,1,'',0),
(6591,180397,1,0,0,1,1,9985.58,2342.43,1330.79,3.19186,0,0,-0.999684,0.0251295,300,255,1,'',0),
(6593,180397,530,0,0,1,1,-3827.9,-11728.4,-106.771,2.18166,0,0,-0.887011,-0.461749,300,255,1,'',0),
(6594,180397,530,0,0,1,1,-3841.83,-11419,-132.059,3.82228,0,0,-0.942641,0.333809,300,255,1,'',0),
(6595,180397,530,0,0,1,1,-3909.87,-11632.8,-138.08,1.62316,0,0,-0.725376,-0.688353,300,255,1,'',0),
(6596,180397,530,0,0,1,1,-3947.58,-11696.7,-138.757,3.52558,0,0,-0.981626,0.190814,300,255,1,'',0),
(6598,180397,530,0,0,1,1,-4161.83,-11451.8,-131.011,0.506145,0,0,-0.25038,-0.968148,300,255,1,'',0),
(6601,180397,0,0,0,1,1,-4675.39,-985.716,501.659,1.37881,0,0,-0.636078,-0.771625,300,255,1,'',0),
(6612,180397,0,0,0,1,1,-4833.68,-1172.16,502.195,1.25664,0,0,-0.587786,-0.809016,300,255,1,'',0),
(6613,180397,0,0,0,1,1,-4917.24,-844.055,501.661,3.82228,0,0,-0.942641,0.333809,300,255,1,'',0),
(6614,180397,0,0,0,1,1,-5035.67,-921.683,501.659,0,0,0,0,1,300,255,1,'',0),
(6615,180397,0,0,0,1,1,-5039.29,-1259.13,505.3,0.715585,0,0,-0.350207,-0.936672,300,255,1,'',0),
(6616,180397,0,0,0,1,1,-8386.36,288.661,120.885,3.81389,0,0,-0.944032,0.329855,300,255,1,'',0),
(6618,180397,0,0,0,1,1,-8404.29,577.574,92.069,5.28835,0,0,-0.477158,0.878817,300,255,1,'',0),
(6619,180397,0,0,0,1,1,-8842.51,654.82,97.2985,3.80482,0,0,-0.945519,0.325567,300,255,1,'',0),
(6620,180397,0,0,0,1,1,-8847.17,593.815,93.454,2.42601,0,0,-0.936673,-0.350206,300,255,1,'',0),
(6621,180397,0,0,0,1,1,-8870.55,545.077,106.284,5.06146,0,0,-0.573576,0.819152,300,255,1,'',0),
(6630,180397,530,0,0,1,1,-1666.9,5187.86,-41.4536,2.04204,0,0,-0.852641,-0.522496,300,255,1,'',0),
(6632,180397,530,0,0,1,1,-1691.49,5529.84,-40.4793,0.087266,0,0,-0.0436194,-0.999048,300,255,1,'',0),
(6636,180397,530,0,0,1,1,-1801.47,5479.88,-12.4281,0.610863,0,0,-0.300705,-0.953717,300,255,1,'',0),
(6637,180397,530,0,0,1,1,-1812.75,5367.69,-12.4281,5.46288,0,0,-0.398748,0.91706,300,255,1,'',0),
(6641,180397,530,0,0,1,1,-1869.72,5145.31,-43.819,0.279252,0,0,-0.139173,-0.990268,300,255,1,'',0),
(6642,180397,530,0,0,1,1,-1911.44,5434.67,-12.4907,0.436332,0,0,-0.216439,-0.976296,300,255,1,'',0),
(6643,180397,530,0,0,1,1,-1913.13,5493.83,-12.4281,1.97222,0,0,-0.833885,-0.551938,300,255,1,'',0),
(6644,180397,530,0,0,1,1,-1925.4,5379.6,-12.4281,4.08408,0,0,-0.891005,0.453993,300,255,1,'',0),
(6648,180397,530,0,0,1,1,-1951.57,5251.61,-42.7737,3.83973,0,0,-0.939692,0.342021,300,255,1,'',0),
(6649,180397,530,0,0,1,1,-2037.52,5329.36,-39.9843,3.28124,0,0,-0.997564,0.0697647,300,255,1,'',0),
(6650,180397,530,0,0,1,1,-2057.35,5247.44,-38.8602,0.401425,0,0,-0.199368,-0.979925,300,255,1,'',0),
(6651,180397,571,0,0,1,1,5913.94,554.304,660.998,0.994837,0,0,-0.477158,-0.878817,300,255,1,'',0),
(6654,180397,571,0,0,1,1,5916.52,566.397,639.628,3.56048,0,0,-0.978147,0.207914,300,255,1,'',0);

DELETE FROM `game_event_gameobject` WHERE `eventEntry`=18 AND `guid` IN (6586,6587,6589,6591,6593,6594,6595,6596,6598,6601,6612,6613,
6614,6615,6616,6618,6619,6620,6621,6630,6632,6636,6637,6641,6642,6643,6644,6648,6649,6650,6651,6654);
INSERT INTO `game_event_gameobject` VALUES
(18,6586),
(18,6587),
(18,6589),
(18,6591),
(18,6593),
(18,6594),
(18,6595),
(18,6596),
(18,6598),
(18,6601),
(18,6612),
(18,6613),
(18,6614),
(18,6615),
(18,6616),
(18,6618),
(18,6619),
(18,6620),
(18,6621),
(18,6630),
(18,6632),
(18,6636),
(18,6637),
(18,6641),
(18,6642),
(18,6643),
(18,6644),
(18,6648),
(18,6649),
(18,6650),
(18,6651),
(18,6654);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_08_00' WHERE sql_rev = '1630944576847563700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
