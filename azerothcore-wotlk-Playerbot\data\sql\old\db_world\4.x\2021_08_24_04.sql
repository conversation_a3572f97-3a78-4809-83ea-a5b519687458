-- DB update 2021_08_24_03 -> 2021_08_24_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_24_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_24_03 2021_08_24_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1629312732062343549'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1629312732062343549');

-- Set the Creature Fingat a patrol route movement
UPDATE `creature` SET `MovementType` = 2  WHERE (`id` = 14446) AND (`guid` IN (43604, 134236, 134237, 134238, 134239, 134240, 134241));
UPDATE `creature_template` SET `MovementType` = 2 WHERE (`entry` = 2754);

-- Delete previous routes
DELETE FROM `creature_addon` WHERE (`guid` IN (43604, 134236, 134237, 134238, 134239, 134240, 134241));

-- Routes
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `isLarge`, `auras`) VALUES
(43604, 436040, 0, 0, 0, 0, 0, NULL),
(134236, 1342360, 0, 0, 0, 0, 0, NULL),
(134237, 1342370, 0, 0, 0, 0, 0, NULL),
(134238, 1342380, 0, 0, 0, 0, 0, NULL),
(134239, 1342390, 0, 0, 0, 0, 0, NULL),
(134240, 1342400, 0, 0, 0, 0, 0, NULL),
(134241, 1342410, 0, 0, 0, 0, 0, NULL);

-- Delete all waypoints routes
DELETE FROM `waypoint_data` WHERE `id` IN (436040, 1342360, 1342370, 1342370, 1342380, 1342390, 1342400, 1342410);

INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
-- Waypoint route 1 (GUID: 43604)
(436040,1,-11009.3,-3665.84,23.38,0,0,0,0,100,0),
(436040,2,-11018.08,-3688.10,23.02,0,0,0,0,100,0),
(436040,3,-11011.80,-3697.90,21.94,0,0,0,0,100,0),
(436040,4,-10990.02,-3701.60,16.42,0,0,0,0,100,0),
(436040,5,-10939.84,-3678.71,8.61,0,0,0,0,100,0),
(436040,6,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(436040,7,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(436040,8,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(436040,9,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(436040,10,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(436040,11,-10948.01,-3714.25,25.62,0,0,0,0,100,0),
(436040,12,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(436040,13,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(436040,14,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(436040,15,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(436040,16,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(436040,17,-10939.84,-3678.71,8.61,0,0,0,0,100,0),
(436040,18,-10990.02,-3701.60,16.42,0,0,0,0,100,0),
(436040,19,-11011.80,-3697.90,21.94,0,0,0,0,100,0),
(436040,20,-11018.08,-3688.10,23.02,0,0,0,0,100,0),
(436040,21,-11009.3,-3665.84,23.38,0,0,0,0,100,0),

-- Waypoint route 2 (GUID: 134236)
(1342360,1,-10906.40,-3627.92,15.78,0,0,0,0,100,0),
(1342360,2,-10938.81,-3609.59,20.38,0,0,0,0,100,0),
(1342360,3,-10950.54,-3615.16,22.56,0,0,0,0,100,0),
(1342360,4,-10957.35,-3655.94,26.99,0,0,0,0,100,0),
(1342360,5,-11008.77,-3663.24,23.34,0,0,0,0,100,0),
(1342360,6,-11018.81,-3687.45,22.90,0,0,0,0,100,0),
(1342360,7,-11011.55,-3698.51,21.83,0,0,0,0,100,0),
(1342360,8,-10990.02,-3701.60,16.42,0,0,0,0,100,0),
(1342360,9,-10939.84,-3678.71,8.61,0,0,0,0,100,0),
(1342360,10,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(1342360,11,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(1342360,12,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(1342360,13,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(1342360,14,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(1342360,15,-10948.01,-3714.25,25.62,0,0,0,0,100,0),
(1342360,16,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(1342360,17,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(1342360,18,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(1342360,19,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(1342360,20,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(1342360,21,-10939.84,-3678.71,8.61,0,0,0,0,100,0),
(1342360,22,-10990.02,-3701.60,16.42,0,0,0,0,100,0),
(1342360,23,-11011.80,-3697.90,21.94,0,0,0,0,100,0),
(1342360,24,-11018.08,-3688.10,23.02,0,0,0,0,100,0),
(1342360,25,-11009.3,-3665.84,23.38,0,0,0,0,100,0),

-- Waypoint route 3 (GUID: 134237)
(1342370,1,-10872.29,-3703.19,21.79,0,0,0,0,100,0),
(1342370,2,-10869.17,-3724.18,22.98,0,0,0,0,100,0),
(1342370,3,-10869.17,-3724.18,22.98,0,0,0,0,100,0),
(1342370,4,-10926.55,-2747.85,23.54,0,0,0,0,100,0),
(1342370,5,-10948.36,-3715.78,25.58,0,0,0,0,100,0),
(1342370,6,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(1342370,7,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(1342370,8,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(1342370,9,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(1342370,10,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(1342370,11,-10939.84,-3678.71,8.61,0,0,0,0,100,0),
(1342370,12,-10990.02,-3701.60,16.42,0,0,0,0,100,0),
(1342370,13,-11011.80,-3697.90,21.94,0,0,0,0,100,0),
(1342370,14,-11018.08,-3688.10,23.02,0,0,0,0,100,0),
(1342370,15,-11009.3,-3665.84,23.38,0,0,0,0,100,0),

-- Waypoint route 4 (GUID: 134238)
(1342380,1,-10860.15,-3697.48,22.61,0,0,0,0,100,0),
(1342380,2,-10870.74,-3725.53,23.12,0,0,0,0,100,0),
(1342380,3,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(1342380,4,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(1342380,5,-10948.01,-3714.25,25.62,0,0,0,0,100,0),
(1342380,6,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(1342380,7,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(1342380,8,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(1342380,9,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(1342380,10,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(1342380,11,-10939.84,-3678.71,8.61,0,0,0,0,100,0),
(1342380,12,-10990.02,-3701.60,16.42,0,0,0,0,100,0),
(1342380,13,-11011.80,-3697.90,21.94,0,0,0,0,100,0),
(1342380,14,-11018.08,-3688.10,23.02,0,0,0,0,100,0),
(1342380,15,-11009.3,-3665.84,23.38,0,0,0,0,100,0),

-- Waypoint route 5 (GUID: 134239)
(1342390,1,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(1342390,2,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(1342390,3,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(1342390,4,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(1342390,5,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(1342390,6,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(1342390,7,-10948.01,-3714.25,25.62,0,0,0,0,100,0),
(1342390,8,-10926.34,-3748.39,23.53,0,0,0,0,100,0),
(1342390,9,-10887.11,-3738.81,22.39,0,0,0,0,100,0),
(1342390,10,-10867.82,-3715.03,23.57,0,0,0,0,100,0),
(1342390,11,-10855.50,-3666.26,19.41,0,0,0,0,100,0),
(1342390,12,-10879.04,-3641.23,11.57,0,0,0,0,100,0),
(1342390,13,-10939.84,-3678.71,8.61,0,0,0,0,100,0),
(1342390,14,-10990.02,-3701.60,16.42,0,0,0,0,100,0),
(1342390,15,-11011.80,-3697.90,21.94,0,0,0,0,100,0),
(1342390,16,-11018.08,-3688.10,23.02,0,0,0,0,100,0),
(1342390,17,-11009.3,-3665.84,23.38,0,0,0,0,100,0),

-- Waypoint route 6 (GUID: 134240)
(1342400,1,-10930.56,-3745.79,23.67,0,0,0,0,100,0),
(1342400,2, -10913.27,-3746.93,23.43,0,0,0,0,100,0),
(1342400,3,-10880.50,-3735.04,22.50,0,0,0,0,100,0),
(1342400,4,-10866.21,-3712.47,23.64,0,0,0,0,100,0),
(1342400,5,-10854.08,-3668.61,19.73,0,0,0,0,100,0),
(1342400,6, -10868.88,-3644.49,13.60,0,0,0,0,100,0),
(1342400,7,-10891.51,-3649.13,12.65,0,0,0,0,100,0),
(1342400,8,-10974.55,-3701.69,13.462,0,0,0,0,100,0),
(1342400,9,-11011.53,-3698.82,21.77,0,0,0,0,100,0),
(1342400,10,-11017.84,-3684.89,23.04,0,0,0,0,100,0),
(1342400,11,-11009.72,-3664.58,23.30,0,0,0,0,100,0),
(1342400,12,-10974.29,-3661.34,27.775,0,0,0,0,100,0),
(1342400,13,-11009.72,-3664.58,23.30,0,0,0,0,100,0),
(1342400,14,-11017.84,-3684.89,23.04,0,0,0,0,100,0),
(1342400,15,-11011.53,-3698.82,21.77,0,0,0,0,100,0),
(1342400,16,-10974.55,-3701.69,13.462,0,0,0,0,100,0),
(1342400,17,-10891.51,-3649.13,12.65,0,0,0,0,100,0),
(1342400,18,-10868.88,-3644.49,13.60,0,0,0,0,100,0),
(1342400,19,-10854.08,-3668.61,19.73,0,0,0,0,100,0),
(1342400,20,-10866.21,-3712.47,23.64,0,0,0,0,100,0),
(1342400,21,-10880.50,-3735.04,22.50,0,0,0,0,100,0),
(1342400,22,-10913.27,-3746.93,23.43,0,0,0,0,100,0),
(1342400,23,-10930.56,-3745.79,23.67,0,0,0,0,100,0),

-- Waypoint route 7 (GUID: 134241)
(1342410,1,-10854.40,-3671.45,19.85,0,0,0,0,100,0),
(1342410,2,-10874.46,-3639.08,11.60,0,0,0,0,100,0),
(1342410,3,-10980.29,-3700.14,14.512859,0,0,0,0,100,0),
(1342410,4,-11011.97,-3698.0253,21.924948,0,0,0,0,100,0),
(1342410,5,-11016.51,-3680.68,23.240431,0,0,0,0,100,0), 
(1342410,6,-11004.15,-3665.214,23.698978,0,0,0,0,100,0),
(1342410,7,-10961.80,-3657.60,27.661201,0,0,0,0,100,0),
(1342410,8,-11004.15,-3665.214,23.698978,0,0,0,0,100,0),
(1342410,9,-11016.51,-3680.68,23.240431,0,0,0,0,100,0), 
(1342410,10,-11011.97,-3698.0253,21.924948,0,0,0,0,100,0),
(1342410,11,-10980.29,-3700.14,14.512859,0,0,0,0,100,0),
(1342410,12,-10874.46,-3639.08,11.60,0,0,0,0,100,0),
(1342410,13,-10854.40,-3671.45,19.85,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_24_04' WHERE sql_rev = '1629312732062343549';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
