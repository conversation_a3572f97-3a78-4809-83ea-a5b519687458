-- DB update 2021_10_26_04 -> 2021_10_27_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_26_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_26_04 2021_10_27_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635094207203255800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635094207203255800');

DELETE FROM `gameobject_template` WHERE `entry` IN (4, 129, 12653, 103575, 128972, 141609, 160462, 160842, 175328, 175590, 176592, 176750, 177493, 177529, 178124, 178248, 178644, 178673, 178963, 179527, 179530, 179531, 180525, 181214, 181290, 181604, 181831, 181838, 181840, 181842, 181844, 183278, 183282, 184910, 184958, 185297, 185305, 185862, 186471, 188130, 188134, 188135, 188139, 188145, 188146, 188147, 190552, 190721, 191146, 194645, 195061, 195089, 195304, 196829, 202208, 202361, 203044, 203971, 204289, 204385, 204434, 204968, 205100, 205547, 205581, 206322, 207418, 208046, 208426, 208543, 208596, 208815, 209282, 209833, 209898, 210857, 210946, 212348, 213351, 215404, 215419, 244605);
INSERT INTO `gameobject_template` (`entry`, `type`, `displayId`, `name`, `IconName`, `castBarCaption`, `unk1`, `size`, `Data0`, `Data1`, `Data2`, `Data3`, `Data4`, `Data5`, `Data6`, `Data7`, `Data8`, `Data9`, `Data10`, `Data11`, `Data12`, `Data13`, `Data14`, `Data15`, `Data16`, `Data17`, `Data18`, `Data19`, `Data20`, `Data21`, `Data22`, `Data23`, `AIName`, `ScriptName`, `VerifiedBuild`) VALUES
(4, 6, 0, 'Bonfire Damage', '', '', '', 1, 0, 1, 3, 7902, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 0),
(129, 6, 0, 'Naxx Teleporter trap', '', '', '', 1, 0, 1, 0, 64446, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 0),
(12653, 6, 327, 'Ghost Saber Trap', '', '', '', 1, 0, 20, 0, 5968, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(103575, 6, 0, 'Containment Coffer TRAP', '', '', '', 1, 0, 0, 0, 9012, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(128972, 6, 327, 'Shallow Grave TRAP', '', '', '', 1, 0, 0, 0, 10247, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(141609, 3, 33, 'Weegli\'s Barrel', '', '', '', 1, 43, 0, 30, 1, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(160462, 6, 2770, 'Mystical Portal', '', '', '', 1, 0, 1, 20, 13461, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(160842, 6, 2770, 'Gor\'tesh\'s Lopped Off Head', '', '', '', 1, 0, 0, 50, 13488, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(175328, 6, 2770, 'Vaelan Spawn Node', '', '', '', 0.5, 0, 50, 50, 10387, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(175590, 6, 0, 'Spire Spider Egg Trap', '', '', '', 1, 0, 0, 0, 16453, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(176592, 6, 327, 'Shellfish Trap', '', '', '', 1, 0, 0, 0, 17679, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(176750, 6, 327, 'Kodo Bones', '', '', '', 1, 0, 0, 0, 17960, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(177493, 6, 327, 'Fire of Elune (Trap', '', '', '', 1, 0, 0, 0, 18955, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(177529, 6, 327, 'Altar of Elune (Trap', '', '', '', 1, 0, 0, 0, 18993, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(178124, 6, 327, 'Resonite Crystal (Trap', '', '', '', 1, 0, 0, 5, 20492, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(178248, 6, 327, 'Naga Brazier (trap', '', '', '', 1, 0, 0, 0, 20863, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(178644, 6, 0, 'Ryson\'s All Seeing Eye Trap', '', '', '', 1, 95, 200, 0, 21546, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(178673, 6, 5752, 'Consuming Flames Trap', '', '', '', 1.2, 95, 60, 20, 21650, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(178963, 6, 2770, 'Incantion of Celebras Trap', '', '', '', 1, 0, 0, 0, 21917, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(179527, 6, 391, 'Warpwood Pod - Root', '', '', '', 0.75, 0, 0, 10, 22800, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(179530, 6, 391, 'Warpwood Pod - Spore', '', '', '', 0.75, 0, 0, 10, 22821, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(179531, 6, 391, 'Warpwood Pod - Summon', '', '', '', 0.75, 0, 0, 10, 22803, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(180525, 6, 6424, 'Tonk Control Console Trap', '', '', '', 1, 0, 0, 0, 24935, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(181214, 6, 327, 'Necropolis critter spawner', '', '', '', 1, 0, 0, 0, 27866, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(181290, 6, 0, 'Midsummer Bonfire Spawn Trap', '', '', '', 1, 0, 0, 0, 28784, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 0),
(181604, 6, 6771, 'TEST Ribbon Pole TRAP', '', '', '', 1, 0, 0, 0, 29708, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(181831, 6, 2770, 'Sealed Tome', '', '', '', 1.01, 0, 0, 0, 30765, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(181838, 6, 2770, 'Sealed Tome', '', '', '', 1.01, 0, 0, 0, 30762, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(181840, 6, 2770, 'Sealed Tome', '', '', '', 1.01, 0, 0, 0, 30764, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(181842, 6, 2770, 'Sealed Tome', '', '', '', 1.01, 0, 0, 0, 30763, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(181844, 6, 2770, 'Sealed Tome', '', '', '', 1.01, 0, 0, 0, 30766, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(183278, 6, 5752, 'Infernaling Summoner', '', '', '', 2.5, 0, 0, 0, 33533, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(183282, 6, 0, 'Infernaling Summoner Damage Trap', '', '', '', 1, 0, 1, 4, 29115, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(184910, 6, 2373, 'Power Converter', '', '', '', 0.5, 0, 0, 0, 37278, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(184958, 6, 7247, 'Nether Drake Egg', '', '', '', 1.5, 0, 0, 0, 37574, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(185297, 6, 1310, 'Lianthe\'s Strongbox', '', '', '', 2, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(185305, 6, 7298, 'Fumper Trap', '', '', '', 1, 0, 0, 0, 39217, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(185862, 6, 0, 'Fel Cannonball Stack Trap', '', '', '', 1, 0, 0, 0, 40181, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(186471, 6, 2770, 'Holiday - Brewfest - Dark Iron - Mug Trap', '', '', '', 1, 0, 0, 3, 42696, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(188130, 2, 7666, 'Ice Stone', '', '', '', 1, 0, 0, 3, 9251, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 11723),
(188134, 2, 7666, 'Ice Stone', '', '', '', 1, 0, 0, 3, 9254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 11723),
(188135, 2, 7666, 'Ice Stone', '', '', '', 1, 0, 0, 3, 9255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 11723),
(188139, 2, 7666, 'Ice Stone', '', '', '', 1, 0, 0, 3, 9258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 11723),
(188145, 2, 7666, 'Ice Stone', '', '', '', 1, 0, 0, 3, 9266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 11723),
(188146, 2, 7666, 'Ice Stone', '', '', '', 1, 0, 0, 3, 9267, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 11723),
(188147, 2, 7666, 'Ice Stone', '', '', '', 1, 0, 0, 3, 9268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 11723),
(190552, 3, 2450, 'Ancient Drakkari Chest', '', '', '', 1, 1691, 27240, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 14007),
(190721, 6, 0, 'Harvested Blight Crystal', '', '', '', 0.5, 0, 0, 0, 52261, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(191146, 6, 7205, 'Ensnaring Trap', '', '', '', 1, 12, 0, 5, 53077, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(194645, 6, 5932, 'Stone Block Trap', '', '', '', 1, 0, 0, 0, 64055, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(195061, 6, 261, 'Glittering Shell Trap', '', '', '', 0.45, 0, 0, 0, 65364, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(195089, 6, 0, 'Spirit Candle', '', '', '', 1, 0, 0, 10, 65459, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(195304, 6, 327, 'Stormforged Mole Machine Trap', '', '', '', 0.75, 0, 0, 0, 66491, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(196829, 6, 2971, 'Living Ire Thyme Linked Trap', '', '', '', 1, 0, 0, 0, 67887, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(202208, 6, 0, 'Discarded Supplies (Trap', '', '', '', 1, 0, 0, 0, 72248, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(202361, 6, 9315, 'Rockin\' Powder Visual', '', '', '', 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(203044, 6, 0, 'Steamwheedle Supplies - Pirate Trap', '', '', '', 1, 0, 0, 0, 76115, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(203971, 6, 0, 'Prickly Pear Fruit Trap', '', '', '', 1, 0, 0, 0, 79324, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(204289, 6, 0, 'Rock Lobster Trap Activator', '', '', '', 1, 0, 0, 0, 80580, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(204385, 6, 0, 'Monstrous Clam Trap Activator', '', '', '', 1, 0, 0, 0, 80911, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(204434, 6, 0, 'Lime Crate Trap', '', '', '', 1, 0, 0, 0, 81445, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(204968, 6, 1310, 'Glow Trap', '', '', '', 3, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 19342),
(205100, 6, 0, 'Ferocious Doomweed', '', '', '', 1, 0, 0, 0, 83523, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(205547, 6, 0, 'Stray Land Mine (Armed', '', '', '', 1, 0, 50, 5, 85452, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(205581, 6, 9932, 'Suspended Starlight - TRAP', '', '', '', 5, 0, 0, 0, 85688, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(206322, 6, 0, 'Wild Black Dragon Egg Trap', '', '', '', 1, 0, 0, 0, 87009, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(207418, 6, 0, 'Chillwind Egg Trap', '', '', '', 1, 0, 0, 0, 93296, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(208046, 6, 10406, 'Fel Cone', '', '', '', 4, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 16309),
(208426, 6, 9145, 'Cinderweb Egg Sac - Spiderling Trap', '', '', '', 0.4, 0, 0, 0, 97369, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(208543, 6, 9145, 'Magmolia', '', '', '', 0.1, 0, 0, 0, 98191, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 19342),
(208596, 6, 8083, 'Flame Druid Idol Trap', '', '', '', 0.05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 22522),
(208815, 6, 49, 'Buried Jar', '', '', '', 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 22522),
(209282, 6, 9749, 'Paper Pile Trap', '', '', '', 0.7, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 22566),
(209833, 6, 378, 'Silk Patch', '', '', '', 1, 0, 0, 13, 105434, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, '', '', 17658),
(209898, 6, 10985, 'Watermelon Boat Flag', '', '', '', 1.6, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 19342),
(210857, 6, 11174, 'Dry Fire Wood', '', '', '', 1, 0, 0, 0, 113649, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, '', '', 22522),
(210946, 6, 9145, 'Mite Trap', '', '', '', 0.4, 0, 0, 0, 114418, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 22522),
(212348, 6, 2971, 'Ancient Haunt Trigger', '', '', '', 1, 0, 0, 0, 122173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(213351, 6, 2971, 'Lushroom Linked Trap', '', '', '', 1, 0, 0, 0, 124427, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(215404, 6, 9941, 'Shroud of Mist', '', '', '', 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 22566),
(215419, 6, 0, 'Luck of the Lotus', '', '', '', 1, 0, 0, 0, 130653, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', -18019),
(244605, 6, 9958, 'Underbelly Hoard Trap', '', '', '', 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', 22566);

DELETE FROM `gameobject_loot_template` WHERE `Entry` = 27240;
INSERT INTO `gameobject_loot_template` (`Entry`, `Item`, `Reference`, `Chance`, `QuestRequired`, `LootMode`, `GroupId`, `MinCount`, `MaxCount`, `Comment`) VALUES
(27240,33470,0,14,0,1,3,1,4,NULL),
(27240,43851,0,15,0,1,2,1,1,NULL),
(27240,43852,0,14,0,1,2,1,1,NULL),
(27240,46368,0,47,0,1,1,1,1,NULL),
(27240,46369,0,52,0,1,1,1,1,NULL);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_27_00' WHERE sql_rev = '1635094207203255800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
