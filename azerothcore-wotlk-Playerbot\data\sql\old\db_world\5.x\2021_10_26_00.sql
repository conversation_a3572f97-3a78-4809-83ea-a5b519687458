-- DB update 2021_10_25_01 -> 2021_10_26_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_25_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_25_01 2021_10_26_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635011006215560900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635011006215560900');

UPDATE `gameobject` SET `position_x`=-7229.43, `position_y`=-3739.23, `position_z`=9.60493 WHERE `guid`=17342 AND `id`=175763;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_26_00' WHERE sql_rev = '1635011006215560900';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
