-- DB update 2021_02_01_01 -> 2021_02_01_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_02_01_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_02_01_01 2021_02_01_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1611875417271883500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1611875417271883500');

/*Cleanup GUIDs that do not exist - These GUIDs were migrated to GUID 241000 and 241002 -241006*/
DELETE FROM `game_event_gameobject` WHERE `eventEntry` = 7 and `guid` IN (11995,11996,11997,11998,11999,12000); 

/* Darnassus Missing Objects and Creatures
*/
DELETE FROM `gameobject` WHERE `guid` IN (82973,82974,82975,82976,82977,82978,82985,82994,82995,82996,82997,82998,82999,83000,83001,83002,83003,83004,83005,83006,83007,83008,83009,83010,83011,83012,83013,83014,83015,83016,83017,7183,7185,7238,7252,7254,7258);
DELETE FROM `game_event_gameobject` WHERE `eventEntry` = 7 and `guid` IN (82973,82974,82975,82976,82977,82978,82985,82994,82995,82996,82997,82998,82999,83000,83001,83002,83003,83004,83005,83006,83007,83008,83009,83010,83011,83012,83013,83014,83015,83016,83017,7183,7185,7238,7252,7254,7258);
DELETE FROM `creature` WHERE `guid` IN (85543,85541,85539,85535,85536,85537);
DELETE FROM `game_event_creature` WHERE `eventEntry` = 7 and `guid` IN (85543,85541,85539,85535,85536,85537);

INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES 
(85543,15897,1,0,0,1,1,0,0,10150.5,2602.14,1330.91,1.5708,300,0,0,0,0,0,0,0,0,'',0),
(85541,15895,1,0,0,1,1,15871,0,10153.6,2593.45,1330.84,4.79965,300,0,0,0,0,0,0,0,0,'',0),
(85539,15898,1,0,0,1,1,15870,0,10148,2572.63,1320.7,4.18879,300,0,0,0,0,0,0,0,0,'',0),
(85535,15892,1,0,0,1,1,0,0,10146.8,2603.15,1330.9,6.02139,300,0,0,0,0,0,0,0,0,'',0),
(85536,15892,1,0,0,1,1,0,0,10151.3,2598.93,1330.9,1.78024,300,0,0,0,0,0,0,0,0,'',0),
(85537,15892,1,0,0,1,1,0,0,10153.2,2604.51,1330.9,3.9619,300,0,0,0,0,0,0,0,0,'',0);


INSERT INTO `gameobject` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`position_x`,`position_y`,`position_z`,`orientation`,`rotation0`,`rotation1`,`rotation2`,`rotation3`,`spawntimesecs`,`animprogress`,`state`,`ScriptName`,`VerifiedBuild`) VALUES 
(82973,180766,1,0,0,1,1,10147,2574.3,1320.72,1.18682,0,0,0.559191,0.829039,120,255,1,'',0),
(82974,180766,1,0,0,1,1,10149.6,2592.33,1330.62,1.58825,0,0,0.713251,0.700909,120,255,1,'',0),
(82975,180766,1,0,0,1,1,10154.6,2593.35,1330.55,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(82976,180766,1,0,0,1,1,10148.5,2610.37,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(82977,180766,1,0,0,1,1,10160,2604.6,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(82978,180766,1,0,0,1,1,10141.7,2600.14,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(82985,180765,1,0,0,1,1,10156.6,2582.27,1345.43,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(82994,180878,1,0,0,1,1,10147.6,2574.05,1320.64,2.1293,0,0,0.874619,0.48481,120,255,1,'',0),
(82995,180878,1,0,0,1,1,10145,2573.84,1321.03,5.5676,0,0,0.350208,-0.936672,120,255,1,'',0),
(82996,180878,1,0,0,1,1,10146.9,2572.09,1320.74,3.54302,0,0,0.979925,-0.199369,120,255,1,'',0),
(82997,180878,1,0,0,1,1,10147.9,2574.83,1320.57,5.58505,0,0,0.342022,-0.939692,120,255,1,'',0),
(82998,180878,1,0,0,1,1,10145.3,2572.1,1320.98,4.50295,0,0,0.777146,-0.629321,120,255,1,'',0),
(82999,180878,1,0,0,1,1,10144.8,2572.77,1321.07,2.68781,0,0,0.97437,0.22495,120,255,1,'',0),
(83000,180878,1,0,0,1,1,10148.3,2574.19,1320.52,4.86947,0,0,0.649448,-0.760406,120,255,1,'',0),
(83001,180879,1,0,0,1,1,10146.3,2573.1,1320.68,5.86431,0,0,0.20791,-0.978148,120,255,1,'',0),
(83002,180777,1,0,0,1,1,10155.5,2571.57,1320.54,4.03171,0,0,0.902585,-0.430511,120,255,1,'',0),
(83003,180777,1,0,0,1,1,10146.3,2574.49,1320.82,1.16937,0,0,0.551937,0.833886,120,255,1,'',0),
(83004,180777,1,0,0,1,1,10148.8,2592.29,1330.53,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(83005,180777,1,0,0,1,1,10155.4,2593.67,1330.43,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(83006,180881,1,0,0,1,1,10146.3,2573.47,1322.49,4.79966,0,0,0.675588,-0.737279,120,255,1,'',0),
(83007,180882,1,0,0,1,1,10146.5,2573.02,1322.49,4.45059,0,0,0.793353,-0.608762,120,255,1,'',0),
(83008,180883,1,0,0,1,1,10145.7,2573.4,1322.48,5.44543,0,0,0.406735,-0.913546,120,255,1,'',0),
(83009,180883,1,0,0,1,1,10145.9,2572.75,1322.49,5.81195,0,0,0.233444,-0.97237,120,255,1,'',0),
(83010,180868,1,0,0,1,1,10148.6,2596.95,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(83011,180868,1,0,0,1,1,10154.4,2598.17,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(83012,180868,1,0,0,1,1,10152.6,2607.9,1330.82,1.58825,0,0,0.713251,0.700909,120,255,1,'',0),
(83013,180868,1,0,0,1,1,10145.2,2600.96,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(83014,180868,1,0,0,1,1,10146.7,2607.01,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(83015,180868,1,0,0,1,1,10156.2,2603.66,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(83016,180869,1,0,0,1,1,10147.5,2600.15,1330.82,1.58825,0,0,0.713251,0.700909,120,255,1,'',0),
(83017,180869,1,0,0,1,1,10153.8,2601.11,1330.82,1.5708,0,0,0.707108,0.707106,120,255,1,'',0),
(7183,180764,1,0,0,1,1,10146.8,2574.27,1322.24,-1.93731,0,0,-0.824125,0.566409,300,0,1,'',0),
(7185,180764,1,0,0,1,1,10149.7,2592.52,1332.08,-2.93214,0,0,-0.994521,0.104535,300,0,1,'',0),
(7238,180870,1,0,0,1,1,10159.8,2604.5,1332.37,-2.87979,0,0,-0.991445,0.130528,300,0,1,'',0),
(7252,180871,1,0,0,1,1,10154.5,2593.53,1332.05,-3.00195,0,0,-0.997563,0.0697646,300,0,1,'',0),
(7254,180871,1,0,0,1,1,10141.8,2600.27,1332.35,0.296705,0,0,0.147809,0.989016,300,0,1,'',0),
(7258,180872,1,0,0,1,1,10148.6,2610.28,1332.4,-1.3439,0,0,-0.622513,0.782609,300,0,1,'',0);


INSERT INTO `game_event_creature` (`eventEntry`,`guid`) VALUES 
(7,85543),
(7,85541),
(7,85539),
(7,85535),
(7,85536),
(7,85537);

INSERT INTO `game_event_gameobject` (`eventEntry`,`guid`) VALUES 
(7,82973),
(7,82974),
(7,82975),
(7,82976),
(7,82977),
(7,82978),
(7,82985),
(7,82994),
(7,82995),
(7,82996),
(7,82997),
(7,82998),
(7,82999),
(7,83000),
(7,83001),
(7,83002),
(7,83003),
(7,83004),
(7,83005),
(7,83006),
(7,83007),
(7,83008),
(7,83009),
(7,83010),
(7,83011),
(7,83012),
(7,83013),
(7,83014),
(7,83015),
(7,83016),
(7,83017),
(7,7183),
(7,7185),
(7,7238),
(7,7252),
(7,7254),
(7,7258);


/* Shattrath Missing Objects and Creatures
*/
DELETE FROM `game_event_creature` WHERE `eventEntry` = 7 and `guid` IN (97686,97687,97688,97677,97678,97679,97692,97693,97680,97684,97681,97683);
DELETE FROM `creature` WHERE `guid` IN (97686,97687,97688,97677,97678,97679,97692,97693,97680,97684,97681,97683);
DELETE FROM `gameobject` WHERE `guid` IN (79786,79772,79767,79768,79769,79770,79771,79773,79774,79783,79784,79785,79788,79791,79792,79795,79796,79797,79798,79799,79800,79801,79802,79818,79819,79820,79821,79822,79827,79828,79829,79830,79834,79835,79839,79840,79844,79848,79850,79851,79852,79853,79854,79855,79856,79857,79858,79859,79871,79872,79873,79874,241016,241017,7199,7244,7247,7248,7250,7251,7261,7268,79787,79789,79790,79803,79804,79805,79806,79807,79808,79809,79823,79826,79831,79836,79841,79845,79846,79860,79861,79862,79863,7175,7241,7245);
DELETE FROM `game_event_gameobject` WHERE `eventEntry` = 7 and `guid` IN (79786,79772,79767,79768,79769,79770,79771,79773,79774,79783,79784,79785,79788,79791,79792,79795,79796,79797,79798,79799,79800,79801,79802,79818,79819,79820,79821,79822,79827,79828,79829,79830,79834,79835,79839,79840,79844,79848,79850,79851,79852,79853,79854,79855,79856,79857,79858,79859,79871,79872,79873,79874,241016,241017,7199,7244,7247,7248,7250,7251,7261,7268,79787,79789,79790,79803,79804,79805,79806,79807,79808,79809,79823,79826,79831,79836,79841,79845,79846,79860,79861,79862,79863,7175,7241,7245);

INSERT INTO `gameobject` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`position_x`,`position_y`,`position_z`,`orientation`,`rotation0`,`rotation1`,`rotation2`,`rotation3`,`spawntimesecs`,`animprogress`,`state`,`ScriptName`,`VerifiedBuild`) VALUES 
(79767,180768,530,0,0,1,1,-1813.06,5493.69,-12.4281,4.43314,0,0,0.798634,-0.601816,120,255,1,'',0),
(79768,180768,530,0,0,1,1,-1810.53,5502.12,-12.4281,3.56047,0,0,0.978148,-0.207911,120,255,1,'',0),
(79769,180768,530,0,0,1,1,-1797.38,5484.88,-12.4281,0.872664,0,0,0.422618,0.906308,120,255,1,'',0),
(79770,180768,530,0,0,1,1,-1802.37,5503.94,-12.4281,2.87979,0,0,0.991445,0.130528,120,255,1,'',0),
(79771,180768,530,0,0,1,1,-1804.87,5483.98,-12.4281,0.122173,0,0,0.0610485,0.998135,120,255,1,'',0),
(79773,180768,530,0,0,1,1,-1791.09,5498.22,-12.4281,0.628317,0,0,0.309016,0.951057,120,255,1,'',0),
(79774,180768,530,0,0,1,1,-1793.72,5492.52,-12.4281,1.27409,0,0,0.594823,0.803857,120,255,1,'',0),
(79783,180766,530,0,0,1,1,-1784.92,5443.97,-12.4281,4.08407,0,0,0.891007,-0.45399,120,255,1,'',0),
(79784,180766,530,0,0,1,1,-1782.34,5432.17,-12.4281,5.86431,0,0,0.20791,-0.978148,120,255,1,'',0),
(79785,180766,530,0,0,1,1,-1779.88,5450.22,-12.4281,3.4383,0,0,0.989016,-0.14781,120,255,1,'',0),
(79788,180766,530,0,0,1,1,-1774.77,5428.9,-12.4281,0.226893,0,0,0.113203,0.993572,120,255,1,'',0),
(79791,180778,530,0,0,1,1,-1807.91,5485.74,-12.4281,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79792,180778,530,0,0,1,1,-1812.19,5490.32,-12.4281,3.87463,0,0,0.933581,-0.358368,120,255,1,'',0),
(79795,180878,530,0,0,1,1,-1790.34,5498.74,-12.4281,3.85718,0,0,0.936672,-0.350209,120,255,1,'',0),
(79796,180878,530,0,0,1,1,-1790.23,5496.29,-12.4281,4.01426,0,0,0.906307,-0.422619,120,255,1,'',0),
(79797,180878,530,0,0,1,1,-1790.9,5497.01,-12.4281,5.77704,0,0,0.25038,-0.968148,120,255,1,'',0),
(79798,180878,530,0,0,1,1,-1789.23,5496.14,-12.4281,6.00393,0,0,0.139174,-0.990268,120,255,1,'',0),
(79799,180878,530,0,0,1,1,-1789.17,5498.92,-12.4281,0.104719,0,0,0.0523356,0.99863,120,255,1,'',0),
(79800,180878,530,0,0,1,1,-1788.42,5496.6,-12.4281,3.08918,0,0,0.999657,0.0262033,120,255,1,'',0),
(79801,180878,530,0,0,1,1,-1788.39,5498.38,-12.4281,4.24115,0,0,0.85264,-0.522498,120,255,1,'',0),
(79802,180878,530,0,0,1,1,-1788.11,5497.56,-12.4281,1.41372,0,0,0.649449,0.760405,120,255,1,'',0),
(79818,180763,530,0,0,1,1,-1804.85,5484.87,-11.5462,0.698131,0,0,0.34202,0.939693,120,255,1,'',0),
(79819,180763,530,0,0,1,1,-1795.72,5504.96,-11.4906,0.453785,0,0,0.224951,0.97437,120,255,1,'',0),
(79820,180763,530,0,0,1,1,-1779.7,5450.05,-11.1434,0.296705,0,0,0.147809,0.989016,120,255,1,'',0),
(79821,180763,530,0,0,1,1,-1784.67,5443.93,-11.1018,3.35105,0,0,0.994521,-0.104537,120,255,1,'',0),
(79822,180763,530,0,0,1,1,-1774.67,5429.13,-11.1226,3.28124,0,0,0.997563,-0.0697669,120,255,1,'',0),
(79827,180777,530,0,0,1,1,-1784.94,5439.94,-12.4281,3.35105,0,0,0.994521,-0.104537,120,255,1,'',0),
(79828,180777,530,0,0,1,1,-1784.01,5436,-12.4281,0.209439,0,0,0.104528,0.994522,120,255,1,'',0),
(79829,180880,530,0,0,1,1,-1789.3,5498.03,-11.7268,2.74016,0,0,0.979924,0.199371,120,255,1,'',0),
(79830,180880,530,0,0,1,1,-1789.68,5498.17,-11.7129,1.98967,0,0,0.838669,0.544641,120,255,1,'',0),
(79834,180881,530,0,0,1,1,-1789.49,5497.1,-11.7476,6.14356,0,0,0.069756,-0.997564,120,255,1,'',0),
(79835,180881,530,0,0,1,1,-1790.06,5497.25,-11.7406,4.57276,0,0,0.75471,-0.656058,120,255,1,'',0),
(79839,180882,530,0,0,1,1,-1789.63,5497.67,-11.7337,3.64774,0,0,0.968147,-0.250381,120,255,1,'',0),
(79840,180882,530,0,0,1,1,-1790.08,5497.86,-11.7337,4.95674,0,0,0.61566,-0.788012,120,255,1,'',0),
(79844,180883,530,0,0,1,1,-1789.06,5497.52,-11.7337,5.65487,0,0,0.309015,-0.951057,120,255,1,'',0),
(79848,180888,530,0,0,1,1,-1789.77,5497.44,-12.4281,1.93731,0,0,0.824125,0.566409,120,255,1,'',0),
(79850,180868,530,0,0,1,1,-1809.28,5488.52,-12.4281,0.733038,0,0,0.358368,0.93358,120,255,1,'',0),
(79851,180868,530,0,0,1,1,-1798.7,5502.05,-12.4281,0.715585,0,0,0.350207,0.936672,120,255,1,'',0),
(79852,180868,530,0,0,1,1,-1801.82,5499.71,-12.4281,0.715585,0,0,0.350207,0.936672,120,255,1,'',0),
(79853,180868,530,0,0,1,1,-1804.33,5487.32,-12.4281,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79854,180868,530,0,0,1,1,-1796.72,5493.62,-12.4281,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79855,180868,530,0,0,1,1,-1809.23,5493.72,-12.4281,0.715585,0,0,0.350207,0.936672,120,255,1,'',0),
(79856,180868,530,0,0,1,1,-1794.08,5495.9,-12.4281,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79857,180868,530,0,0,1,1,-1780.21,5435.1,-12.4281,0.261798,0,0,0.130525,0.991445,120,255,1,'',0),
(79858,180868,530,0,0,1,1,-1781.54,5442.23,-12.4281,0.191985,0,0,0.0958451,0.995396,120,255,1,'',0),
(79859,180868,530,0,0,1,1,-1784.19,5438.14,-12.4281,0.226893,0,0,0.113203,0.993572,120,255,1,'',0),
(79871,180869,530,0,0,1,1,-1808.3,5498.6,-12.4281,5.14872,0,0,0.5373,-0.843391,120,255,1,'',0),
(79872,180869,530,0,0,1,1,-1799.88,5487.62,-12.4281,2.44346,0,0,0.939692,0.342021,120,255,1,'',0),
(79873,180869,530,0,0,1,1,-1778.71,5445.92,-12.4281,4.67748,0,0,0.719341,-0.694658,120,255,1,'',0),
(79874,180869,530,0,0,1,1,-1775.92,5433.1,-12.4281,0.279252,0,0,0.139173,0.990268,120,255,1,'',0),
(241016,180867,530,0,0,1,1,-1804.27,5492.68,-12.4278,5.1019,0,0,0.631024,-0.775763,25,100,1,'',0),
(241017,180867,530,0,0,1,1,-1778.08,5439.71,-12.4276,1.53108,0,0,0.631024,-0.775763,25,100,1,'',0),
(7199,180764,530,0,0,1,1,-1794.38,5492.93,-11.3934,-2.53072,0,0,-0.953716,0.300709,300,0,1,'',0),
(7244,180870,530,0,0,1,1,-1782.14,5432.37,-11.1087,-2.96704,0,0,-0.996194,0.0871655,300,0,1,'',0),
(7247,180870,530,0,0,1,1,-1810.03,5501.46,-11.4559,-2.47837,0,0,-0.945519,0.325567,300,0,1,'',0),
(7248,180870,530,0,0,1,1,-1812.15,5493.6,-11.4976,-2.37364,0,0,-0.927182,0.37461,300,0,1,'',0),
(7250,180870,530,0,0,1,1,-1795.72,5504.96,-11.4906,0.453785,0,0,0.224951,0.97437,300,0,1,'',0),
(7251,180870,530,0,0,1,1,-1797.88,5485.51,-11.4143,0.610864,0,0,0.300705,0.953717,300,0,1,'',0),
(7261,180872,530,0,0,1,1,-1791.49,5499.04,-11.4212,-2.32129,0,0,-0.91706,0.398748,300,0,1,'',0),
(7268,180873,530,0,0,1,1,-1802.42,5503.25,-11.3587,-2.68781,0,0,-0.97437,0.22495,300,0,1,'',0),
(79786,180766,530,0,0,1,1,-1771.79,5447.63,-12.4281,2.74016,0,0,0.979924,0.199371,120,255,1,'',0),
(79787,180766,530,0,0,1,1,-1768.5,5435.3,-12.4281,0.785397,0,0,0.382683,0.92388,120,255,1,'',0),
(79789,180766,530,0,0,1,1,-1764.91,5447.47,-12.4281,3.42085,0,0,0.990268,-0.139175,120,255,1,'',0),
(79790,180766,530,0,0,1,1,-1762.98,5438.72,-12.4281,0.157079,0,0,0.0784588,0.996917,120,255,1,'',0),
(79803,180878,530,0,0,1,1,-1765.72,5449.93,-12.4281,3.54302,0,0,0.979925,-0.199369,120,255,1,'',0),
(79804,180878,530,0,0,1,1,-1764.81,5450.18,-12.4281,2.1293,0,0,0.874619,0.48481,120,255,1,'',0),
(79805,180878,530,0,0,1,1,-1766.23,5448.12,-12.4281,2.68781,0,0,0.97437,0.22495,120,255,1,'',0),
(79806,180878,530,0,0,1,1,-1766.09,5449.03,-12.4281,4.50295,0,0,0.777146,-0.629321,120,255,1,'',0),
(79807,180878,530,0,0,1,1,-1765.54,5447.31,-12.4281,5.5676,0,0,0.350208,-0.936672,120,255,1,'',0),
(79808,180878,530,0,0,1,1,-1763.99,5449.78,-12.4281,5.58505,0,0,0.342022,-0.939692,120,255,1,'',0),
(79809,180878,530,0,0,1,1,-1763.4,5449.15,-12.4281,4.86947,0,0,0.649448,-0.760406,120,255,1,'',0),
(79823,180763,530,0,0,1,1,-1762.88,5438.89,-11.1851,0.279252,0,0,0.139173,0.990268,120,255,1,'',0),
(79826,180879,530,0,0,1,1,-1764.91,5448.85,-12.4281,1.20428,0,0,0.566407,0.824125,120,255,1,'',0),
(79831,180880,530,0,0,1,1,-1764.49,5448.48,-10.5879,3.42085,0,0,0.990268,-0.139175,120,255,1,'',0),
(79836,180881,530,0,0,1,1,-1764.79,5449.2,-10.6156,4.79966,0,0,0.675588,-0.737279,120,255,1,'',0),
(79841,180882,530,0,0,1,1,-1764.31,5449.01,-10.6087,3.76991,0,0,0.951057,-0.309016,120,255,1,'',0),
(79845,180883,530,0,0,1,1,-1764.88,5448.25,-10.5879,5.44543,0,0,0.406735,-0.913546,120,255,1,'',0),
(79846,180883,530,0,0,1,1,-1765.28,5448.7,-10.6087,5.81195,0,0,0.233444,-0.97237,120,255,1,'',0),
(79860,180868,530,0,0,1,1,-1770.83,5437.18,-12.4281,0.261798,0,0,0.130525,0.991445,120,255,1,'',0),
(79861,180868,530,0,0,1,1,-1768.8,5445.46,-12.4281,0.191985,0,0,0.0958451,0.995396,120,255,1,'',0),
(79862,180868,530,0,0,1,1,-1772.66,5444.5,-12.4281,0.191985,0,0,0.0958451,0.995396,120,255,1,'',0),
(79863,180868,530,0,0,1,1,-1766.99,5438.22,-12.4281,0.261798,0,0,0.130525,0.991445,120,255,1,'',0),
(7175,180763,530,0,0,1,1,-1764.76,5447.28,-11.1643,0.209439,0,0,0.104528,0.994522,300,0,1,'',0),
(7241,180870,530,0,0,1,1,-1768.5,5435.47,-11.199,-2.91469,0,0,-0.993571,0.113208,300,0,1,'',0),
(7245,180870,530,0,0,1,1,-1771.73,5447.55,-11.1434,-2.87979,0,0,-0.991445,0.130528,300,0,1,'',0),
(79772,180768,530,0,0,1,1,-1796.44,5505.65,-12.4281,3.82227,0,0,0.942642,-0.333806,120,255,1,'',0);

INSERT INTO `game_event_gameobject` (`eventEntry`,`guid`) VALUES 
(7,79767),
(7,79768),
(7,79769),
(7,79770),
(7,79771),
(7,79773),
(7,79774),
(7,79783),
(7,79784),
(7,79785),
(7,79788),
(7,79791),
(7,79792),
(7,79795),
(7,79796),
(7,79797),
(7,79798),
(7,79799),
(7,79800),
(7,79801),
(7,79802),
(7,79818),
(7,79819),
(7,79820),
(7,79821),
(7,79822),
(7,79827),
(7,79828),
(7,79829),
(7,79830),
(7,79834),
(7,79835),
(7,79839),
(7,79840),
(7,79844),
(7,79848),
(7,79850),
(7,79851),
(7,79852),
(7,79853),
(7,79854),
(7,79855),
(7,79856),
(7,79857),
(7,79858),
(7,79859),
(7,79871),
(7,79872),
(7,79873),
(7,79874),
(7,241016),
(7,241017),
(7,7199),
(7,7244),
(7,7247),
(7,7248),
(7,7250),
(7,7251),
(7,7261),
(7,7268),
(7,79786),
(7,79787),
(7,79789),
(7,79790),
(7,79803),
(7,79804),
(7,79805),
(7,79806),
(7,79807),
(7,79808),
(7,79809),
(7,79823),
(7,79826),
(7,79831),
(7,79836),
(7,79841),
(7,79845),
(7,79846),
(7,79860),
(7,79861),
(7,79862),
(7,79863),
(7,7175),
(7,7241),
(7,7245),
(7,79772);

INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES 
(97686,15891,530,0,0,1,1,0,0,-1799.08,5491.6,-12.3448,2.87979,300,0,0,0,0,0,0,0,0,'',0),
(97687,15891,530,0,0,1,1,0,0,-1804.24,5498.25,-12.3448,4.69494,300,0,0,0,0,0,0,0,0,'',0),
(97688,15891,530,0,0,1,1,0,0,-1807.64,5489.97,-12.3448,0.750492,300,0,0,0,0,0,0,0,0,'',0),
(97677,15892,530,0,0,1,1,0,0,-1781.75,5438.71,-12.3448,0.261799,300,0,0,0,0,0,0,0,0,'',0),
(97678,15892,530,0,0,1,1,0,0,-1775.56,5444.46,-12.3448,4.2237,300,0,0,0,0,0,0,0,0,'',0),
(97679,15892,530,0,0,1,1,0,0,-1773.55,5436.28,-12.3448,2.49582,300,0,0,0,0,0,0,0,0,'',0),
(97692,15897,530,0,0,1,1,0,0,-1804.34,5492.79,-12.3448,5.37561,300,0,0,0,0,0,0,0,0,'',0),
(97693,15897,530,0,0,1,1,0,0,-1778.06,5439.69,-12.3448,4.41568,300,0,0,0,0,0,0,0,0,'',0),
(97680,15898,530,0,0,1,1,0,0,-1790.07,5499.83,-12.3448,0.698132,300,0,0,0,0,0,0,0,0,'',0),
(97684,15895,530,0,0,1,1,15871,0,-1762.11,5439.02,-12.3448,0.331613,300,0,0,0,0,0,0,0,0,'',0),
(97681,15898,530,0,0,1,1,15870,0,-1763.57,5447.72,-12.3448,0.226893,300,0,0,0,0,0,0,0,0,'',0),
(97683,15895,530,0,0,1,1,0,0,-1794.92,5506.39,-12.3448,0.680678,300,0,0,0,0,0,0,0,0,'',0);

INSERT INTO `game_event_creature` (`eventEntry`,`guid`) VALUES 
(7,97686),
(7,97687),
(7,97688),
(7,97677),
(7,97678),
(7,97679),
(7,97692),
(7,97693),
(7,97680),
(7,97684),
(7,97681),
(7,97683);


/* Dalaran Missing Objects and Creatures
*/
DELETE FROM `game_event_creature` WHERE `eventEntry` = 7 and `guid` IN (61988,61989,61990,61992,61993,61991);
DELETE FROM `creature` WHERE `guid` IN (61988,61989,61990,61992,61993,61991);
DELETE FROM `gameobject` WHERE `guid` IN (36,79673,79664,79652,79606,79605,79604,79603,79602,79601,79600,79593,79592,79591,79590,79589,79588,79587,79586,79585,79584,79692,79706,79719,79720,79723,79737,79744,79745,79748,79753,79754,79755,79756,79757,79758,79759,79760,79761,79762,79763,79764,79765,79766,7177,7196,7235,7246,7257,7262,7266);
DELETE FROM `game_event_gameobject` WHERE `eventEntry` = 7 and `guid` IN (36,79673,79664,79652,79606,79605,79604,79603,79602,79601,79600,79593,79592,79591,79590,79589,79588,79587,79586,79585,79584,79692,79706,79719,79720,79723,79737,79744,79745,79748,79753,79754,79755,79756,79757,79758,79759,79760,79761,79762,79763,79764,79765,79766,7177,7196,7235,7246,7257,7262,7266);

INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES 
(61988,15892,571,0,0,1,1,0,0,5825.6,642.92,647.877,3.10669,300,0,0,0,0,0,0,0,0,'',0),
(61989,15892,571,0,0,1,1,0,0,5820.26,646.17,647.852,5.23599,300,0,0,0,0,0,0,0,0,'',0),
(61990,15892,571,0,0,1,1,0,0,5819.78,639.332,647.913,1.02974,300,0,0,0,0,0,0,0,0,'',0),
(61992,15895,571,0,0,1,1,0,0,5824.65,657.071,647.614,3.24631,300,0,0,0,0,0,0,0,0,'',0),
(61993,15897,571,0,0,1,1,0,0,5821.92,642.784,648.11,5.16617,300,0,0,0,0,0,0,0,0,'',0),
(61991,15898,571,0,0,1,1,0,0,5827.15,654.626,647.578,3.15905,300,0,0,0,0,0,0,0,0,'',0);

INSERT INTO `gameobject` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`position_x`,`position_y`,`position_z`,`orientation`,`rotation0`,`rotation1`,`rotation2`,`rotation3`,`spawntimesecs`,`animprogress`,`state`,`ScriptName`,`VerifiedBuild`) VALUES 
(36,180867,571,0,0,1,1,5820.25,646.16,647.851,5.23599,0,0,0.265429,0.96413,300,0,1,'',0),
(79673,180763,571,0,0,1,1,5820.6,692.219,647.637,0.994837,0,0,0.477158,0.878817,120,255,1,'',0),
(79664,180763,571,0,0,1,1,5833.69,645.768,610.185,4.11898,0,0,0.882947,-0.469473,120,255,1,'',0),
(79652,180763,571,0,0,1,1,5820.6,692.219,647.637,0.994837,0,0,0.477158,0.878817,120,255,1,'',0),
(79606,180878,571,0,0,1,1,5828.87,653.247,647.43,4.86947,0,0,0.649448,-0.760406,120,255,1,'',0),
(79605,180878,571,0,0,1,1,5829.67,655.498,647.284,4.50295,0,0,0.777146,-0.629321,120,255,1,'',0),
(79604,180878,571,0,0,1,1,5830,656.168,647.279,3.54302,0,0,0.979925,-0.199369,120,255,1,'',0),
(79603,180878,571,0,0,1,1,5829.29,656.304,647.347,2.1293,0,0,0.874619,0.48481,120,255,1,'',0),
(79602,180878,571,0,0,1,1,5829.48,652.814,647.389,5.58505,0,0,0.342022,-0.939692,120,255,1,'',0),
(79601,180878,571,0,0,1,1,5830.43,655.562,647.194,2.68781,0,0,0.97437,0.22495,120,255,1,'',0),
(79600,180878,571,0,0,1,1,5831.14,655.095,647.124,5.5676,0,0,0.350208,-0.936672,120,255,1,'',0),
(79593,180765,571,0,0,1,1,5818.56,689.675,617.538,5.81195,0,0,0.233444,-0.97237,120,255,1,'',0),
(79592,180765,571,0,0,1,1,5862.58,608.979,622.725,3.52557,0,0,0.981627,-0.190811,120,255,1,'',0),
(79591,180766,571,0,0,1,1,5816.47,638.815,647.198,5.11382,0,0,0.551935,-0.833887,120,255,1,'',0),
(79590,180766,571,0,0,1,1,5818.41,635.255,647.185,5.51524,0,0,0.374607,-0.927184,120,255,1,'',0),
(79589,180766,571,0,0,1,1,5818.29,648.659,647.173,4.11898,0,0,0.882947,-0.469473,120,255,1,'',0),
(79588,180766,571,0,0,1,1,5815.52,643.167,647.197,4.64258,0,0,0.731352,-0.682,120,255,1,'',0),
(79587,180766,571,0,0,1,1,5822.49,652.925,647.212,3.07177,0,0,0.999391,0.0349043,120,255,1,'',0),
(79586,180766,571,0,0,1,1,5821.81,633.411,647.195,0,0,0,0,1,120,255,1,'',0),
(79585,180766,571,0,0,1,1,5827.5,645.635,647.142,1.93731,0,0,0.824125,0.566409,120,255,1,'',0),
(79584,180766,571,0,0,1,1,5827.36,638.956,647.212,1.0821,0,0,0.515036,0.857168,120,255,1,'',0),
(79692,180763,571,0,0,1,1,5820.6,692.219,647.637,0.994837,0,0,0.477158,0.878817,120,255,1,'',0),
(79706,180764,571,0,0,1,1,5864.01,600.728,652.601,2.37364,0,0,0.927182,0.37461,120,255,1,'',0),
(79719,180764,571,0,0,1,1,5833.69,645.768,610.185,4.11898,0,0,0.882947,-0.469473,120,255,1,'',0),
(79720,180764,571,0,0,1,1,5827.23,645.592,648.326,3.31614,0,0,0.996194,-0.0871629,120,255,1,'',0),
(79723,180764,571,0,0,1,1,5816.61,639.005,648.413,2.16421,0,0,0.882948,0.469471,120,255,1,'',0),
(79737,180764,571,0,0,1,1,5864.01,600.728,652.601,2.37364,0,0,0.927182,0.37461,120,255,1,'',0),
(79744,180879,571,0,0,1,1,5829.64,654.443,647.32,5.68977,0,0,0.292373,-0.956304,120,255,1,'',0),
(79745,180767,571,0,0,1,1,5867.94,616.236,622.725,2.46091,0,0,0.942641,0.333809,120,255,1,'',0),
(79748,180767,571,0,0,1,1,5814.17,684.255,617.581,4.45059,0,0,0.793353,-0.608762,120,255,1,'',0),
(79753,180880,571,0,0,1,1,5828.16,651.357,647.522,3.42085,0,0,0.990268,-0.139175,120,255,1,'',0),
(79754,180881,571,0,0,1,1,5829.1,651.775,647.449,4.79966,0,0,0.675588,-0.737279,120,255,1,'',0),
(79755,180882,571,0,0,1,1,5829.02,651.124,647.474,3.76991,0,0,0.951057,-0.309016,120,255,1,'',0),
(79756,180883,571,0,0,1,1,5828.46,652.315,647.464,5.44543,0,0,0.406735,-0.913546,120,255,1,'',0),
(79757,180883,571,0,0,1,1,5828.01,651.922,647.507,5.81195,0,0,0.233444,-0.97237,120,255,1,'',0),
(79758,180868,571,0,0,1,1,5820.4,633.168,647.392,4.17134,0,0,0.870355,-0.492425,120,255,1,'',0),
(79759,180868,571,0,0,1,1,5818.99,634.128,647.392,4.11898,0,0,0.882947,-0.469473,120,255,1,'',0),
(79760,180868,571,0,0,1,1,5815.44,644.689,647.392,2.65289,0,0,0.970294,0.241927,120,255,1,'',0),
(79761,180868,571,0,0,1,1,5818.69,650.12,647.396,2.46091,0,0,0.942641,0.333809,120,255,1,'',0),
(79762,180868,571,0,0,1,1,5817.03,647.832,647.392,2.63544,0,0,0.968147,0.250383,120,255,1,'',0),
(79763,180868,571,0,0,1,1,5819.98,651.618,647.4,2.40855,0,0,0.93358,0.35837,120,255,1,'',0),
(79764,180868,571,0,0,1,1,5816.06,637.798,647.392,3.6652,0,0,0.965925,-0.258823,120,255,1,'',0),
(79765,180869,571,0,0,1,1,5816.27,646.344,647.392,2.65289,0,0,0.970294,0.241927,120,255,1,'',0),
(79766,180869,571,0,0,1,1,5817.03,636.229,647.392,3.73501,0,0,0.956304,-0.292374,120,255,1,'',0),
(7177,180763,571,0,0,1,1,5818.33,648.446,648.343,0.750491,0,0,0.366501,0.930418,300,0,1,'',0),
(7196,180764,571,0,0,1,1,5821.69,633.573,648.384,-2.68781,0,0,-0.97437,0.22495,300,0,1,'',0),
(7235,180775,571,0,0,1,1,5862.43,606.057,654.389,2.82743,0,0,0.987688,0.156436,300,0,1,'',0),
(7246,180870,571,0,0,1,1,5822.37,652.821,648.454,2.84488,0,0,0.989015,0.147813,300,0,1,'',0),
(7257,180871,571,0,0,1,1,5827.15,639.102,648.376,2.82743,0,0,0.987688,0.156436,300,0,1,'',0),
(7262,180873,571,0,0,1,1,5815.6,643.039,648.351,0.890117,0,0,0.430511,0.902586,300,0,1,'',0),
(7266,180873,571,0,0,1,1,5818.44,635.468,648.362,2.80997,0,0,0.986285,0.165053,300,0,1,'',0);

INSERT INTO `game_event_creature` (`eventEntry`,`guid`) VALUES 
(7,61988),
(7,61989),
(7,61990),
(7,61992),
(7,61993),
(7,61991);

INSERT INTO `game_event_gameobject` (`eventEntry`,`guid`) VALUES 
(7,36),
(7,79673),
(7,79664),
(7,79652),
(7,79606),
(7,79605),
(7,79604),
(7,79603),
(7,79602),
(7,79601),
(7,79600),
(7,79593),
(7,79592),
(7,79591),
(7,79590),
(7,79589),
(7,79588),
(7,79587),
(7,79586),
(7,79585),
(7,79584),
(7,79692),
(7,79706),
(7,79719),
(7,79720),
(7,79723),
(7,79737),
(7,79744),
(7,79745),
(7,79748),
(7,79753),
(7,79754),
(7,79755),
(7,79756),
(7,79757),
(7,79758),
(7,79759),
(7,79760),
(7,79761),
(7,79762),
(7,79763),
(7,79764),
(7,79765),
(7,79766),
(7,7177),
(7,7196),
(7,7235),
(7,7246),
(7,7257),
(7,7262),
(7,7266);

/*Exodar Missing Objects and Creatures
*/
DELETE FROM `game_event_creature` WHERE `eventEntry` = 7 and `guid` IN (85532,85533,85534,85540,85542,85538);
DELETE FROM `creature` WHERE `guid` IN (85532,85533,85534,85540,85542,85538);
DELETE FROM `game_event_gameobject` WHERE `eventEntry` = 7 and `guid` IN (40,9729,82948,82949,82950,82951,82952,82953,82954,82955,82956,82957,82958,82959,82960,82961,82962,82963,82964,82965,82966,82967,82968,82969,82970,82971,82972,83031,83032,83033,83034,83035,83036,83037,91326,91328,91481,91482,91487,91488,91489,91490,91491,91492,91494,92045,92047,92048,92051,92052);
DELETE FROM `gameobject` WHERE `guid` IN (40,9729,82948,82949,82950,82951,82952,82953,82954,82955,82956,82957,82958,82959,82960,82961,82962,82963,82964,82965,82966,82967,82968,82969,82970,82971,82972,83031,83032,83033,83034,83035,83036,83037,91326,91328,91481,91482,91487,91488,91489,91490,91491,91492,91494,92045,92047,92048,92051,92052);

INSERT INTO `gameobject` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`position_x`,`position_y`,`position_z`,`orientation`,`rotation0`,`rotation1`,`rotation2`,`rotation3`,`spawntimesecs`,`animprogress`,`state`,`ScriptName`,`VerifiedBuild`) VALUES 
(40,180867,530,0,0,1,1,-4017.32,-11837.6,0.081,2.69313,0,0,0.755281,-0.655401,300,0,1,'',0),
(9729,180749,530,0,0,1,1,-4002.94,-11879.9,-0.793618,3.89209,0,0,0,1,120,255,1,'',21742),
(82948,180879,530,0,0,1,1,-4021.67,-11847.3,0.006294,1.90241,0,0,0.814116,0.580702,120,255,1,'',0),
(82949,180777,530,0,0,1,1,-4016.04,-11831.7,0.122772,1.6057,0,0,0.719339,0.694659,120,255,1,'',0),
(82950,180777,530,0,0,1,1,-4019.72,-11831.8,0.095343,4.69494,0,0,0.713249,-0.700911,120,255,1,'',0),
(82951,180880,530,0,0,1,1,-4021.57,-11847.9,1.86508,3.42085,0,0,0.990268,-0.139175,120,255,1,'',0),
(82952,180881,530,0,0,1,1,-4021.98,-11847.4,1.80631,4.79966,0,0,0.675588,-0.737279,120,255,1,'',0),
(82953,180882,530,0,0,1,1,-4021.18,-11847.7,1.82134,3.76991,0,0,0.951057,-0.309016,120,255,1,'',0),
(82954,180883,530,0,0,1,1,-4021.11,-11847.3,1.80092,5.81195,0,0,0.233444,-0.97237,120,255,1,'',0),
(82955,180883,530,0,0,1,1,-4021.65,-11847,1.82936,5.44543,0,0,0.406735,-0.913546,120,255,1,'',0),
(82956,180869,530,0,0,1,1,-4023.46,-11837.8,0.015313,4.7473,0,0,0.694657,-0.719341,120,255,1,'',0),
(82957,180869,530,0,0,1,1,-4011.06,-11837.7,0.147322,4.69494,0,0,0.713249,-0.700911,120,255,1,'',0),
(82958,180766,530,0,0,1,1,-4012.71,-11842.4,0.117445,0.802851,0,0,0.390731,0.920505,120,255,1,'',0),
(82959,180766,530,0,0,1,1,-4020.12,-11847.2,0.023653,4.64258,0,0,0.731352,-0.682,120,255,1,'',0),
(82960,180766,530,0,0,1,1,-4014.78,-11847.4,0.082153,1.62316,0,0,0.725376,0.688353,120,255,1,'',0),
(82961,180766,530,0,0,1,1,-4022.49,-11842.5,0.009148,5.3058,0,0,0.469472,-0.882947,120,255,1,'',0),
(82962,180766,530,0,0,1,1,-4008.92,-11837.8,0.170679,1.36136,0,0,0.629322,0.777145,120,255,1,'',0),
(82963,180766,530,0,0,1,1,-4012.8,-11831.6,0.146643,2.14675,0,0,0.878816,0.477161,120,255,1,'',0),
(82964,180766,530,0,0,1,1,-4025.33,-11837,0.008528,4.67748,0,0,0.719341,-0.694658,120,255,1,'',0),
(82965,180766,530,0,0,1,1,-4023.5,-11831.4,0.072735,4.06662,0,0,0.894934,-0.446199,120,255,1,'',0),
(82966,180878,530,0,0,1,1,-4022.73,-11846.5,-0.003436,4.50295,0,0,0.777146,-0.629321,120,255,1,'',0),
(82967,180878,530,0,0,1,1,-4023.05,-11847.3,-0.00903,3.54302,0,0,0.979925,-0.199369,120,255,1,'',0),
(82968,180878,530,0,0,1,1,-4022.06,-11846,0.005262,2.68781,0,0,0.97437,0.22495,120,255,1,'',0),
(82969,180878,530,0,0,1,1,-4021.27,-11845.9,0.014263,5.5676,0,0,0.350208,-0.936672,120,255,1,'',0),
(82970,180878,530,0,0,1,1,-4021.34,-11848.8,0.009881,4.86947,0,0,0.649448,-0.760406,120,255,1,'',0),
(82971,180878,530,0,0,1,1,-4022.73,-11848,-0.005639,2.1293,0,0,0.874619,0.48481,120,255,1,'',0),
(82972,180878,530,0,0,1,1,-4022.06,-11848.6,0.00076,5.58505,0,0,0.342022,-0.939692,120,255,1,'',0),
(83031,180868,530,0,0,1,1,-4014.64,-11845.6,0.088204,4.69494,0,0,0.713249,-0.700911,120,255,1,'',0),
(83032,180868,530,0,0,1,1,-4020.39,-11845.4,0.025103,4.7473,0,0,0.694657,-0.719341,120,255,1,'',0),
(83033,180868,530,0,0,1,1,-4014.58,-11842.6,0.096342,4.69494,0,0,0.713249,-0.700911,120,255,1,'',0),
(83034,180868,530,0,0,1,1,-4014.2,-11835.3,0.118586,4.69494,0,0,0.713249,-0.700911,120,255,1,'',0),
(83035,180868,530,0,0,1,1,-4021.15,-11835.3,0.0539,4.7473,0,0,0.694657,-0.719341,120,255,1,'',0),
(83036,180868,530,0,0,1,1,-4020.45,-11842.3,0.032153,4.7473,0,0,0.694657,-0.719341,120,255,1,'',0),
(83037,180868,530,0,0,1,1,-4017.74,-11832.4,0.104225,4.72984,0,0,0.70091,-0.71325,120,255,1,'',0),
(91326,180698,530,0,0,1,1,-4022.35,-11878.1,0.197603,5.07891,0,0,-0.566406,0.824126,120,255,1,'',21742),
(91328,180698,530,0,0,1,1,-4023.37,-11875.7,0.504847,5.09636,0,0,-0.559193,0.829038,120,255,1,'',21742),
(91481,180699,530,0,0,1,1,-4034.79,-11887.7,-0.830214,5.18363,0,0,-0.522498,0.852641,120,255,1,'',21742),
(91482,180699,530,0,0,1,1,-4026.64,-11883.6,-0.036509,5.21854,0,0,-0.507538,0.861629,120,255,1,'',21742),
(91487,180699,530,0,0,1,1,-4030.59,-11885.4,-0.258558,5.14872,0,0,-0.537299,0.843392,120,255,1,'',21742),
(91488,180699,530,0,0,1,1,-4019.19,-11862.2,0.003737,4.86947,0,0,-0.649447,0.760406,120,255,1,'',21742),
(91489,180699,530,0,0,1,1,-3990.26,-11884.4,0.295851,4.25861,0,0,-0.848047,0.529921,120,255,1,'',21742),
(91490,180699,530,0,0,1,1,-3986.28,-11886.5,0.372531,4.18879,0,0,-0.866025,0.500001,120,255,1,'',21742),
(91491,180699,530,0,0,1,1,-3982.63,-11888.7,0.449291,4.20625,0,0,-0.861628,0.507539,120,255,1,'',21742),
(91492,180699,530,0,0,1,1,-4026.7,-11862.4,-0.081734,5.02655,0,0,-0.587785,0.809017,120,255,1,'',21742),
(91494,180699,530,0,0,1,1,-4023.12,-11861.4,-0.039385,4.95674,0,0,-0.615661,0.788011,120,255,1,'',21742),
(92045,180700,530,0,0,1,1,-4036.37,-11875.8,0.381698,0.104719,0,0,0.0523357,0.99863,120,255,1,'',21742),
(92047,180700,530,0,0,1,1,-4034.88,-11879,0.430586,4.97419,0,0,-0.608761,0.793354,120,255,1,'',21742),
(92048,180700,530,0,0,1,1,-3989.18,-11880.5,0.31943,2.60053,0,0,0.96363,0.267241,120,255,1,'',21742),
(92051,180700,530,0,0,1,1,-4014.78,-11860.4,0.056869,1.43117,0,0,0.656058,0.75471,120,255,1,'',21742),
(92052,180700,530,0,0,1,1,-4029.87,-11859.1,-0.10782,3.78737,0,0,-0.948323,0.317306,120,255,1,'',21742);


INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES 
(85532,15892,530,0,0,1,1,0,0,-4014.53,-11839.5,0.187831,2.61799,300,0,0,0,0,0,0,0,0,'',0),
(85533,15892,530,0,0,1,1,0,0,-4017.63,-11834.3,0.171095,4.74729,300,0,0,0,0,0,0,0,0,'',0),
(85534,15892,530,0,0,1,1,0,0,-4020.61,-11839.4,0.120863,0.471239,300,0,0,0,0,0,0,0,0,'',0),
(85540,15895,530,0,0,1,1,15871,0,-4014.71,-11848.5,0.163467,4.79965,300,0,0,0,0,0,0,0,0,'',0),
(85542,15897,530,0,0,1,1,0,0,-4017.51,-11837.7,0.159306,4.39823,300,0,0,0,0,0,0,0,0,'',0),
(85538,15898,530,0,0,1,1,15870,0,-4020.06,-11848.4,0.105093,4.67748,300,0,0,0,0,0,0,0,0,'',0);


INSERT INTO `game_event_creature` (`eventEntry`,`guid`) VALUES 
(7,85532),
(7,85533),
(7,85534),
(7,85540),
(7,85542),
(7,85538);

INSERT INTO `game_event_gameobject` (`eventEntry`,`guid`) VALUES 
(7,40),
(7,9729),
(7,82948),
(7,82949),
(7,82950),
(7,82951),
(7,82952),
(7,82953),
(7,82954),
(7,82955),
(7,82956),
(7,82957),
(7,82958),
(7,82959),
(7,82960),
(7,82961),
(7,82962),
(7,82963),
(7,82964),
(7,82965),
(7,82966),
(7,82967),
(7,82968),
(7,82969),
(7,82970),
(7,82971),
(7,82972),
(7,83031),
(7,83032),
(7,83033),
(7,83034),
(7,83035),
(7,83036),
(7,83037),
(7,91326),
(7,91328),
(7,91481),
(7,91482),
(7,91487),
(7,91488),
(7,91489),
(7,91490),
(7,91491),
(7,91492),
(7,91494),
(7,92045),
(7,92047),
(7,92048),
(7,92051),
(7,92052);

/* Exodar - Remove New Years GameObjects from Lunar Event - Festival Kegs, Party Tables, and Haybales 
*/
DELETE FROM `game_event_gameobject` WHERE `eventEntry` = 7 and `guid` IN(91326,91328,91481,91482,91487,91488,91489,91490,91491,91492,91494,92045,92047,92048,92051,92052);



/*Silvermoon Missing Objects and Creatures
*/
DELETE FROM `game_event_gameobject` WHERE `eventEntry` = 7 and `guid` IN (38,79775,79776,79777,79778,79779,79780,79781,79782,79793,79794,79810,79811,79812,79813,79814,79815,79816,79817,79824,79825,79832,79833,79837,79838,79842,79843,79847,79849,79864,79865,79866,79867,79868,79869,79870,79875,79876);
DELETE FROM `gameobject` WHERE `guid` IN (38,79775,79776,79777,79778,79779,79780,79781,79782,79793,79794,79810,79811,79812,79813,79814,79815,79816,79817,79824,79825,79832,79833,79837,79838,79842,79843,79847,79849,79864,79865,79866,79867,79868,79869,79870,79875,79876);
DELETE FROM `creature` WHERE `guid` IN (97689,97690,97691,97685,97694,97682);
DELETE FROM `game_event_creature` WHERE `eventEntry` = 7 and `guid` IN (97689,97690,97691,97685,97694,97682);

INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES 
(97689,15891,530,0,0,1,1,0,0,9483.58,-7296.71,14.4308,2.87979,300,0,0,0,0,0,0,0,0,'',0),
(97690,15891,530,0,0,1,1,0,0,9479.12,-7290.21,14.3603,4.69494,300,0,0,0,0,0,0,0,0,'',0),
(97691,15891,530,0,0,1,1,0,0,9475.68,-7297.99,14.436,0.750492,300,0,0,0,0,0,0,0,0,'',0),
(97685,15895,530,0,0,1,1,0,0,9487.33,-7285.74,14.3737,0.680678,300,0,0,0,0,0,0,0,0,'',0),
(97694,15897,530,0,0,1,1,0,0,9479.26,-7295.21,14.4095,4.39823,300,0,0,0,0,0,0,0,0,'',0),
(97682,15898,530,0,0,1,1,0,0,9490.62,-7290.23,14.3964,0.698132,300,0,0,0,0,0,0,0,0,'',0);

INSERT INTO `gameobject` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`position_x`,`position_y`,`position_z`,`orientation`,`rotation0`,`rotation1`,`rotation2`,`rotation3`,`spawntimesecs`,`animprogress`,`state`,`ScriptName`,`VerifiedBuild`) VALUES 
(38,180867,530,0,0,1,1,9479.35,-7295.02,14.32,0.536152,0,0,0.422307,0.906453,300,0,1,'',0),
(79775,180768,530,0,0,1,1,9487.02,-7295.95,14.3429,1.27409,0,0,0.594823,0.803857,120,255,1,'',0),
(79776,180768,530,0,0,1,1,9484.24,-7301.35,14.3824,0.872664,0,0,0.422618,0.906308,120,255,1,'',0),
(79777,180768,530,0,0,1,1,9489.61,-7291.33,14.3155,0.628317,0,0,0.309016,0.951057,120,255,1,'',0),
(79778,180768,530,0,0,1,1,9477.15,-7304.07,14.3664,0.122173,0,0,0.0610485,0.998135,120,255,1,'',0),
(79779,180768,530,0,0,1,1,9486.31,-7286.41,14.287,3.82227,0,0,0.942642,-0.333806,120,255,1,'',0),
(79780,180768,530,0,0,1,1,9475.41,-7288.15,14.2511,3.56047,0,0,0.978148,-0.207911,120,255,1,'',0),
(79781,180768,530,0,0,1,1,9481.29,-7287.19,14.267,2.87979,0,0,0.991445,0.130528,120,255,1,'',0),
(79782,180768,530,0,0,1,1,9470.74,-7293.92,14.3025,4.43314,0,0,0.798634,-0.601816,120,255,1,'',0),
(79793,180778,530,0,0,1,1,9471.94,-7298.06,14.3492,3.87463,0,0,0.933581,-0.358368,120,255,1,'',0),
(79794,180778,530,0,0,1,1,9474.56,-7301.35,14.371,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79810,180878,530,0,0,1,1,9491.68,-7292.46,14.3297,0.104719,0,0,0.0523356,0.99863,120,255,1,'',0),
(79811,180878,530,0,0,1,1,9490.62,-7293.79,14.3365,1.41372,0,0,0.649449,0.760405,120,255,1,'',0),
(79812,180878,530,0,0,1,1,9491.37,-7293.29,14.3349,4.24115,0,0,0.85264,-0.522498,120,255,1,'',0),
(79813,180878,530,0,0,1,1,9489.72,-7293.83,14.3342,3.08918,0,0,0.999657,0.0262033,120,255,1,'',0),
(79814,180878,530,0,0,1,1,9488.96,-7293.35,14.3284,6.00393,0,0,0.139174,-0.990268,120,255,1,'',0),
(79815,180878,530,0,0,1,1,9491.27,-7291.56,14.3219,3.85718,0,0,0.936672,-0.350209,120,255,1,'',0),
(79816,180878,530,0,0,1,1,9488.67,-7292.52,14.3215,4.01426,0,0,0.906307,-0.422619,120,255,1,'',0),
(79817,180878,530,0,0,1,1,9488.97,-7291.69,14.3163,5.77704,0,0,0.25038,-0.968148,120,255,1,'',0),
(79824,180764,530,0,0,1,1,9489.19,-7290.49,15.2951,3.9619,0,0,0.91706,-0.39875,120,255,1,'',0),
(79825,180764,530,0,0,1,1,9471.67,-7294.08,15.2562,0.698131,0,0,0.34202,0.939693,120,255,1,'',0),
(79832,180880,530,0,0,1,1,9490.52,-7292.75,15.0382,2.74016,0,0,0.979924,0.199371,120,255,1,'',0),
(79833,180880,530,0,0,1,1,9490.09,-7292.49,15.0417,1.98967,0,0,0.838669,0.544641,120,255,1,'',0),
(79837,180881,530,0,0,1,1,9489.58,-7292.59,15.0253,4.57276,0,0,0.75471,-0.656058,120,255,1,'',0),
(79838,180881,530,0,0,1,1,9489.87,-7292.06,15.0151,6.14356,0,0,0.069756,-0.997564,120,255,1,'',0),
(79842,180882,530,0,0,1,1,9490.71,-7292.34,15.0193,4.95674,0,0,0.61566,-0.788012,120,255,1,'',0),
(79843,180882,530,0,0,1,1,9490.47,-7292.04,15.0279,3.64774,0,0,0.968147,-0.250381,120,255,1,'',0),
(79847,180883,530,0,0,1,1,9490.11,-7293.04,15.0314,5.65487,0,0,0.309015,-0.951057,120,255,1,'',0),
(79849,180888,530,0,0,1,1,9489.95,-7292.67,14.3263,1.93731,0,0,0.824125,0.566409,120,255,1,'',0),
(79864,180868,530,0,0,1,1,9487.39,-7292.86,14.3203,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79865,180868,530,0,0,1,1,9485.13,-7294.79,14.3281,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79866,180868,530,0,0,1,1,9483.84,-7288.15,14.2774,0.715585,0,0,0.350207,0.936672,120,255,1,'',0),
(79867,180868,530,0,0,1,1,9478.39,-7299.73,14.3748,0.750491,0,0,0.366501,0.930418,120,255,1,'',0),
(79868,180868,530,0,0,1,1,9474,-7299.41,14.3664,0.733038,0,0,0.358368,0.93358,120,255,1,'',0),
(79869,180868,530,0,0,1,1,9481.45,-7289.98,14.282,0.715585,0,0,0.350207,0.936672,120,255,1,'',0),
(79870,180868,530,0,0,1,1,9474.83,-7294.81,14.3169,0.715585,0,0,0.350207,0.936672,120,255,1,'',0),
(79875,180869,530,0,0,1,1,9482.43,-7299.7,14.379,2.44346,0,0,0.939692,0.342021,120,255,1,'',0),
(79876,180869,530,0,0,1,1,9476.03,-7289.98,14.2663,5.14872,0,0,0.5373,-0.843391,120,255,1,'',0);

INSERT INTO `game_event_creature` (`eventEntry`,`guid`) VALUES 
(7,97689),
(7,97690),
(7,97691),
(7,97685),
(7,97694),
(7,97682);


INSERT INTO `game_event_gameobject` (`eventEntry`,`guid`) VALUES 
(7,38),
(7,79775),
(7,79776),
(7,79777),
(7,79778),
(7,79779),
(7,79780),
(7,79781),
(7,79782),
(7,79793),
(7,79794),
(7,79810),
(7,79811),
(7,79812),
(7,79813),
(7,79814),
(7,79815),
(7,79816),
(7,79817),
(7,79824),
(7,79825),
(7,79832),
(7,79833),
(7,79837),
(7,79838),
(7,79842),
(7,79843),
(7,79847),
(7,79849),
(7,79864),
(7,79865),
(7,79866),
(7,79867),
(7,79868),
(7,79869),
(7,79870),
(7,79875),
(7,79876);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
