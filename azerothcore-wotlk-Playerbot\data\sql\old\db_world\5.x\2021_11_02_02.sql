-- DB update 2021_11_02_01 -> 2021_11_02_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_02_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_02_01 2021_11_02_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635438058134962700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635438058134962700');

SET @NPC_REND := 10429;
DELETE FROM `creature_summon_groups` WHERE `summonerId` = 10429;
INSERT INTO `creature_summon_groups` (`summonerId`, `summonerType`, `groupId`, `entry`, `position_x`, `position_y`, `position_z`, `orientation`, `summonType`, `summonTime`) VALUES
(@NPC_REND, 0, 0, 10447, 202.511, -421.307, 110.9877, 0.0, 4, 30000),
(@NPC_REND, 0, 0, 10442, 204.015, -418.443, 110.989, 0.0, 4, 30000),
(@NPC_REND, 0, 0, 10442, 203.142, -423.999, 110.986, 0.0, 4, 30000),
(@NPC_REND, 0, 0, 10442, 201.008, -416.648, 110.974, 0.0, 4, 30000),
(@NPC_REND, 0, 1, 10447, 209.8637, -428.2729, 110.9877, 0.0, 4, 30000),
(@NPC_REND, 0, 1, 10442, 209.3122, -430.8724, 110.9814, 0.0, 4, 30000),
(@NPC_REND, 0, 1, 10442, 211.3309, -425.9111, 111.0006, 0.0, 4, 30000),
(@NPC_REND, 0, 2, 10742, 208.6493, -424.5787, 110.9872, 0.0, 4, 30000),
(@NPC_REND, 0, 2, 10447, 203.9482, -428.9446, 110.982, 0.0, 4, 30000),
(@NPC_REND, 0, 2, 10442, 203.3441, -426.8668, 110.9772, 0.0, 4, 30000),
(@NPC_REND, 0, 2, 10442, 206.3079, -424.7509, 110.9943, 0.0, 4, 30000),
(@NPC_REND, 0, 3, 10742, 212.3541, -412.6826, 111.0352, 0.0, 4, 30000),
(@NPC_REND, 0, 3, 10447, 212.5754, -410.2841, 111.0296, 0.0, 4, 30000),
(@NPC_REND, 0, 3, 10442, 212.3449, -414.8659, 111.0348, 0.0, 4, 30000),
(@NPC_REND, 0, 3, 10442, 210.6568, -412.1552, 111.0124, 0.0, 4, 30000),
(@NPC_REND, 0, 4, 10742, 210.2188, -410.6686, 111.0211, 0.0, 4, 30000),
(@NPC_REND, 0, 4, 10447, 209.4078, -414.13,   111.0264, 0.0, 4, 30000),
(@NPC_REND, 0, 4, 10442, 208.0858, -409.3145, 111.0118, 0.0, 4, 30000),
(@NPC_REND, 0, 4, 10442, 207.9811, -413.0728, 111.0098, 0.0, 4, 30000),
(@NPC_REND, 0, 4, 10442, 208.0854, -412.1505, 111.0057, 0.0, 4, 30000),
(@NPC_REND, 0, 5, 10742, 213.9138, -426.512,  111.0013, 0.0, 4, 30000),
(@NPC_REND, 0, 5, 10447, 213.7121, -429.8102, 110.9888, 0.0, 4, 30000),
(@NPC_REND, 0, 5, 10447, 213.7157, -424.4268, 111.009, 0.0, 4, 30000),
(@NPC_REND, 0, 5, 10442, 210.8935, -423.913,  111.0125, 0.0, 4, 30000),
(@NPC_REND, 0, 5, 10442, 212.2642, -430.7648, 110.9807, 0.0, 4, 30000);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_02_02' WHERE sql_rev = '1635438058134962700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
