-- DB update 2020_05_03_00 -> 2020_05_04_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_05_03_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_05_03_00 2020_05_04_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1585441940559204300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1585441940559204300');

DELETE FROM `creature_addon` WHERE `GUID` IN (127996,128030,127997,128029,127995,127998);
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `isLarge`, `auras`) VALUES 
(127996, 0, 0, 8, 1, 0, 1, '21157'),
(128030, 0, 0, 8, 1, 0, 1, '21157'),
(128029, 0, 0, 8, 1, 0, 1, '21157'),
(127998, 0, 0, 8, 1, 0, 1, '21157'),
(127995, 0, 0, 8, 1, 0, 1, '21157'),
(127997, 0, 0, 8, 1, 0, 1, '21157');

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
