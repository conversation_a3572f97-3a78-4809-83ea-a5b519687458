-- DB update 2021_03_14_11 -> 2021_03_14_12
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_14_11';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_14_11 2021_03_14_12 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1615649181432525500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1615649181432525500');

DELETE FROM `game_event_gameobject` WHERE (`eventEntry` = 24 AND `guid` = 59179);
INSERT INTO `game_event_gameobject` VALUES (24, 59179);

DELETE FROM `game_event_gameobject` WHERE (`eventEntry` = 24 AND `guid` = 59180);
INSERT INTO `game_event_gameobject` VALUES (24, 59180);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
