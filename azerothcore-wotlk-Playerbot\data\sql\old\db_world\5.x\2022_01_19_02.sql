-- DB update 2022_01_19_01 -> 2022_01_19_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_19_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_19_01 2022_01_19_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1642555566702599074'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1642555566702599074');

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57969;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=8.295194,`position_y`=2012.2843,`position_z`=76.31958 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,8.295194,2012.2843,76.31958,0,0,0,0,100,0),
(@PATH,2,33.496746,2037.1613,76.92554,0,0,0,0,100,0),
(@PATH,3,46.847874,2074.0833,82.62042,0,0,0,0,100,0),
(@PATH,4,63.019424,2105.5737,83.156044,0,0,0,0,100,0),
(@PATH,5,68.13243,2136.0315,86.08387,0,0,0,0,100,0),
(@PATH,6,77.91905,2168.4329,88.159294,0,0,0,0,100,0),
(@PATH,7,74.599014,2206.2634,82.47779,0,0,0,0,100,0),
(@PATH,8,81.946075,2252.14,69.45474,0,0,0,0,100,0),
(@PATH,9,76.03722,2285.3948,71.39897,0,0,0,0,100,0),
(@PATH,10,75.76508,2320.6042,69.450485,0,0,0,0,100,0),
(@PATH,11,76.03722,2285.3948,71.39897,0,0,0,0,100,0),
(@PATH,12,81.946075,2252.14,69.45474,0,0,0,0,100,0),
(@PATH,13,74.599014,2206.2634,82.47779,0,0,0,0,100,0),
(@PATH,14,77.91905,2168.4329,88.159294,0,0,0,0,100,0),
(@PATH,15,68.13243,2136.0315,86.08387,0,0,0,0,100,0),
(@PATH,16,63.019424,2105.5737,83.156044,0,0,0,0,100,0),
(@PATH,17,46.847874,2074.0833,82.62042,0,0,0,0,100,0),
(@PATH,18,33.496746,2037.1613,76.92554,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57970;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=181.16841,`position_y`=2505.0637,`position_z`=64.536674 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,181.16841,2505.0637,64.536674,0,0,0,0,100,0),
(@PATH,2,171.57341,2488.3018,60.924515,0,0,0,0,100,0),
(@PATH,3,178.24959,2457.8325,58.006622,0,0,0,0,100,0),
(@PATH,4,207.31749,2427.6375,55.46733,0,0,0,0,100,0),
(@PATH,5,220.56502,2395.725,53.927708,0,0,0,0,100,0),
(@PATH,6,234.39107,2375.9763,59.015415,0,0,0,0,100,0),
(@PATH,7,280.83487,2361.2205,69.48169,0,0,0,0,100,0),
(@PATH,8,314.2502,2333.4038,73.8147,0,0,0,0,100,0),
(@PATH,9,316.9781,2294.2021,72.17517,0,0,0,0,100,0),
(@PATH,10,322.6038,2316.8164,75.06119,0,0,0,0,100,0),
(@PATH,11,317.63123,2359.3347,80.14819,0,0,0,0,100,0),
(@PATH,12,314.56937,2381.7844,84.60368,0,0,0,0,100,0),
(@PATH,13,307.9707,2412.0576,89.32112,0,0,0,0,100,0),
(@PATH,14,283.767,2429.4563,85.64797,0,0,0,0,100,0),
(@PATH,15,279.55664,2457.128,92.247505,0,0,0,0,100,0),
(@PATH,16,275.2421,2482.7346,99.57844,0,0,0,0,100,0),
(@PATH,17,257.4479,2494.1,99.81812,0,0,0,0,100,0),
(@PATH,18,234.26169,2494.6782,96.58546,0,0,0,0,100,0),
(@PATH,19,257.4479,2494.1,99.81812,0,0,0,0,100,0),
(@PATH,20,275.2421,2482.7346,99.57844,0,0,0,0,100,0),
(@PATH,21,279.53424,2457.145,92.30048,0,0,0,0,100,0),
(@PATH,22,283.7461,2429.4727,85.626854,0,0,0,0,100,0),
(@PATH,23,307.93375,2412.166,89.38716,0,0,0,0,100,0),
(@PATH,24,314.56937,2381.7844,84.60368,0,0,0,0,100,0),
(@PATH,25,317.63123,2359.3347,80.14819,0,0,0,0,100,0),
(@PATH,26,322.6038,2316.8164,75.06119,0,0,0,0,100,0),
(@PATH,27,316.9781,2294.2021,72.17517,0,0,0,0,100,0),
(@PATH,28,314.2502,2333.4038,73.8147,0,0,0,0,100,0),
(@PATH,29,280.83487,2361.2205,69.48169,0,0,0,0,100,0),
(@PATH,30,234.39107,2375.9763,59.015415,0,0,0,0,100,0),
(@PATH,31,220.60938,2395.6406,53.927708,0,0,0,0,100,0),
(@PATH,32,207.31749,2427.6375,55.46733,0,0,0,0,100,0),
(@PATH,33,178.24959,2457.8325,58.006622,0,0,0,0,100,0),
(@PATH,34,171.57341,2488.3018,60.924515,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57971;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=720.35065,`position_y`=2825.4783,`position_z`=25.85273 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,720.35065,2825.4783,25.85273,0,0,0,0,100,0),
(@PATH,2,686.37177,2846.2114,28.813393,0,0,0,0,100,0),
(@PATH,3,676.84235,2872.2954,25.961529,0,0,0,0,100,0),
(@PATH,4,640.67706,2894.3665,36.36252,0,0,0,0,100,0),
(@PATH,5,610.6246,2885.1306,42.125164,0,0,0,0,100,0),
(@PATH,6,583.09015,2893.734,38.302986,0,0,0,0,100,0),
(@PATH,7,559.14777,2908.1104,34.19982,0,0,0,0,100,0),
(@PATH,8,546.429,2932.496,29.806025,0,0,0,0,100,0),
(@PATH,9,526.55396,2960.3628,22.698233,0,0,0,0,100,0),
(@PATH,10,510.2692,2993.0586,22.68488,0,0,0,0,100,0),
(@PATH,11,511.05246,3023.708,18.860674,0,0,0,0,100,0),
(@PATH,12,486.56836,3043.3381,17.97623,0,0,0,0,100,0),
(@PATH,13,458.99142,3068.345,15.644532,0,0,0,0,100,0),
(@PATH,14,451.2187,3095.6758,14.0426035,0,0,0,0,100,0),
(@PATH,15,466.7436,3131.79,20.927288,0,0,0,0,100,0),
(@PATH,16,478.34048,3165.3127,26.066383,0,0,0,0,100,0),
(@PATH,17,509.54358,3178.3848,26.157913,0,0,0,0,100,0),
(@PATH,18,478.34048,3165.3127,26.066383,0,0,0,0,100,0),
(@PATH,19,466.7436,3131.79,20.927288,0,0,0,0,100,0),
(@PATH,20,451.2187,3095.6758,14.0426035,0,0,0,0,100,0),
(@PATH,21,458.99142,3068.345,15.644532,0,0,0,0,100,0),
(@PATH,22,486.56836,3043.3381,17.97623,0,0,0,0,100,0),
(@PATH,23,511.05246,3023.708,18.860674,0,0,0,0,100,0),
(@PATH,24,510.2692,2993.0586,22.68488,0,0,0,0,100,0),
(@PATH,25,526.55396,2960.3628,22.698233,0,0,0,0,100,0),
(@PATH,26,546.429,2932.496,29.806025,0,0,0,0,100,0),
(@PATH,27,559.14777,2908.1104,34.19982,0,0,0,0,100,0),
(@PATH,28,583.09015,2893.734,38.302986,0,0,0,0,100,0),
(@PATH,29,610.6246,2885.1306,42.125164,0,0,0,0,100,0),
(@PATH,30,640.67706,2894.3665,36.36252,0,0,0,0,100,0),
(@PATH,31,676.84235,2872.2954,25.961529,0,0,0,0,100,0),
(@PATH,32,686.37177,2846.2114,28.813393,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57972;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=138.20964,`position_y`=2929.8398,`position_z`=27.948868 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,138.20964,2929.8398,27.948868,0,0,0,0,100,0),
(@PATH,2,108.16005,2942.1846,26.31901,0,0,0,0,100,0),
(@PATH,3,62.416885,2952.5361,23.40034,0,0,0,0,100,0),
(@PATH,4,20.320312,2958.7874,21.337893,0,0,0,0,100,0),
(@PATH,5,-12.235894,2957.0247,20.917387,0,0,0,0,100,0),
(@PATH,6,-39.027237,2943.3308,23.192675,0,0,0,0,100,0),
(@PATH,7,-53.78049,2919.4404,27.9341,0,0,0,0,100,0),
(@PATH,8,-51.727215,2880.2183,37.318672,0,0,0,0,100,0),
(@PATH,9,-53.788956,2847.0488,43.81383,0,0,0,0,100,0),
(@PATH,10,-68.28125,2815.1934,49.48897,0,0,0,0,100,0),
(@PATH,11,-87.62652,2790.0005,50.711437,0,0,0,0,100,0),
(@PATH,12,-68.32411,2815.049,49.48409,0,0,0,0,100,0),
(@PATH,13,-53.788956,2847.0488,43.81383,0,0,0,0,100,0),
(@PATH,14,-51.727215,2880.2183,37.318672,0,0,0,0,100,0),
(@PATH,15,-53.78049,2919.4404,27.9341,0,0,0,0,100,0),
(@PATH,16,-39.070312,2943.2568,23.204882,0,0,0,0,100,0),
(@PATH,17,-12.235894,2957.0247,20.917387,0,0,0,0,100,0),
(@PATH,18,20.320312,2958.7874,21.337893,0,0,0,0,100,0),
(@PATH,19,62.416885,2952.5361,23.40034,0,0,0,0,100,0),
(@PATH,20,108.16005,2942.1846,26.31901,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57973;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=328.75543,`position_y`=3132.6704,`position_z`=32.57294 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,328.75543,3132.6704,32.57294,0,0,0,0,100,0),
(@PATH,2,343.30386,3105.2456,28.2042,0,0,0,0,100,0),
(@PATH,3,366.42053,3082.0593,19.751076,0,0,0,0,100,0),
(@PATH,4,406.7754,3078.214,13.98882,0,0,0,0,100,0),
(@PATH,5,426.9999,3061.6768,14.47884,0,0,0,0,100,0),
(@PATH,6,452.44693,3031.2207,17.882639,0,0,0,0,100,0),
(@PATH,7,469.74002,2996.6829,22.158552,0,0,0,0,100,0),
(@PATH,8,489.3876,2971.8218,27.663557,0,0,0,0,100,0),
(@PATH,9,515.73285,2957.4207,25.678335,0,0,0,0,100,0),
(@PATH,10,525.555,2939.1284,34.932,0,0,0,0,100,0),
(@PATH,11,558.55743,2935.4775,24.87055,0,0,0,0,100,0),
(@PATH,12,587.12286,2925.163,22.13646,0,0,0,0,100,0),
(@PATH,13,558.55743,2935.4775,24.87055,0,0,0,0,100,0),
(@PATH,14,525.555,2939.1284,34.932,0,0,0,0,100,0),
(@PATH,15,515.73285,2957.4207,25.678335,0,0,0,0,100,0),
(@PATH,16,489.42383,2971.8057,27.69798,0,0,0,0,100,0),
(@PATH,17,469.74002,2996.6829,22.158552,0,0,0,0,100,0),
(@PATH,18,452.44693,3031.2207,17.882639,0,0,0,0,100,0),
(@PATH,19,426.9999,3061.6768,14.47884,0,0,0,0,100,0),
(@PATH,20,406.7754,3078.214,13.98882,0,0,0,0,100,0),
(@PATH,21,366.42053,3082.0593,19.751076,0,0,0,0,100,0),
(@PATH,22,343.30386,3105.2456,28.2042,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57974;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-83.67491,`position_y`=2500.1985,`position_z`=49.760178 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-84.10547,2504.0771,49.85369,0,0,0,0,100,0),
(@PATH,2,-83.84098,2549.0774,52.13395,0,0,0,0,100,0),
(@PATH,3,-81.37788,2583.2148,54.19162,0,0,0,0,100,0),
(@PATH,4,-81.02398,2622.9238,55.314896,0,0,0,0,100,0),
(@PATH,5,-83.49783,2658.0793,55.408463,0,0,0,0,100,0),
(@PATH,6,-86.2136,2690.4236,55.01814,0,0,0,0,100,0),
(@PATH,7,-75.92155,2719.071,56.71374,0,0,0,0,100,0),
(@PATH,8,-57.306858,2749.1028,59.48336,0,0,0,0,100,0),
(@PATH,9,-55.716797,2786.0664,55.713142,0,0,0,0,100,0),
(@PATH,10,-79.43848,2812.135,48.63619,0,0,0,0,100,0),
(@PATH,11,-55.85547,2786.295,55.713142,0,0,0,0,100,0),
(@PATH,12,-57.306858,2749.1028,59.48336,0,0,0,0,100,0),
(@PATH,13,-75.92155,2719.071,56.71374,0,0,0,0,100,0),
(@PATH,14,-86.2136,2690.4236,55.01814,0,0,0,0,100,0),
(@PATH,15,-83.49783,2658.0793,55.408463,0,0,0,0,100,0),
(@PATH,16,-81.02398,2622.9238,55.314896,0,0,0,0,100,0),
(@PATH,17,-81.37788,2583.2148,54.19162,0,0,0,0,100,0),
(@PATH,18,-83.84098,2549.0774,52.13395,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57975;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-421.32877,`position_y`=2356.8604,`position_z`=41.383045 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-421.32877,2356.8604,41.383045,0,0,0,0,100,0),
(@PATH,2,-443.09863,2382.4072,39.052135,0,0,0,0,100,0),
(@PATH,3,-451.48352,2415.8887,38.445362,0,0,0,0,100,0),
(@PATH,4,-461.32922,2453.5374,39.71805,0,0,0,0,100,0),
(@PATH,5,-445.53342,2485.3723,43.007282,0,0,0,0,100,0),
(@PATH,6,-414.65344,2521.4314,46.87212,0,0,0,0,100,0),
(@PATH,7,-426.7221,2558.308,52.113834,0,0,0,0,100,0),
(@PATH,8,-440.02344,2585.2031,57.010277,0,0,0,0,100,0),
(@PATH,9,-433.0497,2615.3618,56.996197,0,0,0,0,100,0),
(@PATH,10,-436.9146,2652.672,56.565197,0,0,0,0,100,0),
(@PATH,11,-435.49533,2699.4504,51.11733,0,0,0,0,100,0),
(@PATH,12,-467.338,2732.6033,54.486683,0,0,0,0,100,0),
(@PATH,13,-495.2539,2768.5835,56.009205,0,0,0,0,100,0),
(@PATH,14,-499.77042,2811.7102,50.16045,0,0,0,0,100,0),
(@PATH,15,-495.2539,2768.5835,56.009205,0,0,0,0,100,0),
(@PATH,16,-467.338,2732.6033,54.486683,0,0,0,0,100,0),
(@PATH,17,-435.49533,2699.4504,51.11733,0,0,0,0,100,0),
(@PATH,18,-436.9146,2652.672,56.565197,0,0,0,0,100,0),
(@PATH,19,-433.0497,2615.3618,56.996197,0,0,0,0,100,0),
(@PATH,20,-440.02408,2585.2644,57.044823,0,0,0,0,100,0),
(@PATH,21,-426.7221,2558.308,52.113834,0,0,0,0,100,0),
(@PATH,22,-414.65344,2521.4314,46.87212,0,0,0,0,100,0),
(@PATH,23,-445.53342,2485.3723,43.007282,0,0,0,0,100,0),
(@PATH,24,-461.32922,2453.5374,39.71805,0,0,0,0,100,0),
(@PATH,25,-451.48352,2415.8887,38.445362,0,0,0,0,100,0),
(@PATH,26,-443.09863,2382.4072,39.052135,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57976;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-289.67465,`position_y`=2021.5533,`position_z`=107.82463 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-289.67465,2021.5533,107.82463,0,0,0,0,100,0),
(@PATH,2,-289.12555,2047.1936,107.95394,0,0,0,0,100,0),
(@PATH,3,-291.53885,2077.3457,101.23334,0,0,0,0,100,0),
(@PATH,4,-314.32367,2098.2454,96.51018,0,0,0,0,100,0),
(@PATH,5,-353.80658,2095.0652,92.87671,0,0,0,0,100,0),
(@PATH,6,-394.5636,2084.4602,91.70854,0,0,0,0,100,0),
(@PATH,7,-417.7207,2072.6533,92.447334,0,0,0,0,100,0),
(@PATH,8,-443.98026,2043.4861,92.14273,0,0,0,0,100,0),
(@PATH,9,-456.3263,2010.9495,90.96744,0,0,0,0,100,0),
(@PATH,10,-484.16125,1983.4874,86.91389,0,0,0,0,100,0),
(@PATH,11,-512.6617,1969.9735,84.01707,0,0,0,0,100,0),
(@PATH,12,-532.83325,1934.6576,82.477516,0,0,0,0,100,0),
(@PATH,13,-512.6617,1969.9735,84.01707,0,0,0,0,100,0),
(@PATH,14,-484.16125,1983.4874,86.91389,0,0,0,0,100,0),
(@PATH,15,-456.3263,2010.9495,90.96744,0,0,0,0,100,0),
(@PATH,16,-443.98026,2043.4861,92.14273,0,0,0,0,100,0),
(@PATH,17,-417.84137,2072.6082,92.42182,0,0,0,0,100,0),
(@PATH,18,-394.5636,2084.4602,91.70854,0,0,0,0,100,0),
(@PATH,19,-353.80658,2095.0652,92.87671,0,0,0,0,100,0),
(@PATH,20,-314.32367,2098.2454,96.51018,0,0,0,0,100,0),
(@PATH,21,-291.53885,2077.3457,101.23334,0,0,0,0,100,0),
(@PATH,22,-289.12555,2047.1936,107.95394,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57977;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-1239.3245,`position_y`=2350.4036,`position_z`=54.66286 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-1239.3245,2350.4036,58.12886,0,0,0,0,100,0),
(@PATH,2,-1205.2224,2361.5444,45.269142,0,0,0,0,100,0),
(@PATH,3,-1187.9047,2374.5781,40.35732,0,0,0,0,100,0),
(@PATH,4,-1149.5396,2380.003,31.525442,0,0,0,0,100,0),
(@PATH,5,-1113.2168,2390.643,24.5731,0,0,0,0,100,0),
(@PATH,6,-1085.6978,2409.2864,22.101404,0,0,0,0,100,0),
(@PATH,7,-1047.9436,2408.7175,16.679092,0,0,0,0,100,0),
(@PATH,8,-1016.0717,2407.7344,11.688957,0,0,0,0,100,0),
(@PATH,9,-975.8137,2394.809,3.9967422,0,0,0,0,100,0),
(@PATH,10,-960.03754,2348.5583,-1.3964663,0,0,0,0,100,0),
(@PATH,11,-947.78204,2307.4128,-0.5295992,0,0,0,0,100,0),
(@PATH,12,-941.23956,2274.3655,3.4348545,0,0,0,0,100,0),
(@PATH,13,-921.4207,2240.2004,5.3701334,0,0,0,0,100,0),
(@PATH,14,-905.4365,2209.5903,9.840721,0,0,0,0,100,0),
(@PATH,15,-921.4207,2240.2004,5.3701334,0,0,0,0,100,0),
(@PATH,16,-941.23956,2274.3655,3.4348545,0,0,0,0,100,0),
(@PATH,17,-947.78204,2307.4128,-0.5295992,0,0,0,0,100,0),
(@PATH,18,-960.03754,2348.5583,-1.3964663,0,0,0,0,100,0),
(@PATH,19,-975.8137,2394.809,3.9967422,0,0,0,0,100,0),
(@PATH,20,-1016.0717,2407.7344,11.688957,0,0,0,0,100,0),
(@PATH,21,-1047.9436,2408.7175,16.679092,0,0,0,0,100,0),
(@PATH,22,-1085.6978,2409.2864,22.101404,0,0,0,0,100,0),
(@PATH,23,-1113.2168,2390.643,24.5731,0,0,0,0,100,0),
(@PATH,24,-1149.5396,2380.003,31.525442,0,0,0,0,100,0),
(@PATH,25,-1187.9047,2374.5781,40.35732,0,0,0,0,100,0),
(@PATH,26,-1205.2224,2361.5444,46.03817,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57978;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-803.32965,`position_y`=1894.6237,`position_z`=57.39513 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-803.32965,1894.6237,57.39513,0,0,0,0,100,0),
(@PATH,2,-766.0576,1913.7157,53.352142,0,0,0,0,100,0),
(@PATH,3,-742.85754,1921.5634,52.41037,0,0,0,0,100,0),
(@PATH,4,-701.6173,1938.5289,52.594826,0,0,0,0,100,0),
(@PATH,5,-661.44183,1964.1454,57.51864,0,0,0,0,100,0),
(@PATH,6,-633.4128,1996.2402,67.2468,0,0,0,0,100,0),
(@PATH,7,-614.7972,2024.038,73.80646,0,0,0,0,100,0),
(@PATH,8,-610.10406,2053.9548,74.258255,0,0,0,0,100,0),
(@PATH,9,-614.33215,2088.6108,68.71945,0,0,0,0,100,0),
(@PATH,10,-619.6321,2124.472,60.59087,0,0,0,0,100,0),
(@PATH,11,-631.5979,2153.6226,49.89872,0,0,0,0,100,0),
(@PATH,12,-619.6321,2124.472,60.59087,0,0,0,0,100,0),
(@PATH,13,-614.33215,2088.6108,68.71945,0,0,0,0,100,0),
(@PATH,14,-610.10406,2053.9548,74.258255,0,0,0,0,100,0),
(@PATH,15,-614.7972,2024.038,73.80646,0,0,0,0,100,0),
(@PATH,16,-633.4128,1996.2402,67.2468,0,0,0,0,100,0),
(@PATH,17,-661.44183,1964.1454,57.51864,0,0,0,0,100,0),
(@PATH,18,-701.6173,1938.5289,52.594826,0,0,0,0,100,0),
(@PATH,19,-742.85754,1921.5634,52.41037,0,0,0,0,100,0),
(@PATH,20,-766.0576,1913.7157,53.352142,0,0,0,0,100,0);

-- Pathing for Crust Burster Entry: 16844
SET @NPC := 57979;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-1063.0006,`position_y`=2856.4304,`position_z`=-5.64365 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-1063.0006,2856.4304,-5.64365,0,0,0,0,100,0),
(@PATH,2,-1103.8293,2868.3142,-5.4611936,0,0,0,0,100,0),
(@PATH,3,-1141.3739,2879.6785,-6.605771,0,0,0,0,100,0),
(@PATH,4,-1183.7292,2891.6228,-4.238922,0,0,0,0,100,0),
(@PATH,5,-1209.9443,2914.617,-0.70197463,0,0,0,0,100,0),
(@PATH,6,-1229.7311,2951.8562,4.463394,0,0,0,0,100,0),
(@PATH,7,-1240.1274,2981.0012,10.688618,0,0,0,0,100,0),
(@PATH,8,-1234.7026,3010.4053,15.914128,0,0,0,0,100,0),
(@PATH,9,-1213.0555,3025.3926,17.684334,0,0,0,0,100,0),
(@PATH,10,-1186.9775,3039.5442,19.595737,0,0,0,0,100,0),
(@PATH,11,-1150.0183,3040.341,18.73265,0,0,0,0,100,0),
(@PATH,12,-1119.0194,3043.4421,18.773407,0,0,0,0,100,0),
(@PATH,13,-1163.8893,3052.337,22.92186,0,0,0,0,100,0),
(@PATH,14,-1211.845,3055.0747,24.418427,0,0,0,0,100,0),
(@PATH,15,-1251.9452,3082.5735,31.462637,0,0,0,0,100,0),
(@PATH,16,-1211.845,3055.0747,24.418427,0,0,0,0,100,0),
(@PATH,17,-1163.8893,3052.337,22.92186,0,0,0,0,100,0),
(@PATH,18,-1119.0194,3043.4421,18.773407,0,0,0,0,100,0),
(@PATH,19,-1150.0183,3040.341,18.73265,0,0,0,0,100,0),
(@PATH,20,-1186.9775,3039.5442,19.595737,0,0,0,0,100,0),
(@PATH,21,-1213.0555,3025.3926,17.684334,0,0,0,0,100,0),
(@PATH,22,-1234.7026,3010.4053,15.914128,0,0,0,0,100,0),
(@PATH,23,-1240.1274,2981.0012,10.688618,0,0,0,0,100,0),
(@PATH,24,-1229.7311,2951.8562,4.463394,0,0,0,0,100,0),
(@PATH,25,-1209.9443,2914.617,-0.70197463,0,0,0,0,100,0),
(@PATH,26,-1183.7292,2891.6228,-4.238922,0,0,0,0,100,0),
(@PATH,27,-1141.3739,2879.6785,-6.605771,0,0,0,0,100,0),
(@PATH,28,-1103.8293,2868.3142,-5.4611936,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_19_02' WHERE sql_rev = '1642555566702599074';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
