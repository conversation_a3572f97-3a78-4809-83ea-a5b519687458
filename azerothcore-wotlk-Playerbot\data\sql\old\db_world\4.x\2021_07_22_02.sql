-- DB update 2021_07_22_01 -> 2021_07_22_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_22_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_22_01 2021_07_22_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1626468468863337100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1626468468863337100');

-- Move herb above ground
UPDATE `gameobject` SET `position_z` = 32.195 WHERE `id` = 2046 AND `guid` = 8802;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_22_02' WHERE sql_rev = '1626468468863337100';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
