-- DB update 2019_06_19_00 -> 2019_06_19_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_06_19_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_06_19_00 2019_06_19_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1560207158398773011'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1560207158398773011');

DELETE FROM `creature` WHERE `guid` = 201215;
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `spawndist`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`)
VALUES
(201215,32485,571,0,0,1,1,0,0,6508.2,5303.38,-40.6842,3.03394,28800,0,0,42540,0,2,0,0,0,'',0);

DELETE FROM `pool_creature` WHERE `guid` = 201215;
INSERT INTO `pool_creature` (`guid`, `pool_entry`, `chance`, `description`)
VALUES
(201215,1102,0,'King Krush Spawnlocation 3');

UPDATE `creature` SET `MovementType` = 2 WHERE `guid` IN (152002,152003);

DELETE FROM `creature_addon` WHERE `guid` IN (201215,152002,152003);
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `auras`)
VALUES
(201215,2012150,0,0,0,0,NULL),
(152002,1520020,0,0,0,0,NULL),
(152003,1520030,0,0,0,0,NULL);

DELETE FROM `smart_scripts` WHERE `entryorguid` = 32485 AND `source_type` = 0 AND `id` = 3;
INSERT INTO `smart_scripts` (`entryorguid`, `source_type`, `id`, `link`, `event_type`, `event_phase_mask`, `event_chance`, `event_flags`, `event_param1`, `event_param2`, `event_param3`, `event_param4`, `event_param5`, `action_type`, `action_param1`, `action_param2`, `action_param3`, `action_param4`, `action_param5`, `action_param6`, `target_type`, `target_param1`, `target_param2`, `target_param3`, `target_param4`, `target_x`, `target_y`, `target_z`, `target_o`, `comment`)
VALUES
(32485,0,3,0,11,0,100,0,0,0,0,0,0,48,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'King Krush - On Respawn - Set Active On');

DELETE FROM `waypoint_data` WHERE `id` = 2012150;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`)
VALUES
(2012150,1,6508.2,5303.38,-40.6842,0,0,0,0,100,0),
(2012150,2,6489.09,5306.5,-41.3717,0,0,0,0,100,0),
(2012150,3,6464.12,5308.56,-42.9405,0,0,0,0,100,0),
(2012150,4,6456.08,5324.93,-39.5496,0,0,0,0,100,0),
(2012150,5,6450.7,5339.45,-36.1915,0,0,0,0,100,0),
(2012150,6,6442.28,5343.49,-37.9123,0,0,0,0,100,0),
(2012150,7,6433.53,5348.15,-42.0779,0,0,0,0,100,0),
(2012150,8,6423.34,5356.29,-43.1212,0,0,0,0,100,0),
(2012150,9,6408.24,5360.45,-42.2738,0,0,0,0,100,0),
(2012150,10,6394.94,5365.37,-40.8715,0,0,0,0,100,0),
(2012150,11,6389.27,5380.19,-40.5767,0,0,0,0,100,0),
(2012150,12,6400.19,5402.6,-38.9154,0,0,0,0,100,0),
(2012150,13,6405.98,5416.56,-35.8187,0,0,0,0,100,0),
(2012150,14,6398.85,5427.16,-39.4525,0,0,0,0,100,0),
(2012150,15,6392.13,5433.81,-34.2483,0,0,0,0,100,0),
(2012150,16,6387,5437.47,-35.275,0,0,0,0,100,0),
(2012150,17,6380.64,5443.7,-39.9828,0,0,0,0,100,0),
(2012150,18,6375.91,5445.97,-37.9713,0,0,0,0,100,0),
(2012150,19,6372.71,5447.07,-36.0775,0,0,0,0,100,0),
(2012150,20,6370.55,5447.15,-35.2644,0,0,0,0,100,0),
(2012150,21,6366.67,5449.95,-33.6952,0,0,0,0,100,0),
(2012150,22,6362.92,5454.67,-33.0803,0,0,0,0,100,0),
(2012150,23,6358.44,5462.05,-34.601,0,0,0,0,100,0),
(2012150,24,6353.85,5468.64,-35.4382,0,0,0,0,100,0),
(2012150,25,6347.08,5474.99,-34.6134,0,0,0,0,100,0),
(2012150,26,6338.5,5481.58,-32.5789,0,0,0,0,100,0),
(2012150,27,6332.61,5487.39,-31.4487,0,0,0,0,100,0),
(2012150,28,6327.07,5490.85,-29.0741,0,0,0,0,100,0),
(2012150,29,6324.66,5492.04,-27.312,0,0,0,0,100,0),
(2012150,30,6317.88,5494.59,-28.8519,0,0,0,0,100,0),
(2012150,31,6311.27,5497.21,-31.3042,0,0,0,0,100,0),
(2012150,32,6303.69,5500.25,-34.2641,0,0,0,0,100,0),
(2012150,33,6296.1,5503.83,-36.9205,0,0,0,0,100,0),
(2012150,34,6289.16,5507.42,-38.1726,0,0,0,0,100,0),
(2012150,35,6281.91,5511.42,-38.1127,0,0,0,0,100,0),
(2012150,36,6273.27,5515.77,-38.6975,0,0,0,0,100,0),
(2012150,37,6264.71,5519.78,-40.8362,0,0,0,0,100,0),
(2012150,38,6259.29,5529.86,-42.2838,0,0,0,0,100,0),
(2012150,39,6253.45,5537.58,-41.2433,0,0,0,0,100,0),
(2012150,40,6245.38,5543.87,-39.7795,0,0,0,0,100,0),
(2012150,41,6237.39,5549.93,-38.2766,0,0,0,0,100,0),
(2012150,42,6232.78,5554.03,-36.0223,0,0,0,0,100,0),
(2012150,43,6226.61,5561.78,-35.1874,0,0,0,0,100,0),
(2012150,44,6218.58,5569.2,-35.0627,0,0,0,0,100,0),
(2012150,45,6211.21,5575.3,-34.2568,0,0,0,0,100,0),
(2012150,46,6204.4,5581.86,-32.5508,0,0,0,0,100,0),
(2012150,47,6191.3,5579.82,-35.2731,0,0,0,0,100,0),
(2012150,48,6180.46,5579.19,-38.5991,0,0,0,0,100,0),
(2012150,49,6170.6,5580.83,-42.0303,0,0,0,0,100,0),
(2012150,50,6162.38,5586.55,-43.363,0,0,0,0,100,0),
(2012150,51,6153.65,5594.97,-42.4066,0,0,0,0,100,0),
(2012150,52,6145.34,5603.8,-41.3984,0,0,0,0,100,0),
(2012150,53,6137.57,5612.02,-42.0373,0,0,0,0,100,0),
(2012150,54,6129.17,5620.54,-44.0247,0,0,0,0,100,0),
(2012150,55,6118.13,5624.69,-47.0015,0,0,0,0,100,0),
(2012150,56,6098.92,5621.66,-52.6439,0,0,0,0,100,0),
(2012150,57,6080.44,5626.62,-54.7218,0,0,0,0,100,0),
(2012150,58,6066.81,5634,-55.2707,0,0,0,0,100,0),
(2012150,59,6056.04,5640.76,-55.9138,0,0,0,0,100,0),
(2012150,60,6041.15,5649.28,-57.3519,0,0,0,0,100,0),
(2012150,61,6029.54,5657.09,-58.302,0,0,0,0,100,0),
(2012150,62,6012.8,5660.07,-59.6611,0,0,0,0,100,0),
(2012150,63,5997.21,5664.96,-61.8914,0,0,0,0,100,0),
(2012150,64,5987.28,5676.5,-61.0282,0,0,0,0,100,0),
(2012150,65,5981.43,5682.25,-60.6401,0,0,0,0,100,0),
(2012150,66,5974.21,5684.97,-61.6869,0,0,0,0,100,0),
(2012150,67,5965.83,5680.66,-65.8757,0,0,0,0,100,0),
(2012150,68,5957.33,5673.08,-66.7196,0,0,0,0,100,0),
(2012150,69,5953.01,5664.24,-65.9693,0,0,0,0,100,0),
(2012150,70,5949.61,5655.8,-67.953,0,0,0,0,100,0),
(2012150,71,5946.02,5647.06,-68.9767,0,0,0,0,100,0),
(2012150,72,5942.45,5639.07,-69.2936,0,0,0,0,100,0),
(2012150,73,5938.23,5628.95,-69.4009,0,0,0,0,100,0),
(2012150,74,5936.8,5617.75,-69.6382,0,0,0,0,100,0),
(2012150,75,5939.71,5607.07,-70.6985,0,0,0,0,100,0),
(2012150,76,5946.44,5599.23,-72.2468,0,0,0,0,100,0),
(2012150,77,5953.95,5592.49,-75.0533,0,0,0,0,100,0),
(2012150,78,5961.5,5585.05,-76.5303,0,0,0,0,100,0),
(2012150,79,5970.05,5577.31,-73.1825,0,0,0,0,100,0),
(2012150,80,5978.19,5570.5,-71.7044,0,0,0,0,100,0),
(2012150,81,5987.5,5562.19,-71.6051,0,0,0,0,100,0),
(2012150,82,5996.94,5553.51,-73.01,0,0,0,0,100,0),
(2012150,83,6005.2,5546.66,-74.9682,0,0,0,0,100,0),
(2012150,84,6015.19,5539.6,-76.5092,0,0,0,0,100,0),
(2012150,85,6024.16,5533.48,-76.7818,0,0,0,0,100,0),
(2012150,86,6033.47,5528.03,-77.2917,0,0,0,0,100,0),
(2012150,87,6041.27,5526.85,-76.8408,0,0,0,0,100,0),
(2012150,88,6048.03,5526.36,-73.9535,0,0,0,0,100,0),
(2012150,89,6057.55,5525.45,-71.4663,0,0,0,0,100,0),
(2012150,90,6065.69,5525.03,-70.3746,0,0,0,0,100,0),
(2012150,91,6075.01,5525.98,-69.4392,0,0,0,0,100,0),
(2012150,92,6088.1,5528.72,-67.4325,0,0,0,0,100,0),
(2012150,93,6101.29,5530.4,-67.8112,0,0,0,0,100,0),
(2012150,94,6114.19,5531.36,-67.8759,0,0,0,0,100,0),
(2012150,95,6130.86,5532.02,-64.0252,0,0,0,0,100,0),
(2012150,96,6145.44,5532.2,-60.4815,0,0,0,0,100,0),
(2012150,97,6158.62,5532.18,-58.6869,0,0,0,0,100,0),
(2012150,98,6173.9,5532.25,-56.5987,0,0,0,0,100,0),
(2012150,99,6186.94,5530.46,-54.9825,0,0,0,0,100,0),
(2012150,100,6200.7,5525.53,-54.3641,0,0,0,0,100,0),
(2012150,101,6210.86,5517.27,-55.5304,0,0,0,0,100,0),
(2012150,102,6218.35,5506.91,-56.7901,0,0,0,0,100,0),
(2012150,103,6224.31,5498.83,-56.5668,0,0,0,0,100,0),
(2012150,104,6228.56,5494.08,-54.219,0,0,0,0,100,0),
(2012150,105,6233.46,5490.98,-51.845,0,0,0,0,100,0),
(2012150,106,6240.52,5488.96,-49.1318,0,0,0,0,100,0),
(2012150,107,6248.67,5486.92,-47.7393,0,0,0,0,100,0),
(2012150,108,6258.07,5484.2,-46.2098,0,0,0,0,100,0),
(2012150,109,6265.03,5479.6,-45.7056,0,0,0,0,100,0),
(2012150,110,6273.74,5471.17,-48.3814,0,0,0,0,100,0),
(2012150,111,6280.87,5463.47,-48.947,0,0,0,0,100,0),
(2012150,112,6286.98,5455.68,-51.1552,0,0,0,0,100,0),
(2012150,113,6293.81,5447.76,-53.6199,0,0,0,0,100,0),
(2012150,114,6302.17,5442.3,-54.4251,0,0,0,0,100,0),
(2012150,115,6314.34,5438.56,-51.6596,0,0,0,0,100,0),
(2012150,116,6325.25,5437.67,-48.0563,0,0,0,0,100,0),
(2012150,117,6336.67,5438.12,-43.9135,0,0,0,0,100,0),
(2012150,118,6344.18,5439.83,-40.8785,0,0,0,0,100,0),
(2012150,119,6351.63,5441.23,-38.6625,0,0,0,0,100,0),
(2012150,120,6359.62,5440.99,-37.0207,0,0,0,0,100,0),
(2012150,121,6367.7,5442.69,-36.4004,0,0,0,0,100,0),
(2012150,122,6376.79,5439.07,-40.5544,0,0,0,0,100,0),
(2012150,123,6381.74,5436.98,-37.8587,0,0,0,0,100,0),
(2012150,124,6386.42,5433.25,-35.9126,0,0,0,0,100,0),
(2012150,125,6395.63,5424.28,-40.0877,0,0,0,0,100,0),
(2012150,126,6400.07,5417.05,-37.8338,0,0,0,0,100,0),
(2012150,127,6391.82,5402.2,-38.8664,0,0,0,0,100,0),
(2012150,128,6378.21,5391.19,-40.5005,0,0,0,0,100,0),
(2012150,129,6368.46,5381.72,-43.1892,0,0,0,0,100,0),
(2012150,130,6357,5373.95,-44.6898,0,0,0,0,100,0),
(2012150,131,6347.11,5366.42,-46.6248,0,0,0,0,100,0),
(2012150,132,6341.71,5356.27,-51.1062,0,0,0,0,100,0),
(2012150,133,6339.1,5342.99,-57.1919,0,0,0,0,100,0),
(2012150,134,6338.88,5327.48,-60.8465,0,0,0,0,100,0),
(2012150,135,6339.33,5314.89,-65.3392,0,0,0,0,100,0),
(2012150,136,6341.51,5304.4,-68.8652,0,0,0,0,100,0),
(2012150,137,6344.82,5292.62,-68.7487,0,0,0,0,100,0),
(2012150,138,6349.76,5282.87,-68.2869,0,0,0,0,100,0),
(2012150,139,6357.7,5273.7,-67.5637,0,0,0,0,100,0),
(2012150,140,6367.87,5264.13,-67.0567,0,0,0,0,100,0),
(2012150,141,6377.47,5256.72,-66.8449,0,0,0,0,100,0),
(2012150,142,6390.28,5248.71,-66.0983,0,0,0,0,100,0),
(2012150,143,6403.88,5237.5,-65.4868,0,0,0,0,100,0),
(2012150,144,6417.63,5228.72,-65.0644,0,0,0,0,100,0),
(2012150,145,6431.82,5220.69,-64.5291,0,0,0,0,100,0),
(2012150,146,6445.24,5218.23,-63.1355,0,0,0,0,100,0),
(2012150,147,6458.42,5222.02,-59.2136,0,0,0,0,100,0),
(2012150,148,6465.9,5230.28,-56.9485,0,0,0,0,100,0),
(2012150,149,6470.32,5240.77,-55.7002,0,0,0,0,100,0),
(2012150,150,6474.12,5248.82,-55.8463,0,0,0,0,100,0),
(2012150,151,6485.4,5252.85,-55.3532,0,0,0,0,100,0),
(2012150,152,6494.85,5257.51,-53.4025,0,0,0,0,100,0),
(2012150,153,6497.13,5266.01,-49.8556,0,0,0,0,100,0),
(2012150,154,6497.66,5276.25,-47.2978,0,0,0,0,100,0),
(2012150,155,6499.06,5284.36,-45.1885,0,0,0,0,100,0),
(2012150,156,6504.83,5292.82,-42.9747,0,0,0,0,100,0);

DELETE FROM `waypoint_data` WHERE `id` = 1520020;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`)
VALUES
(1520020,1,4865.04,4679.54,-69.2385,0,0,0,0,100,0),
(1520020,2,4882.27,4696.17,-71.829,0,0,0,0,100,0),
(1520020,3,4894.68,4712.35,-74.7751,0,0,0,0,100,0),
(1520020,4,4893.97,4726.67,-75.75,0,0,0,0,100,0),
(1520020,5,4885.42,4740.31,-75.5778,0,0,0,0,100,0),
(1520020,6,4872.81,4748.72,-75.2067,0,0,0,0,100,0),
(1520020,7,4861.13,4751.11,-76.745,0,0,0,0,100,0),
(1520020,8,4850.78,4750.98,-75.6193,0,0,0,0,100,0),
(1520020,9,4841.09,4742.79,-73.2251,0,0,0,0,100,0),
(1520020,10,4837,4728.22,-71.6072,0,0,0,0,100,0),
(1520020,11,4838.93,4713.05,-70.2948,0,0,0,0,100,0),
(1520020,12,4838.91,4693.51,-68.6816,0,0,0,0,100,0),
(1520020,13,4830.51,4675.73,-65.6856,0,0,0,0,100,0),
(1520020,14,4826.41,4659.95,-65.4011,0,0,0,0,100,0),
(1520020,15,4827.06,4638.73,-64.5704,0,0,0,0,100,0),
(1520020,16,4834.59,4620.02,-65.1742,0,0,0,0,100,0),
(1520020,17,4831.57,4597.3,-63.9759,0,0,0,0,100,0),
(1520020,18,4822.3,4583.6,-59.9731,0,0,0,0,100,0),
(1520020,19,4820.36,4562.34,-63.8201,0,0,0,0,100,0),
(1520020,20,4820.76,4537.55,-65.2141,0,0,0,0,100,0),
(1520020,21,4829.1,4517.85,-65.0614,0,0,0,0,100,0),
(1520020,22,4841.01,4496.33,-65.1975,0,0,0,0,100,0),
(1520020,23,4854.41,4473.64,-65.0237,0,0,0,0,100,0),
(1520020,24,4865.1,4454.62,-63.8645,0,0,0,0,100,0),
(1520020,25,4874.81,4434.7,-63.4423,0,0,0,0,100,0),
(1520020,26,4881.77,4409.57,-61.125,0,0,0,0,100,0),
(1520020,27,4885.08,4390.83,-60.6086,0,0,0,0,100,0),
(1520020,28,4881.51,4372.09,-59.5913,0,0,0,0,100,0),
(1520020,29,4870.78,4353.89,-57.3215,0,0,0,0,100,0),
(1520020,30,4860.37,4334.41,-56.3881,0,0,0,0,100,0),
(1520020,31,4859.3,4318.24,-52.8339,0,0,0,0,100,0),
(1520020,32,4858.73,4304.12,-51.408,0,0,0,0,100,0),
(1520020,33,4850.99,4288.21,-46.6351,0,0,0,0,100,0),
(1520020,34,4850.35,4272.81,-43.8,0,0,0,0,100,0),
(1520020,35,4860.77,4258.75,-43.4729,0,0,0,0,100,0),
(1520020,36,4875.69,4244.67,-44.1895,0,0,0,0,100,0),
(1520020,37,4875.9,4225.9,-43.0955,0,0,0,0,100,0),
(1520020,38,4876.15,4210.16,-39.7507,0,0,0,0,100,0),
(1520020,39,4869.24,4187.29,-33.8273,0,0,0,0,100,0),
(1520020,40,4869.01,4168.22,-28.464,0,0,0,0,100,0),
(1520020,41,4875.55,4148.82,-25.9555,0,0,0,0,100,0),
(1520020,42,4889.06,4134.86,-29.8303,0,0,0,0,100,0),
(1520020,43,4904.23,4126.22,-31.6976,0,0,0,0,100,0),
(1520020,44,4923.57,4118.36,-31.7256,0,0,0,0,100,0),
(1520020,45,4938.31,4106.35,-27.2918,0,0,0,0,100,0),
(1520020,46,4941.89,4091.24,-22.6607,0,0,0,0,100,0),
(1520020,47,4944.02,4075.64,-20.4904,0,0,0,0,100,0),
(1520020,48,4946.66,4056.46,-20.1541,0,0,0,0,100,0),
(1520020,49,4955.46,4041.75,-21.9683,0,0,0,0,100,0),
(1520020,50,4971.52,4032.81,-22.8129,0,0,0,0,100,0),
(1520020,51,4987.69,4032.62,-25.8115,0,0,0,0,100,0),
(1520020,52,5001.85,4039.11,-30.9755,0,0,0,0,100,0),
(1520020,53,5010.16,4052.91,-35.4062,0,0,0,0,100,0),
(1520020,54,5010.2,4065.52,-34.044,0,0,0,0,100,0),
(1520020,55,5004.57,4083.41,-33.107,0,0,0,0,100,0),
(1520020,56,4998.3,4100.12,-32.4021,0,0,0,0,100,0),
(1520020,57,4990.25,4116.56,-34.8791,0,0,0,0,100,0),
(1520020,58,4979.88,4133.05,-36.8895,0,0,0,0,100,0),
(1520020,59,4967.26,4140.48,-37.5859,0,0,0,0,100,0),
(1520020,60,4947.95,4140.45,-37.0285,0,0,0,0,100,0),
(1520020,61,4933.33,4138.85,-38.7064,0,0,0,0,100,0),
(1520020,62,4920.96,4140.79,-37.4966,0,0,0,0,100,0),
(1520020,63,4911.13,4152.66,-37.6767,0,0,0,0,100,0),
(1520020,64,4900.1,4167.71,-38.0262,0,0,0,0,100,0),
(1520020,65,4886.84,4175.12,-37.3863,0,0,0,0,100,0),
(1520020,66,4875.39,4186.35,-36.4971,0,0,0,0,100,0),
(1520020,67,4873.42,4198.89,-37.2471,0,0,0,0,100,0),
(1520020,68,4876.69,4213.34,-40.5244,0,0,0,0,100,0),
(1520020,69,4876.12,4227.33,-43.575,0,0,0,0,100,0),
(1520020,70,4875.35,4243.88,-44.1707,0,0,0,0,100,0),
(1520020,71,4869.05,4259.28,-44.6236,0,0,0,0,100,0),
(1520020,72,4856.53,4272.32,-45.3786,0,0,0,0,100,0),
(1520020,73,4850.54,4284.31,-45.7167,0,0,0,0,100,0),
(1520020,74,4853.91,4297.02,-49.0219,0,0,0,0,100,0),
(1520020,75,4861.54,4314.16,-52.7978,0,0,0,0,100,0),
(1520020,76,4871.15,4324.39,-54.8634,0,0,0,0,100,0),
(1520020,77,4879.47,4338.21,-57.8297,0,0,0,0,100,0),
(1520020,78,4895.34,4360.21,-61.9069,0,0,0,0,100,0),
(1520020,79,4912.33,4383.36,-63.0653,0,0,0,0,100,0),
(1520020,80,4946.13,4405.99,-71.5968,0,0,0,0,100,0),
(1520020,81,4949.81,4446.06,-74.3223,0,0,0,0,100,0),
(1520020,82,4928.02,4460.91,-72.7117,0,0,0,0,100,0),
(1520020,83,4906.17,4464.86,-68.4417,0,0,0,0,100,0),
(1520020,84,4886,4480.71,-68.8113,0,0,0,0,100,0),
(1520020,85,4875.11,4490.22,-68.3796,0,0,0,0,100,0),
(1520020,86,4869.04,4506.77,-70.6852,0,0,0,0,100,0),
(1520020,87,4863.06,4522.73,-71.7566,0,0,0,0,100,0),
(1520020,88,4857.06,4537.29,-72.5529,0,0,0,0,100,0),
(1520020,89,4849.09,4553.25,-67.1116,0,0,0,0,100,0),
(1520020,90,4839.7,4573.6,-64.781,0,0,0,0,100,0),
(1520020,91,4836.71,4593.17,-65.4578,0,0,0,0,100,0),
(1520020,92,4833.59,4609.05,-64.8195,0,0,0,0,100,0),
(1520020,93,4840.17,4628.26,-65.8872,0,0,0,0,100,0),
(1520020,94,4842.5,4644.28,-66.9279,0,0,0,0,100,0),
(1520020,95,4855.13,4656.89,-68.3977,0,0,0,0,100,0);

DELETE FROM `waypoint_data` WHERE `id` = 1520030;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`)
VALUES
(1520030,1,6087.94,4603.13,-96.8083,0,0,0,0,100,0),
(1520030,2,6075.24,4618.04,-97.4288,0,0,0,0,100,0),
(1520030,3,6063.07,4633.27,-95.345,0,0,0,0,100,0),
(1520030,4,6047.12,4652.94,-94.5749,0,0,0,0,100,0),
(1520030,5,6033.23,4671.37,-94.6291,0,0,0,0,100,0),
(1520030,6,6018.66,4692.36,-95.4387,0,0,0,0,100,0),
(1520030,7,6004.15,4713.39,-96.2336,0,0,0,0,100,0),
(1520030,8,5995.25,4727.91,-98.8986,0,0,0,0,100,0),
(1520030,9,5985.47,4748.94,-97.329,0,0,0,0,100,0),
(1520030,10,5978.73,4766.46,-98.2525,0,0,0,0,100,0),
(1520030,11,5972.73,4782.89,-103.074,0,0,0,0,100,0),
(1520030,12,5967.13,4801.75,-105.765,0,0,0,0,100,0),
(1520030,13,5962.83,4826.92,-102.241,0,0,0,0,100,0),
(1520030,14,5961.59,4845.42,-101.218,0,0,0,0,100,0),
(1520030,15,5965.29,4860.51,-99.0756,0,0,0,0,100,0),
(1520030,16,5975.28,4869.23,-97.6385,0,0,0,0,100,0),
(1520030,17,5984.47,4871.78,-97.5249,0,0,0,0,100,0),
(1520030,18,5994.4,4870.37,-97.9849,0,0,0,0,100,0),
(1520030,19,6006.68,4863.85,-98.0179,0,0,0,0,100,0),
(1520030,20,6011.41,4856.1,-98.4073,0,0,0,0,100,0),
(1520030,21,6013.07,4848.18,-99.2101,0,0,0,0,100,0),
(1520030,22,6009.44,4838.42,-100.358,0,0,0,0,100,0),
(1520030,23,6001.43,4826.52,-100.548,0,0,0,0,100,0),
(1520030,24,5995.9,4815.6,-100.815,0,0,0,0,100,0),
(1520030,25,5995.38,4803.78,-99.6919,0,0,0,0,100,0),
(1520030,26,6000.36,4791.98,-98.0362,0,0,0,0,100,0),
(1520030,27,6007.91,4781.44,-96.7573,0,0,0,0,100,0),
(1520030,28,6018.61,4767.73,-96.2089,0,0,0,0,100,0),
(1520030,29,6027.2,4754.7,-96.6951,0,0,0,0,100,0),
(1520030,30,6033.96,4741.13,-95.302,0,0,0,0,100,0),
(1520030,31,6041.95,4721.47,-94.5625,0,0,0,0,100,0),
(1520030,32,6049.15,4700.62,-94.5729,0,0,0,0,100,0),
(1520030,33,6055.95,4682.74,-94.5729,0,0,0,0,100,0),
(1520030,34,6067.69,4670.15,-94.5729,0,0,0,0,100,0),
(1520030,35,6081,4659.16,-94.5729,0,0,0,0,100,0),
(1520030,36,6095.48,4646.48,-94.5729,0,0,0,0,100,0),
(1520030,37,6105.03,4637.08,-94.5573,0,0,0,0,100,0),
(1520030,38,6111.59,4628.79,-93.3843,0,0,0,0,100,0),
(1520030,39,6114.73,4619.43,-93.3148,0,0,0,0,100,0),
(1520030,40,6121.13,4604.06,-93.6132,0,0,0,0,100,0),
(1520030,41,6133.04,4596.95,-95.911,0,0,0,0,100,0),
(1520030,42,6148.98,4593.96,-94.4831,0,0,0,0,100,0),
(1520030,43,6158.38,4580.86,-93.1592,0,0,0,0,100,0),
(1520030,44,6165.86,4566.89,-91.8468,0,0,0,0,100,0),
(1520030,45,6164.09,4550.57,-89.5403,0,0,0,0,100,0),
(1520030,46,6149.61,4542.7,-87.5689,0,0,0,0,100,0),
(1520030,47,6136.23,4548.75,-88.7528,0,0,0,0,100,0),
(1520030,48,6126.15,4561.88,-91.0118,0,0,0,0,100,0),
(1520030,49,6118.33,4578.17,-94.5329,0,0,0,0,100,0),
(1520030,50,6106.81,4588.64,-93.7485,0,0,0,0,100,0),
(1520030,51,6095.46,4592.79,-95.1145,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
