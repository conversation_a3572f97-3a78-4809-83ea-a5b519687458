-- DB update 2021_11_08_01 -> 2021_11_08_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_08_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_08_01 2021_11_08_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1636235807735905400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1636235807735905400');

DELETE FROM `creature` WHERE `guid` IN (162001, 162002, 162003, 162004, 162005, 162006, 162007, 162008, 162009, 162010, 162011, 162012, 162013, 162014, 162015, 162016, 162017, 162018, 162019, 162020, 162021, 162022, 162023, 162024, 162025, 162026, 162027, 162028, 162029);
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `wander_distance`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`) VALUES 
(162001, 727, 0, 0, 0, 1, 1, 0, 1, -3972.82, -1513.31, 169.195, 5.53077, 300, 0, 0, 1910, 0, 0, 0, 0, 0, '', 0),
(162002, 620, 0, 0, 0, 1, 1, 0, 0, -3964.18, -1526.38, 169.163, 5.06818, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162003, 727, 0, 0, 0, 1, 1, 0, 1, -3985.24, -1508.53, 168.705, 4.14848, 300, 0, 0, 1910, 0, 0, 0, 0, 0, '', 0),
(162004, 620, 0, 0, 0, 1, 1, 0, 0, -4001.15, -1502.53, 169.703, 5.06818, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162005, 721, 0, 0, 0, 1, 1, 0, 0, -3971.98, -1494.18, 169.931, 2.12686, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162006, 620, 0, 0, 0, 1, 1, 0, 0, -4017.85, -1460.55, 168.343, 4.69512, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162007, 620, 0, 0, 0, 1, 1, 0, 0, -4022.11, -1449.52, 165.607, 5.71378, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162008, 620, 0, 0, 0, 1, 1, 0, 0, -4039.9, -1446.43, 166.2, 4.68177, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162009, 12998, 0, 0, 0, 1, 1, 0, 1, -4029.86, -1453.53, 167.187, 4.37309, 300, 5, 0, 656, 0, 1, 0, 0, 0, '', 0),
(162010, 721, 0, 0, 0, 1, 1, 0, 0, -3994.42, -1376.7, 150.124, 1.10741, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162011, 12996, 0, 0, 0, 1, 1, 0, 1, -4025.61, -1404.07, 155.248, 3.81547, 300, 0, 0, 1910, 0, 2, 0, 0, 0, '', 0),
(162012, 721, 0, 0, 0, 1, 1, 0, 0, -4026.06, -1270.89, 146.802, 1.82841, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162013, 620, 0, 0, 0, 1, 1, 0, 0, -4074.13, -1268.01, 146.158, 4.99592, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162014, 620, 0, 0, 0, 1, 1, 0, 0, -4091.94, -1279.57, 146.18, 3.76912, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162015, 620, 0, 0, 0, 1, 1, 0, 0, -4080.96, -1256.69, 146.684, 3.85709, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162016, 12998, 0, 0, 0, 1, 1, 0, 1, -4101.46, -1260.41, 148.958, 4.91972, 300, 0, 0, 656, 0, 0, 0, 0, 0, '', 0),
(162017, 2442, 0, 0, 0, 1, 1, 0, 0, -4055.91, -1247.15, 146.314, 4.19089, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162018, 2442, 0, 0, 0, 1, 1, 0, 0, -4041.5, -1230.92, 146.314, 3.80212, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162019, 2442, 0, 0, 0, 1, 1, 0, 0, -4053.71, -1187.58, 146.314, 4.69747, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162020, 12998, 0, 0, 0, 1, 1, 0, 1, -4074.56, -1256.74, 146.399, 0.106011, 300, 0, 0, 656, 0, 2, 0, 0, 0, '', 0),
(162021, 2110, 0, 0, 0, 1, 1, 0, 0, -4048.29, -1227.35, 146.314, 5.142, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162022, 721, 0, 0, 0, 1, 1, 0, 0, -4096.43, -1088.23, 170.199, 1.84097, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162023, 620, 0, 0, 0, 1, 1, 0, 0, -4117.23, -1101.1, 168.953, 3.54764, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162024, 620, 0, 0, 0, 1, 1, 0, 0, -4126.41, -1091.58, 168.916, 4.29377, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162025, 620, 0, 0, 0, 1, 1, 0, 0, -4137.29, -1102.33, 168.916, 3.74399, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(162026, 12998, 0, 0, 0, 1, 1, 0, 1, -4147.28, -1106.97, 168.917, 0.341632, 300, 0, 0, 656, 0, 0, 0, 0, 0, '', 0),
(162027, 12998, 0, 0, 0, 1, 1, 0, 1, -4110.72, -1068.8, 168.909, 4.87338, 300, 5, 0, 656, 0, 1, 0, 0, 0, '', 0),
(162028, 2098, 0, 0, 0, 1, 1, 0, 0, -4131.62, -1063.01, 167.925, 2.442589, 300, 0, 0, 102, 0, 0, 0, 2, 0, '', 0),
(162029, 2098, 0, 0, 0, 1, 1, 0, 0, -4142.79, -1053.89, 167.925, 5.52528, 300, 0, 0, 102, 0, 0, 0, 2, 0, '', 0);

DELETE FROM `creature_addon` WHERE `guid` IN (162011, 162020);
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `auras`) VALUES 
(162011, 1620110, 2786, 0, 1, 0, NULL),
(162020, 1620200, 0, 0, 1, 0, NULL);

DELETE FROM `waypoint_data` WHERE `id` IN (1620110, 1620200);
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES 
(1620110, 1, -4032.54, -1410.32, 156.721, 255, 0, 0, 0, 100, 0),
(1620110, 2, -4074.89, -1407.43, 166.031, 255, 0, 0, 0, 100, 0),
(1620110, 3, -4121.2, -1392.13, 186.617, 255, 0, 0, 0, 100, 0),
(1620110, 4, -4139.65, -1392.91, 194.53, 255, 0, 0, 0, 100, 0),
(1620110, 5, -4160.59, -1400.4, 197.085, 255, 0, 0, 0, 100, 0),
(1620110, 6, -4139.07, -1393.15, 194.301, 255, 0, 0, 0, 100, 0),
(1620110, 7, -4120.76, -1392.22, 186.412, 255, 0, 0, 0, 100, 0),
(1620110, 8, -4074.23, -1407.58, 165.77, 255, 0, 0, 0, 100, 0),
(1620110, 9, -4032.55, -1410.22, 156.712, 255, 0, 0, 0, 100, 0),
(1620110, 10, -4020.63, -1397.18, 153.371, 255, 0, 0, 0, 100, 0),
(1620110, 11, -4017.04, -1356.28, 148.046, 255, 0, 0, 0, 100, 0),
(1620110, 12, -4009.14, -1314.06, 146.624, 255, 0, 0, 0, 100, 0),
(1620110, 13, -4018.54, -1285.38, 146.625, 255, 0, 0, 0, 100, 0),
(1620110, 14, -3993.26, -1242.82, 146.911, 255, 0, 0, 0, 100, 0),
(1620110, 15, -3994.51, -1185.12, 154.931, 255, 0, 0, 0, 100, 0),
(1620110, 16, -4020.88, -1159.39, 157.864, 255, 0, 0, 0, 100, 0),
(1620110, 17, -4044.1, -1151.67, 160.279, 255, 0, 0, 0, 100, 0),
(1620110, 18, -4067.39, -1109.54, 166.369, 255, 0, 0, 0, 100, 0),
(1620110, 19, -4087.61, -1099.67, 168.504, 255, 0, 0, 0, 100, 0),
(1620110, 20, -4128.91, -1098.02, 168.917, 255, 0, 0, 0, 100, 0),
(1620110, 21, -4136.27, -1079.66, 168.917, 255, 0, 0, 0, 100, 0),
(1620110, 22, -4128.58, -1098.3, 168.916, 255, 0, 0, 0, 100, 0),
(1620110, 23, -4086.78, -1099.88, 168.434, 255, 0, 0, 0, 100, 0),
(1620110, 24, -4066.85, -1110.06, 166.183, 255, 0, 0, 0, 100, 0),
(1620110, 25, -4043.64, -1151.81, 160.212, 255, 0, 0, 0, 100, 0),
(1620110, 26, -4020.62, -1159.42, 157.841, 255, 0, 0, 0, 100, 0),
(1620110, 27, -3994.12, -1185.26, 154.956, 255, 0, 0, 0, 100, 0),
(1620110, 28, -3992.99, -1243.2, 146.901, 255, 0, 0, 0, 100, 0),
(1620110, 29, -4018.42, -1285.85, 146.623, 255, 0, 0, 0, 100, 0),
(1620110, 30, -4008.84, -1314.12, 146.627, 255, 0, 0, 0, 100, 0),
(1620110, 31, -4016.71, -1356.63, 148.065, 255, 0, 0, 0, 100, 0),
(1620110, 32, -4020.15, -1397.47, 153.434, 255, 0, 0, 0, 100, 0),
(1620200, 1, -4056.62, -1257.13, 146.314, 255, 0, 0, 0, 100, 0),
(1620200, 2, -4022.13, -1239.04, 146.314, 255, 0, 0, 0, 100, 0),
(1620200, 3, -4027.52, -1197.61, 146.314, 255, 0, 0, 0, 100, 0),
(1620200, 4, -4073.56, -1205.96, 146.314, 255, 0, 0, 0, 100, 0),
(1620200, 5, -4074.82, -1234.27, 146.481, 255, 0, 0, 0, 100, 0),
(1620200, 6, -4071.62, -1244.78, 146.406, 255, 0, 0, 0, 100, 0),
(1620200, 7, -4073.64, -1253.11, 146.433, 255, 0, 0, 0, 100, 0);
--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_08_02' WHERE sql_rev = '1636235807735905400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
