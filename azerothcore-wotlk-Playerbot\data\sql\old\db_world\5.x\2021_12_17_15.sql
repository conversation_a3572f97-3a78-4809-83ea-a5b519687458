-- DB update 2021_12_17_14 -> 2021_12_17_15
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_17_14';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_17_14 2021_12_17_15 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639521847268743300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639521847268743300');

UPDATE `creature_template` SET `mechanic_immune_mask`= `mechanic_immune_mask`&~ 16384 WHERE (`entry` IN (11502, 12057, 12056));

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_17_15' WHERE sql_rev = '1639521847268743300';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
