-- DB update 2021_11_13_03 -> 2021_11_14_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_13_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_13_03 2021_11_14_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1636734036216950528'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1636734036216950528');

-- Add missing gossip text for <PERSON><PERSON>
DELETE FROM `gossip_menu` WHERE `MenuID` IN (2994,2995,2996,2997,2998,2999);
INSERT INTO `gossip_menu` (`MenuID`,`TextID`) VALUES
(2994,3660),(2995,3661),(2996,3662),(2997,3663),(2998,3664),(2999,3665);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_14_00' WHERE sql_rev = '1636734036216950528';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
