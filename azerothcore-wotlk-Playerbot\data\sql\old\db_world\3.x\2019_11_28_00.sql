-- DB update 2019_11_27_00 -> 2019_11_28_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_11_27_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_11_27_00 2019_11_28_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1572129873301966655'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1572129873301966655');

DELETE FROM `waypoint_data` WHERE `id` = 1008240;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`)
VALUES
(1008240,1,3479.21,376.565,244.863,0,0,0,0,100,0),
(1008240,2,3411.42,366.747,244.863,0,0,0,0,100,0),
(1008240,3,3370.33,427.035,244.863,0,0,0,0,100,0),
(1008240,4,3406.9,485.82,244.863,0,0,0,0,100,0),
(1008240,5,3491.84,496.926,244.863,0,0,0,0,100,0),
(1008240,6,3589.48,475.778,244.863,0,0,0,0,100,0),
(1008240,7,3685.21,493.712,244.863,0,0,0,0,100,0),
(1008240,8,3769.01,516.134,244.863,0,0,0,0,100,0),
(1008240,9,3811.1,463.134,244.863,0,0,0,0,100,0),
(1008240,10,3786.66,388.97,244.863,0,0,0,0,100,0),
(1008240,11,3727.34,385.362,244.863,0,0,0,0,100,0),
(1008240,12,3683.28,437.189,244.863,0,0,0,0,100,0),
(1008240,13,3625.95,456.773,244.863,0,0,0,0,100,0),
(1008240,14,3559.79,412.912,244.863,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
