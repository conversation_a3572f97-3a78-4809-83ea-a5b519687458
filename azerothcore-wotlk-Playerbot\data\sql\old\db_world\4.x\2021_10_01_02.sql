-- DB update 2021_10_01_01 -> 2021_10_01_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_01_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_01_01 2021_10_01_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1632327857472629400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1632327857472629400');

DELETE FROM `creature_loot_template` WHERE `Entry` = 8891 AND `Item` IN (24086, 774, 818, 1206, 1210, 1529, 1705, 2449, 3356, 3357, 3820, 5498, 5500);

# Note: This reference_loot_template entry has multiple (1000+) different mobs referencing it.
# Checked a couple of the mobs (On Wowhead) and found that it varied whether they dropped the item (Citrine)
# Those that did had it in their individual table
DELETE FROM `reference_loot_template` WHERE `Entry` = 24036 AND `Item` = 3864;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_01_02' WHERE sql_rev = '1632327857472629400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
