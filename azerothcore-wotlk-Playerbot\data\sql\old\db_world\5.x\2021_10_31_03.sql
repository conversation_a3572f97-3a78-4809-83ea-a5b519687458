-- DB update 2021_10_31_02 -> 2021_10_31_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_31_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_31_02 2021_10_31_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635442402182198240'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635442402182198240');

-- Fix location of Darcy
UPDATE `creature` SET `position_x`=-9218.02, `position_y`=-2148.62, `position_z`=64.3548, `orientation`=4.47915 WHERE `id`=379;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_31_03' WHERE sql_rev = '1635442402182198240';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
