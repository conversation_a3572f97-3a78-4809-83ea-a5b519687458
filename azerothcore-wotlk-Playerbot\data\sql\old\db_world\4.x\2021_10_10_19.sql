-- DB update 2021_10_10_18 -> 2021_10_10_19
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_18';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_18 2021_10_10_19 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633534272596360700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633534272596360700');

DELETE FROM `creature_loot_template` WHERE `item`=13463 AND `entry` NOT IN (1806,1812,1813,1851,6510,6512,6517,6518,6519,6520,6527,6551,6552,6557,6559,6560,7039,
7092,7100,7101,7132,7138,7139,7149,8519,8520,8521,8522,8766,8981,9477,9601,9878,9879,11458,11459,11461,11462,11464,11465,11480,11698,11722,11724,11729,11744,
11745,11747,13021,13022,13136,13196,13197,13279,13285,14303,14462,15207,15335,16043);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_19' WHERE sql_rev = '1633534272596360700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
