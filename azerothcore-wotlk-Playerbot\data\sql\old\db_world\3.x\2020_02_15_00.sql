-- DB update 2020_02_11_00 -> 2020_02_15_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_02_11_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_02_11_00 2020_02_15_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1579467735449000252'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1579467735449000252');

DELETE FROM `npc_trainer` WHERE `SpellID` IN (1785, 1786, 1787, 6783, 8649, 8650, 9913, 11197, 11198, 26866, 48669, 49913, 49914, 49915, 49916, 51426, 51427, 51428, 51429, 53720);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
