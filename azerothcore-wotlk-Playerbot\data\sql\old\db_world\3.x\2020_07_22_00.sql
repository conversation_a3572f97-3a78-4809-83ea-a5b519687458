-- DB update 2020_07_21_01 -> 2020_07_22_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_07_21_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_07_21_01 2020_07_22_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1588804973320592800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1588804973320592800');
-- ----------------------------
-- Table structure for areatable_dbc
-- ----------------------------
DROP TABLE IF EXISTS `areatable_dbc`; 
CREATE TABLE `areatable_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ContinentID` INT NOT NULL DEFAULT '0',
 `ParentAreaID` INT NOT NULL DEFAULT '0',
 `AreaBit` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `SoundProviderPref` INT NOT NULL DEFAULT '0',
 `SoundProviderPrefUnderwater` INT NOT NULL DEFAULT '0',
 `AmbienceID` INT NOT NULL DEFAULT '0',
 `ZoneMusic` INT NOT NULL DEFAULT '0',
 `IntroSound` INT NOT NULL DEFAULT '0',
 `ExplorationLevel` INT NOT NULL DEFAULT '0',
 `AreaName_Lang_enUS` varchar(100) NULL,
 `AreaName_Lang_enGB` varchar(100) NULL,
 `AreaName_Lang_koKR` varchar(100) NULL,
 `AreaName_Lang_frFR` varchar(100) NULL,
 `AreaName_Lang_deDE` varchar(100) NULL,
 `AreaName_Lang_enCN` varchar(100) NULL,
 `AreaName_Lang_zhCN` varchar(100) NULL,
 `AreaName_Lang_enTW` varchar(100) NULL,
 `AreaName_Lang_zhTW` varchar(100) NULL,
 `AreaName_Lang_esES` varchar(100) NULL,
 `AreaName_Lang_esMX` varchar(100) NULL,
 `AreaName_Lang_ruRU` varchar(100) NULL,
 `AreaName_Lang_ptPT` varchar(100) NULL,
 `AreaName_Lang_ptBR` varchar(100) NULL,
 `AreaName_Lang_itIT` varchar(100) NULL,
 `AreaName_Lang_Unk` varchar(100) NULL,
 `AreaName_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `FactionGroupMask` INT NOT NULL DEFAULT '0',
 `LiquidTypeID_1` INT NOT NULL DEFAULT '0',
 `LiquidTypeID_2` INT NOT NULL DEFAULT '0',
 `LiquidTypeID_3` INT NOT NULL DEFAULT '0',
 `LiquidTypeID_4` INT NOT NULL DEFAULT '0',
 `MinElevation` FLOAT NOT NULL DEFAULT '0',
 `Ambient_Multiplier` FLOAT NOT NULL DEFAULT '0',
 `Lightid` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for achievement_dbc
-- ----------------------------

-- Create temp db table
DROP TABLE IF EXISTS `_achievement_dbc`;
CREATE TABLE `_achievement_dbc` (
  `ID` int(10) unsigned NOT NULL,
  `requiredFaction` int(11) NOT NULL DEFAULT '-1',
  `mapID` int(11) NOT NULL DEFAULT '-1',
  `points` int(10) unsigned NOT NULL DEFAULT '0',
  `flags` int(10) unsigned NOT NULL DEFAULT '0',
  `count` int(10) unsigned NOT NULL DEFAULT '0',
  `refAchievement` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- instert temp data to temp db table
INSERT INTO `_achievement_dbc` SELECT * FROM `achievement_dbc`;

-- create new table
DROP TABLE IF EXISTS `achievement_dbc`;
CREATE TABLE `achievement_dbc`  (
  `ID` int(11) NOT NULL DEFAULT 0,
  `Faction` int(11) NOT NULL DEFAULT 0,
  `Instance_Id` int(11) NOT NULL DEFAULT 0,
  `Supercedes` int(11) NOT NULL DEFAULT 0,
  `Title_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Title_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Description_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Category` int(11) NOT NULL DEFAULT 0,
  `Points` int(11) NOT NULL DEFAULT 0,
  `Ui_Order` int(11) NOT NULL DEFAULT 0,
  `Flags` int(11) NOT NULL DEFAULT 0,
  `IconID` int(11) NOT NULL DEFAULT 0,
  `Reward_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Reward_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Minimum_Criteria` int(11) NOT NULL DEFAULT 0,
  `Shares_Criteria` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Insert data from temp
-- ----------------------------
INSERT INTO `achievement_dbc`(`ID`, `Faction`, `Instance_Id`, `Points`, `Flags`, `Minimum_Criteria`, `Shares_Criteria`) 
(SELECT `ID`, `requiredFaction`, `mapID`, `points`, `flags`, `count`, `refAchievement` FROM `_achievement_dbc`);

-- detele temp table
DROP TABLE IF EXISTS `_achievement_dbc`;

-- ----------------------------
-- Table structure for achievement_category_dbc
-- ----------------------------
DROP TABLE IF EXISTS `achievement_category_dbc`;
CREATE TABLE `achievement_category_dbc`  (
  `ID` int(11) NOT NULL DEFAULT 0,
  `Parent` int(11) NOT NULL DEFAULT 0,
  `Name_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Ui_Order` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for achievement_criteria_dbc
-- ----------------------------
DROP TABLE IF EXISTS `achievement_criteria_dbc`;
CREATE TABLE `achievement_criteria_dbc`  (
  `ID` int(11) NOT NULL DEFAULT 0,
  `Achievement_Id` int(11) NOT NULL DEFAULT 0,
  `Type` int(11) NOT NULL DEFAULT 0,
  `Asset_Id` int(11) NOT NULL DEFAULT 0,
  `Quantity` int(11) NOT NULL DEFAULT 0,
  `Start_Event` int(11) NOT NULL DEFAULT 0,
  `Start_Asset` int(11) NOT NULL DEFAULT 0,
  `Fail_Event` int(11) NOT NULL DEFAULT 0,
  `Fail_Asset` int(11) NOT NULL DEFAULT 0,
  `Description_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Flags` int(11) NOT NULL DEFAULT 0,
  `Timer_Start_Event` int(11) NOT NULL DEFAULT 0,
  `Timer_Asset_Id` int(11) NOT NULL DEFAULT 0,
  `Timer_Time` int(11) NOT NULL DEFAULT 0,
  `Ui_Order` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for areagroup_dbc
-- ----------------------------
DROP TABLE IF EXISTS `areagroup_dbc`; 
CREATE TABLE `areagroup_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `AreaID_1` INT NOT NULL DEFAULT '0',
 `AreaID_2` INT NOT NULL DEFAULT '0',
 `AreaID_3` INT NOT NULL DEFAULT '0',
 `AreaID_4` INT NOT NULL DEFAULT '0',
 `AreaID_5` INT NOT NULL DEFAULT '0',
 `AreaID_6` INT NOT NULL DEFAULT '0',
 `NextAreaID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for areapoi_dbc
-- ----------------------------
DROP TABLE IF EXISTS `areapoi_dbc`; 
CREATE TABLE `areapoi_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Importance` INT NOT NULL DEFAULT '0',
 `Icon_1` INT NOT NULL DEFAULT '0',
 `Icon_2` INT NOT NULL DEFAULT '0',
 `Icon_3` INT NOT NULL DEFAULT '0',
 `Icon_4` INT NOT NULL DEFAULT '0',
 `Icon_5` INT NOT NULL DEFAULT '0',
 `Icon_6` INT NOT NULL DEFAULT '0',
 `Icon_7` INT NOT NULL DEFAULT '0',
 `Icon_8` INT NOT NULL DEFAULT '0',
 `Icon_9` INT NOT NULL DEFAULT '0',
 `FactionID` INT NOT NULL DEFAULT '0',
 `X` FLOAT NOT NULL DEFAULT '0',
 `Y` FLOAT NOT NULL DEFAULT '0',
 `Z` FLOAT NOT NULL DEFAULT '0',
 `ContinentID` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `AreaID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Description_Lang_enUS` varchar(100) NULL,
 `Description_Lang_enGB` varchar(100) NULL,
 `Description_Lang_koKR` varchar(100) NULL,
 `Description_Lang_frFR` varchar(100) NULL,
 `Description_Lang_deDE` varchar(100) NULL,
 `Description_Lang_enCN` varchar(100) NULL,
 `Description_Lang_zhCN` varchar(100) NULL,
 `Description_Lang_enTW` varchar(100) NULL,
 `Description_Lang_zhTW` varchar(100) NULL,
 `Description_Lang_esES` varchar(100) NULL,
 `Description_Lang_esMX` varchar(100) NULL,
 `Description_Lang_ruRU` varchar(100) NULL,
 `Description_Lang_ptPT` varchar(100) NULL,
 `Description_Lang_ptBR` varchar(100) NULL,
 `Description_Lang_itIT` varchar(100) NULL,
 `Description_Lang_Unk` varchar(100) NULL,
 `Description_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `WorldStateID` INT NOT NULL DEFAULT '0',
 `WorldMapLink` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for auctionhouse_dbc
-- ----------------------------
DROP TABLE IF EXISTS `auctionhouse_dbc`; 
CREATE TABLE `auctionhouse_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `FactionID` INT NOT NULL DEFAULT '0',
 `DepositRate` INT NOT NULL DEFAULT '0',
 `ConsignmentRate` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for bankbagslotprices_dbc
-- ----------------------------
DROP TABLE IF EXISTS `bankbagslotprices_dbc`; 
CREATE TABLE `bankbagslotprices_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Cost` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for battlemasterlist_dbc
-- ----------------------------
DROP TABLE IF EXISTS `battlemasterlist_dbc`; 
CREATE TABLE `battlemasterlist_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `MapID_1` INT NOT NULL DEFAULT '0',
 `MapID_2` INT NOT NULL DEFAULT '0',
 `MapID_3` INT NOT NULL DEFAULT '0',
 `MapID_4` INT NOT NULL DEFAULT '0',
 `MapID_5` INT NOT NULL DEFAULT '0',
 `MapID_6` INT NOT NULL DEFAULT '0',
 `MapID_7` INT NOT NULL DEFAULT '0',
 `MapID_8` INT NOT NULL DEFAULT '0',
 `InstanceType` INT NOT NULL DEFAULT '0',
 `GroupsAllowed` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `MaxGroupSize` INT NOT NULL DEFAULT '0',
 `HolidayWorldState` INT NOT NULL DEFAULT '0',
 `Minlevel` INT NOT NULL DEFAULT '0',
 `Maxlevel` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for barbershopstyle_dbc
-- ----------------------------
DROP TABLE IF EXISTS `barbershopstyle_dbc`; 
CREATE TABLE `barbershopstyle_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Type` INT NOT NULL DEFAULT '0',
 `DisplayName_Lang_enUS` varchar(100) NULL,
 `DisplayName_Lang_enGB` varchar(100) NULL,
 `DisplayName_Lang_koKR` varchar(100) NULL,
 `DisplayName_Lang_frFR` varchar(100) NULL,
 `DisplayName_Lang_deDE` varchar(100) NULL,
 `DisplayName_Lang_enCN` varchar(100) NULL,
 `DisplayName_Lang_zhCN` varchar(100) NULL,
 `DisplayName_Lang_enTW` varchar(100) NULL,
 `DisplayName_Lang_zhTW` varchar(100) NULL,
 `DisplayName_Lang_esES` varchar(100) NULL,
 `DisplayName_Lang_esMX` varchar(100) NULL,
 `DisplayName_Lang_ruRU` varchar(100) NULL,
 `DisplayName_Lang_ptPT` varchar(100) NULL,
 `DisplayName_Lang_ptBR` varchar(100) NULL,
 `DisplayName_Lang_itIT` varchar(100) NULL,
 `DisplayName_Lang_Unk` varchar(100) NULL,
 `DisplayName_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Description_Lang_enUS` varchar(100) NULL,
 `Description_Lang_enGB` varchar(100) NULL,
 `Description_Lang_koKR` varchar(100) NULL,
 `Description_Lang_frFR` varchar(100) NULL,
 `Description_Lang_deDE` varchar(100) NULL,
 `Description_Lang_enCN` varchar(100) NULL,
 `Description_Lang_zhCN` varchar(100) NULL,
 `Description_Lang_enTW` varchar(100) NULL,
 `Description_Lang_zhTW` varchar(100) NULL,
 `Description_Lang_esES` varchar(100) NULL,
 `Description_Lang_esMX` varchar(100) NULL,
 `Description_Lang_ruRU` varchar(100) NULL,
 `Description_Lang_ptPT` varchar(100) NULL,
 `Description_Lang_ptBR` varchar(100) NULL,
 `Description_Lang_itIT` varchar(100) NULL,
 `Description_Lang_Unk` varchar(100) NULL,
 `Description_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Cost_Modifier` FLOAT NOT NULL DEFAULT '0',
 `Race` INT NOT NULL DEFAULT '0',
 `Sex` INT NOT NULL DEFAULT '0',
 `Data` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for charstartoutfit_dbc
-- ----------------------------
DROP TABLE IF EXISTS `charstartoutfit_dbc`; 
CREATE TABLE `charstartoutfit_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `RaceID` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `ClassID` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `SexID` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `OutfitID` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `ItemID_1` INT NOT NULL DEFAULT '0',
 `ItemID_2` INT NOT NULL DEFAULT '0',
 `ItemID_3` INT NOT NULL DEFAULT '0',
 `ItemID_4` INT NOT NULL DEFAULT '0',
 `ItemID_5` INT NOT NULL DEFAULT '0',
 `ItemID_6` INT NOT NULL DEFAULT '0',
 `ItemID_7` INT NOT NULL DEFAULT '0',
 `ItemID_8` INT NOT NULL DEFAULT '0',
 `ItemID_9` INT NOT NULL DEFAULT '0',
 `ItemID_10` INT NOT NULL DEFAULT '0',
 `ItemID_11` INT NOT NULL DEFAULT '0',
 `ItemID_12` INT NOT NULL DEFAULT '0',
 `ItemID_13` INT NOT NULL DEFAULT '0',
 `ItemID_14` INT NOT NULL DEFAULT '0',
 `ItemID_15` INT NOT NULL DEFAULT '0',
 `ItemID_16` INT NOT NULL DEFAULT '0',
 `ItemID_17` INT NOT NULL DEFAULT '0',
 `ItemID_18` INT NOT NULL DEFAULT '0',
 `ItemID_19` INT NOT NULL DEFAULT '0',
 `ItemID_20` INT NOT NULL DEFAULT '0',
 `ItemID_21` INT NOT NULL DEFAULT '0',
 `ItemID_22` INT NOT NULL DEFAULT '0',
 `ItemID_23` INT NOT NULL DEFAULT '0',
 `ItemID_24` INT NOT NULL DEFAULT '0',
 `DisplayItemID_1` INT NOT NULL DEFAULT '0',
 `DisplayItemID_2` INT NOT NULL DEFAULT '0',
 `DisplayItemID_3` INT NOT NULL DEFAULT '0',
 `DisplayItemID_4` INT NOT NULL DEFAULT '0',
 `DisplayItemID_5` INT NOT NULL DEFAULT '0',
 `DisplayItemID_6` INT NOT NULL DEFAULT '0',
 `DisplayItemID_7` INT NOT NULL DEFAULT '0',
 `DisplayItemID_8` INT NOT NULL DEFAULT '0',
 `DisplayItemID_9` INT NOT NULL DEFAULT '0',
 `DisplayItemID_10` INT NOT NULL DEFAULT '0',
 `DisplayItemID_11` INT NOT NULL DEFAULT '0',
 `DisplayItemID_12` INT NOT NULL DEFAULT '0',
 `DisplayItemID_13` INT NOT NULL DEFAULT '0',
 `DisplayItemID_14` INT NOT NULL DEFAULT '0',
 `DisplayItemID_15` INT NOT NULL DEFAULT '0',
 `DisplayItemID_16` INT NOT NULL DEFAULT '0',
 `DisplayItemID_17` INT NOT NULL DEFAULT '0',
 `DisplayItemID_18` INT NOT NULL DEFAULT '0',
 `DisplayItemID_19` INT NOT NULL DEFAULT '0',
 `DisplayItemID_20` INT NOT NULL DEFAULT '0',
 `DisplayItemID_21` INT NOT NULL DEFAULT '0',
 `DisplayItemID_22` INT NOT NULL DEFAULT '0',
 `DisplayItemID_23` INT NOT NULL DEFAULT '0',
 `DisplayItemID_24` INT NOT NULL DEFAULT '0',
 `InventoryType_1` INT NOT NULL DEFAULT '0',
 `InventoryType_2` INT NOT NULL DEFAULT '0',
 `InventoryType_3` INT NOT NULL DEFAULT '0',
 `InventoryType_4` INT NOT NULL DEFAULT '0',
 `InventoryType_5` INT NOT NULL DEFAULT '0',
 `InventoryType_6` INT NOT NULL DEFAULT '0',
 `InventoryType_7` INT NOT NULL DEFAULT '0',
 `InventoryType_8` INT NOT NULL DEFAULT '0',
 `InventoryType_9` INT NOT NULL DEFAULT '0',
 `InventoryType_10` INT NOT NULL DEFAULT '0',
 `InventoryType_11` INT NOT NULL DEFAULT '0',
 `InventoryType_12` INT NOT NULL DEFAULT '0',
 `InventoryType_13` INT NOT NULL DEFAULT '0',
 `InventoryType_14` INT NOT NULL DEFAULT '0',
 `InventoryType_15` INT NOT NULL DEFAULT '0',
 `InventoryType_16` INT NOT NULL DEFAULT '0',
 `InventoryType_17` INT NOT NULL DEFAULT '0',
 `InventoryType_18` INT NOT NULL DEFAULT '0',
 `InventoryType_19` INT NOT NULL DEFAULT '0',
 `InventoryType_20` INT NOT NULL DEFAULT '0',
 `InventoryType_21` INT NOT NULL DEFAULT '0',
 `InventoryType_22` INT NOT NULL DEFAULT '0',
 `InventoryType_23` INT NOT NULL DEFAULT '0',
 `InventoryType_24` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for chartitles_dbc
-- ----------------------------
DROP TABLE IF EXISTS `chartitles_dbc`; 
CREATE TABLE `chartitles_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Condition_ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Name1_Lang_enUS` varchar(100) NULL,
 `Name1_Lang_enGB` varchar(100) NULL,
 `Name1_Lang_koKR` varchar(100) NULL,
 `Name1_Lang_frFR` varchar(100) NULL,
 `Name1_Lang_deDE` varchar(100) NULL,
 `Name1_Lang_enCN` varchar(100) NULL,
 `Name1_Lang_zhCN` varchar(100) NULL,
 `Name1_Lang_enTW` varchar(100) NULL,
 `Name1_Lang_zhTW` varchar(100) NULL,
 `Name1_Lang_esES` varchar(100) NULL,
 `Name1_Lang_esMX` varchar(100) NULL,
 `Name1_Lang_ruRU` varchar(100) NULL,
 `Name1_Lang_ptPT` varchar(100) NULL,
 `Name1_Lang_ptBR` varchar(100) NULL,
 `Name1_Lang_itIT` varchar(100) NULL,
 `Name1_Lang_Unk` varchar(100) NULL,
 `Name1_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Mask_ID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for chatchannels_dbc
-- ----------------------------
DROP TABLE IF EXISTS `chatchannels_dbc`; 
CREATE TABLE `chatchannels_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `FactionGroup` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Shortcut_Lang_enUS` varchar(100) NULL,
 `Shortcut_Lang_enGB` varchar(100) NULL,
 `Shortcut_Lang_koKR` varchar(100) NULL,
 `Shortcut_Lang_frFR` varchar(100) NULL,
 `Shortcut_Lang_deDE` varchar(100) NULL,
 `Shortcut_Lang_enCN` varchar(100) NULL,
 `Shortcut_Lang_zhCN` varchar(100) NULL,
 `Shortcut_Lang_enTW` varchar(100) NULL,
 `Shortcut_Lang_zhTW` varchar(100) NULL,
 `Shortcut_Lang_esES` varchar(100) NULL,
 `Shortcut_Lang_esMX` varchar(100) NULL,
 `Shortcut_Lang_ruRU` varchar(100) NULL,
 `Shortcut_Lang_ptPT` varchar(100) NULL,
 `Shortcut_Lang_ptBR` varchar(100) NULL,
 `Shortcut_Lang_itIT` varchar(100) NULL,
 `Shortcut_Lang_Unk` varchar(100) NULL,
 `Shortcut_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for chrclasses_dbc
-- ----------------------------
DROP TABLE IF EXISTS `chrclasses_dbc`; 
CREATE TABLE `chrclasses_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Field01` INT NOT NULL DEFAULT '0',
 `DisplayPower` INT NOT NULL DEFAULT '0',
 `PetNameToken` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Name_Female_Lang_enUS` varchar(100) NULL,
 `Name_Female_Lang_enGB` varchar(100) NULL,
 `Name_Female_Lang_koKR` varchar(100) NULL,
 `Name_Female_Lang_frFR` varchar(100) NULL,
 `Name_Female_Lang_deDE` varchar(100) NULL,
 `Name_Female_Lang_enCN` varchar(100) NULL,
 `Name_Female_Lang_zhCN` varchar(100) NULL,
 `Name_Female_Lang_enTW` varchar(100) NULL,
 `Name_Female_Lang_zhTW` varchar(100) NULL,
 `Name_Female_Lang_esES` varchar(100) NULL,
 `Name_Female_Lang_esMX` varchar(100) NULL,
 `Name_Female_Lang_ruRU` varchar(100) NULL,
 `Name_Female_Lang_ptPT` varchar(100) NULL,
 `Name_Female_Lang_ptBR` varchar(100) NULL,
 `Name_Female_Lang_itIT` varchar(100) NULL,
 `Name_Female_Lang_Unk` varchar(100) NULL,
 `Name_Female_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Name_Male_Lang_enUS` varchar(100) NULL,
 `Name_Male_Lang_enGB` varchar(100) NULL,
 `Name_Male_Lang_koKR` varchar(100) NULL,
 `Name_Male_Lang_frFR` varchar(100) NULL,
 `Name_Male_Lang_deDE` varchar(100) NULL,
 `Name_Male_Lang_enCN` varchar(100) NULL,
 `Name_Male_Lang_zhCN` varchar(100) NULL,
 `Name_Male_Lang_enTW` varchar(100) NULL,
 `Name_Male_Lang_zhTW` varchar(100) NULL,
 `Name_Male_Lang_esES` varchar(100) NULL,
 `Name_Male_Lang_esMX` varchar(100) NULL,
 `Name_Male_Lang_ruRU` varchar(100) NULL,
 `Name_Male_Lang_ptPT` varchar(100) NULL,
 `Name_Male_Lang_ptBR` varchar(100) NULL,
 `Name_Male_Lang_itIT` varchar(100) NULL,
 `Name_Male_Lang_Unk` varchar(100) NULL,
 `Name_Male_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Filename` varchar(100) NULL,
 `SpellClassSet` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `CinematicSequenceID` INT NOT NULL DEFAULT '0',
 `Required_Expansion` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for chrraces_dbc
-- ----------------------------
DROP TABLE IF EXISTS `chrraces_dbc`; 
CREATE TABLE `chrraces_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `FactionID` INT NOT NULL DEFAULT '0',
 `ExplorationSoundID` INT NOT NULL DEFAULT '0',
 `MaleDisplayId` INT NOT NULL DEFAULT '0',
 `FemaleDisplayId` INT NOT NULL DEFAULT '0',
 `ClientPrefix` varchar(100) NULL,
 `BaseLanguage` INT NOT NULL DEFAULT '0',
 `CreatureType` INT NOT NULL DEFAULT '0',
 `ResSicknessSpellID` INT NOT NULL DEFAULT '0',
 `SplashSoundID` INT NOT NULL DEFAULT '0',
 `ClientFilestring` varchar(100) NULL,
 `CinematicSequenceID` INT NOT NULL DEFAULT '0',
 `Alliance` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Name_Female_Lang_enUS` varchar(100) NULL,
 `Name_Female_Lang_enGB` varchar(100) NULL,
 `Name_Female_Lang_koKR` varchar(100) NULL,
 `Name_Female_Lang_frFR` varchar(100) NULL,
 `Name_Female_Lang_deDE` varchar(100) NULL,
 `Name_Female_Lang_enCN` varchar(100) NULL,
 `Name_Female_Lang_zhCN` varchar(100) NULL,
 `Name_Female_Lang_enTW` varchar(100) NULL,
 `Name_Female_Lang_zhTW` varchar(100) NULL,
 `Name_Female_Lang_esES` varchar(100) NULL,
 `Name_Female_Lang_esMX` varchar(100) NULL,
 `Name_Female_Lang_ruRU` varchar(100) NULL,
 `Name_Female_Lang_ptPT` varchar(100) NULL,
 `Name_Female_Lang_ptBR` varchar(100) NULL,
 `Name_Female_Lang_itIT` varchar(100) NULL,
 `Name_Female_Lang_Unk` varchar(100) NULL,
 `Name_Female_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Name_Male_Lang_enUS` varchar(100) NULL,
 `Name_Male_Lang_enGB` varchar(100) NULL,
 `Name_Male_Lang_koKR` varchar(100) NULL,
 `Name_Male_Lang_frFR` varchar(100) NULL,
 `Name_Male_Lang_deDE` varchar(100) NULL,
 `Name_Male_Lang_enCN` varchar(100) NULL,
 `Name_Male_Lang_zhCN` varchar(100) NULL,
 `Name_Male_Lang_enTW` varchar(100) NULL,
 `Name_Male_Lang_zhTW` varchar(100) NULL,
 `Name_Male_Lang_esES` varchar(100) NULL,
 `Name_Male_Lang_esMX` varchar(100) NULL,
 `Name_Male_Lang_ruRU` varchar(100) NULL,
 `Name_Male_Lang_ptPT` varchar(100) NULL,
 `Name_Male_Lang_ptBR` varchar(100) NULL,
 `Name_Male_Lang_itIT` varchar(100) NULL,
 `Name_Male_Lang_Unk` varchar(100) NULL,
 `Name_Male_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `FacialHairCustomization_1` varchar(100) NULL,
 `FacialHairCustomization_2` varchar(100) NULL,
 `HairCustomization` varchar(100) NULL,
 `Required_Expansion` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for cinematicsequences_dbc
-- ----------------------------
DROP TABLE IF EXISTS `cinematicsequences_dbc`; 
CREATE TABLE `cinematicsequences_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `SoundID` INT NOT NULL DEFAULT '0',
 `Camera_1` INT NOT NULL DEFAULT '0',
 `Camera_2` INT NOT NULL DEFAULT '0',
 `Camera_3` INT NOT NULL DEFAULT '0',
 `Camera_4` INT NOT NULL DEFAULT '0',
 `Camera_5` INT NOT NULL DEFAULT '0',
 `Camera_6` INT NOT NULL DEFAULT '0',
 `Camera_7` INT NOT NULL DEFAULT '0',
 `Camera_8` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for creaturedisplayinfo_dbc
-- ----------------------------
DROP TABLE IF EXISTS `creaturedisplayinfo_dbc`; 
CREATE TABLE `creaturedisplayinfo_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ModelID` INT NOT NULL DEFAULT '0',
 `SoundID` INT NOT NULL DEFAULT '0',
 `ExtendedDisplayInfoID` INT NOT NULL DEFAULT '0',
 `CreatureModelScale` FLOAT NOT NULL DEFAULT '0',
 `CreatureModelAlpha` INT NOT NULL DEFAULT '0',
 `TextureVariation_1` varchar(100) NULL,
 `TextureVariation_2` varchar(100) NULL,
 `TextureVariation_3` varchar(100) NULL,
 `PortraitTextureName` varchar(100) NULL,
 `BloodLevel` INT NOT NULL DEFAULT '0',
 `BloodID` INT NOT NULL DEFAULT '0',
 `NPCSoundID` INT NOT NULL DEFAULT '0',
 `ParticleColorID` INT NOT NULL DEFAULT '0',
 `CreatureGeosetData` INT NOT NULL DEFAULT '0',
 `ObjectEffectPackageID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for creaturefamily_dbc
-- ----------------------------
DROP TABLE IF EXISTS `creaturefamily_dbc`; 
CREATE TABLE `creaturefamily_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `MinScale` FLOAT NOT NULL DEFAULT '0',
 `MinScaleLevel` INT NOT NULL DEFAULT '0',
 `MaxScale` FLOAT NOT NULL DEFAULT '0',
 `MaxScaleLevel` INT NOT NULL DEFAULT '0',
 `SkillLine_1` INT NOT NULL DEFAULT '0',
 `SkillLine_2` INT NOT NULL DEFAULT '0',
 `PetFoodMask` INT NOT NULL DEFAULT '0',
 `PetTalentType` INT NOT NULL DEFAULT '0',
 `CategoryEnumID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `IconFile` varchar(100) NULL,
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for creaturemodeldata_dbc
-- ----------------------------
DROP TABLE IF EXISTS `creaturemodeldata_dbc`; 
CREATE TABLE `creaturemodeldata_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `ModelName` varchar(100) NULL,
 `SizeClass` INT NOT NULL DEFAULT '0',
 `ModelScale` FLOAT NOT NULL DEFAULT '0',
 `BloodID` INT NOT NULL DEFAULT '0',
 `FootprintTextureID` INT NOT NULL DEFAULT '0',
 `FootprintTextureLength` FLOAT NOT NULL DEFAULT '0',
 `FootprintTextureWidth` FLOAT NOT NULL DEFAULT '0',
 `FootprintParticleScale` FLOAT NOT NULL DEFAULT '0',
 `FoleyMaterialID` INT NOT NULL DEFAULT '0',
 `FootstepShakeSize` INT NOT NULL DEFAULT '0',
 `DeathThudShakeSize` INT NOT NULL DEFAULT '0',
 `SoundID` INT NOT NULL DEFAULT '0',
 `CollisionWidth` FLOAT NOT NULL DEFAULT '0',
 `CollisionHeight` FLOAT NOT NULL DEFAULT '0',
 `MountHeight` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMinX` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMinY` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMinZ` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMaxX` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMaxY` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMaxZ` FLOAT NOT NULL DEFAULT '0',
 `WorldEffectScale` FLOAT NOT NULL DEFAULT '0',
 `AttachedEffectScale` FLOAT NOT NULL DEFAULT '0',
 `MissileCollisionRadius` FLOAT NOT NULL DEFAULT '0',
 `MissileCollisionPush` FLOAT NOT NULL DEFAULT '0',
 `MissileCollisionRaise` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for creaturespelldata_dbc
-- ----------------------------
DROP TABLE IF EXISTS `creaturespelldata_dbc`; 
CREATE TABLE `creaturespelldata_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Spells_1` INT NOT NULL DEFAULT '0',
 `Spells_2` INT NOT NULL DEFAULT '0',
 `Spells_3` INT NOT NULL DEFAULT '0',
 `Spells_4` INT NOT NULL DEFAULT '0',
 `Availability_1` INT NOT NULL DEFAULT '0',
 `Availability_2` INT NOT NULL DEFAULT '0',
 `Availability_3` INT NOT NULL DEFAULT '0',
 `Availability_4` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for creaturetype_dbc
-- ----------------------------
DROP TABLE IF EXISTS `creaturetype_dbc`; 
CREATE TABLE `creaturetype_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for currencytypes_dbc
-- ----------------------------
DROP TABLE IF EXISTS `currencytypes_dbc`; 
CREATE TABLE `currencytypes_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ItemID` INT NOT NULL DEFAULT '0',
 `CategoryID` INT NOT NULL DEFAULT '0',
 `BitIndex` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for destructiblemodeldata_dbc
-- ----------------------------
DROP TABLE IF EXISTS `destructiblemodeldata_dbc`; 
CREATE TABLE `destructiblemodeldata_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `State0Wmo` INT NOT NULL DEFAULT '0',
 `State0DestructionDoodadSet` INT NOT NULL DEFAULT '0',
 `State0ImpactEffectDoodadSet` INT NOT NULL DEFAULT '0',
 `State0AmbientDoodadSet` INT NOT NULL DEFAULT '0',
 `State1Wmo` INT NOT NULL DEFAULT '0',
 `State1DestructionDoodadSet` INT NOT NULL DEFAULT '0',
 `State1ImpactEffectDoodadSet` INT NOT NULL DEFAULT '0',
 `State1AmbientDoodadSet` INT NOT NULL DEFAULT '0',
 `State2Wmo` INT NOT NULL DEFAULT '0',
 `State2DestructionDoodadSet` INT NOT NULL DEFAULT '0',
 `State2ImpactEffectDoodadSet` INT NOT NULL DEFAULT '0',
 `State2AmbientDoodadSet` INT NOT NULL DEFAULT '0',
 `State3Wmo` INT NOT NULL DEFAULT '0',
 `State3DestructionDoodadSet` INT NOT NULL DEFAULT '0',
 `State3ImpactEffectDoodadSet` INT NOT NULL DEFAULT '0',
 `State3AmbientDoodadSet` INT NOT NULL DEFAULT '0',
 `Field17` INT NOT NULL DEFAULT '0',
 `Field18` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for dungeonencounter_dbc
-- ----------------------------
DROP TABLE IF EXISTS `dungeonencounter_dbc`; 
CREATE TABLE `dungeonencounter_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `MapID` INT NOT NULL DEFAULT '0',
 `Difficulty` INT NOT NULL DEFAULT '0',
 `OrderIndex` INT NOT NULL DEFAULT '0',
 `Bit` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `SpellIconID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for durabilitycosts_dbc
-- ----------------------------
DROP TABLE IF EXISTS `durabilitycosts_dbc`; 
CREATE TABLE `durabilitycosts_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_1` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_2` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_3` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_4` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_5` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_6` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_7` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_8` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_9` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_10` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_11` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_12` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_13` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_14` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_15` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_16` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_17` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_18` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_19` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_20` INT NOT NULL DEFAULT '0',
 `WeaponSubClassCost_21` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_1` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_2` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_3` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_4` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_5` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_6` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_7` INT NOT NULL DEFAULT '0',
 `ArmorSubClassCost_8` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for durabilityquality_dbc
-- ----------------------------
DROP TABLE IF EXISTS `durabilityquality_dbc`; 
CREATE TABLE `durabilityquality_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for emotes_dbc
-- ----------------------------
DROP TABLE IF EXISTS `emotes_dbc`; 
CREATE TABLE `emotes_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `EmoteSlashCommand` varchar(100) NULL,
 `AnimID` INT NOT NULL DEFAULT '0',
 `EmoteFlags` INT NOT NULL DEFAULT '0',
 `EmoteSpecProc` INT NOT NULL DEFAULT '0',
 `EmoteSpecProcParam` INT NOT NULL DEFAULT '0',
 `EventSoundID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for emotestext_dbc
-- ----------------------------
DROP TABLE IF EXISTS `emotestext_dbc`; 
CREATE TABLE `emotestext_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name` varchar(100) NULL,
 `EmoteID` INT NOT NULL DEFAULT '0',
 `EmoteText_1` INT NOT NULL DEFAULT '0',
 `EmoteText_2` INT NOT NULL DEFAULT '0',
 `EmoteText_3` INT NOT NULL DEFAULT '0',
 `EmoteText_4` INT NOT NULL DEFAULT '0',
 `EmoteText_5` INT NOT NULL DEFAULT '0',
 `EmoteText_6` INT NOT NULL DEFAULT '0',
 `EmoteText_7` INT NOT NULL DEFAULT '0',
 `EmoteText_8` INT NOT NULL DEFAULT '0',
 `EmoteText_9` INT NOT NULL DEFAULT '0',
 `EmoteText_10` INT NOT NULL DEFAULT '0',
 `EmoteText_11` INT NOT NULL DEFAULT '0',
 `EmoteText_12` INT NOT NULL DEFAULT '0',
 `EmoteText_13` INT NOT NULL DEFAULT '0',
 `EmoteText_14` INT NOT NULL DEFAULT '0',
 `EmoteText_15` INT NOT NULL DEFAULT '0',
 `EmoteText_16` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 
 
-- ----------------------------
-- Table structure for faction_dbc
-- ----------------------------
DROP TABLE IF EXISTS `faction_dbc`; 
CREATE TABLE `faction_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ReputationIndex` INT NOT NULL DEFAULT '0',
 `ReputationRaceMask_1` INT NOT NULL DEFAULT '0',
 `ReputationRaceMask_2` INT NOT NULL DEFAULT '0',
 `ReputationRaceMask_3` INT NOT NULL DEFAULT '0',
 `ReputationRaceMask_4` INT NOT NULL DEFAULT '0',
 `ReputationClassMask_1` INT NOT NULL DEFAULT '0',
 `ReputationClassMask_2` INT NOT NULL DEFAULT '0',
 `ReputationClassMask_3` INT NOT NULL DEFAULT '0',
 `ReputationClassMask_4` INT NOT NULL DEFAULT '0',
 `ReputationBase_1` INT NOT NULL DEFAULT '0',
 `ReputationBase_2` INT NOT NULL DEFAULT '0',
 `ReputationBase_3` INT NOT NULL DEFAULT '0',
 `ReputationBase_4` INT NOT NULL DEFAULT '0',
 `ReputationFlags_1` INT NOT NULL DEFAULT '0',
 `ReputationFlags_2` INT NOT NULL DEFAULT '0',
 `ReputationFlags_3` INT NOT NULL DEFAULT '0',
 `ReputationFlags_4` INT NOT NULL DEFAULT '0',
 `ParentFactionID` INT NOT NULL DEFAULT '0',
 `ParentFactionMod_1` FLOAT NOT NULL DEFAULT '0',
 `ParentFactionMod_2` FLOAT NOT NULL DEFAULT '0',
 `ParentFactionCap_1` INT NOT NULL DEFAULT '0',
 `ParentFactionCap_2` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Description_Lang_enUS` varchar(100) NULL,
 `Description_Lang_enGB` varchar(100) NULL,
 `Description_Lang_koKR` varchar(100) NULL,
 `Description_Lang_frFR` varchar(100) NULL,
 `Description_Lang_deDE` varchar(100) NULL,
 `Description_Lang_enCN` varchar(100) NULL,
 `Description_Lang_zhCN` varchar(100) NULL,
 `Description_Lang_enTW` varchar(100) NULL,
 `Description_Lang_zhTW` varchar(100) NULL,
 `Description_Lang_esES` varchar(100) NULL,
 `Description_Lang_esMX` varchar(100) NULL,
 `Description_Lang_ruRU` varchar(100) NULL,
 `Description_Lang_ptPT` varchar(100) NULL,
 `Description_Lang_ptBR` varchar(100) NULL,
 `Description_Lang_itIT` varchar(100) NULL,
 `Description_Lang_Unk` varchar(100) NULL,
 `Description_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for factiontemplate_dbc
-- ----------------------------
DROP TABLE IF EXISTS `factiontemplate_dbc`; 
CREATE TABLE `factiontemplate_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Faction` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `FactionGroup` INT NOT NULL DEFAULT '0',
 `FriendGroup` INT NOT NULL DEFAULT '0',
 `EnemyGroup` INT NOT NULL DEFAULT '0',
 `Enemies_1` INT NOT NULL DEFAULT '0',
 `Enemies_2` INT NOT NULL DEFAULT '0',
 `Enemies_3` INT NOT NULL DEFAULT '0',
 `Enemies_4` INT NOT NULL DEFAULT '0',
 `Friend_1` INT NOT NULL DEFAULT '0',
 `Friend_2` INT NOT NULL DEFAULT '0',
 `Friend_3` INT NOT NULL DEFAULT '0',
 `Friend_4` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for gameobjectdisplayinfo_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gameobjectdisplayinfo_dbc`; 
CREATE TABLE `gameobjectdisplayinfo_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ModelName` varchar(100) NULL,
 `Sound_1` INT NOT NULL DEFAULT '0',
 `Sound_2` INT NOT NULL DEFAULT '0',
 `Sound_3` INT NOT NULL DEFAULT '0',
 `Sound_4` INT NOT NULL DEFAULT '0',
 `Sound_5` INT NOT NULL DEFAULT '0',
 `Sound_6` INT NOT NULL DEFAULT '0',
 `Sound_7` INT NOT NULL DEFAULT '0',
 `Sound_8` INT NOT NULL DEFAULT '0',
 `Sound_9` INT NOT NULL DEFAULT '0',
 `Sound_10` INT NOT NULL DEFAULT '0',
 `GeoBoxMinX` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMinY` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMinZ` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMaxX` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMaxY` FLOAT NOT NULL DEFAULT '0',
 `GeoBoxMaxZ` FLOAT NOT NULL DEFAULT '0',
 `ObjectEffectPackageID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for gemproperties_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gemproperties_dbc`; 
CREATE TABLE `gemproperties_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Enchant_Id` INT NOT NULL DEFAULT '0',
 `Maxcount_Inv` INT NOT NULL DEFAULT '0',
 `Maxcount_Item` INT NOT NULL DEFAULT '0',
 `Type` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for glyphproperties_dbc
-- ----------------------------
DROP TABLE IF EXISTS `glyphproperties_dbc`; 
CREATE TABLE `glyphproperties_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `SpellID` INT NOT NULL DEFAULT '0',
 `GlyphSlotFlags` INT NOT NULL DEFAULT '0',
 `SpellIconID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for glyphslot_dbc
-- ----------------------------
DROP TABLE IF EXISTS `glyphslot_dbc`; 
CREATE TABLE `glyphslot_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Type` INT NOT NULL DEFAULT '0',
 `Tooltip` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for gtbarbershopcostbase_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtbarbershopcostbase_dbc`; 
CREATE TABLE `gtbarbershopcostbase_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Records of gtbarbershopcostbase_dbc
-- ----------------------------
INSERT INTO `gtbarbershopcostbase_dbc` VALUES 
(1, 0),
(2, 3),
(3, 4),
(4, 4),
(5, 4),
(6, 6),
(7, 12),
(8, 23),
(9, 40),
(10, 65),
(11, 100),
(12, 146),
(13, 204),
(14, 276),
(15, 364),
(16, 469),
(17, 592),
(18, 735),
(19, 900),
(20, 1088),
(21, 1300),
(22, 1538),
(23, 1804),
(24, 2099),
(25, 2424),
(26, 2781),
(27, 3172),
(28, 3598),
(29, 4060),
(30, 4560),
(31, 5100),
(32, 5681),
(33, 6304),
(34, 6971),
(35, 7684),
(36, 8444),
(37, 9252),
(38, 10110),
(39, 11020),
(40, 11983),
(41, 13000),
(42, 14073),
(43, 15204),
(44, 16394),
(45, 17644),
(46, 18956),
(47, 20332),
(48, 21773),
(49, 23280),
(50, 24855),
(51, 26500),
(52, 28216),
(53, 30004),
(54, 31866),
(55, 33804),
(56, 35819),
(57, 37912),
(58, 40085),
(59, 42340),
(60, 44678),
(61, 47100),
(62, 49608),
(63, 52204),
(64, 54889),
(65, 57664),
(66, 60531),
(67, 63492),
(68, 66548),
(69, 69700),
(70, 72950),
(71, 76300),
(72, 79751),
(73, 83304),
(74, 86961),
(75, 90724),
(76, 94594),
(77, 98572),
(78, 102660),
(79, 106860),
(80, 111173),
(81, 115600),
(82, 120143),
(83, 124804),
(84, 129584),
(85, 134484),
(86, 139506),
(87, 144652),
(88, 149923),
(89, 155320),
(90, 160845),
(91, 166500),
(92, 172286),
(93, 178204),
(94, 184256),
(95, 190444),
(96, 196769),
(97, 203232),
(98, 209835),
(99, 216580),
(100, 223468);

-- ----------------------------
-- Table structure for gtcombatratings_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtcombatratings_dbc`; 
CREATE TABLE `gtcombatratings_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of gtcombatratings_dbc
-- ----------------------------
INSERT INTO `gtcombatratings_dbc` VALUES 
(1,0.096154),
(2,0.096154),
(3,0.096154),
(4,0.096154),
(5,0.096154),
(6,0.096154),
(7,0.096154),
(8,0.096154),
(9,0.096154),
(10,0.096154),
(11,0.144231),
(12,0.192308),
(13,0.240385),
(14,0.288462),
(15,0.336538),
(16,0.384615),
(17,0.432692),
(18,0.480769),
(19,0.528846),
(20,0.576923),
(21,0.625),
(22,0.673077),
(23,0.721154),
(24,0.769231),
(25,0.817308),
(26,0.865385),
(27,0.913462),
(28,0.961538),
(29,1.009615),
(30,1.057692),
(31,1.105769),
(32,1.153846),
(33,1.201923),
(34,1.25),
(35,1.298077),
(36,1.346154),
(37,1.394231),
(38,1.442308),
(39,1.490385),
(40,1.538462),
(41,1.586538),
(42,1.634615),
(43,1.682692),
(44,1.730769),
(45,1.778846),
(46,1.826923),
(47,1.875),
(48,1.923077),
(49,1.971154),
(50,2.019231),
(51,2.067308),
(52,2.115385),
(53,2.163461),
(54,2.211539),
(55,2.259615),
(56,2.307692),
(57,2.355769),
(58,2.403846),
(59,2.451923),
(60,2.5),
(61,2.594937),
(62,2.697368),
(63,2.808219),
(64,2.928571),
(65,3.059702),
(66,3.203125),
(67,3.360656),
(68,3.534483),
(69,3.727273),
(70,3.942308),
(71,4.241735),
(72,4.563906),
(73,4.910545),
(74,5.283514),
(75,5.684809),
(76,6.116584),
(77,6.581153),
(78,7.081008),
(79,7.618829),
(80,8.197496),
(81,8.820117),
(82,9.490026),
(83,10.21082),
(84,10.98635),
(85,11.8208),
(86,12.71861),
(87,13.68462),
(88,14.724),
(89,15.84233),
(90,17.04559),
(91,18.34024),
(92,19.73323),
(93,21.23202),
(94,22.84464),
(95,24.57975),
(96,26.44664),
(97,28.45532),
(98,30.61658),
(99,32.94198),
(100,35.444),
(101,0.75),
(102,0.75),
(103,0.75),
(104,0.75),
(105,0.75),
(106,0.75),
(107,0.75),
(108,0.75),
(109,0.75),
(110,0.75),
(111,0.75),
(112,0.75),
(113,0.75),
(114,0.75),
(115,0.75),
(116,0.75),
(117,0.75),
(118,0.75),
(119,0.75),
(120,0.75),
(121,0.75),
(122,0.75),
(123,0.75),
(124,0.75),
(125,0.75),
(126,0.75),
(127,0.75),
(128,0.75),
(129,0.75),
(130,0.75),
(131,0.75),
(132,0.75),
(133,0.75),
(134,0.75),
(135,0.778846),
(136,0.807692),
(137,0.836538),
(138,0.865385),
(139,0.894231),
(140,0.923077),
(141,0.951923),
(142,0.980769),
(143,1.009615),
(144,1.038462),
(145,1.067308),
(146,1.096154),
(147,1.125),
(148,1.153846),
(149,1.182692),
(150,1.211538),
(151,1.240385),
(152,1.269231),
(153,1.298077),
(154,1.326923),
(155,1.355769),
(156,1.384615),
(157,1.413461),
(158,1.442308),
(159,1.471154),
(160,1.5),
(161,1.556962),
(162,1.618421),
(163,1.684931),
(164,1.757143),
(165,1.835821),
(166,1.921875),
(167,2.016393),
(168,2.12069),
(169,2.236364),
(170,2.365385),
(171,2.545041),
(172,2.738343),
(173,2.946327),
(174,3.170108),
(175,3.410886),
(176,3.66995),
(177,3.948692),
(178,4.248605),
(179,4.571297),
(180,4.918498),
(181,5.29207),
(182,5.694016),
(183,6.12649),
(184,6.591812),
(185,7.092476),
(186,7.631167),
(187,8.210773),
(188,8.834401),
(189,9.505396),
(190,10.22735),
(191,11.00415),
(192,11.83994),
(193,12.73921),
(194,13.70678),
(195,14.74785),
(196,15.86798),
(197,17.07319),
(198,18.36995),
(199,19.76519),
(200,21.2664),
(201,6.900001),
(202,6.900001),
(203,6.900001),
(204,6.900001),
(205,6.900001),
(206,6.900001),
(207,6.900001),
(208,6.900001),
(209,6.900001),
(210,6.900001),
(211,6.900001),
(212,6.900001),
(213,6.900001),
(214,6.900001),
(215,6.900001),
(216,6.900001),
(217,6.900001),
(218,6.900001),
(219,6.900001),
(220,6.900001),
(221,6.900001),
(222,6.900001),
(223,6.900001),
(224,6.900001),
(225,6.900001),
(226,6.900001),
(227,6.900001),
(228,6.900001),
(229,6.900001),
(230,6.900001),
(231,6.900001),
(232,6.900001),
(233,6.900001),
(234,6.900001),
(235,7.165385),
(236,7.430769),
(237,7.696154),
(238,7.961538),
(239,8.226923),
(240,8.492308),
(241,8.757692),
(242,9.023077),
(243,9.288462),
(244,9.553846),
(245,9.819231),
(246,10.08462),
(247,10.35),
(248,10.61539),
(249,10.88077),
(250,11.14615),
(251,11.41154),
(252,11.67692),
(253,11.94231),
(254,12.20769),
(255,12.47308),
(256,12.73846),
(257,13.00385),
(258,13.26923),
(259,13.53462),
(260,13.8),
(261,14.32405),
(262,14.88948),
(263,15.50137),
(264,16.16571),
(265,16.88955),
(266,17.68125),
(267,18.55082),
(268,19.51035),
(269,20.57455),
(270,21.76154),
(271,23.41438),
(272,25.19276),
(273,27.10621),
(274,29.165),
(275,31.38015),
(276,33.76355),
(277,36.32797),
(278,39.08717),
(279,42.05593),
(280,45.25019),
(281,48.68705),
(282,52.38495),
(283,56.36371),
(284,60.64467),
(285,65.25079),
(286,70.20674),
(287,75.53911),
(288,81.2765),
(289,87.44964),
(290,94.09166),
(291,101.2381),
(292,108.9274),
(293,117.2007),
(294,126.1024),
(295,135.6802),
(296,145.9854),
(297,157.0734),
(298,169.0035),
(299,181.8397),
(300,195.6509),
(301,6.900001),
(302,6.900001),
(303,6.900001),
(304,6.900001),
(305,6.900001),
(306,6.900001),
(307,6.900001),
(308,6.900001),
(309,6.900001),
(310,6.900001),
(311,6.900001),
(312,6.900001),
(313,6.900001),
(314,6.900001),
(315,6.900001),
(316,6.900001),
(317,6.900001),
(318,6.900001),
(319,6.900001),
(320,6.900001),
(321,6.900001),
(322,6.900001),
(323,6.900001),
(324,6.900001),
(325,6.900001),
(326,6.900001),
(327,6.900001),
(328,6.900001),
(329,6.900001),
(330,6.900001),
(331,6.900001),
(332,6.900001),
(333,6.900001),
(334,6.900001),
(335,7.165385),
(336,7.430769),
(337,7.696154),
(338,7.961538),
(339,8.226923),
(340,8.492308),
(341,8.757692),
(342,9.023077),
(343,9.288462),
(344,9.553846),
(345,9.819231),
(346,10.08462),
(347,10.35),
(348,10.61539),
(349,10.88077),
(350,11.14615),
(351,11.41154),
(352,11.67692),
(353,11.94231),
(354,12.20769),
(355,12.47308),
(356,12.73846),
(357,13.00385),
(358,13.26923),
(359,13.53462),
(360,13.8),
(361,14.32405),
(362,14.88948),
(363,15.50137),
(364,16.16571),
(365,16.88955),
(366,17.68125),
(367,18.55082),
(368,19.51035),
(369,20.57455),
(370,21.76154),
(371,23.41438),
(372,25.19276),
(373,27.10621),
(374,29.165),
(375,31.38015),
(376,33.76355),
(377,36.32797),
(378,39.08717),
(379,42.05593),
(380,45.25019),
(381,48.68705),
(382,52.38495),
(383,56.36371),
(384,60.64467),
(385,65.25079),
(386,70.20674),
(387,75.53911),
(388,81.2765),
(389,87.44964),
(390,94.09166),
(391,101.2381),
(392,108.9274),
(393,117.2007),
(394,126.1024),
(395,135.6802),
(396,145.9854),
(397,157.0734),
(398,169.0035),
(399,181.8397),
(400,195.6509),
(401,2.5),
(402,2.5),
(403,2.5),
(404,2.5),
(405,2.5),
(406,2.5),
(407,2.5),
(408,2.5),
(409,2.5),
(410,2.5),
(411,2.5),
(412,2.5),
(413,2.5),
(414,2.5),
(415,2.5),
(416,2.5),
(417,2.5),
(418,2.5),
(419,2.5),
(420,2.5),
(421,2.5),
(422,2.5),
(423,2.5),
(424,2.5),
(425,2.5),
(426,2.5),
(427,2.5),
(428,2.5),
(429,2.5),
(430,2.5),
(431,2.5),
(432,2.5),
(433,2.5),
(434,2.5),
(435,2.596154),
(436,2.692308),
(437,2.788461),
(438,2.884615),
(439,2.980769),
(440,3.076923),
(441,3.173077),
(442,3.269231),
(443,3.365385),
(444,3.461538),
(445,3.557692),
(446,3.653846),
(447,3.75),
(448,3.846154),
(449,3.942308),
(450,4.038462),
(451,4.134615),
(452,4.230769),
(453,4.326923),
(454,4.423077),
(455,4.519231),
(456,4.615385),
(457,4.711538),
(458,4.807693),
(459,4.903846),
(460,5),
(461,5.189874),
(462,5.394737),
(463,5.616438),
(464,5.857142),
(465,6.119403),
(466,6.406251),
(467,6.721312),
(468,7.068965),
(469,7.454545),
(470,7.884615),
(471,8.483471),
(472,9.127811),
(473,9.821091),
(474,10.56703),
(475,11.36962),
(476,12.23317),
(477,13.16231),
(478,14.16202),
(479,15.23766),
(480,16.39499),
(481,17.64024),
(482,18.98005),
(483,20.42164),
(484,21.97271),
(485,23.64159),
(486,25.43723),
(487,27.36925),
(488,29.44801),
(489,31.68465),
(490,34.09118),
(491,36.68049),
(492,39.46646),
(493,42.46404),
(494,45.68928),
(495,49.15949),
(496,52.89328),
(497,56.91065),
(498,61.23315),
(499,65.88396),
(500,70.88801),
(501,0.384615),
(502,0.384615),
(503,0.384615),
(504,0.384615),
(505,0.384615),
(506,0.384615),
(507,0.384615),
(508,0.384615),
(509,0.384615),
(510,0.384615),
(511,0.576923),
(512,0.769231),
(513,0.961538),
(514,1.153846),
(515,1.346154),
(516,1.538462),
(517,1.730769),
(518,1.923077),
(519,2.115385),
(520,2.307692),
(521,2.5),
(522,2.692308),
(523,2.884615),
(524,3.076923),
(525,3.269231),
(526,3.461538),
(527,3.653846),
(528,3.846154),
(529,4.038462),
(530,4.230769),
(531,4.423077),
(532,4.615385),
(533,4.807693),
(534,5),
(535,5.192308),
(536,5.384615),
(537,5.576923),
(538,5.769231),
(539,5.961538),
(540,6.153846),
(541,6.346154),
(542,6.538462),
(543,6.730769),
(544,6.923077),
(545,7.115385),
(546,7.307693),
(547,7.5),
(548,7.692307),
(549,7.884615),
(550,8.076923),
(551,8.269231),
(552,8.461538),
(553,8.653846),
(554,8.846154),
(555,9.038462),
(556,9.230769),
(557,9.423077),
(558,9.615385),
(559,9.807693),
(560,10),
(561,10.37975),
(562,10.78947),
(563,11.23288),
(564,11.71429),
(565,12.23881),
(566,12.8125),
(567,13.44262),
(568,14.13793),
(569,14.90909),
(570,15.76923),
(571,16.96694),
(572,18.25562),
(573,19.64218),
(574,21.13405),
(575,22.73924),
(576,24.46634),
(577,26.32462),
(578,28.32404),
(579,30.47532),
(580,32.78999),
(581,35.28047),
(582,37.96011),
(583,40.84327),
(584,43.94542),
(585,47.28318),
(586,50.87445),
(587,54.73849),
(588,58.89602),
(589,63.36931),
(590,68.18237),
(591,73.36098),
(592,78.93292),
(593,84.92807),
(594,91.37856),
(595,98.31898),
(596,105.7866),
(597,113.8213),
(598,122.4663),
(599,131.7679),
(600,141.776),
(601,0.384615),
(602,0.384615),
(603,0.384615),
(604,0.384615),
(605,0.384615),
(606,0.384615),
(607,0.384615),
(608,0.384615),
(609,0.384615),
(610,0.384615),
(611,0.576923),
(612,0.769231),
(613,0.961538),
(614,1.153846),
(615,1.346154),
(616,1.538462),
(617,1.730769),
(618,1.923077),
(619,2.115385),
(620,2.307692),
(621,2.5),
(622,2.692308),
(623,2.884615),
(624,3.076923),
(625,3.269231),
(626,3.461538),
(627,3.653846),
(628,3.846154),
(629,4.038462),
(630,4.230769),
(631,4.423077),
(632,4.615385),
(633,4.807693),
(634,5),
(635,5.192308),
(636,5.384615),
(637,5.576923),
(638,5.769231),
(639,5.961538),
(640,6.153846),
(641,6.346154),
(642,6.538462),
(643,6.730769),
(644,6.923077),
(645,7.115385),
(646,7.307693),
(647,7.5),
(648,7.692307),
(649,7.884615),
(650,8.076923),
(651,8.269231),
(652,8.461538),
(653,8.653846),
(654,8.846154),
(655,9.038462),
(656,9.230769),
(657,9.423077),
(658,9.615385),
(659,9.807693),
(660,10),
(661,10.37975),
(662,10.78947),
(663,11.23288),
(664,11.71429),
(665,12.23881),
(666,12.8125),
(667,13.44262),
(668,14.13793),
(669,14.90909),
(670,15.76923),
(671,16.96694),
(672,18.25562),
(673,19.64218),
(674,21.13405),
(675,22.73924),
(676,24.46634),
(677,26.32462),
(678,28.32404),
(679,30.47532),
(680,32.78999),
(681,35.28047),
(682,37.96011),
(683,40.84327),
(684,43.94542),
(685,47.28318),
(686,50.87445),
(687,54.73849),
(688,58.89602),
(689,63.36931),
(690,68.18237),
(691,73.36098),
(692,78.93292),
(693,84.92807),
(694,91.37856),
(695,98.31898),
(696,105.7866),
(697,113.8213),
(698,122.4663),
(699,131.7679),
(700,141.776),
(701,0.307692),
(702,0.307692),
(703,0.307692),
(704,0.307692),
(705,0.307692),
(706,0.307692),
(707,0.307692),
(708,0.307692),
(709,0.307692),
(710,0.307692),
(711,0.461538),
(712,0.615385),
(713,0.769231),
(714,0.923077),
(715,1.076923),
(716,1.230769),
(717,1.384615),
(718,1.538462),
(719,1.692308),
(720,1.846154),
(721,2),
(722,2.153846),
(723,2.307692),
(724,2.461539),
(725,2.615385),
(726,2.769231),
(727,2.923077),
(728,3.076923),
(729,3.230769),
(730,3.384615),
(731,3.538462),
(732,3.692308),
(733,3.846154),
(734,4),
(735,4.153846),
(736,4.307692),
(737,4.461538),
(738,4.615385),
(739,4.769231),
(740,4.923077),
(741,5.076923),
(742,5.230769),
(743,5.384615),
(744,5.538462),
(745,5.692307),
(746,5.846154),
(747,6),
(748,6.153846),
(749,6.307693),
(750,6.461538),
(751,6.615385),
(752,6.769231),
(753,6.923077),
(754,7.076923),
(755,7.230769),
(756,7.384615),
(757,7.538462),
(758,7.692307),
(759,7.846154),
(760,8),
(761,8.303798),
(762,8.631579),
(763,8.986301),
(764,9.371428),
(765,9.791045),
(766,10.25),
(767,10.7541),
(768,11.31035),
(769,11.92727),
(770,12.61539),
(771,13.57355),
(772,14.6045),
(773,15.71375),
(774,16.90724),
(775,18.19139),
(776,19.57307),
(777,21.05969),
(778,22.65923),
(779,24.38025),
(780,26.23199),
(781,28.22437),
(782,30.36809),
(783,32.67461),
(784,35.15633),
(785,37.82654),
(786,40.69956),
(787,43.79079),
(788,47.11681),
(789,50.69545),
(790,54.54589),
(791,58.68878),
(792,63.14634),
(793,67.94246),
(794,73.10285),
(795,78.65519),
(796,84.62924),
(797,91.05704),
(798,97.97305),
(799,105.4143),
(800,113.4208),
(801,0.538462),
(802,0.538462),
(803,0.538462),
(804,0.538462),
(805,0.538462),
(806,0.538462),
(807,0.538462),
(808,0.538462),
(809,0.538462),
(810,0.538462),
(811,0.807692),
(812,1.076923),
(813,1.346154),
(814,1.615385),
(815,1.884615),
(816,2.153846),
(817,2.423077),
(818,2.692308),
(819,2.961538),
(820,3.230769),
(821,3.5),
(822,3.769231),
(823,4.038462),
(824,4.307692),
(825,4.576923),
(826,4.846154),
(827,5.115385),
(828,5.384615),
(829,5.653846),
(830,5.923077),
(831,6.192307),
(832,6.461538),
(833,6.730769),
(834,7),
(835,7.269231),
(836,7.538462),
(837,7.807693),
(838,8.076923),
(839,8.346154),
(840,8.615384),
(841,8.884615),
(842,9.153846),
(843,9.423077),
(844,9.692307),
(845,9.961538),
(846,10.23077),
(847,10.5),
(848,10.76923),
(849,11.03846),
(850,11.30769),
(851,11.57692),
(852,11.84616),
(853,12.11539),
(854,12.38462),
(855,12.65385),
(856,12.92308),
(857,13.19231),
(858,13.46154),
(859,13.73077),
(860,14),
(861,14.53165),
(862,15.10526),
(863,15.72603),
(864,16.4),
(865,17.13433),
(866,17.9375),
(867,18.81967),
(868,19.7931),
(869,20.87273),
(870,22.07692),
(871,23.75372),
(872,25.55787),
(873,27.49906),
(874,29.58768),
(875,31.83493),
(876,34.25287),
(877,36.85447),
(878,39.65365),
(879,42.66544),
(880,45.90599),
(881,49.39266),
(882,53.14415),
(883,57.18058),
(884,61.52359),
(885,66.19645),
(886,71.22423),
(887,76.63388),
(888,82.45441),
(889,88.71703),
(890,95.45531),
(891,102.7054),
(892,110.5061),
(893,118.8993),
(894,127.93),
(895,137.6466),
(896,148.1012),
(897,159.3498),
(898,171.4528),
(899,184.4751),
(900,198.4864),
(901,0.538462),
(902,0.538462),
(903,0.538462),
(904,0.538462),
(905,0.538462),
(906,0.538462),
(907,0.538462),
(908,0.538462),
(909,0.538462),
(910,0.538462),
(911,0.807692),
(912,1.076923),
(913,1.346154),
(914,1.615385),
(915,1.884615),
(916,2.153846),
(917,2.423077),
(918,2.692308),
(919,2.961538),
(920,3.230769),
(921,3.5),
(922,3.769231),
(923,4.038462),
(924,4.307692),
(925,4.576923),
(926,4.846154),
(927,5.115385),
(928,5.384615),
(929,5.653846),
(930,5.923077),
(931,6.192307),
(932,6.461538),
(933,6.730769),
(934,7),
(935,7.269231),
(936,7.538462),
(937,7.807693),
(938,8.076923),
(939,8.346154),
(940,8.615384),
(941,8.884615),
(942,9.153846),
(943,9.423077),
(944,9.692307),
(945,9.961538),
(946,10.23077),
(947,10.5),
(948,10.76923),
(949,11.03846),
(950,11.30769),
(951,11.57692),
(952,11.84616),
(953,12.11539),
(954,12.38462),
(955,12.65385),
(956,12.92308),
(957,13.19231),
(958,13.46154),
(959,13.73077),
(960,14),
(961,14.53165),
(962,15.10526),
(963,15.72603),
(964,16.4),
(965,17.13433),
(966,17.9375),
(967,18.81967),
(968,19.7931),
(969,20.87273),
(970,22.07692),
(971,23.75372),
(972,25.55787),
(973,27.49906),
(974,29.58768),
(975,31.83493),
(976,34.25287),
(977,36.85447),
(978,39.65365),
(979,42.66544),
(980,45.90599),
(981,49.39266),
(982,53.14415),
(983,57.18058),
(984,61.52359),
(985,66.19645),
(986,71.22423),
(987,76.63388),
(988,82.45441),
(989,88.71703),
(990,95.45531),
(991,102.7054),
(992,110.5061),
(993,118.8993),
(994,127.93),
(995,137.6466),
(996,148.1012),
(997,159.3498),
(998,171.4528),
(999,184.4751),
(1000,198.4864),
(1001,0.538462),
(1002,0.538462),
(1003,0.538462),
(1004,0.538462),
(1005,0.538462),
(1006,0.538462),
(1007,0.538462),
(1008,0.538462),
(1009,0.538462),
(1010,0.538462),
(1011,0.807692),
(1012,1.076923),
(1013,1.346154),
(1014,1.615385),
(1015,1.884615),
(1016,2.153846),
(1017,2.423077),
(1018,2.692308),
(1019,2.961538),
(1020,3.230769),
(1021,3.5),
(1022,3.769231),
(1023,4.038462),
(1024,4.307692),
(1025,4.576923),
(1026,4.846154),
(1027,5.115385),
(1028,5.384615),
(1029,5.653846),
(1030,5.923077),
(1031,6.192307),
(1032,6.461538),
(1033,6.730769),
(1034,7),
(1035,7.269231),
(1036,7.538462),
(1037,7.807693),
(1038,8.076923),
(1039,8.346154),
(1040,8.615384),
(1041,8.884615),
(1042,9.153846),
(1043,9.423077),
(1044,9.692307),
(1045,9.961538),
(1046,10.23077),
(1047,10.5),
(1048,10.76923),
(1049,11.03846),
(1050,11.30769),
(1051,11.57692),
(1052,11.84616),
(1053,12.11539),
(1054,12.38462),
(1055,12.65385),
(1056,12.92308),
(1057,13.19231),
(1058,13.46154),
(1059,13.73077),
(1060,14),
(1061,14.53165),
(1062,15.10526),
(1063,15.72603),
(1064,16.4),
(1065,17.13433),
(1066,17.9375),
(1067,18.81967),
(1068,19.7931),
(1069,20.87273),
(1070,22.07692),
(1071,23.75372),
(1072,25.55787),
(1073,27.49906),
(1074,29.58768),
(1075,31.83493),
(1076,34.25287),
(1077,36.85447),
(1078,39.65365),
(1079,42.66544),
(1080,45.90599),
(1081,49.39266),
(1082,53.14415),
(1083,57.18058),
(1084,61.52359),
(1085,66.19645),
(1086,71.22423),
(1087,76.63388),
(1088,82.45441),
(1089,88.71703),
(1090,95.45531),
(1091,102.7054),
(1092,110.5061),
(1093,118.8993),
(1094,127.93),
(1095,137.6466),
(1096,148.1012),
(1097,159.3498),
(1098,171.4528),
(1099,184.4751),
(1100,198.4864),
(1101,0.384615),
(1102,0.384615),
(1103,0.384615),
(1104,0.384615),
(1105,0.384615),
(1106,0.384615),
(1107,0.384615),
(1108,0.384615),
(1109,0.384615),
(1110,0.384615),
(1111,0.576923),
(1112,0.769231),
(1113,0.961538),
(1114,1.153846),
(1115,1.346154),
(1116,1.538462),
(1117,1.730769),
(1118,1.923077),
(1119,2.115385),
(1120,2.307692),
(1121,2.5),
(1122,2.692308),
(1123,2.884615),
(1124,3.076923),
(1125,3.269231),
(1126,3.461538),
(1127,3.653846),
(1128,3.846154),
(1129,4.038462),
(1130,4.230769),
(1131,4.423077),
(1132,4.615385),
(1133,4.807693),
(1134,5),
(1135,5.192308),
(1136,5.384615),
(1137,5.576923),
(1138,5.769231),
(1139,5.961538),
(1140,6.153846),
(1141,6.346154),
(1142,6.538462),
(1143,6.730769),
(1144,6.923077),
(1145,7.115385),
(1146,7.307693),
(1147,7.5),
(1148,7.692307),
(1149,7.884615),
(1150,8.076923),
(1151,8.269231),
(1152,8.461538),
(1153,8.653846),
(1154,8.846154),
(1155,9.038462),
(1156,9.230769),
(1157,9.423077),
(1158,9.615385),
(1159,9.807693),
(1160,10),
(1161,10.37975),
(1162,10.78947),
(1163,11.23288),
(1164,11.71429),
(1165,12.23881),
(1166,12.8125),
(1167,13.44262),
(1168,14.13793),
(1169,14.90909),
(1170,15.76923),
(1171,16.96694),
(1172,18.25562),
(1173,19.64218),
(1174,21.13405),
(1175,22.73924),
(1176,24.46634),
(1177,26.32462),
(1178,28.32404),
(1179,30.47532),
(1180,32.78999),
(1181,35.28047),
(1182,37.96011),
(1183,40.84327),
(1184,43.94542),
(1185,47.28318),
(1186,50.87445),
(1187,54.73849),
(1188,58.89602),
(1189,63.36931),
(1190,68.18237),
(1191,73.36098),
(1192,78.93292),
(1193,84.92807),
(1194,91.37856),
(1195,98.31898),
(1196,105.7866),
(1197,113.8213),
(1198,122.4663),
(1199,131.7679),
(1200,141.776),
(1201,0.384615),
(1202,0.384615),
(1203,0.384615),
(1204,0.384615),
(1205,0.384615),
(1206,0.384615),
(1207,0.384615),
(1208,0.384615),
(1209,0.384615),
(1210,0.384615),
(1211,0.576923),
(1212,0.769231),
(1213,0.961538),
(1214,1.153846),
(1215,1.346154),
(1216,1.538462),
(1217,1.730769),
(1218,1.923077),
(1219,2.115385),
(1220,2.307692),
(1221,2.5),
(1222,2.692308),
(1223,2.884615),
(1224,3.076923),
(1225,3.269231),
(1226,3.461538),
(1227,3.653846),
(1228,3.846154),
(1229,4.038462),
(1230,4.230769),
(1231,4.423077),
(1232,4.615385),
(1233,4.807693),
(1234,5),
(1235,5.192308),
(1236,5.384615),
(1237,5.576923),
(1238,5.769231),
(1239,5.961538),
(1240,6.153846),
(1241,6.346154),
(1242,6.538462),
(1243,6.730769),
(1244,6.923077),
(1245,7.115385),
(1246,7.307693),
(1247,7.5),
(1248,7.692307),
(1249,7.884615),
(1250,8.076923),
(1251,8.269231),
(1252,8.461538),
(1253,8.653846),
(1254,8.846154),
(1255,9.038462),
(1256,9.230769),
(1257,9.423077),
(1258,9.615385),
(1259,9.807693),
(1260,10),
(1261,10.37975),
(1262,10.78947),
(1263,11.23288),
(1264,11.71429),
(1265,12.23881),
(1266,12.8125),
(1267,13.44262),
(1268,14.13793),
(1269,14.90909),
(1270,15.76923),
(1271,16.96694),
(1272,18.25562),
(1273,19.64218),
(1274,21.13405),
(1275,22.73924),
(1276,24.46634),
(1277,26.32462),
(1278,28.32404),
(1279,30.47532),
(1280,32.78999),
(1281,35.28047),
(1282,37.96011),
(1283,40.84327),
(1284,43.94542),
(1285,47.28318),
(1286,50.87445),
(1287,54.73849),
(1288,58.89602),
(1289,63.36931),
(1290,68.18237),
(1291,73.36098),
(1292,78.93292),
(1293,84.92807),
(1294,91.37856),
(1295,98.31898),
(1296,105.7866),
(1297,113.8213),
(1298,122.4663),
(1299,131.7679),
(1300,141.776),
(1301,0.307692),
(1302,0.307692),
(1303,0.307692),
(1304,0.307692),
(1305,0.307692),
(1306,0.307692),
(1307,0.307692),
(1308,0.307692),
(1309,0.307692),
(1310,0.307692),
(1311,0.461538),
(1312,0.615385),
(1313,0.769231),
(1314,0.923077),
(1315,1.076923),
(1316,1.230769),
(1317,1.384615),
(1318,1.538462),
(1319,1.692308),
(1320,1.846154),
(1321,2),
(1322,2.153846),
(1323,2.307692),
(1324,2.461539),
(1325,2.615385),
(1326,2.769231),
(1327,2.923077),
(1328,3.076923),
(1329,3.230769),
(1330,3.384615),
(1331,3.538462),
(1332,3.692308),
(1333,3.846154),
(1334,4),
(1335,4.153846),
(1336,4.307692),
(1337,4.461538),
(1338,4.615385),
(1339,4.769231),
(1340,4.923077),
(1341,5.076923),
(1342,5.230769),
(1343,5.384615),
(1344,5.538462),
(1345,5.692307),
(1346,5.846154),
(1347,6),
(1348,6.153846),
(1349,6.307693),
(1350,6.461538),
(1351,6.615385),
(1352,6.769231),
(1353,6.923077),
(1354,7.076923),
(1355,7.230769),
(1356,7.384615),
(1357,7.538462),
(1358,7.692307),
(1359,7.846154),
(1360,8),
(1361,8.303798),
(1362,8.631579),
(1363,8.986301),
(1364,9.371428),
(1365,9.791045),
(1366,10.25),
(1367,10.7541),
(1368,11.31035),
(1369,11.92727),
(1370,12.61539),
(1371,13.57355),
(1372,14.6045),
(1373,15.71375),
(1374,16.90724),
(1375,18.19139),
(1376,19.57307),
(1377,21.05969),
(1378,22.65923),
(1379,24.38025),
(1380,26.23199),
(1381,28.22437),
(1382,30.36809),
(1383,32.67461),
(1384,35.15633),
(1385,37.82654),
(1386,40.69956),
(1387,43.79079),
(1388,47.11681),
(1389,50.69545),
(1390,54.54589),
(1391,58.68878),
(1392,63.14634),
(1393,67.94246),
(1394,73.10285),
(1395,78.65519),
(1396,84.62924),
(1397,91.05704),
(1398,97.97305),
(1399,105.4143),
(1400,113.4208),
(1401,14.375),
(1402,14.375),
(1403,14.375),
(1404,14.375),
(1405,14.375),
(1406,14.375),
(1407,14.375),
(1408,14.375),
(1409,14.375),
(1410,14.375),
(1411,14.375),
(1412,14.375),
(1413,14.375),
(1414,14.375),
(1415,14.375),
(1416,14.375),
(1417,14.375),
(1418,14.375),
(1419,14.375),
(1420,14.375),
(1421,14.375),
(1422,14.375),
(1423,14.375),
(1424,14.375),
(1425,14.375),
(1426,14.375),
(1427,14.375),
(1428,14.375),
(1429,14.375),
(1430,14.375),
(1431,14.375),
(1432,14.375),
(1433,14.375),
(1434,14.375),
(1435,14.92789),
(1436,15.48077),
(1437,16.03366),
(1438,16.58654),
(1439,17.13942),
(1440,17.69231),
(1441,18.24519),
(1442,18.79808),
(1443,19.35096),
(1444,19.90385),
(1445,20.45673),
(1446,21.00962),
(1447,21.5625),
(1448,22.11539),
(1449,22.66827),
(1450,23.22116),
(1451,23.77404),
(1452,24.32693),
(1453,24.87981),
(1454,25.43269),
(1455,25.98558),
(1456,26.53846),
(1457,27.09135),
(1458,27.64423),
(1459,28.19712),
(1460,28.75),
(1461,29.84177),
(1462,31.01974),
(1463,32.29452),
(1464,33.67857),
(1465,35.18657),
(1466,36.83594),
(1467,38.64754),
(1468,40.64655),
(1469,42.86364),
(1470,45.33654),
(1471,48.77996),
(1472,52.48492),
(1473,56.47128),
(1474,60.76041),
(1475,65.37531),
(1476,70.34072),
(1477,75.68327),
(1478,81.4316),
(1479,87.61653),
(1480,94.27122),
(1481,101.4313),
(1482,109.1353),
(1483,117.4244),
(1484,126.3431),
(1485,135.9391),
(1486,146.264),
(1487,157.3731),
(1488,169.326),
(1489,182.1868),
(1490,196.0243),
(1491,210.9128),
(1492,226.9321),
(1493,244.1682),
(1494,262.7133),
(1495,282.6671),
(1496,304.1364),
(1497,327.2362),
(1498,352.0906),
(1499,378.8328),
(1500,407.606),
(1501,14.375),
(1502,14.375),
(1503,14.375),
(1504,14.375),
(1505,14.375),
(1506,14.375),
(1507,14.375),
(1508,14.375),
(1509,14.375),
(1510,14.375),
(1511,14.375),
(1512,14.375),
(1513,14.375),
(1514,14.375),
(1515,14.375),
(1516,14.375),
(1517,14.375),
(1518,14.375),
(1519,14.375),
(1520,14.375),
(1521,14.375),
(1522,14.375),
(1523,14.375),
(1524,14.375),
(1525,14.375),
(1526,14.375),
(1527,14.375),
(1528,14.375),
(1529,14.375),
(1530,14.375),
(1531,14.375),
(1532,14.375),
(1533,14.375),
(1534,14.375),
(1535,14.92789),
(1536,15.48077),
(1537,16.03366),
(1538,16.58654),
(1539,17.13942),
(1540,17.69231),
(1541,18.24519),
(1542,18.79808),
(1543,19.35096),
(1544,19.90385),
(1545,20.45673),
(1546,21.00962),
(1547,21.5625),
(1548,22.11539),
(1549,22.66827),
(1550,23.22116),
(1551,23.77404),
(1552,24.32693),
(1553,24.87981),
(1554,25.43269),
(1555,25.98558),
(1556,26.53846),
(1557,27.09135),
(1558,27.64423),
(1559,28.19712),
(1560,28.75),
(1561,29.84177),
(1562,31.01974),
(1563,32.29452),
(1564,33.67857),
(1565,35.18657),
(1566,36.83594),
(1567,38.64754),
(1568,40.64655),
(1569,42.86364),
(1570,45.33654),
(1571,48.77996),
(1572,52.48492),
(1573,56.47128),
(1574,60.76041),
(1575,65.37531),
(1576,70.34072),
(1577,75.68327),
(1578,81.4316),
(1579,87.61653),
(1580,94.27122),
(1581,101.4313),
(1582,109.1353),
(1583,117.4244),
(1584,126.3431),
(1585,135.9391),
(1586,146.264),
(1587,157.3731),
(1588,169.326),
(1589,182.1868),
(1590,196.0243),
(1591,210.9128),
(1592,226.9321),
(1593,244.1682),
(1594,262.7133),
(1595,282.6671),
(1596,304.1364),
(1597,327.2362),
(1598,352.0906),
(1599,378.8328),
(1600,407.606),
(1601,14.375),
(1602,14.375),
(1603,14.375),
(1604,14.375),
(1605,14.375),
(1606,14.375),
(1607,14.375),
(1608,14.375),
(1609,14.375),
(1610,14.375),
(1611,14.375),
(1612,14.375),
(1613,14.375),
(1614,14.375),
(1615,14.375),
(1616,14.375),
(1617,14.375),
(1618,14.375),
(1619,14.375),
(1620,14.375),
(1621,14.375),
(1622,14.375),
(1623,14.375),
(1624,14.375),
(1625,14.375),
(1626,14.375),
(1627,14.375),
(1628,14.375),
(1629,14.375),
(1630,14.375),
(1631,14.375),
(1632,14.375),
(1633,14.375),
(1634,14.375),
(1635,14.92789),
(1636,15.48077),
(1637,16.03366),
(1638,16.58654),
(1639,17.13942),
(1640,17.69231),
(1641,18.24519),
(1642,18.79808),
(1643,19.35096),
(1644,19.90385),
(1645,20.45673),
(1646,21.00962),
(1647,21.5625),
(1648,22.11539),
(1649,22.66827),
(1650,23.22116),
(1651,23.77404),
(1652,24.32693),
(1653,24.87981),
(1654,25.43269),
(1655,25.98558),
(1656,26.53846),
(1657,27.09135),
(1658,27.64423),
(1659,28.19712),
(1660,28.75),
(1661,29.84177),
(1662,31.01974),
(1663,32.29452),
(1664,33.67857),
(1665,35.18657),
(1666,36.83594),
(1667,38.64754),
(1668,40.64655),
(1669,42.86364),
(1670,45.33654),
(1671,48.77996),
(1672,52.48492),
(1673,56.47128),
(1674,60.76041),
(1675,65.37531),
(1676,70.34072),
(1677,75.68327),
(1678,81.4316),
(1679,87.61653),
(1680,94.27122),
(1681,101.4313),
(1682,109.1353),
(1683,117.4244),
(1684,126.3431),
(1685,135.9391),
(1686,146.264),
(1687,157.3731),
(1688,169.326),
(1689,182.1868),
(1690,196.0243),
(1691,210.9128),
(1692,226.9321),
(1693,244.1682),
(1694,262.7133),
(1695,282.6671),
(1696,304.1364),
(1697,327.2362),
(1698,352.0906),
(1699,378.8328),
(1700,407.606),
(1701,0.384615),
(1702,0.384615),
(1703,0.384615),
(1704,0.384615),
(1705,0.384615),
(1706,0.384615),
(1707,0.384615),
(1708,0.384615),
(1709,0.384615),
(1710,0.384615),
(1711,0.576923),
(1712,0.769231),
(1713,0.961538),
(1714,1.153846),
(1715,1.346154),
(1716,1.538462),
(1717,1.730769),
(1718,1.923077),
(1719,2.115385),
(1720,2.307692),
(1721,2.5),
(1722,2.692308),
(1723,2.884615),
(1724,3.076923),
(1725,3.269231),
(1726,3.461538),
(1727,3.653846),
(1728,3.846154),
(1729,4.038462),
(1730,4.230769),
(1731,4.423077),
(1732,4.615385),
(1733,4.807693),
(1734,5),
(1735,5.192308),
(1736,5.384615),
(1737,5.576923),
(1738,5.769231),
(1739,5.961538),
(1740,6.153846),
(1741,6.346154),
(1742,6.538462),
(1743,6.730769),
(1744,6.923077),
(1745,7.115385),
(1746,7.307693),
(1747,7.5),
(1748,7.692307),
(1749,7.884615),
(1750,8.076923),
(1751,8.269231),
(1752,8.461538),
(1753,8.653846),
(1754,8.846154),
(1755,9.038462),
(1756,9.230769),
(1757,9.423077),
(1758,9.615385),
(1759,9.807693),
(1760,10),
(1761,10.37975),
(1762,10.78947),
(1763,11.23288),
(1764,11.71429),
(1765,12.23881),
(1766,12.8125),
(1767,13.44262),
(1768,14.13793),
(1769,14.90909),
(1770,15.76923),
(1771,16.96694),
(1772,18.25562),
(1773,19.64218),
(1774,21.13405),
(1775,22.73924),
(1776,24.46634),
(1777,26.32462),
(1778,28.32404),
(1779,30.47532),
(1780,32.78999),
(1781,35.28047),
(1782,37.96011),
(1783,40.84327),
(1784,43.94542),
(1785,47.28318),
(1786,50.87445),
(1787,54.73849),
(1788,58.89602),
(1789,63.36931),
(1790,68.18237),
(1791,73.36098),
(1792,78.93292),
(1793,84.92807),
(1794,91.37856),
(1795,98.31898),
(1796,105.7866),
(1797,113.8213),
(1798,122.4663),
(1799,131.7679),
(1800,141.776),
(1801,0.384615),
(1802,0.384615),
(1803,0.384615),
(1804,0.384615),
(1805,0.384615),
(1806,0.384615),
(1807,0.384615),
(1808,0.384615),
(1809,0.384615),
(1810,0.384615),
(1811,0.576923),
(1812,0.769231),
(1813,0.961538),
(1814,1.153846),
(1815,1.346154),
(1816,1.538462),
(1817,1.730769),
(1818,1.923077),
(1819,2.115385),
(1820,2.307692),
(1821,2.5),
(1822,2.692308),
(1823,2.884615),
(1824,3.076923),
(1825,3.269231),
(1826,3.461538),
(1827,3.653846),
(1828,3.846154),
(1829,4.038462),
(1830,4.230769),
(1831,4.423077),
(1832,4.615385),
(1833,4.807693),
(1834,5),
(1835,5.192308),
(1836,5.384615),
(1837,5.576923),
(1838,5.769231),
(1839,5.961538),
(1840,6.153846),
(1841,6.346154),
(1842,6.538462),
(1843,6.730769),
(1844,6.923077),
(1845,7.115385),
(1846,7.307693),
(1847,7.5),
(1848,7.692307),
(1849,7.884615),
(1850,8.076923),
(1851,8.269231),
(1852,8.461538),
(1853,8.653846),
(1854,8.846154),
(1855,9.038462),
(1856,9.230769),
(1857,9.423077),
(1858,9.615385),
(1859,9.807693),
(1860,10),
(1861,10.37975),
(1862,10.78947),
(1863,11.23288),
(1864,11.71429),
(1865,12.23881),
(1866,12.8125),
(1867,13.44262),
(1868,14.13793),
(1869,14.90909),
(1870,15.76923),
(1871,16.96694),
(1872,18.25562),
(1873,19.64218),
(1874,21.13405),
(1875,22.73924),
(1876,24.46634),
(1877,26.32462),
(1878,28.32404),
(1879,30.47532),
(1880,32.78999),
(1881,35.28047),
(1882,37.96011),
(1883,40.84327),
(1884,43.94542),
(1885,47.28318),
(1886,50.87445),
(1887,54.73849),
(1888,58.89602),
(1889,63.36931),
(1890,68.18237),
(1891,73.36098),
(1892,78.93292),
(1893,84.92807),
(1894,91.37856),
(1895,98.31898),
(1896,105.7866),
(1897,113.8213),
(1898,122.4663),
(1899,131.7679),
(1900,141.776),
(1901,0.384615),
(1902,0.384615),
(1903,0.384615),
(1904,0.384615),
(1905,0.384615),
(1906,0.384615),
(1907,0.384615),
(1908,0.384615),
(1909,0.384615),
(1910,0.384615),
(1911,0.576923),
(1912,0.769231),
(1913,0.961538),
(1914,1.153846),
(1915,1.346154),
(1916,1.538462),
(1917,1.730769),
(1918,1.923077),
(1919,2.115385),
(1920,2.307692),
(1921,2.5),
(1922,2.692308),
(1923,2.884615),
(1924,3.076923),
(1925,3.269231),
(1926,3.461538),
(1927,3.653846),
(1928,3.846154),
(1929,4.038462),
(1930,4.230769),
(1931,4.423077),
(1932,4.615385),
(1933,4.807693),
(1934,5),
(1935,5.192308),
(1936,5.384615),
(1937,5.576923),
(1938,5.769231),
(1939,5.961538),
(1940,6.153846),
(1941,6.346154),
(1942,6.538462),
(1943,6.730769),
(1944,6.923077),
(1945,7.115385),
(1946,7.307693),
(1947,7.5),
(1948,7.692307),
(1949,7.884615),
(1950,8.076923),
(1951,8.269231),
(1952,8.461538),
(1953,8.653846),
(1954,8.846154),
(1955,9.038462),
(1956,9.230769),
(1957,9.423077),
(1958,9.615385),
(1959,9.807693),
(1960,10),
(1961,10.37975),
(1962,10.78947),
(1963,11.23288),
(1964,11.71429),
(1965,12.23881),
(1966,12.8125),
(1967,13.44262),
(1968,14.13793),
(1969,14.90909),
(1970,15.76923),
(1971,16.96694),
(1972,18.25562),
(1973,19.64218),
(1974,21.13405),
(1975,22.73924),
(1976,24.46634),
(1977,26.32462),
(1978,28.32404),
(1979,30.47532),
(1980,32.78999),
(1981,35.28047),
(1982,37.96011),
(1983,40.84327),
(1984,43.94542),
(1985,47.28318),
(1986,50.87445),
(1987,54.73849),
(1988,58.89602),
(1989,63.36931),
(1990,68.18237),
(1991,73.36098),
(1992,78.93292),
(1993,84.92807),
(1994,91.37856),
(1995,98.31898),
(1996,105.7866),
(1997,113.8213),
(1998,122.4663),
(1999,131.7679),
(2000,141.776),
(2001,0.096154),
(2002,0.096154),
(2003,0.096154),
(2004,0.096154),
(2005,0.096154),
(2006,0.096154),
(2007,0.096154),
(2008,0.096154),
(2009,0.096154),
(2010,0.096154),
(2011,0.144231),
(2012,0.192308),
(2013,0.240385),
(2014,0.288462),
(2015,0.336538),
(2016,0.384615),
(2017,0.432692),
(2018,0.480769),
(2019,0.528846),
(2020,0.576923),
(2021,0.625),
(2022,0.673077),
(2023,0.721154),
(2024,0.769231),
(2025,0.817308),
(2026,0.865385),
(2027,0.913462),
(2028,0.961538),
(2029,1.009615),
(2030,1.057692),
(2031,1.105769),
(2032,1.153846),
(2033,1.201923),
(2034,1.25),
(2035,1.298077),
(2036,1.346154),
(2037,1.394231),
(2038,1.442308),
(2039,1.490385),
(2040,1.538462),
(2041,1.586538),
(2042,1.634615),
(2043,1.682692),
(2044,1.730769),
(2045,1.778846),
(2046,1.826923),
(2047,1.875),
(2048,1.923077),
(2049,1.971154),
(2050,2.019231),
(2051,2.067308),
(2052,2.115385),
(2053,2.163461),
(2054,2.211539),
(2055,2.259615),
(2056,2.307692),
(2057,2.355769),
(2058,2.403846),
(2059,2.451923),
(2060,2.5),
(2061,2.594937),
(2062,2.697368),
(2063,2.808219),
(2064,2.928571),
(2065,3.059702),
(2066,3.203125),
(2067,3.360656),
(2068,3.534483),
(2069,3.727273),
(2070,3.942308),
(2071,4.241735),
(2072,4.563906),
(2073,4.910545),
(2074,5.283514),
(2075,5.684809),
(2076,6.116584),
(2077,6.581153),
(2078,7.081008),
(2079,7.618829),
(2080,8.197496),
(2081,8.820117),
(2082,9.490026),
(2083,10.21082),
(2084,10.98635),
(2085,11.8208),
(2086,12.71861),
(2087,13.68462),
(2088,14.724),
(2089,15.84233),
(2090,17.04559),
(2091,18.34024),
(2092,19.73323),
(2093,21.23202),
(2094,22.84464),
(2095,24.57975),
(2096,26.44664),
(2097,28.45532),
(2098,30.61658),
(2099,32.94198),
(2100,35.444),
(2101,0.096154),
(2102,0.096154),
(2103,0.096154),
(2104,0.096154),
(2105,0.096154),
(2106,0.096154),
(2107,0.096154),
(2108,0.096154),
(2109,0.096154),
(2110,0.096154),
(2111,0.144231),
(2112,0.192308),
(2113,0.240385),
(2114,0.288462),
(2115,0.336538),
(2116,0.384615),
(2117,0.432692),
(2118,0.480769),
(2119,0.528846),
(2120,0.576923),
(2121,0.625),
(2122,0.673077),
(2123,0.721154),
(2124,0.769231),
(2125,0.817308),
(2126,0.865385),
(2127,0.913462),
(2128,0.961538),
(2129,1.009615),
(2130,1.057692),
(2131,1.105769),
(2132,1.153846),
(2133,1.201923),
(2134,1.25),
(2135,1.298077),
(2136,1.346154),
(2137,1.394231),
(2138,1.442308),
(2139,1.490385),
(2140,1.538462),
(2141,1.586538),
(2142,1.634615),
(2143,1.682692),
(2144,1.730769),
(2145,1.778846),
(2146,1.826923),
(2147,1.875),
(2148,1.923077),
(2149,1.971154),
(2150,2.019231),
(2151,2.067308),
(2152,2.115385),
(2153,2.163461),
(2154,2.211539),
(2155,2.259615),
(2156,2.307692),
(2157,2.355769),
(2158,2.403846),
(2159,2.451923),
(2160,2.5),
(2161,2.594937),
(2162,2.697368),
(2163,2.808219),
(2164,2.928571),
(2165,3.059702),
(2166,3.203125),
(2167,3.360656),
(2168,3.534483),
(2169,3.727273),
(2170,3.942308),
(2171,4.241735),
(2172,4.563906),
(2173,4.910545),
(2174,5.283514),
(2175,5.684809),
(2176,6.116584),
(2177,6.581153),
(2178,7.081008),
(2179,7.618829),
(2180,8.197496),
(2181,8.820117),
(2182,9.490026),
(2183,10.21082),
(2184,10.98635),
(2185,11.8208),
(2186,12.71861),
(2187,13.68462),
(2188,14.724),
(2189,15.84233),
(2190,17.04559),
(2191,18.34024),
(2192,19.73323),
(2193,21.23202),
(2194,22.84464),
(2195,24.57975),
(2196,26.44664),
(2197,28.45532),
(2198,30.61658),
(2199,32.94198),
(2200,35.444),
(2201,0.096154),
(2202,0.096154),
(2203,0.096154),
(2204,0.096154),
(2205,0.096154),
(2206,0.096154),
(2207,0.096154),
(2208,0.096154),
(2209,0.096154),
(2210,0.096154),
(2211,0.144231),
(2212,0.192308),
(2213,0.240385),
(2214,0.288462),
(2215,0.336538),
(2216,0.384615),
(2217,0.432692),
(2218,0.480769),
(2219,0.528846),
(2220,0.576923),
(2221,0.625),
(2222,0.673077),
(2223,0.721154),
(2224,0.769231),
(2225,0.817308),
(2226,0.865385),
(2227,0.913462),
(2228,0.961538),
(2229,1.009615),
(2230,1.057692),
(2231,1.105769),
(2232,1.153846),
(2233,1.201923),
(2234,1.25),
(2235,1.298077),
(2236,1.346154),
(2237,1.394231),
(2238,1.442308),
(2239,1.490385),
(2240,1.538462),
(2241,1.586538),
(2242,1.634615),
(2243,1.682692),
(2244,1.730769),
(2245,1.778846),
(2246,1.826923),
(2247,1.875),
(2248,1.923077),
(2249,1.971154),
(2250,2.019231),
(2251,2.067308),
(2252,2.115385),
(2253,2.163461),
(2254,2.211539),
(2255,2.259615),
(2256,2.307692),
(2257,2.355769),
(2258,2.403846),
(2259,2.451923),
(2260,2.5),
(2261,2.594937),
(2262,2.697368),
(2263,2.808219),
(2264,2.928571),
(2265,3.059702),
(2266,3.203125),
(2267,3.360656),
(2268,3.534483),
(2269,3.727273),
(2270,3.942308),
(2271,4.241735),
(2272,4.563906),
(2273,4.910545),
(2274,5.283514),
(2275,5.684809),
(2276,6.116584),
(2277,6.581153),
(2278,7.081008),
(2279,7.618829),
(2280,8.197496),
(2281,8.820117),
(2282,9.490026),
(2283,10.21082),
(2284,10.98635),
(2285,11.8208),
(2286,12.71861),
(2287,13.68462),
(2288,14.724),
(2289,15.84233),
(2290,17.04559),
(2291,18.34024),
(2292,19.73323),
(2293,21.23202),
(2294,22.84464),
(2295,24.57975),
(2296,26.44664),
(2297,28.45532),
(2298,30.61658),
(2299,32.94198),
(2300,35.444),
(2301,0.096154),
(2302,0.096154),
(2303,0.096154),
(2304,0.096154),
(2305,0.096154),
(2306,0.096154),
(2307,0.096154),
(2308,0.096154),
(2309,0.096154),
(2310,0.096154),
(2311,0.144231),
(2312,0.192308),
(2313,0.240385),
(2314,0.288462),
(2315,0.336538),
(2316,0.384615),
(2317,0.432692),
(2318,0.480769),
(2319,0.528846),
(2320,0.576923),
(2321,0.625),
(2322,0.673077),
(2323,0.721154),
(2324,0.769231),
(2325,0.817308),
(2326,0.865385),
(2327,0.913462),
(2328,0.961538),
(2329,1.009615),
(2330,1.057692),
(2331,1.105769),
(2332,1.153846),
(2333,1.201923),
(2334,1.25),
(2335,1.298077),
(2336,1.346154),
(2337,1.394231),
(2338,1.442308),
(2339,1.490385),
(2340,1.538462),
(2341,1.586538),
(2342,1.634615),
(2343,1.682692),
(2344,1.730769),
(2345,1.778846),
(2346,1.826923),
(2347,1.875),
(2348,1.923077),
(2349,1.971154),
(2350,2.019231),
(2351,2.067308),
(2352,2.115385),
(2353,2.163461),
(2354,2.211539),
(2355,2.259615),
(2356,2.307692),
(2357,2.355769),
(2358,2.403846),
(2359,2.451923),
(2360,2.5),
(2361,2.594937),
(2362,2.697368),
(2363,2.808219),
(2364,2.928571),
(2365,3.059702),
(2366,3.203125),
(2367,3.360656),
(2368,3.534483),
(2369,3.727273),
(2370,3.942308),
(2371,4.241735),
(2372,4.563906),
(2373,4.910545),
(2374,5.283514),
(2375,5.684809),
(2376,6.116584),
(2377,6.581153),
(2378,7.081008),
(2379,7.618829),
(2380,8.197496),
(2381,8.820117),
(2382,9.490026),
(2383,10.21082),
(2384,10.98635),
(2385,11.8208),
(2386,12.71861),
(2387,13.68462),
(2388,14.724),
(2389,15.84233),
(2390,17.04559),
(2391,18.34024),
(2392,19.73323),
(2393,21.23202),
(2394,22.84464),
(2395,24.57975),
(2396,26.44664),
(2397,28.45532),
(2398,30.61658),
(2399,32.94198),
(2400,35.444),
(2401,0.180582),
(2402,0.180582),
(2403,0.180582),
(2404,0.180582),
(2405,0.180582),
(2406,0.180582),
(2407,0.180582),
(2408,0.180582),
(2409,0.180582),
(2410,0.180582),
(2411,0.270872),
(2412,0.361163),
(2413,0.451454),
(2414,0.541745),
(2415,0.632036),
(2416,0.722326),
(2417,0.812617),
(2418,0.902908),
(2419,0.993199),
(2420,1.08349),
(2421,1.173781),
(2422,1.264071),
(2423,1.354362),
(2424,1.444653),
(2425,1.534944),
(2426,1.625234),
(2427,1.715525),
(2428,1.805816),
(2429,1.896107),
(2430,1.986398),
(2431,2.076689),
(2432,2.166979),
(2433,2.25727),
(2434,2.347561),
(2435,2.437852),
(2436,2.528143),
(2437,2.618433),
(2438,2.708724),
(2439,2.799015),
(2440,2.889306),
(2441,2.979597),
(2442,3.069887),
(2443,3.160178),
(2444,3.250469),
(2445,3.34076),
(2446,3.431051),
(2447,3.521342),
(2448,3.611632),
(2449,3.701923),
(2450,3.792214),
(2451,3.882505),
(2452,3.972795),
(2453,4.063086),
(2454,4.153377),
(2455,4.243668),
(2456,4.333959),
(2457,4.424249),
(2458,4.51454),
(2459,4.604831),
(2460,4.695122),
(2461,4.873418),
(2462,5.06579),
(2463,5.273973),
(2464,5.5),
(2465,5.746268),
(2466,6.015625),
(2467,6.311475),
(2468,6.637931),
(2469,7),
(2470,7.403846),
(2471,7.966187),
(2472,8.571238),
(2473,9.222244),
(2474,9.922696),
(2475,10.67635),
(2476,11.48724),
(2477,12.35973),
(2478,13.29848),
(2479,14.30853),
(2480,15.3953),
(2481,16.56461),
(2482,17.82273),
(2483,19.17641),
(2484,20.63291),
(2485,22.20003),
(2486,23.88618),
(2487,25.70039),
(2488,27.65239),
(2489,29.75266),
(2490,32.01245),
(2491,34.44387),
(2492,37.05997),
(2493,39.87477),
(2494,42.90335),
(2495,46.16196),
(2496,49.66808),
(2497,53.44049),
(2498,57.49942),
(2499,61.86665),
(2500,66.56557),
(2501,0),
(2502,0),
(2503,0),
(2504,0),
(2505,0),
(2506,0),
(2507,0),
(2508,0),
(2509,0),
(2510,0),
(2511,0),
(2512,0),
(2513,0),
(2514,0),
(2515,0),
(2516,0),
(2517,0),
(2518,0),
(2519,0),
(2520,0),
(2521,0),
(2522,0),
(2523,0),
(2524,0),
(2525,0),
(2526,0),
(2527,0),
(2528,0),
(2529,0),
(2530,0),
(2531,0),
(2532,0),
(2533,0),
(2534,0),
(2535,0),
(2536,0),
(2537,0),
(2538,0),
(2539,0),
(2540,0),
(2541,0),
(2542,0),
(2543,0),
(2544,0),
(2545,0),
(2546,0),
(2547,0),
(2548,0),
(2549,0),
(2550,0),
(2551,0),
(2552,0),
(2553,0),
(2554,0),
(2555,0),
(2556,0),
(2557,0),
(2558,0),
(2559,0),
(2560,0),
(2561,0),
(2562,0),
(2563,0),
(2564,0),
(2565,0),
(2566,0),
(2567,0),
(2568,0),
(2569,0),
(2570,0),
(2571,0),
(2572,0),
(2573,0),
(2574,0),
(2575,0),
(2576,0),
(2577,0),
(2578,0),
(2579,0),
(2580,0),
(2581,0),
(2582,0),
(2583,0),
(2584,0),
(2585,0),
(2586,0),
(2587,0),
(2588,0),
(2589,0),
(2590,0),
(2591,0),
(2592,0),
(2593,0),
(2594,0),
(2595,0),
(2596,0),
(2597,0),
(2598,0),
(2599,0),
(2600,0),
(2601,0),
(2602,0),
(2603,0),
(2604,0),
(2605,0),
(2606,0),
(2607,0),
(2608,0),
(2609,0),
(2610,0),
(2611,0),
(2612,0),
(2613,0),
(2614,0),
(2615,0),
(2616,0),
(2617,0),
(2618,0),
(2619,0),
(2620,0),
(2621,0),
(2622,0),
(2623,0),
(2624,0),
(2625,0),
(2626,0),
(2627,0),
(2628,0),
(2629,0),
(2630,0),
(2631,0),
(2632,0),
(2633,0),
(2634,0),
(2635,0),
(2636,0),
(2637,0),
(2638,0),
(2639,0),
(2640,0),
(2641,0),
(2642,0),
(2643,0),
(2644,0),
(2645,0),
(2646,0),
(2647,0),
(2648,0),
(2649,0),
(2650,0),
(2651,0),
(2652,0),
(2653,0),
(2654,0),
(2655,0),
(2656,0),
(2657,0),
(2658,0),
(2659,0),
(2660,0),
(2661,0),
(2662,0),
(2663,0),
(2664,0),
(2665,0),
(2666,0),
(2667,0),
(2668,0),
(2669,0),
(2670,0),
(2671,0),
(2672,0),
(2673,0),
(2674,0),
(2675,0),
(2676,0),
(2677,0),
(2678,0),
(2679,0),
(2680,0),
(2681,0),
(2682,0),
(2683,0),
(2684,0),
(2685,0),
(2686,0),
(2687,0),
(2688,0),
(2689,0),
(2690,0),
(2691,0),
(2692,0),
(2693,0),
(2694,0),
(2695,0),
(2696,0),
(2697,0),
(2698,0),
(2699,0),
(2700,0),
(2701,0),
(2702,0),
(2703,0),
(2704,0),
(2705,0),
(2706,0),
(2707,0),
(2708,0),
(2709,0),
(2710,0),
(2711,0),
(2712,0),
(2713,0),
(2714,0),
(2715,0),
(2716,0),
(2717,0),
(2718,0),
(2719,0),
(2720,0),
(2721,0),
(2722,0),
(2723,0),
(2724,0),
(2725,0),
(2726,0),
(2727,0),
(2728,0),
(2729,0),
(2730,0),
(2731,0),
(2732,0),
(2733,0),
(2734,0),
(2735,0),
(2736,0),
(2737,0),
(2738,0),
(2739,0),
(2740,0),
(2741,0),
(2742,0),
(2743,0),
(2744,0),
(2745,0),
(2746,0),
(2747,0),
(2748,0),
(2749,0),
(2750,0),
(2751,0),
(2752,0),
(2753,0),
(2754,0),
(2755,0),
(2756,0),
(2757,0),
(2758,0),
(2759,0),
(2760,0),
(2761,0),
(2762,0),
(2763,0),
(2764,0),
(2765,0),
(2766,0),
(2767,0),
(2768,0),
(2769,0),
(2770,0),
(2771,0),
(2772,0),
(2773,0),
(2774,0),
(2775,0),
(2776,0),
(2777,0),
(2778,0),
(2779,0),
(2780,0),
(2781,0),
(2782,0),
(2783,0),
(2784,0),
(2785,0),
(2786,0),
(2787,0),
(2788,0),
(2789,0),
(2790,0),
(2791,0),
(2792,0),
(2793,0),
(2794,0),
(2795,0),
(2796,0),
(2797,0),
(2798,0),
(2799,0),
(2800,0),
(2801,0),
(2802,0),
(2803,0),
(2804,0),
(2805,0),
(2806,0),
(2807,0),
(2808,0),
(2809,0),
(2810,0),
(2811,0),
(2812,0),
(2813,0),
(2814,0),
(2815,0),
(2816,0),
(2817,0),
(2818,0),
(2819,0),
(2820,0),
(2821,0),
(2822,0),
(2823,0),
(2824,0),
(2825,0),
(2826,0),
(2827,0),
(2828,0),
(2829,0),
(2830,0),
(2831,0),
(2832,0),
(2833,0),
(2834,0),
(2835,0),
(2836,0),
(2837,0),
(2838,0),
(2839,0),
(2840,0),
(2841,0),
(2842,0),
(2843,0),
(2844,0),
(2845,0),
(2846,0),
(2847,0),
(2848,0),
(2849,0),
(2850,0),
(2851,0),
(2852,0),
(2853,0),
(2854,0),
(2855,0),
(2856,0),
(2857,0),
(2858,0),
(2859,0),
(2860,0),
(2861,0),
(2862,0),
(2863,0),
(2864,0),
(2865,0),
(2866,0),
(2867,0),
(2868,0),
(2869,0),
(2870,0),
(2871,0),
(2872,0),
(2873,0),
(2874,0),
(2875,0),
(2876,0),
(2877,0),
(2878,0),
(2879,0),
(2880,0),
(2881,0),
(2882,0),
(2883,0),
(2884,0),
(2885,0),
(2886,0),
(2887,0),
(2888,0),
(2889,0),
(2890,0),
(2891,0),
(2892,0),
(2893,0),
(2894,0),
(2895,0),
(2896,0),
(2897,0),
(2898,0),
(2899,0),
(2900,0),
(2901,0),
(2902,0),
(2903,0),
(2904,0),
(2905,0),
(2906,0),
(2907,0),
(2908,0),
(2909,0),
(2910,0),
(2911,0),
(2912,0),
(2913,0),
(2914,0),
(2915,0),
(2916,0),
(2917,0),
(2918,0),
(2919,0),
(2920,0),
(2921,0),
(2922,0),
(2923,0),
(2924,0),
(2925,0),
(2926,0),
(2927,0),
(2928,0),
(2929,0),
(2930,0),
(2931,0),
(2932,0),
(2933,0),
(2934,0),
(2935,0),
(2936,0),
(2937,0),
(2938,0),
(2939,0),
(2940,0),
(2941,0),
(2942,0),
(2943,0),
(2944,0),
(2945,0),
(2946,0),
(2947,0),
(2948,0),
(2949,0),
(2950,0),
(2951,0),
(2952,0),
(2953,0),
(2954,0),
(2955,0),
(2956,0),
(2957,0),
(2958,0),
(2959,0),
(2960,0),
(2961,0),
(2962,0),
(2963,0),
(2964,0),
(2965,0),
(2966,0),
(2967,0),
(2968,0),
(2969,0),
(2970,0),
(2971,0),
(2972,0),
(2973,0),
(2974,0),
(2975,0),
(2976,0),
(2977,0),
(2978,0),
(2979,0),
(2980,0),
(2981,0),
(2982,0),
(2983,0),
(2984,0),
(2985,0),
(2986,0),
(2987,0),
(2988,0),
(2989,0),
(2990,0),
(2991,0),
(2992,0),
(2993,0),
(2994,0),
(2995,0),
(2996,0),
(2997,0),
(2998,0),
(2999,0),
(3000,0),
(3001,0),
(3002,0),
(3003,0),
(3004,0),
(3005,0),
(3006,0),
(3007,0),
(3008,0),
(3009,0),
(3010,0),
(3011,0),
(3012,0),
(3013,0),
(3014,0),
(3015,0),
(3016,0),
(3017,0),
(3018,0),
(3019,0),
(3020,0),
(3021,0),
(3022,0),
(3023,0),
(3024,0),
(3025,0),
(3026,0),
(3027,0),
(3028,0),
(3029,0),
(3030,0),
(3031,0),
(3032,0),
(3033,0),
(3034,0),
(3035,0),
(3036,0),
(3037,0),
(3038,0),
(3039,0),
(3040,0),
(3041,0),
(3042,0),
(3043,0),
(3044,0),
(3045,0),
(3046,0),
(3047,0),
(3048,0),
(3049,0),
(3050,0),
(3051,0),
(3052,0),
(3053,0),
(3054,0),
(3055,0),
(3056,0),
(3057,0),
(3058,0),
(3059,0),
(3060,0),
(3061,0),
(3062,0),
(3063,0),
(3064,0),
(3065,0),
(3066,0),
(3067,0),
(3068,0),
(3069,0),
(3070,0),
(3071,0),
(3072,0),
(3073,0),
(3074,0),
(3075,0),
(3076,0),
(3077,0),
(3078,0),
(3079,0),
(3080,0),
(3081,0),
(3082,0),
(3083,0),
(3084,0),
(3085,0),
(3086,0),
(3087,0),
(3088,0),
(3089,0),
(3090,0),
(3091,0),
(3092,0),
(3093,0),
(3094,0),
(3095,0),
(3096,0),
(3097,0),
(3098,0),
(3099,0),
(3100,0),
(3101,0),
(3102,0),
(3103,0),
(3104,0),
(3105,0),
(3106,0),
(3107,0),
(3108,0),
(3109,0),
(3110,0),
(3111,0),
(3112,0),
(3113,0),
(3114,0),
(3115,0),
(3116,0),
(3117,0),
(3118,0),
(3119,0),
(3120,0),
(3121,0),
(3122,0),
(3123,0),
(3124,0),
(3125,0),
(3126,0),
(3127,0),
(3128,0),
(3129,0),
(3130,0),
(3131,0),
(3132,0),
(3133,0),
(3134,0),
(3135,0),
(3136,0),
(3137,0),
(3138,0),
(3139,0),
(3140,0),
(3141,0),
(3142,0),
(3143,0),
(3144,0),
(3145,0),
(3146,0),
(3147,0),
(3148,0),
(3149,0),
(3150,0),
(3151,0),
(3152,0),
(3153,0),
(3154,0),
(3155,0),
(3156,0),
(3157,0),
(3158,0),
(3159,0),
(3160,0),
(3161,0),
(3162,0),
(3163,0),
(3164,0),
(3165,0),
(3166,0),
(3167,0),
(3168,0),
(3169,0),
(3170,0),
(3171,0),
(3172,0),
(3173,0),
(3174,0),
(3175,0),
(3176,0),
(3177,0),
(3178,0),
(3179,0),
(3180,0),
(3181,0),
(3182,0),
(3183,0),
(3184,0),
(3185,0),
(3186,0),
(3187,0),
(3188,0),
(3189,0),
(3190,0),
(3191,0),
(3192,0),
(3193,0),
(3194,0),
(3195,0),
(3196,0),
(3197,0),
(3198,0),
(3199,0),
(3200,0);

-- ----------------------------
-- Table structure for gtchancetomeleecritbase_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtchancetomeleecritbase_dbc`; 
CREATE TABLE `gtchancetomeleecritbase_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Records of gtchancetomeleecritbase_dbc
-- ----------------------------
INSERT INTO `gtchancetomeleecritbase_dbc` VALUES 
(1,0.031891),
(2,0.032685),
(3,-0.01532),
(4,-0.00295),
(5,0.031765),
(6,0.031891),
(7,0.02922),
(8,0.03454),
(9,0.02622),
(10,0.2),
(11,0.074755);

-- ----------------------------
-- Table structure for gtchancetomeleecrit_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtchancetomeleecrit_dbc`; 
CREATE TABLE `gtchancetomeleecrit_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of gtchancetomeleecrit_dbc
-- ----------------------------
INSERT INTO `gtchancetomeleecrit_dbc` VALUES 
(1,0.002587),
(2,0.002264),
(3,0.002264),
(4,0.002264),
(5,0.002264),
(6,0.002012),
(7,0.002012),
(8,0.002012),
(9,0.002012),
(10,0.002012),
(11,0.001811),
(12,0.001811),
(13,0.001646),
(14,0.001646),
(15,0.001509),
(16,0.001509),
(17,0.001509),
(18,0.001393),
(19,0.001393),
(20,0.001293),
(21,0.001293),
(22,0.001293),
(23,0.001207),
(24,0.001132),
(25,0.001132),
(26,0.001065),
(27,0.001065),
(28,0.001006),
(29,0.001006),
(30,0.000953),
(31,0.000953),
(32,0.000905),
(33,0.000905),
(34,0.000862),
(35,0.000862),
(36,0.000823),
(37,0.000823),
(38,0.000787),
(39,0.000787),
(40,0.000755),
(41,0.000724),
(42,0.000724),
(43,0.000696),
(44,0.000696),
(45,0.000671),
(46,0.000671),
(47,0.000647),
(48,0.000624),
(49,0.000624),
(50,0.000604),
(51,0.000604),
(52,0.000584),
(53,0.000566),
(54,0.000566),
(55,0.000549),
(56,0.000549),
(57,0.000533),
(58,0.000517),
(59,0.000517),
(60,0.000503),
(61,0.000477),
(62,0.000453),
(63,0.000431),
(64,0.000421),
(65,0.000402),
(66,0.000385),
(67,0.00037),
(68,0.000355),
(69,0.000342),
(70,0.000335),
(71,0.000312),
(72,0.000287),
(73,0.000266),
(74,0.000248),
(75,0.000232),
(76,0.000216),
(77,0.000199),
(78,0.000185),
(79,0.000172),
(80,0.00016),
(81,0.000148),
(82,0.000138),
(83,0.000128),
(84,0.00012),
(85,0.000111),
(86,0.000103),
(87,9.6E-05),
(88,8.9E-05),
(89,8.3E-05),
(90,7.7E-05),
(91,7.2E-05),
(92,6.7E-05),
(93,6.2E-05),
(94,5.7E-05),
(95,5.3E-05),
(96,5E-05),
(97,4.6E-05),
(98,4.3E-05),
(99,4E-05),
(100,3.7E-05),
(101,0.002164),
(102,0.002164),
(103,0.002164),
(104,0.001924),
(105,0.001924),
(106,0.001924),
(107,0.001924),
(108,0.001732),
(109,0.001732),
(110,0.001732),
(111,0.001732),
(112,0.001732),
(113,0.001574),
(114,0.001574),
(115,0.001443),
(116,0.001443),
(117,0.001443),
(118,0.001332),
(119,0.001332),
(120,0.001237),
(121,0.001237),
(122,0.001237),
(123,0.001154),
(124,0.001082),
(125,0.001082),
(126,0.001082),
(127,0.001019),
(128,0.001019),
(129,0.000962),
(130,0.000962),
(131,0.000911),
(132,0.000911),
(133,0.000866),
(134,0.000866),
(135,0.000825),
(136,0.000825),
(137,0.000825),
(138,0.000787),
(139,0.000787),
(140,0.000753),
(141,0.000753),
(142,0.000753),
(143,0.000721),
(144,0.000693),
(145,0.000693),
(146,0.000666),
(147,0.000666),
(148,0.000641),
(149,0.000641),
(150,0.000618),
(151,0.000597),
(152,0.000597),
(153,0.000577),
(154,0.000577),
(155,0.000559),
(156,0.000559),
(157,0.000541),
(158,0.000525),
(159,0.000525),
(160,0.000509),
(161,0.000495),
(162,0.000481),
(163,0.000468),
(164,0.000456),
(165,0.000444),
(166,0.000444),
(167,0.000422),
(168,0.000422),
(169,0.000412),
(170,0.000403),
(171,0.000368),
(172,0.000346),
(173,0.000321),
(174,0.000299),
(175,0.000275),
(176,0.000258),
(177,0.00024),
(178,0.000222),
(179,0.000206),
(180,0.000192),
(181,0.00018),
(182,0.000168),
(183,0.000159),
(184,0.000151),
(185,0.000142),
(186,0.000135),
(187,0.000128),
(188,0.000123),
(189,0.000118),
(190,0.000112),
(191,0.000108),
(192,0.000104),
(193,0.0001),
(194,9.7E-05),
(195,9.3E-05),
(196,9E-05),
(197,8.7E-05),
(198,8.4E-05),
(199,8.2E-05),
(200,8E-05),
(201,0.00284),
(202,0.002834),
(203,0.002711),
(204,0.00253),
(205,0.00243),
(206,0.002337),
(207,0.002251),
(208,0.002171),
(209,0.002051),
(210,0.001984),
(211,0.001848),
(212,0.00167),
(213,0.001547),
(214,0.001441),
(215,0.00133),
(216,0.001267),
(217,0.001194),
(218,0.001117),
(219,0.00106),
(220,0.000998),
(221,0.000962),
(222,0.00091),
(223,0.000872),
(224,0.000829),
(225,0.000797),
(226,0.000767),
(227,0.000734),
(228,0.000709),
(229,0.00068),
(230,0.000654),
(231,0.000637),
(232,0.000614),
(233,0.000592),
(234,0.000575),
(235,0.000556),
(236,0.000541),
(237,0.000524),
(238,0.000508),
(239,0.000493),
(240,0.000481),
(241,0.00047),
(242,0.000457),
(243,0.000444),
(244,0.000433),
(245,0.000421),
(246,0.000413),
(247,0.000402),
(248,0.000391),
(249,0.000382),
(250,0.000373),
(251,0.000366),
(252,0.000358),
(253,0.00035),
(254,0.000341),
(255,0.000334),
(256,0.000328),
(257,0.000321),
(258,0.000314),
(259,0.000307),
(260,0.000301),
(261,0.000297),
(262,0.00029),
(263,0.000284),
(264,0.000279),
(265,0.000273),
(266,0.00027),
(267,0.000264),
(268,0.000259),
(269,0.000254),
(270,0.00025),
(271,0.000232),
(272,0.000216),
(273,0.000201),
(274,0.000187),
(275,0.000173),
(276,0.000161),
(277,0.00015),
(278,0.000139),
(279,0.000129),
(280,0.00012),
(281,0.000131),
(282,0.000129),
(283,0.000127),
(284,0.000124),
(285,0.000123),
(286,0.000121),
(287,0.000119),
(288,0.000117),
(289,0.000115),
(290,0.000114),
(291,0.000112),
(292,0.000111),
(293,0.000109),
(294,0.000108),
(295,0.000106),
(296,0.000105),
(297,0.000103),
(298,0.000102),
(299,0.0001),
(300,9.9E-05),
(301,0.004476),
(302,0.00429),
(303,0.004118),
(304,0.003813),
(305,0.003677),
(306,0.00355),
(307,0.003321),
(308,0.003217),
(309,0.00312),
(310,0.002941),
(311,0.00264),
(312,0.002394),
(313,0.002145),
(314,0.00198),
(315,0.001775),
(316,0.00166),
(317,0.00156),
(318,0.00145),
(319,0.001355),
(320,0.001271),
(321,0.001197),
(322,0.001144),
(323,0.001084),
(324,0.00104),
(325,0.00098),
(326,0.000936),
(327,0.000903),
(328,0.000865),
(329,0.00083),
(330,0.000792),
(331,0.000768),
(332,0.000741),
(333,0.000715),
(334,0.000691),
(335,0.000664),
(336,0.000643),
(337,0.000628),
(338,0.000609),
(339,0.000592),
(340,0.000572),
(341,0.000556),
(342,0.000542),
(343,0.000528),
(344,0.000512),
(345,0.000497),
(346,0.000486),
(347,0.000474),
(348,0.000464),
(349,0.000454),
(350,0.00044),
(351,0.000431),
(352,0.000422),
(353,0.000412),
(354,0.000404),
(355,0.000394),
(356,0.000386),
(357,0.000378),
(358,0.00037),
(359,0.000364),
(360,0.000355),
(361,0.000334),
(362,0.000322),
(363,0.000307),
(364,0.000296),
(365,0.000286),
(366,0.000276),
(367,0.000268),
(368,0.000262),
(369,0.000256),
(370,0.00025),
(371,0.000232),
(372,0.000216),
(373,0.000201),
(374,0.000187),
(375,0.000173),
(376,0.000161),
(377,0.00015),
(378,0.000139),
(379,0.000129),
(380,0.00012),
(381,0.000112),
(382,0.000105),
(383,9.9E-05),
(384,9.4E-05),
(385,8.9E-05),
(386,8.5E-05),
(387,8.1E-05),
(388,7.7E-05),
(389,7.4E-05),
(390,7.1E-05),
(391,6.8E-05),
(392,6.5E-05),
(393,6.3E-05),
(394,6.1E-05),
(395,5.9E-05),
(396,5.7E-05),
(397,5.5E-05),
(398,5.3E-05),
(399,5.2E-05),
(400,5E-05),
(401,0.000912),
(402,0.000912),
(403,0.000912),
(404,0.000868),
(405,0.000868),
(406,0.000868),
(407,0.000868),
(408,0.000829),
(409,0.000829),
(410,0.000829),
(411,0.000829),
(412,0.000793),
(413,0.000793),
(414,0.000793),
(415,0.000793),
(416,0.00076),
(417,0.00076),
(418,0.00076),
(419,0.000729),
(420,0.000729),
(421,0.000729),
(422,0.000729),
(423,0.000701),
(424,0.000701),
(425,0.000701),
(426,0.000675),
(427,0.000675),
(428,0.000675),
(429,0.000651),
(430,0.000651),
(431,0.000651),
(432,0.000629),
(433,0.000629),
(434,0.000629),
(435,0.000608),
(436,0.000608),
(437,0.000608),
(438,0.000588),
(439,0.000588),
(440,0.000588),
(441,0.00057),
(442,0.00057),
(443,0.000553),
(444,0.000553),
(445,0.000553),
(446,0.000536),
(447,0.000536),
(448,0.000521),
(449,0.000521),
(450,0.000521),
(451,0.000507),
(452,0.000507),
(453,0.000493),
(454,0.000493),
(455,0.00048),
(456,0.00048),
(457,0.000468),
(458,0.000468),
(459,0.000456),
(460,0.000456),
(461,0.000445),
(462,0.000446),
(463,0.000443),
(464,0.000434),
(465,0.000427),
(466,0.000421),
(467,0.000415),
(468,0.000413),
(469,0.000412),
(470,0.000401),
(471,0.000372),
(472,0.000344),
(473,0.00032),
(474,0.000299),
(475,0.000276),
(476,0.000257),
(477,0.00024),
(478,0.000222),
(479,0.000207),
(480,0.000192),
(481,0.000385),
(482,0.000385),
(483,0.000377),
(484,0.00037),
(485,0.00037),
(486,0.000364),
(487,0.000357),
(488,0.000357),
(489,0.000351),
(490,0.000345),
(491,0.000345),
(492,0.000339),
(493,0.000333),
(494,0.000328),
(495,0.000328),
(496,0.000323),
(497,0.000317),
(498,0.000313),
(499,0.000308),
(500,0.000308),
(501,0.002587),
(502,0.002264),
(503,0.002264),
(504,0.002264),
(505,0.002264),
(506,0.002012),
(507,0.002012),
(508,0.002012),
(509,0.002012),
(510,0.002012),
(511,0.001811),
(512,0.001811),
(513,0.001646),
(514,0.001646),
(515,0.001509),
(516,0.001509),
(517,0.001509),
(518,0.001393),
(519,0.001393),
(520,0.001293),
(521,0.001293),
(522,0.001293),
(523,0.001207),
(524,0.001132),
(525,0.001132),
(526,0.001065),
(527,0.001065),
(528,0.001006),
(529,0.001006),
(530,0.000953),
(531,0.000953),
(532,0.000905),
(533,0.000905),
(534,0.000862),
(535,0.000862),
(536,0.000823),
(537,0.000823),
(538,0.000787),
(539,0.000787),
(540,0.000755),
(541,0.000724),
(542,0.000724),
(543,0.000696),
(544,0.000696),
(545,0.000671),
(546,0.000671),
(547,0.000647),
(548,0.000624),
(549,0.000624),
(550,0.000604),
(551,0.000604),
(552,0.000584),
(553,0.000566),
(554,0.000566),
(555,0.000549),
(556,0.000549),
(557,0.000533),
(558,0.000517),
(559,0.000517),
(560,0.000503),
(561,0.000477),
(562,0.000453),
(563,0.000431),
(564,0.000421),
(565,0.000402),
(566,0.000385),
(567,0.00037),
(568,0.000355),
(569,0.000342),
(570,0.000335),
(571,0.000312),
(572,0.000287),
(573,0.000266),
(574,0.000248),
(575,0.000232),
(576,0.000216),
(577,0.000199),
(578,0.000185),
(579,0.000172),
(580,0.00016),
(581,0.000148),
(582,0.000138),
(583,0.000128),
(584,0.00012),
(585,0.000111),
(586,0.000103),
(587,9.6E-05),
(588,8.9E-05),
(589,8.3E-05),
(590,7.7E-05),
(591,7.2E-05),
(592,6.7E-05),
(593,6.2E-05),
(594,5.7E-05),
(595,5.3E-05),
(596,5E-05),
(597,4.6E-05),
(598,4.3E-05),
(599,4E-05),
(600,3.7E-05),
(601,0.001039),
(602,0.001039),
(603,0.00099),
(604,0.00099),
(605,0.000945),
(606,0.000945),
(607,0.000945),
(608,0.000903),
(609,0.000903),
(610,0.000866),
(611,0.000866),
(612,0.000831),
(613,0.000831),
(614,0.000799),
(615,0.00077),
(616,0.000742),
(617,0.000742),
(618,0.000717),
(619,0.000717),
(620,0.00067),
(621,0.00067),
(622,0.000649),
(623,0.000649),
(624,0.00063),
(625,0.000611),
(626,0.000594),
(627,0.000594),
(628,0.000577),
(629,0.000577),
(630,0.000547),
(631,0.000547),
(632,0.000533),
(633,0.00052),
(634,0.00052),
(635,0.000495),
(636,0.000483),
(637,0.000483),
(638,0.000472),
(639,0.000472),
(640,0.000452),
(641,0.000442),
(642,0.000442),
(643,0.000433),
(644,0.000424),
(645,0.000416),
(646,0.000407),
(647,0.0004),
(648,0.000392),
(649,0.000392),
(650,0.000378),
(651,0.000371),
(652,0.000365),
(653,0.000365),
(654,0.000358),
(655,0.000346),
(656,0.000341),
(657,0.000335),
(658,0.000335),
(659,0.00033),
(660,0.00032),
(661,0.00031),
(662,0.000304),
(663,0.000294),
(664,0.000285),
(665,0.000281),
(666,0.000273),
(667,0.000267),
(668,0.000261),
(669,0.000255),
(670,0.00025),
(671,0.000232),
(672,0.000216),
(673,0.000201),
(674,0.000187),
(675,0.000173),
(676,0.000161),
(677,0.00015),
(678,0.000139),
(679,0.000129),
(680,0.00012),
(681,0.000371),
(682,0.000363),
(683,0.000359),
(684,0.000355),
(685,0.000347),
(686,0.000344),
(687,0.00034),
(688,0.000333),
(689,0.00033),
(690,0.000324),
(691,0.00032),
(692,0.000314),
(693,0.000311),
(694,0.000308),
(695,0.0003),
(696,0.000297),
(697,0.000295),
(698,0.000289),
(699,0.000287),
(700,0.000282),
(701,0.000773),
(702,0.000773),
(703,0.000773),
(704,0.000736),
(705,0.000736),
(706,0.000736),
(707,0.000736),
(708,0.000736),
(709,0.000736),
(710,0.000703),
(711,0.000703),
(712,0.000703),
(713,0.000703),
(714,0.000703),
(715,0.000672),
(716,0.000672),
(717,0.000672),
(718,0.000672),
(719,0.000672),
(720,0.000644),
(721,0.000644),
(722,0.000644),
(723,0.000644),
(724,0.000618),
(725,0.000618),
(726,0.000618),
(727,0.000618),
(728,0.000618),
(729,0.000595),
(730,0.000595),
(731,0.000595),
(732,0.000595),
(733,0.000573),
(734,0.000573),
(735,0.000573),
(736,0.000552),
(737,0.000552),
(738,0.000552),
(739,0.000552),
(740,0.000533),
(741,0.000533),
(742,0.000533),
(743,0.000533),
(744,0.000515),
(745,0.000515),
(746,0.000515),
(747,0.000499),
(748,0.000499),
(749,0.000499),
(750,0.000483),
(751,0.000483),
(752,0.000483),
(753,0.000468),
(754,0.000468),
(755,0.000468),
(756,0.000455),
(757,0.000455),
(758,0.000455),
(759,0.000442),
(760,0.000442),
(761,0.000442),
(762,0.000442),
(763,0.000429),
(764,0.000429),
(765,0.000429),
(766,0.000418),
(767,0.000418),
(768,0.000418),
(769,0.000407),
(770,0.000407),
(771,0.000377),
(772,0.000351),
(773,0.000329),
(774,0.000303),
(775,0.000281),
(776,0.000262),
(777,0.000242),
(778,0.000227),
(779,0.000209),
(780,0.000196),
(781,0.000184),
(782,0.000174),
(783,0.000164),
(784,0.000156),
(785,0.000149),
(786,0.000142),
(787,0.000136),
(788,0.00013),
(789,0.000125),
(790,0.00012),
(791,0.000115),
(792,0.000111),
(793,0.000107),
(794,0.000104),
(795,0.0001),
(796,9.7E-05),
(797,9.4E-05),
(798,9.1E-05),
(799,8.9E-05),
(800,8.6E-05),
(801,0.001189),
(802,0.001189),
(803,0.001132),
(804,0.001132),
(805,0.001132),
(806,0.001081),
(807,0.001081),
(808,0.001081),
(809,0.001034),
(810,0.001034),
(811,0.000991),
(812,0.000991),
(813,0.000991),
(814,0.000959),
(815,0.000944),
(816,0.000928),
(817,0.000914),
(818,0.000899),
(819,0.000885),
(820,0.000871),
(821,0.000857),
(822,0.000844),
(823,0.000831),
(824,0.000818),
(825,0.000805),
(826,0.000792),
(827,0.00078),
(828,0.000768),
(829,0.000756),
(830,0.000745),
(831,0.000733),
(832,0.000722),
(833,0.000711),
(834,0.0007),
(835,0.00069),
(836,0.000679),
(837,0.000669),
(838,0.000659),
(839,0.000649),
(840,0.000639),
(841,0.00063),
(842,0.00062),
(843,0.000611),
(844,0.000602),
(845,0.000593),
(846,0.000584),
(847,0.000576),
(848,0.000567),
(849,0.000559),
(850,0.000551),
(851,0.000543),
(852,0.000535),
(853,0.000527),
(854,0.000519),
(855,0.000512),
(856,0.000504),
(857,0.000497),
(858,0.00049),
(859,0.000483),
(860,0.000476),
(861,0.000469),
(862,0.000462),
(863,0.000455),
(864,0.000449),
(865,0.000442),
(866,0.000436),
(867,0.00043),
(868,0.000424),
(869,0.000418),
(870,0.000412),
(871,0.000384),
(872,0.000355),
(873,0.00033),
(874,0.000309),
(875,0.000287),
(876,0.000264),
(877,0.000245),
(878,0.000229),
(879,0.000212),
(880,0.000198),
(881,0.000366),
(882,0.000366),
(883,0.000361),
(884,0.000357),
(885,0.000349),
(886,0.000345),
(887,0.000341),
(888,0.000333),
(889,0.00033),
(890,0.000323),
(891,0.000319),
(892,0.000316),
(893,0.000313),
(894,0.000309),
(895,0.000303),
(896,0.0003),
(897,0.000297),
(898,0.000291),
(899,0.000288),
(900,0.000283),
(901,0.01),
(902,0.01),
(903,0.01),
(904,0.01),
(905,0.01),
(906,0.01),
(907,0.01),
(908,0.01),
(909,0.01),
(910,0.01),
(911,0.01),
(912,0.01),
(913,0.01),
(914,0.01),
(915,0.01),
(916,0.01),
(917,0.01),
(918,0.01),
(919,0.01),
(920,0.01),
(921,0.01),
(922,0.01),
(923,0.01),
(924,0.01),
(925,0.01),
(926,0.01),
(927,0.01),
(928,0.01),
(929,0.01),
(930,0.01),
(931,0.01),
(932,0.01),
(933,0.01),
(934,0.01),
(935,0.01),
(936,0.01),
(937,0.01),
(938,0.01),
(939,0.01),
(940,0.01),
(941,0.01),
(942,0.01),
(943,0.01),
(944,0.01),
(945,0.01),
(946,0.01),
(947,0.01),
(948,0.01),
(949,0.01),
(950,0.01),
(951,0.01),
(952,0.01),
(953,0.01),
(954,0.01),
(955,0.01),
(956,0.01),
(957,0.01),
(958,0.01),
(959,0.01),
(960,0.01),
(961,0.01),
(962,0.01),
(963,0.01),
(964,0.01),
(965,0.01),
(966,0.01),
(967,0.01),
(968,0.01),
(969,0.01),
(970,0.01),
(971,0.01),
(972,0.01),
(973,0.01),
(974,0.01),
(975,0.01),
(976,0.01),
(977,0.01),
(978,0.01),
(979,0.01),
(980,0.01),
(981,0.01),
(982,0.01),
(983,0.01),
(984,0.01),
(985,0.01),
(986,0.01),
(987,0.01),
(988,0.01),
(989,0.01),
(990,0.01),
(991,0.01),
(992,0.01),
(993,0.01),
(994,0.01),
(995,0.01),
(996,0.01),
(997,0.01),
(998,0.01),
(999,0.01),
(1000,0.01),
(1001,0.001262),
(1002,0.001262),
(1003,0.001202),
(1004,0.001202),
(1005,0.001148),
(1006,0.001148),
(1007,0.001098),
(1008,0.001098),
(1009,0.001052),
(1010,0.000971),
(1011,0.000935),
(1012,0.000935),
(1013,0.000902),
(1014,0.000902),
(1015,0.000842),
(1016,0.000842),
(1017,0.000814),
(1018,0.000789),
(1019,0.000789),
(1020,0.000701),
(1021,0.000701),
(1022,0.000682),
(1023,0.000664),
(1024,0.000664),
(1025,0.000631),
(1026,0.000631),
(1027,0.000616),
(1028,0.000601),
(1029,0.000601),
(1030,0.000549),
(1031,0.000537),
(1032,0.000537),
(1033,0.000526),
(1034,0.000515),
(1035,0.000505),
(1036,0.000495),
(1037,0.000485),
(1038,0.000485),
(1039,0.000476),
(1040,0.000443),
(1041,0.000435),
(1042,0.000435),
(1043,0.000428),
(1044,0.000421),
(1045,0.000407),
(1046,0.000401),
(1047,0.000401),
(1048,0.000394),
(1049,0.000388),
(1050,0.000366),
(1051,0.000361),
(1052,0.000356),
(1053,0.000351),
(1054,0.000351),
(1055,0.000341),
(1056,0.000337),
(1057,0.000332),
(1058,0.000328),
(1059,0.000324),
(1060,0.000308),
(1061,0.000299),
(1062,0.000295),
(1063,0.000285),
(1064,0.000279),
(1065,0.000274),
(1066,0.000269),
(1067,0.000265),
(1068,0.000258),
(1069,0.000254),
(1070,0.00025),
(1071,0.000232),
(1072,0.000216),
(1073,0.000201),
(1074,0.000187),
(1075,0.000173),
(1076,0.000161),
(1077,0.00015),
(1078,0.000139),
(1079,0.000129),
(1080,0.00012),
(1081,0.000376),
(1082,0.000369),
(1083,0.000366),
(1084,0.000363),
(1085,0.000353),
(1086,0.00035),
(1087,0.000347),
(1088,0.000342),
(1089,0.000339),
(1090,0.000333),
(1091,0.000328),
(1092,0.000325),
(1093,0.00032),
(1094,0.000318),
(1095,0.000311),
(1096,0.000308),
(1097,0.000304),
(1098,0.000299),
(1099,0.000297),
(1100,0.000291);

-- ----------------------------
-- Table structure for gtchancetospellcritbase_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtchancetospellcritbase_dbc`; 
CREATE TABLE `gtchancetospellcritbase_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Records of gtchancetospellcritbase_dbc
-- ----------------------------
INSERT INTO `gtchancetospellcritbase_dbc` VALUES 
(1,0),
(2,0.033355),
(3,0.03602),
(4,0),
(5,0.012375),
(6,0),
(7,0.02201),
(8,0.009075),
(9,0.017),
(10,0.2),
(11,0.018515);

-- ----------------------------
-- Table structure for gtchancetospellcrit_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtchancetospellcrit_dbc`; 
CREATE TABLE `gtchancetospellcrit_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Records of gtchancetospellcrit_dbc
-- ----------------------------
INSERT INTO `gtchancetospellcrit_dbc` VALUES 
(1,0),
(2,0),
(3,0),
(4,0),
(5,0),
(6,0),
(7,0),
(8,0),
(9,0),
(10,0),
(11,0),
(12,0),
(13,0),
(14,0),
(15,0),
(16,0),
(17,0),
(18,0),
(19,0),
(20,0),
(21,0),
(22,0),
(23,0),
(24,0),
(25,0),
(26,0),
(27,0),
(28,0),
(29,0),
(30,0),
(31,0),
(32,0),
(33,0),
(34,0),
(35,0),
(36,0),
(37,0),
(38,0),
(39,0),
(40,0),
(41,0),
(42,0),
(43,0),
(44,0),
(45,0),
(46,0),
(47,0),
(48,0),
(49,0),
(50,0),
(51,0),
(52,0),
(53,0),
(54,0),
(55,0),
(56,0),
(57,0),
(58,0),
(59,0),
(60,0),
(61,0),
(62,0),
(63,0),
(64,0),
(65,0),
(66,0),
(67,0),
(68,0),
(69,0),
(70,0),
(71,0),
(72,0),
(73,0),
(74,0),
(75,0),
(76,0),
(77,0),
(78,0),
(79,0),
(80,0),
(81,0),
(82,0),
(83,0),
(84,0),
(85,0),
(86,0),
(87,0),
(88,0),
(89,0),
(90,0),
(91,0),
(92,0),
(93,0),
(94,0),
(95,0),
(96,0),
(97,0),
(98,0),
(99,0),
(100,0),
(101,0.000832),
(102,0.000793),
(103,0.000793),
(104,0.000757),
(105,0.000757),
(106,0.000724),
(107,0.000694),
(108,0.000694),
(109,0.000666),
(110,0.000666),
(111,0.00064),
(112,0.000616),
(113,0.000594),
(114,0.000574),
(115,0.000537),
(116,0.000537),
(117,0.00052),
(118,0.00049),
(119,0.00049),
(120,0.000462),
(121,0.00045),
(122,0.000438),
(123,0.000427),
(124,0.000416),
(125,0.000396),
(126,0.000387),
(127,0.000387),
(128,0.00037),
(129,0.000362),
(130,0.000347),
(131,0.00034),
(132,0.000333),
(133,0.000326),
(134,0.00032),
(135,0.000308),
(136,0.000303),
(137,0.000297),
(138,0.000287),
(139,0.000282),
(140,0.000273),
(141,0.000268),
(142,0.000264),
(143,0.000256),
(144,0.000256),
(145,0.000248),
(146,0.000245),
(147,0.000238),
(148,0.000231),
(149,0.000228),
(150,0.000222),
(151,0.000219),
(152,0.000216),
(153,0.000211),
(154,0.000208),
(155,0.000203),
(156,0.000201),
(157,0.000198),
(158,0.000191),
(159,0.000189),
(160,0.000185),
(161,0.000159),
(162,0.000154),
(163,0.000149),
(164,0.000145),
(165,0.00014),
(166,0.000136),
(167,0.000134),
(168,0.000131),
(169,0.000128),
(170,0.000125),
(171,0.000116),
(172,0.000108),
(173,0.000101),
(174,9.3E-05),
(175,8.7E-05),
(176,8.1E-05),
(177,7.5E-05),
(178,7E-05),
(179,6.5E-05),
(180,6E-05),
(181,5.6E-05),
(182,5.3E-05),
(183,5E-05),
(184,4.7E-05),
(185,4.4E-05),
(186,4.2E-05),
(187,4E-05),
(188,3.8E-05),
(189,3.7E-05),
(190,3.5E-05),
(191,3.4E-05),
(192,3.3E-05),
(193,3.1E-05),
(194,3E-05),
(195,2.9E-05),
(196,2.8E-05),
(197,2.7E-05),
(198,2.7E-05),
(199,2.6E-05),
(200,2.5E-05),
(201,0.000699),
(202,0.000666),
(203,0.000666),
(204,0.000635),
(205,0.000635),
(206,0.000608),
(207,0.000608),
(208,0.000583),
(209,0.000583),
(210,0.000559),
(211,0.000559),
(212,0.000538),
(213,0.000499),
(214,0.000499),
(215,0.000466),
(216,0.000466),
(217,0.000451),
(218,0.000424),
(219,0.000424),
(220,0.000399),
(221,0.000388),
(222,0.000388),
(223,0.000368),
(224,0.000358),
(225,0.00035),
(226,0.000341),
(227,0.000333),
(228,0.000325),
(229,0.000318),
(230,0.000304),
(231,0.000297),
(232,0.000297),
(233,0.000285),
(234,0.00028),
(235,0.000269),
(236,0.000264),
(237,0.000264),
(238,0.000254),
(239,0.00025),
(240,0.000241),
(241,0.000237),
(242,0.000237),
(243,0.000229),
(244,0.000225),
(245,0.000218),
(246,0.000215),
(247,0.000212),
(248,0.000206),
(249,0.000203),
(250,0.000197),
(251,0.000194),
(252,0.000192),
(253,0.000186),
(254,0.000184),
(255,0.000179),
(256,0.000177),
(257,0.000175),
(258,0.00017),
(259,0.000168),
(260,0.000164),
(261,0.000157),
(262,0.000154),
(263,0.00015),
(264,0.000144),
(265,0.000141),
(266,0.000137),
(267,0.000133),
(268,0.00013),
(269,0.000128),
(270,0.000125),
(271,0.000116),
(272,0.000108),
(273,0.000101),
(274,9.3E-05),
(275,8.7E-05),
(276,8.1E-05),
(277,7.5E-05),
(278,7E-05),
(279,6.5E-05),
(280,6E-05),
(281,5.6E-05),
(282,5.3E-05),
(283,5E-05),
(284,4.7E-05),
(285,4.4E-05),
(286,4.2E-05),
(287,4E-05),
(288,3.8E-05),
(289,3.7E-05),
(290,3.5E-05),
(291,3.4E-05),
(292,3.3E-05),
(293,3.1E-05),
(294,3E-05),
(295,2.9E-05),
(296,2.8E-05),
(297,2.7E-05),
(298,2.7E-05),
(299,2.6E-05),
(300,2.5E-05),
(301,0),
(302,0),
(303,0),
(304,0),
(305,0),
(306,0),
(307,0),
(308,0),
(309,0),
(310,0),
(311,0),
(312,0),
(313,0),
(314,0),
(315,0),
(316,0),
(317,0),
(318,0),
(319,0),
(320,0),
(321,0),
(322,0),
(323,0),
(324,0),
(325,0),
(326,0),
(327,0),
(328,0),
(329,0),
(330,0),
(331,0),
(332,0),
(333,0),
(334,0),
(335,0),
(336,0),
(337,0),
(338,0),
(339,0),
(340,0),
(341,0),
(342,0),
(343,0),
(344,0),
(345,0),
(346,0),
(347,0),
(348,0),
(349,0),
(350,0),
(351,0),
(352,0),
(353,0),
(354,0),
(355,0),
(356,0),
(357,0),
(358,0),
(359,0),
(360,0),
(361,0),
(362,0),
(363,0),
(364,0),
(365,0),
(366,0),
(367,0),
(368,0),
(369,0),
(370,0),
(371,0),
(372,0),
(373,0),
(374,0),
(375,0),
(376,0),
(377,0),
(378,0),
(379,0),
(380,0),
(381,0),
(382,0),
(383,0),
(384,0),
(385,0),
(386,0),
(387,0),
(388,0),
(389,0),
(390,0),
(391,0),
(392,0),
(393,0),
(394,0),
(395,0),
(396,0),
(397,0),
(398,0),
(399,0),
(400,0),
(401,0.00171),
(402,0.001636),
(403,0.001568),
(404,0.001505),
(405,0.001394),
(406,0.001344),
(407,0.001297),
(408,0.001254),
(409,0.001214),
(410,0.00114),
(411,0.001045),
(412,0.000941),
(413,0.000875),
(414,0.000784),
(415,0.000724),
(416,0.000684),
(417,0.000627),
(418,0.000597),
(419,0.000562),
(420,0.000523),
(421,0.000502),
(422,0.00047),
(423,0.000453),
(424,0.000428),
(425,0.000409),
(426,0.000392),
(427,0.000376),
(428,0.000362),
(429,0.000348),
(430,0.000333),
(431,0.000322),
(432,0.000311),
(433,0.000301),
(434,0.000289),
(435,0.000281),
(436,0.000273),
(437,0.000263),
(438,0.000256),
(439,0.000249),
(440,0.000241),
(441,0.000235),
(442,0.000228),
(443,0.000223),
(444,0.000216),
(445,0.00021),
(446,0.000206),
(447,0.0002),
(448,0.000196),
(449,0.000191),
(450,0.000186),
(451,0.000183),
(452,0.000178),
(453,0.000175),
(454,0.000171),
(455,0.000166),
(456,0.000164),
(457,0.00016),
(458,0.000157),
(459,0.000154),
(460,0.000151),
(461,0.000148),
(462,0.000145),
(463,0.000143),
(464,0.000139),
(465,0.000137),
(466,0.000134),
(467,0.000132),
(468,0.00013),
(469,0.000127),
(470,0.000125),
(471,0.000116),
(472,0.000108),
(473,0.000101),
(474,9.3E-05),
(475,8.7E-05),
(476,8.1E-05),
(477,7.5E-05),
(478,7E-05),
(479,6.5E-05),
(480,6E-05),
(481,5.6E-05),
(482,5.3E-05),
(483,5E-05),
(484,4.7E-05),
(485,4.4E-05),
(486,4.2E-05),
(487,4E-05),
(488,3.8E-05),
(489,3.7E-05),
(490,3.5E-05),
(491,3.4E-05),
(492,3.3E-05),
(493,3.1E-05),
(494,3E-05),
(495,2.9E-05),
(496,2.8E-05),
(497,2.7E-05),
(498,2.7E-05),
(499,2.6E-05),
(500,2.5E-05),
(501,0),
(502,0),
(503,0),
(504,0),
(505,0),
(506,0),
(507,0),
(508,0),
(509,0),
(510,0),
(511,0),
(512,0),
(513,0),
(514,0),
(515,0),
(516,0),
(517,0),
(518,0),
(519,0),
(520,0),
(521,0),
(522,0),
(523,0),
(524,0),
(525,0),
(526,0),
(527,0),
(528,0),
(529,0),
(530,0),
(531,0),
(532,0),
(533,0),
(534,0),
(535,0),
(536,0),
(537,0),
(538,0),
(539,0),
(540,0),
(541,0),
(542,0),
(543,0),
(544,0),
(545,0),
(546,0),
(547,0),
(548,0),
(549,0),
(550,0),
(551,0),
(552,0),
(553,0),
(554,0),
(555,0),
(556,0),
(557,0),
(558,0),
(559,0),
(560,0),
(561,0),
(562,0),
(563,0),
(564,0),
(565,0),
(566,0),
(567,0),
(568,0),
(569,0),
(570,0),
(571,0),
(572,0),
(573,0),
(574,0),
(575,0),
(576,0),
(577,0),
(578,0),
(579,0),
(580,0),
(581,0),
(582,0),
(583,0),
(584,0),
(585,0),
(586,0),
(587,0),
(588,0),
(589,0),
(590,0),
(591,0),
(592,0),
(593,0),
(594,0),
(595,0),
(596,0),
(597,0),
(598,0),
(599,0),
(600,0),
(601,0.001333),
(602,0.001272),
(603,0.001217),
(604,0.001217),
(605,0.001166),
(606,0.00112),
(607,0.001077),
(608,0.001037),
(609,0.001),
(610,0.001),
(611,0.000933),
(612,0.000875),
(613,0.0008),
(614,0.000756),
(615,0.0007),
(616,0.000666),
(617,0.000636),
(618,0.000596),
(619,0.000571),
(620,0.000538),
(621,0.000518),
(622,0.0005),
(623,0.000474),
(624,0.000459),
(625,0.000437),
(626,0.000424),
(627,0.000412),
(628,0.000394),
(629,0.000383),
(630,0.000368),
(631,0.000354),
(632,0.000346),
(633,0.000333),
(634,0.000325),
(635,0.000314),
(636,0.000304),
(637,0.000298),
(638,0.000289),
(639,0.000283),
(640,0.000272),
(641,0.000267),
(642,0.000262),
(643,0.000254),
(644,0.000248),
(645,0.000241),
(646,0.000235),
(647,0.000231),
(648,0.000226),
(649,0.00022),
(650,0.000215),
(651,0.00021),
(652,0.000207),
(653,0.000201),
(654,0.000199),
(655,0.000193),
(656,0.00019),
(657,0.000187),
(658,0.000182),
(659,0.000179),
(660,0.000175),
(661,0.000164),
(662,0.000159),
(663,0.000152),
(664,0.000147),
(665,0.000142),
(666,0.000138),
(667,0.000134),
(668,0.000131),
(669,0.000128),
(670,0.000125),
(671,0.000116),
(672,0.000108),
(673,0.000101),
(674,9.3E-05),
(675,8.7E-05),
(676,8.1E-05),
(677,7.5E-05),
(678,7E-05),
(679,6.5E-05),
(680,6E-05),
(681,5.6E-05),
(682,5.3E-05),
(683,5E-05),
(684,4.7E-05),
(685,4.4E-05),
(686,4.2E-05),
(687,4E-05),
(688,3.8E-05),
(689,3.7E-05),
(690,3.5E-05),
(691,3.4E-05),
(692,3.3E-05),
(693,3.1E-05),
(694,3E-05),
(695,2.9E-05),
(696,2.8E-05),
(697,2.7E-05),
(698,2.7E-05),
(699,2.6E-05),
(700,2.5E-05),
(701,0.001637),
(702,0.001574),
(703,0.001516),
(704,0.001411),
(705,0.001364),
(706,0.00132),
(707,0.001279),
(708,0.00124),
(709,0.001169),
(710,0.001137),
(711,0.001049),
(712,0.00093),
(713,0.000871),
(714,0.000731),
(715,0.000671),
(716,0.000639),
(717,0.000602),
(718,0.000568),
(719,0.000538),
(720,0.000505),
(721,0.000487),
(722,0.00046),
(723,0.000445),
(724,0.000422),
(725,0.000405),
(726,0.00039),
(727,0.000372),
(728,0.000338),
(729,0.000325),
(730,0.000312),
(731,0.000305),
(732,0.000294),
(733,0.000286),
(734,0.000278),
(735,0.000269),
(736,0.000262),
(737,0.000254),
(738,0.000248),
(739,0.000241),
(740,0.000235),
(741,0.00023),
(742,0.000215),
(743,0.000211),
(744,0.000206),
(745,0.000201),
(746,0.000197),
(747,0.000192),
(748,0.000188),
(749,0.000184),
(750,0.000179),
(751,0.000176),
(752,0.000173),
(753,0.00017),
(754,0.000166),
(755,0.000162),
(756,0.000154),
(757,0.000151),
(758,0.000149),
(759,0.000146),
(760,0.000143),
(761,0.000143),
(762,0.000143),
(763,0.000143),
(764,0.000143),
(765,0.000142),
(766,0.000138),
(767,0.000134),
(768,0.000131),
(769,0.000128),
(770,0.000125),
(771,0.000116),
(772,0.000108),
(773,0.000101),
(774,9.3E-05),
(775,8.7E-05),
(776,8.1E-05),
(777,7.5E-05),
(778,7E-05),
(779,6.5E-05),
(780,6E-05),
(781,5.6E-05),
(782,5.3E-05),
(783,5E-05),
(784,4.7E-05),
(785,4.4E-05),
(786,4.2E-05),
(787,4E-05),
(788,3.8E-05),
(789,3.7E-05),
(790,3.5E-05),
(791,3.4E-05),
(792,3.3E-05),
(793,3.1E-05),
(794,3E-05),
(795,2.9E-05),
(796,2.8E-05),
(797,2.7E-05),
(798,2.7E-05),
(799,2.6E-05),
(800,2.5E-05),
(801,0.0015),
(802,0.001435),
(803,0.001375),
(804,0.00132),
(805,0.001269),
(806,0.001222),
(807,0.001179),
(808,0.001138),
(809,0.0011),
(810,0.001065),
(811,0.000971),
(812,0.000892),
(813,0.000825),
(814,0.000767),
(815,0.000717),
(816,0.000688),
(817,0.000635),
(818,0.0006),
(819,0.000569),
(820,0.000541),
(821,0.000516),
(822,0.000493),
(823,0.000471),
(824,0.000446),
(825,0.000429),
(826,0.000418),
(827,0.000398),
(828,0.000384),
(829,0.000367),
(830,0.000355),
(831,0.000347),
(832,0.000333),
(833,0.000324),
(834,0.000311),
(835,0.000303),
(836,0.000295),
(837,0.000284),
(838,0.000277),
(839,0.000268),
(840,0.000262),
(841,0.000256),
(842,0.000248),
(843,0.000243),
(844,0.000236),
(845,0.000229),
(846,0.000224),
(847,0.00022),
(848,0.000214),
(849,0.000209),
(850,0.000204),
(851,0.0002),
(852,0.000195),
(853,0.000191),
(854,0.000186),
(855,0.000182),
(856,0.000179),
(857,0.000176),
(858,0.000172),
(859,0.000168),
(860,0.000165),
(861,0.000159),
(862,0.000154),
(863,0.000148),
(864,0.000143),
(865,0.000138),
(866,0.000135),
(867,0.00013),
(868,0.000127),
(869,0.000126),
(870,0.000125),
(871,0.000116),
(872,0.000108),
(873,0.000101),
(874,9.3E-05),
(875,8.7E-05),
(876,8.1E-05),
(877,7.5E-05),
(878,7E-05),
(879,6.5E-05),
(880,6E-05),
(881,5.6E-05),
(882,5.3E-05),
(883,5E-05),
(884,4.7E-05),
(885,4.4E-05),
(886,4.2E-05),
(887,4E-05),
(888,3.8E-05),
(889,3.7E-05),
(890,3.5E-05),
(891,3.4E-05),
(892,3.3E-05),
(893,3.1E-05),
(894,3E-05),
(895,2.9E-05),
(896,2.8E-05),
(897,2.7E-05),
(898,2.7E-05),
(899,2.6E-05),
(900,2.5E-05),
(901,0),
(902,0),
(903,0),
(904,0),
(905,0),
(906,0),
(907,0),
(908,0),
(909,0),
(910,0),
(911,0),
(912,0),
(913,0),
(914,0),
(915,0),
(916,0),
(917,0),
(918,0),
(919,0),
(920,0),
(921,0),
(922,0),
(923,0),
(924,0),
(925,0),
(926,0),
(927,0),
(928,0),
(929,0),
(930,0),
(931,0),
(932,0),
(933,0),
(934,0),
(935,0),
(936,0),
(937,0),
(938,0),
(939,0),
(940,0),
(941,0),
(942,0),
(943,0),
(944,0),
(945,0),
(946,0),
(947,0),
(948,0),
(949,0),
(950,0),
(951,0),
(952,0),
(953,0),
(954,0),
(955,0),
(956,0),
(957,0),
(958,0),
(959,0),
(960,0),
(961,0),
(962,0),
(963,0),
(964,0),
(965,0),
(966,0),
(967,0),
(968,0),
(969,0),
(970,0),
(971,0),
(972,0),
(973,0),
(974,0),
(975,0),
(976,0),
(977,0),
(978,0),
(979,0),
(980,0),
(981,0),
(982,0),
(983,0),
(984,0),
(985,0),
(986,0),
(987,0),
(988,0),
(989,0),
(990,0),
(991,0),
(992,0),
(993,0),
(994,0),
(995,0),
(996,0),
(997,0),
(998,0),
(999,0),
(1000,0),
(1001,0.001431),
(1002,0.001369),
(1003,0.001312),
(1004,0.001259),
(1005,0.001211),
(1006,0.001166),
(1007,0.001124),
(1008,0.001124),
(1009,0.001086),
(1010,0.000984),
(1011,0.000926),
(1012,0.000851),
(1013,0.000807),
(1014,0.00075),
(1015,0.000684),
(1016,0.000656),
(1017,0.000617),
(1018,0.000594),
(1019,0.000562),
(1020,0.000516),
(1021,0.0005),
(1022,0.000477),
(1023,0.000463),
(1024,0.000437),
(1025,0.00042),
(1026,0.000409),
(1027,0.000394),
(1028,0.000384),
(1029,0.000366),
(1030,0.000346),
(1031,0.000339),
(1032,0.000325),
(1033,0.000318),
(1034,0.000309),
(1035,0.000297),
(1036,0.000292),
(1037,0.000284),
(1038,0.000276),
(1039,0.000269),
(1040,0.000256),
(1041,0.000252),
(1042,0.000244),
(1043,0.00024),
(1044,0.000233),
(1045,0.000228),
(1046,0.000223),
(1047,0.000219),
(1048,0.000214),
(1049,0.000209),
(1050,0.000202),
(1051,0.000198),
(1052,0.000193),
(1053,0.000191),
(1054,0.000186),
(1055,0.000182),
(1056,0.000179),
(1057,0.000176),
(1058,0.000173),
(1059,0.000169),
(1060,0.000164),
(1061,0.000162),
(1062,0.000157),
(1063,0.00015),
(1064,0.000146),
(1065,0.000142),
(1066,0.000137),
(1067,0.000133),
(1068,0.000131),
(1069,0.000128),
(1070,0.000125),
(1071,0.000116),
(1072,0.000108),
(1073,0.000101),
(1074,9.3E-05),
(1075,8.7E-05),
(1076,8.1E-05),
(1077,7.5E-05),
(1078,7E-05),
(1079,6.5E-05),
(1080,6E-05),
(1081,5.6E-05),
(1082,5.3E-05),
(1083,5E-05),
(1084,4.7E-05),
(1085,4.4E-05),
(1086,4.2E-05),
(1087,4E-05),
(1088,3.8E-05),
(1089,3.7E-05),
(1090,3.5E-05),
(1091,3.4E-05),
(1092,3.3E-05),
(1093,3.1E-05),
(1094,3E-05),
(1095,2.9E-05),
(1096,2.8E-05),
(1097,2.7E-05),
(1098,2.7E-05),
(1099,2.6E-05),
(1100,2.5E-05);

-- ----------------------------
-- Table structure for gtnpcmanacostscaler_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtnpcmanacostscaler_dbc`; 
CREATE TABLE `gtnpcmanacostscaler_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Records of gtnpcmanacostscaler_dbc
-- ----------------------------
INSERT INTO `gtnpcmanacostscaler_dbc` VALUES 
(1,0.193),
(2,0.216),
(3,0.264),
(4,0.31),
(5,0.31),
(6,0.395),
(7,0.436),
(8,0.475),
(9,0.514),
(10,0.552),
(11,0.588),
(12,0.625),
(13,0.661),
(14,0.696),
(15,0.766),
(16,0.8),
(17,0.835),
(18,0.885),
(19,0.919),
(20,1),
(21,1.034),
(22,1.067),
(23,1.101),
(24,1.165),
(25,1.229),
(26,1.278),
(27,1.328),
(28,1.405),
(29,1.522),
(30,1.612),
(31,1.662),
(32,1.752),
(33,1.805),
(34,1.858),
(35,1.964),
(36,2.032),
(37,2.126),
(38,2.196),
(39,2.292),
(40,2.351),
(41,2.446),
(42,2.506),
(43,2.626),
(44,2.686),
(45,2.782),
(46,2.854),
(47,2.95),
(48,3.012),
(49,3.074),
(50,3.195),
(51,3.269),
(52,3.378),
(53,3.475),
(54,3.583),
(55,3.658),
(56,3.788),
(57,3.863),
(58,3.972),
(59,4.048),
(60,4.167),
(61,4.266),
(62,4.4),
(63,4.514),
(64,4.662),
(65,4.768),
(66,4.908),
(67,5.016),
(68,5.169),
(69,5.292),
(70,5.437),
(71,5.593),
(72,5.709),
(73,5.858),
(74,5.998),
(75,6.15),
(76,6.282),
(77,6.415),
(78,6.594),
(79,6.762),
(80,6.899),
(81,7.082),
(82,7.222),
(83,7.376),
(84,7.552),
(85,7.697),
(86,7.876),
(87,8.024),
(88,8.196),
(89,8.347),
(90,8.533),
(91,8.741),
(92,8.898),
(93,9.055),
(94,9.215),
(95,9.408),
(96,9.572),
(97,9.736),
(98,9.902),
(99,10.091),
(100,10.293);

-- ----------------------------
-- Table structure for gtoctclasscombatratingscalar_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtoctclasscombatratingscalar_dbc`; 
CREATE TABLE `gtoctclasscombatratingscalar_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of gtoctclasscombatratingscalar_dbc
-- ----------------------------
INSERT INTO `gtoctclasscombatratingscalar_dbc` VALUES 
(1,1),
(2,1),
(3,1),
(4,1),
(5,1),
(6,1),
(7,1),
(8,1),
(9,1),
(10,1),
(11,1),
(12,1),
(13,1),
(14,1),
(15,1),
(16,1),
(17,1),
(18,1),
(19,1),
(20,1),
(21,1),
(22,1),
(23,1),
(24,1),
(25,1.1),
(26,1),
(27,1),
(28,1),
(29,1),
(30,1),
(31,1),
(32,1),
(33,1),
(34,1),
(35,1),
(36,1),
(37,1),
(38,1),
(39,1),
(40,1),
(41,1),
(42,1),
(43,1),
(44,1),
(45,1),
(46,1),
(47,1),
(48,1),
(49,1),
(50,1.3),
(51,1),
(52,1),
(53,1),
(54,1),
(55,1),
(56,1),
(57,1.1),
(58,1),
(59,1),
(60,1),
(61,1),
(62,1),
(63,1),
(64,1),
(65,1),
(66,1),
(67,1),
(68,1),
(69,1),
(70,1),
(71,1),
(72,1),
(73,1),
(74,1),
(75,1),
(76,1),
(77,1),
(78,1),
(79,1),
(80,1),
(81,1),
(82,1),
(83,1),
(84,1),
(85,1),
(86,1),
(87,1),
(88,1),
(89,1.1),
(90,1),
(91,1),
(92,1),
(93,1),
(94,1),
(95,1),
(96,1),
(97,1),
(98,1),
(99,1),
(100,1),
(101,1),
(102,1),
(103,1),
(104,1),
(105,1),
(106,1),
(107,1),
(108,1),
(109,1),
(110,1),
(111,1),
(112,1),
(113,1),
(114,1),
(115,1),
(116,1),
(117,1),
(118,1),
(119,1),
(120,1),
(121,1.1),
(122,1),
(123,1),
(124,1),
(125,1),
(126,1),
(127,1),
(128,1),
(129,1),
(130,1),
(131,1),
(132,1),
(133,1),
(134,1),
(135,1),
(136,1),
(137,1),
(138,1),
(139,1),
(140,1),
(141,1),
(142,1),
(143,1),
(144,1),
(145,1),
(146,1),
(147,1),
(148,1),
(149,1),
(150,1),
(151,1),
(152,1),
(153,1.1),
(154,1),
(155,1),
(156,1),
(157,1),
(158,1),
(159,1),
(160,1),
(161,1),
(162,1),
(163,1),
(164,1),
(165,1),
(166,1),
(167,1),
(168,1),
(169,1),
(170,1),
(171,1),
(172,1),
(173,1),
(174,1),
(175,1),
(176,1),
(177,1),
(178,1.3),
(179,1),
(180,1),
(181,1),
(182,1),
(183,1),
(184,1),
(185,1.1),
(186,1),
(187,1),
(188,1),
(189,1),
(190,1),
(191,1),
(192,1),
(193,1),
(194,1),
(195,1),
(196,1),
(197,1),
(198,1),
(199,1),
(200,1),
(201,1),
(202,1),
(203,1),
(204,1),
(205,1),
(206,1),
(207,1),
(208,1),
(209,1),
(210,1.3),
(211,1),
(212,1),
(213,1),
(214,1),
(215,1),
(216,1),
(217,1.1),
(218,1),
(219,1),
(220,1),
(221,1),
(222,1),
(223,1),
(224,1),
(225,1),
(226,1),
(227,1),
(228,1),
(229,1),
(230,1),
(231,1),
(232,1),
(233,1),
(234,1),
(235,1),
(236,1),
(237,1),
(238,1),
(239,1),
(240,1),
(241,1),
(242,1),
(243,1),
(244,1),
(245,1),
(246,1),
(247,1),
(248,1),
(249,1.1),
(250,1),
(251,1),
(252,1),
(253,1),
(254,1),
(255,1),
(256,1),
(257,1),
(258,1),
(259,1),
(260,1),
(261,1),
(262,1),
(263,1),
(264,1),
(265,1),
(266,1),
(267,1),
(268,1),
(269,1),
(270,1),
(271,1),
(272,1),
(273,1),
(274,1),
(275,1),
(276,1),
(277,1),
(278,1),
(279,1),
(280,1),
(281,1.1),
(282,1),
(283,1),
(284,1),
(285,1),
(286,1),
(287,1),
(288,1),
(289,0),
(290,0),
(291,0),
(292,0),
(293,0),
(294,0),
(295,0),
(296,0),
(297,0),
(298,0),
(299,0),
(300,0),
(301,0),
(302,0),
(303,0),
(304,0),
(305,0),
(306,0),
(307,0),
(308,0),
(309,0),
(310,0),
(311,0),
(312,0),
(313,0),
(314,0),
(315,0),
(316,0),
(317,0),
(318,0),
(319,0),
(320,0),
(321,1),
(322,1),
(323,1),
(324,1),
(325,1),
(326,1),
(327,1),
(328,1),
(329,1),
(330,1),
(331,1),
(332,1),
(333,1),
(334,1),
(335,1),
(336,1),
(337,1),
(338,1.3),
(339,1),
(340,1),
(341,1),
(342,1),
(343,1),
(344,1),
(345,1.1),
(346,1),
(347,1),
(348,1),
(349,1),
(350,1),
(351,1),
(352,1);

-- ----------------------------
-- Table structure for gtoctregenhp_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtoctregenhp_dbc`; 
CREATE TABLE `gtoctregenhp_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Records of gtoctregenhp_dbc
-- ----------------------------
INSERT INTO `gtoctregenhp_dbc` VALUES 
(1,0.394737),
(2,0.462264),
(3,0.474862),
(4,0.515375),
(5,0.521857),
(6,0.533258),
(7,0.520401),
(8,0.509427),
(9,0.500415),
(10,0.462648),
(11,0.488835),
(12,0.486706),
(13,0.449973),
(14,0.450681),
(15,0.454545),
(16,0.454545),
(17,0.454545),
(18,0.5),
(19,0.5),
(20,0.5),
(21,0.5),
(22,0.5),
(23,0.555556),
(24,0.555556),
(25,0.555556),
(26,0.555556),
(27,0.555556),
(28,0.555556),
(29,0.555556),
(30,0.555556),
(31,0.555556),
(32,0.555556),
(33,0.555556),
(34,0.555556),
(35,0.555556),
(36,0.555556),
(37,0.555556),
(38,0.555556),
(39,0.555556),
(40,0.555556),
(41,0.555556),
(42,0.555556),
(43,0.555556),
(44,0.555556),
(45,0.555556),
(46,0.625),
(47,0.625),
(48,0.625),
(49,0.625),
(50,0.625),
(51,0.625),
(52,0.625),
(53,0.625),
(54,0.625),
(55,0.625),
(56,0.625),
(57,0.625),
(58,0.625),
(59,0.625),
(60,0.625),
(61,0.625),
(62,0.625),
(63,0.625),
(64,0.625),
(65,0.625),
(66,0.625),
(67,0.625),
(68,0.625),
(69,0.625),
(70,0.625),
(71,0.625),
(72,0.625),
(73,0.625),
(74,0.625),
(75,0.625),
(76,0.625),
(77,0.625),
(78,0.625),
(79,0.625),
(80,0.625),
(81,0.625),
(82,0.625),
(83,0.625),
(84,0.625),
(85,0.625),
(86,0.625),
(87,0.625),
(88,0.625),
(89,0.625),
(90,0.625),
(91,0.625),
(92,0.625),
(93,0.625),
(94,0.625),
(95,0.625),
(96,0.625),
(97,0.625),
(98,0.625),
(99,0.625),
(100,0.625),
(101,0.277778),
(102,0.301476),
(103,0.328751),
(104,0.320368),
(105,0.313114),
(106,0.316622),
(107,0.302168),
(108,0.298629),
(109,0.276091),
(110,0.255254),
(111,0.25347),
(112,0.243353),
(113,0.243736),
(114,0.22534),
(115,0.208333),
(116,0.208333),
(117,0.208333),
(118,0.217391),
(119,0.217391),
(120,0.217391),
(121,0.217391),
(122,0.217391),
(123,0.217391),
(124,0.227273),
(125,0.227273),
(126,0.227273),
(127,0.227273),
(128,0.238095),
(129,0.238095),
(130,0.238095),
(131,0.238095),
(132,0.238095),
(133,0.25),
(134,0.25),
(135,0.25),
(136,0.25),
(137,0.25),
(138,0.25),
(139,0.25),
(140,0.25),
(141,0.25),
(142,0.263158),
(143,0.263158),
(144,0.263158),
(145,0.263158),
(146,0.263158),
(147,0.263158),
(148,0.263158),
(149,0.263158),
(150,0.263158),
(151,0.263158),
(152,0.263158),
(153,0.263158),
(154,0.263158),
(155,0.263158),
(156,0.263158),
(157,0.263158),
(158,0.263158),
(159,0.263158),
(160,0.263158),
(161,0.263158),
(162,0.263158),
(163,0.263158),
(164,0.263158),
(165,0.263158),
(166,0.263158),
(167,0.263158),
(168,0.263158),
(169,0.263158),
(170,0.263158),
(171,0.263158),
(172,0.263158),
(173,0.263158),
(174,0.263158),
(175,0.263158),
(176,0.263158),
(177,0.263158),
(178,0.263158),
(179,0.263158),
(180,0.263158),
(181,0.263158),
(182,0.263158),
(183,0.263158),
(184,0.263158),
(185,0.263158),
(186,0.263158),
(187,0.263158),
(188,0.263158),
(189,0.263158),
(190,0.263158),
(191,0.263158),
(192,0.263158),
(193,0.263158),
(194,0.263158),
(195,0.263158),
(196,0.263158),
(197,0.263158),
(198,0.263158),
(199,0.263158),
(200,0.263158),
(201,0.267857),
(202,0.288915),
(203,0.312714),
(204,0.311937),
(205,0.313114),
(206,0.297997),
(207,0.302168),
(208,0.279363),
(209,0.266888),
(210,0.255254),
(211,0.235989),
(212,0.218179),
(213,0.208916),
(214,0.193149),
(215,0.178571),
(216,0.178571),
(217,0.178571),
(218,0.178571),
(219,0.178571),
(220,0.178571),
(221,0.178571),
(222,0.178571),
(223,0.178571),
(224,0.178571),
(225,0.178571),
(226,0.185185),
(227,0.185185),
(228,0.185185),
(229,0.185185),
(230,0.185185),
(231,0.192308),
(232,0.192308),
(233,0.192308),
(234,0.192308),
(235,0.192308),
(236,0.192308),
(237,0.192308),
(238,0.192308),
(239,0.192308),
(240,0.192308),
(241,0.192308),
(242,0.192308),
(243,0.2),
(244,0.2),
(245,0.2),
(246,0.2),
(247,0.2),
(248,0.2),
(249,0.2),
(250,0.2),
(251,0.2),
(252,0.2),
(253,0.2),
(254,0.2),
(255,0.2),
(256,0.2),
(257,0.2),
(258,0.2),
(259,0.2),
(260,0.2),
(261,0.2),
(262,0.2),
(263,0.2),
(264,0.2),
(265,0.2),
(266,0.2),
(267,0.2),
(268,0.2),
(269,0.2),
(270,0.2),
(271,0.2),
(272,0.2),
(273,0.2),
(274,0.2),
(275,0.2),
(276,0.2),
(277,0.2),
(278,0.2),
(279,0.2),
(280,0.2),
(281,0.2),
(282,0.2),
(283,0.2),
(284,0.2),
(285,0.2),
(286,0.2),
(287,0.2),
(288,0.2),
(289,0.2),
(290,0.2),
(291,0.2),
(292,0.2),
(293,0.2),
(294,0.2),
(295,0.2),
(296,0.2),
(297,0.2),
(298,0.2),
(299,0.2),
(300,0.2),
(301,0.365854),
(302,0.42024),
(303,0.442113),
(304,0.455908),
(305,0.476478),
(306,0.460541),
(307,0.468361),
(308,0.455803),
(309,0.421402),
(310,0.411242),
(311,0.40257),
(312,0.372187),
(313,0.365603),
(314,0.33801),
(315,0.3125),
(316,0.333333),
(317,0.333333),
(318,0.333333),
(319,0.333333),
(320,0.333333),
(321,0.333333),
(322,0.333333),
(323,0.333333),
(324,0.333333),
(325,0.333333),
(326,0.333333),
(327,0.333333),
(328,0.333333),
(329,0.333333),
(330,0.333333),
(331,0.333333),
(332,0.333333),
(333,0.333333),
(334,0.333333),
(335,0.333333),
(336,0.333333),
(337,0.333333),
(338,0.333333),
(339,0.333333),
(340,0.333333),
(341,0.333333),
(342,0.357143),
(343,0.357143),
(344,0.357143),
(345,0.357143),
(346,0.357143),
(347,0.357143),
(348,0.357143),
(349,0.357143),
(350,0.357143),
(351,0.357143),
(352,0.357143),
(353,0.357143),
(354,0.357143),
(355,0.357143),
(356,0.357143),
(357,0.357143),
(358,0.357143),
(359,0.357143),
(360,0.357143),
(361,0.357143),
(362,0.357143),
(363,0.357143),
(364,0.357143),
(365,0.357143),
(366,0.357143),
(367,0.357143),
(368,0.357143),
(369,0.357143),
(370,0.357143),
(371,0.357143),
(372,0.357143),
(373,0.357143),
(374,0.357143),
(375,0.357143),
(376,0.357143),
(377,0.357143),
(378,0.357143),
(379,0.357143),
(380,0.357143),
(381,0.357143),
(382,0.357143),
(383,0.357143),
(384,0.357143),
(385,0.357143),
(386,0.357143),
(387,0.357143),
(388,0.357143),
(389,0.357143),
(390,0.357143),
(391,0.357143),
(392,0.357143),
(393,0.357143),
(394,0.357143),
(395,0.357143),
(396,0.357143),
(397,0.357143),
(398,0.357143),
(399,0.357143),
(400,0.357143),
(401,0.357143),
(402,0.355588),
(403,0.337402),
(404,0.311937),
(405,0.288395),
(406,0.266629),
(407,0.246506),
(408,0.227901),
(409,0.210701),
(410,0.194799),
(411,0.180097),
(412,0.166505),
(413,0.153938),
(414,0.14232),
(415,0.131579),
(416,0.131579),
(417,0.131579),
(418,0.131579),
(419,0.131579),
(420,0.131579),
(421,0.131579),
(422,0.131579),
(423,0.131579),
(424,0.142857),
(425,0.142857),
(426,0.142857),
(427,0.142857),
(428,0.142857),
(429,0.142857),
(430,0.142857),
(431,0.142857),
(432,0.142857),
(433,0.142857),
(434,0.142857),
(435,0.142857),
(436,0.166667),
(437,0.166667),
(438,0.166667),
(439,0.166667),
(440,0.166667),
(441,0.166667),
(442,0.166667),
(443,0.166667),
(444,0.166667),
(445,0.166667),
(446,0.166667),
(447,0.172414),
(448,0.178571),
(449,0.178571),
(450,0.178571),
(451,0.178571),
(452,0.178571),
(453,0.178571),
(454,0.178571),
(455,0.178571),
(456,0.178571),
(457,0.178571),
(458,0.178571),
(459,0.178571),
(460,0.192308),
(461,0.192308),
(462,0.192308),
(463,0.192308),
(464,0.192308),
(465,0.192308),
(466,0.192308),
(467,0.192308),
(468,0.192308),
(469,0.192308),
(470,0.192308),
(471,0.192308),
(472,0.192308),
(473,0.192308),
(474,0.192308),
(475,0.192308),
(476,0.192308),
(477,0.192308),
(478,0.192308),
(479,0.192308),
(480,0.192308),
(481,0.192308),
(482,0.192308),
(483,0.192308),
(484,0.192308),
(485,0.192308),
(486,0.192308),
(487,0.192308),
(488,0.192308),
(489,0.192308),
(490,0.192308),
(491,0.192308),
(492,0.192308),
(493,0.192308),
(494,0.192308),
(495,0.192308),
(496,0.192308),
(497,0.192308),
(498,0.192308),
(499,0.192308),
(500,0.192308),
(501,0.394737),
(502,0.462264),
(503,0.474862),
(504,0.515375),
(505,0.521857),
(506,0.533258),
(507,0.520401),
(508,0.509427),
(509,0.500415),
(510,0.462648),
(511,0.488835),
(512,0.486706),
(513,0.449973),
(514,0.450681),
(515,0.454545),
(516,0.454545),
(517,0.454545),
(518,0.5),
(519,0.5),
(520,0.5),
(521,0.5),
(522,0.5),
(523,0.555556),
(524,0.555556),
(525,0.555556),
(526,0.555556),
(527,0.555556),
(528,0.555556),
(529,0.555556),
(530,0.555556),
(531,0.555556),
(532,0.555556),
(533,0.555556),
(534,0.555556),
(535,0.555556),
(536,0.555556),
(537,0.555556),
(538,0.555556),
(539,0.555556),
(540,0.555556),
(541,0.555556),
(542,0.555556),
(543,0.555556),
(544,0.555556),
(545,0.555556),
(546,0.625),
(547,0.625),
(548,0.625),
(549,0.625),
(550,0.625),
(551,0.625),
(552,0.625),
(553,0.625),
(554,0.625),
(555,0.625),
(556,0.625),
(557,0.625),
(558,0.625),
(559,0.625),
(560,0.625),
(561,0.625),
(562,0.625),
(563,0.625),
(564,0.625),
(565,0.625),
(566,0.625),
(567,0.625),
(568,0.625),
(569,0.625),
(570,0.625),
(571,0.625),
(572,0.625),
(573,0.625),
(574,0.625),
(575,0.625),
(576,0.625),
(577,0.625),
(578,0.625),
(579,0.625),
(580,0.625),
(581,0.625),
(582,0.625),
(583,0.625),
(584,0.625),
(585,0.625),
(586,0.625),
(587,0.625),
(588,0.625),
(589,0.625),
(590,0.625),
(591,0.625),
(592,0.625),
(593,0.625),
(594,0.625),
(595,0.625),
(596,0.625),
(597,0.625),
(598,0.625),
(599,0.625),
(600,0.625),
(601,0.258621),
(602,0.277358),
(603,0.284917),
(604,0.289113),
(605,0.281),
(606,0.273835),
(607,0.260201),
(608,0.240563),
(609,0.222407),
(610,0.205621),
(611,0.190103),
(612,0.175755),
(613,0.16249),
(614,0.150227),
(615,0.138889),
(616,0.138889),
(617,0.142857),
(618,0.147059),
(619,0.151515),
(620,0.151515),
(621,0.151515),
(622,0.15625),
(623,0.15625),
(624,0.15625),
(625,0.15625),
(626,0.15625),
(627,0.16129),
(628,0.16129),
(629,0.16129),
(630,0.166667),
(631,0.166667),
(632,0.166667),
(633,0.166667),
(634,0.166667),
(635,0.166667),
(636,0.166667),
(637,0.172414),
(638,0.172414),
(639,0.172414),
(640,0.172414),
(641,0.172414),
(642,0.172414),
(643,0.172414),
(644,0.172414),
(645,0.172414),
(646,0.172414),
(647,0.172414),
(648,0.172414),
(649,0.172414),
(650,0.172414),
(651,0.172414),
(652,0.172414),
(653,0.172414),
(654,0.172414),
(655,0.172414),
(656,0.172414),
(657,0.172414),
(658,0.172414),
(659,0.172414),
(660,0.172414),
(661,0.172414),
(662,0.172414),
(663,0.172414),
(664,0.172414),
(665,0.172414),
(666,0.172414),
(667,0.172414),
(668,0.172414),
(669,0.172414),
(670,0.172414),
(671,0.172414),
(672,0.172414),
(673,0.172414),
(674,0.172414),
(675,0.172414),
(676,0.172414),
(677,0.172414),
(678,0.172414),
(679,0.172414),
(680,0.172414),
(681,0.172414),
(682,0.172414),
(683,0.172414),
(684,0.172414),
(685,0.172414),
(686,0.172414),
(687,0.172414),
(688,0.172414),
(689,0.172414),
(690,0.172414),
(691,0.172414),
(692,0.172414),
(693,0.172414),
(694,0.172414),
(695,0.172414),
(696,0.172414),
(697,0.172414),
(698,0.172414),
(699,0.172414),
(700,0.172414),
(701,0.238095),
(702,0.252144),
(703,0.261659),
(704,0.241911),
(705,0.223653),
(706,0.206773),
(707,0.191168),
(708,0.17674),
(709,0.163401),
(710,0.151069),
(711,0.139667),
(712,0.129126),
(713,0.119381),
(714,0.110371),
(715,0.102041),
(716,0.102041),
(717,0.102041),
(718,0.102041),
(719,0.102041),
(720,0.102041),
(721,0.102041),
(722,0.102041),
(723,0.102041),
(724,0.102041),
(725,0.102041),
(726,0.102041),
(727,0.102041),
(728,0.102041),
(729,0.102041),
(730,0.102041),
(731,0.102041),
(732,0.102041),
(733,0.102041),
(734,0.102041),
(735,0.104167),
(736,0.104167),
(737,0.104167),
(738,0.104167),
(739,0.106383),
(740,0.106383),
(741,0.106383),
(742,0.108696),
(743,0.108696),
(744,0.108696),
(745,0.108696),
(746,0.108696),
(747,0.111111),
(748,0.111111),
(749,0.111111),
(750,0.113636),
(751,0.113636),
(752,0.113636),
(753,0.116279),
(754,0.116279),
(755,0.116279),
(756,0.116279),
(757,0.119048),
(758,0.119048),
(759,0.119048),
(760,0.119048),
(761,0.121951),
(762,0.121951),
(763,0.121951),
(764,0.121951),
(765,0.121951),
(766,0.121951),
(767,0.121951),
(768,0.121951),
(769,0.121951),
(770,0.121951),
(771,0.121951),
(772,0.121951),
(773,0.121951),
(774,0.121951),
(775,0.121951),
(776,0.121951),
(777,0.121951),
(778,0.121951),
(779,0.121951),
(780,0.121951),
(781,0.121951),
(782,0.121951),
(783,0.121951),
(784,0.121951),
(785,0.121951),
(786,0.121951),
(787,0.121951),
(788,0.121951),
(789,0.121951),
(790,0.121951),
(791,0.121951),
(792,0.121951),
(793,0.121951),
(794,0.121951),
(795,0.121951),
(796,0.121951),
(797,0.121951),
(798,0.121951),
(799,0.121951),
(800,0.121951),
(801,0.241935),
(802,0.256813),
(803,0.261659),
(804,0.263414),
(805,0.249068),
(806,0.23027),
(807,0.212891),
(808,0.196824),
(809,0.181969),
(810,0.168236),
(811,0.155538),
(812,0.1438),
(813,0.132947),
(814,0.122913),
(815,0.113636),
(816,0.113636),
(817,0.113636),
(818,0.113636),
(819,0.113636),
(820,0.113636),
(821,0.113636),
(822,0.113636),
(823,0.113636),
(824,0.113636),
(825,0.113636),
(826,0.113636),
(827,0.113636),
(828,0.113636),
(829,0.113636),
(830,0.113636),
(831,0.113636),
(832,0.116279),
(833,0.116279),
(834,0.116279),
(835,0.116279),
(836,0.116279),
(837,0.116279),
(838,0.116279),
(839,0.116279),
(840,0.119048),
(841,0.119048),
(842,0.119048),
(843,0.119048),
(844,0.119048),
(845,0.119048),
(846,0.121951),
(847,0.121951),
(848,0.121951),
(849,0.121951),
(850,0.121951),
(851,0.125),
(852,0.125),
(853,0.125),
(854,0.125),
(855,0.125),
(856,0.125),
(857,0.125),
(858,0.128205),
(859,0.128205),
(860,0.128205),
(861,0.128205),
(862,0.128205),
(863,0.128205),
(864,0.128205),
(865,0.128205),
(866,0.128205),
(867,0.128205),
(868,0.128205),
(869,0.128205),
(870,0.128205),
(871,0.128205),
(872,0.128205),
(873,0.128205),
(874,0.128205),
(875,0.128205),
(876,0.128205),
(877,0.128205),
(878,0.128205),
(879,0.128205),
(880,0.128205),
(881,0.128205),
(882,0.128205),
(883,0.128205),
(884,0.128205),
(885,0.128205),
(886,0.128205),
(887,0.128205),
(888,0.128205),
(889,0.128205),
(890,0.128205),
(891,0.128205),
(892,0.128205),
(893,0.128205),
(894,0.128205),
(895,0.128205),
(896,0.128205),
(897,0.128205),
(898,0.128205),
(899,0.128205),
(900,0.128205),
(901,1),
(902,1),
(903,1),
(904,1),
(905,1),
(906,1),
(907,1),
(908,1),
(909,1),
(910,1),
(911,1),
(912,1),
(913,1),
(914,1),
(915,1),
(916,1),
(917,1),
(918,1),
(919,1),
(920,1),
(921,1),
(922,1),
(923,1),
(924,1),
(925,1),
(926,1),
(927,1),
(928,1),
(929,1),
(930,1),
(931,1),
(932,1),
(933,1),
(934,1),
(935,1),
(936,1),
(937,1),
(938,1),
(939,1),
(940,1),
(941,1),
(942,1),
(943,1),
(944,1),
(945,1),
(946,1),
(947,1),
(948,1),
(949,1),
(950,1),
(951,1),
(952,1),
(953,1),
(954,1),
(955,1),
(956,1),
(957,1),
(958,1),
(959,1),
(960,1),
(961,1),
(962,1),
(963,1),
(964,1),
(965,1),
(966,1),
(967,1),
(968,1),
(969,1),
(970,1),
(971,1),
(972,1),
(973,1),
(974,1),
(975,1),
(976,1),
(977,1),
(978,1),
(979,1),
(980,1),
(981,1),
(982,1),
(983,1),
(984,1),
(985,1),
(986,1),
(987,1),
(988,1),
(989,1),
(990,1),
(991,1),
(992,1),
(993,1),
(994,1),
(995,1),
(996,1),
(997,1),
(998,1),
(999,1),
(1000,1),
(1001,0.245902),
(1002,0.266691),
(1003,0.278723),
(1004,0.282229),
(1005,0.273975),
(1006,0.266629),
(1007,0.246506),
(1008,0.227901),
(1009,0.210701),
(1010,0.194799),
(1011,0.180097),
(1012,0.166505),
(1013,0.153938),
(1014,0.14232),
(1015,0.131579),
(1016,0.131579),
(1017,0.131579),
(1018,0.131579),
(1019,0.131579),
(1020,0.131579),
(1021,0.131579),
(1022,0.135135),
(1023,0.135135),
(1024,0.135135),
(1025,0.135135),
(1026,0.135135),
(1027,0.135135),
(1028,0.135135),
(1029,0.135135),
(1030,0.135135),
(1031,0.138889),
(1032,0.138889),
(1033,0.138889),
(1034,0.138889),
(1035,0.138889),
(1036,0.138889),
(1037,0.138889),
(1038,0.138889),
(1039,0.138889),
(1040,0.138889),
(1041,0.138889),
(1042,0.138889),
(1043,0.138889),
(1044,0.138889),
(1045,0.138889),
(1046,0.138889),
(1047,0.138889),
(1048,0.138889),
(1049,0.138889),
(1050,0.138889),
(1051,0.138889),
(1052,0.138889),
(1053,0.138889),
(1054,0.138889),
(1055,0.138889),
(1056,0.138889),
(1057,0.138889),
(1058,0.138889),
(1059,0.138889),
(1060,0.138889),
(1061,0.138889),
(1062,0.138889),
(1063,0.138889),
(1064,0.138889),
(1065,0.138889),
(1066,0.138889),
(1067,0.138889),
(1068,0.138889),
(1069,0.138889),
(1070,0.138889),
(1071,0.138889),
(1072,0.138889),
(1073,0.138889),
(1074,0.138889),
(1075,0.138889),
(1076,0.138889),
(1077,0.138889),
(1078,0.138889),
(1079,0.138889),
(1080,0.138889),
(1081,0.138889),
(1082,0.138889),
(1083,0.138889),
(1084,0.138889),
(1085,0.138889),
(1086,0.138889),
(1087,0.138889),
(1088,0.138889),
(1089,0.138889),
(1090,0.138889),
(1091,0.138889),
(1092,0.138889),
(1093,0.138889),
(1094,0.138889),
(1095,0.138889),
(1096,0.138889),
(1097,0.138889),
(1098,0.138889),
(1099,0.138889),
(1100,0.138889);

-- ----------------------------
-- Table structure for gtregenhpperspt_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtregenhpperspt_dbc`; 
CREATE TABLE `gtregenhpperspt_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Records of gtregenhpperspt_dbc
-- ----------------------------
INSERT INTO `gtregenhpperspt_dbc` VALUES
(1,1.5),
(2,1.386791),
(3,1.282127),
(4,1.185362),
(5,1.0959),
(6,1.01319),
(7,0.936722),
(8,0.866025),
(9,0.800664),
(10,0.740236),
(11,0.684369),
(12,0.632718),
(13,0.584965),
(14,0.540817),
(15,0.5),
(16,0.5),
(17,0.5),
(18,0.5),
(19,0.5),
(20,0.5),
(21,0.5),
(22,0.5),
(23,0.5),
(24,0.5),
(25,0.5),
(26,0.5),
(27,0.5),
(28,0.5),
(29,0.5),
(30,0.5),
(31,0.5),
(32,0.5),
(33,0.5),
(34,0.5),
(35,0.5),
(36,0.5),
(37,0.5),
(38,0.5),
(39,0.5),
(40,0.5),
(41,0.5),
(42,0.5),
(43,0.5),
(44,0.5),
(45,0.5),
(46,0.5),
(47,0.5),
(48,0.5),
(49,0.5),
(50,0.5),
(51,0.5),
(52,0.5),
(53,0.5),
(54,0.5),
(55,0.5),
(56,0.5),
(57,0.5),
(58,0.5),
(59,0.5),
(60,0.5),
(61,0.5),
(62,0.5),
(63,0.5),
(64,0.5),
(65,0.5),
(66,0.5),
(67,0.5),
(68,0.5),
(69,0.5),
(70,0.5),
(71,0.5),
(72,0.5),
(73,0.5),
(74,0.5),
(75,0.5),
(76,0.5),
(77,0.5),
(78,0.5),
(79,0.5),
(80,0.5),
(81,0.5),
(82,0.5),
(83,0.5),
(84,0.5),
(85,0.5),
(86,0.5),
(87,0.5),
(88,0.5),
(89,0.5),
(90,0.5),
(91,0.5),
(92,0.5),
(93,0.5),
(94,0.5),
(95,0.5),
(96,0.5),
(97,0.5),
(98,0.5),
(99,0.5),
(100,0.5),
(101,0.375),
(102,0.346698),
(103,0.320532),
(104,0.29634),
(105,0.273975),
(106,0.253297),
(107,0.234181),
(108,0.216506),
(109,0.200166),
(110,0.185059),
(111,0.171092),
(112,0.15818),
(113,0.146241),
(114,0.135204),
(115,0.125),
(116,0.125),
(117,0.125),
(118,0.125),
(119,0.125),
(120,0.125),
(121,0.125),
(122,0.125),
(123,0.125),
(124,0.125),
(125,0.125),
(126,0.125),
(127,0.125),
(128,0.125),
(129,0.125),
(130,0.125),
(131,0.125),
(132,0.125),
(133,0.125),
(134,0.125),
(135,0.125),
(136,0.125),
(137,0.125),
(138,0.125),
(139,0.125),
(140,0.125),
(141,0.125),
(142,0.125),
(143,0.125),
(144,0.125),
(145,0.125),
(146,0.125),
(147,0.125),
(148,0.125),
(149,0.125),
(150,0.125),
(151,0.125),
(152,0.125),
(153,0.125),
(154,0.125),
(155,0.125),
(156,0.125),
(157,0.125),
(158,0.125),
(159,0.125),
(160,0.125),
(161,0.125),
(162,0.125),
(163,0.125),
(164,0.125),
(165,0.125),
(166,0.125),
(167,0.125),
(168,0.125),
(169,0.125),
(170,0.125),
(171,0.125),
(172,0.125),
(173,0.125),
(174,0.125),
(175,0.125),
(176,0.125),
(177,0.125),
(178,0.125),
(179,0.125),
(180,0.125),
(181,0.125),
(182,0.125),
(183,0.125),
(184,0.125),
(185,0.125),
(186,0.125),
(187,0.125),
(188,0.125),
(189,0.125),
(190,0.125),
(191,0.125),
(192,0.125),
(193,0.125),
(194,0.125),
(195,0.125),
(196,0.125),
(197,0.125),
(198,0.125),
(199,0.125),
(200,0.125),
(201,0.375),
(202,0.346698),
(203,0.320532),
(204,0.29634),
(205,0.273975),
(206,0.253297),
(207,0.234181),
(208,0.216506),
(209,0.200166),
(210,0.185059),
(211,0.171092),
(212,0.15818),
(213,0.146241),
(214,0.135204),
(215,0.125),
(216,0.125),
(217,0.125),
(218,0.125),
(219,0.125),
(220,0.125),
(221,0.125),
(222,0.125),
(223,0.125),
(224,0.125),
(225,0.125),
(226,0.125),
(227,0.125),
(228,0.125),
(229,0.125),
(230,0.125),
(231,0.125),
(232,0.125),
(233,0.125),
(234,0.125),
(235,0.125),
(236,0.125),
(237,0.125),
(238,0.125),
(239,0.125),
(240,0.125),
(241,0.125),
(242,0.125),
(243,0.125),
(244,0.125),
(245,0.125),
(246,0.125),
(247,0.125),
(248,0.125),
(249,0.125),
(250,0.125),
(251,0.125),
(252,0.125),
(253,0.125),
(254,0.125),
(255,0.125),
(256,0.125),
(257,0.125),
(258,0.125),
(259,0.125),
(260,0.125),
(261,0.125),
(262,0.125),
(263,0.125),
(264,0.125),
(265,0.125),
(266,0.125),
(267,0.125),
(268,0.125),
(269,0.125),
(270,0.125),
(271,0.125),
(272,0.125),
(273,0.125),
(274,0.125),
(275,0.125),
(276,0.125),
(277,0.125),
(278,0.125),
(279,0.125),
(280,0.125),
(281,0.125),
(282,0.125),
(283,0.125),
(284,0.125),
(285,0.125),
(286,0.125),
(287,0.125),
(288,0.125),
(289,0.125),
(290,0.125),
(291,0.125),
(292,0.125),
(293,0.125),
(294,0.125),
(295,0.125),
(296,0.125),
(297,0.125),
(298,0.125),
(299,0.125),
(300,0.125),
(301,1),
(302,0.924528),
(303,0.854751),
(304,0.790241),
(305,0.7306),
(306,0.67546),
(307,0.624481),
(308,0.57735),
(309,0.533776),
(310,0.493491),
(311,0.456246),
(312,0.421812),
(313,0.389977),
(314,0.360544),
(315,0.333333),
(316,0.333333),
(317,0.333333),
(318,0.333333),
(319,0.333333),
(320,0.333333),
(321,0.333333),
(322,0.333333),
(323,0.333333),
(324,0.333333),
(325,0.333333),
(326,0.333333),
(327,0.333333),
(328,0.333333),
(329,0.333333),
(330,0.333333),
(331,0.333333),
(332,0.333333),
(333,0.333333),
(334,0.333333),
(335,0.333333),
(336,0.333333),
(337,0.333333),
(338,0.333333),
(339,0.333333),
(340,0.333333),
(341,0.333333),
(342,0.333333),
(343,0.333333),
(344,0.333333),
(345,0.333333),
(346,0.333333),
(347,0.333333),
(348,0.333333),
(349,0.333333),
(350,0.333333),
(351,0.333333),
(352,0.333333),
(353,0.333333),
(354,0.333333),
(355,0.333333),
(356,0.333333),
(357,0.333333),
(358,0.333333),
(359,0.333333),
(360,0.333333),
(361,0.333333),
(362,0.333333),
(363,0.333333),
(364,0.333333),
(365,0.333333),
(366,0.333333),
(367,0.333333),
(368,0.333333),
(369,0.333333),
(370,0.333333),
(371,0.333333),
(372,0.333333),
(373,0.333333),
(374,0.333333),
(375,0.333333),
(376,0.333333),
(377,0.333333),
(378,0.333333),
(379,0.333333),
(380,0.333333),
(381,0.333333),
(382,0.333333),
(383,0.333333),
(384,0.333333),
(385,0.333333),
(386,0.333333),
(387,0.333333),
(388,0.333333),
(389,0.333333),
(390,0.333333),
(391,0.333333),
(392,0.333333),
(393,0.333333),
(394,0.333333),
(395,0.333333),
(396,0.333333),
(397,0.333333),
(398,0.333333),
(399,0.333333),
(400,0.333333),
(401,0.125),
(402,0.115566),
(403,0.106844),
(404,0.09878),
(405,0.091325),
(406,0.084432),
(407,0.07806),
(408,0.072169),
(409,0.066722),
(410,0.061686),
(411,0.057031),
(412,0.052727),
(413,0.048747),
(414,0.045068),
(415,0.041667),
(416,0.041667),
(417,0.041667),
(418,0.041667),
(419,0.041667),
(420,0.041667),
(421,0.041667),
(422,0.041667),
(423,0.041667),
(424,0.041667),
(425,0.041667),
(426,0.041667),
(427,0.041667),
(428,0.041667),
(429,0.041667),
(430,0.041667),
(431,0.041667),
(432,0.041667),
(433,0.041667),
(434,0.041667),
(435,0.041667),
(436,0.041667),
(437,0.041667),
(438,0.041667),
(439,0.041667),
(440,0.041667),
(441,0.041667),
(442,0.041667),
(443,0.041667),
(444,0.041667),
(445,0.041667),
(446,0.041667),
(447,0.041667),
(448,0.041667),
(449,0.041667),
(450,0.041667),
(451,0.041667),
(452,0.041667),
(453,0.041667),
(454,0.041667),
(455,0.041667),
(456,0.041667),
(457,0.041667),
(458,0.041667),
(459,0.041667),
(460,0.041667),
(461,0.041667),
(462,0.041667),
(463,0.041667),
(464,0.041667),
(465,0.041667),
(466,0.041667),
(467,0.041667),
(468,0.041667),
(469,0.041667),
(470,0.041667),
(471,0.041667),
(472,0.041667),
(473,0.041667),
(474,0.041667),
(475,0.041667),
(476,0.041667),
(477,0.041667),
(478,0.041667),
(479,0.041667),
(480,0.041667),
(481,0.041667),
(482,0.041667),
(483,0.041667),
(484,0.041667),
(485,0.041667),
(486,0.041667),
(487,0.041667),
(488,0.041667),
(489,0.041667),
(490,0.041667),
(491,0.041667),
(492,0.041667),
(493,0.041667),
(494,0.041667),
(495,0.041667),
(496,0.041667),
(497,0.041667),
(498,0.041667),
(499,0.041667),
(500,0.041667),
(501,1.5),
(502,1.386791),
(503,1.282127),
(504,1.185362),
(505,1.0959),
(506,1.01319),
(507,0.936722),
(508,0.866025),
(509,0.800664),
(510,0.740236),
(511,0.684369),
(512,0.632718),
(513,0.584965),
(514,0.540817),
(515,0.5),
(516,0.5),
(517,0.5),
(518,0.5),
(519,0.5),
(520,0.5),
(521,0.5),
(522,0.5),
(523,0.5),
(524,0.5),
(525,0.5),
(526,0.5),
(527,0.5),
(528,0.5),
(529,0.5),
(530,0.5),
(531,0.5),
(532,0.5),
(533,0.5),
(534,0.5),
(535,0.5),
(536,0.5),
(537,0.5),
(538,0.5),
(539,0.5),
(540,0.5),
(541,0.5),
(542,0.5),
(543,0.5),
(544,0.5),
(545,0.5),
(546,0.5),
(547,0.5),
(548,0.5),
(549,0.5),
(550,0.5),
(551,0.5),
(552,0.5),
(553,0.5),
(554,0.5),
(555,0.5),
(556,0.5),
(557,0.5),
(558,0.5),
(559,0.5),
(560,0.5),
(561,0.5),
(562,0.5),
(563,0.5),
(564,0.5),
(565,0.5),
(566,0.5),
(567,0.5),
(568,0.5),
(569,0.5),
(570,0.5),
(571,0.5),
(572,0.5),
(573,0.5),
(574,0.5),
(575,0.5),
(576,0.5),
(577,0.5),
(578,0.5),
(579,0.5),
(580,0.5),
(581,0.5),
(582,0.5),
(583,0.5),
(584,0.5),
(585,0.5),
(586,0.5),
(587,0.5),
(588,0.5),
(589,0.5),
(590,0.5),
(591,0.5),
(592,0.5),
(593,0.5),
(594,0.5),
(595,0.5),
(596,0.5),
(597,0.5),
(598,0.5),
(599,0.5),
(600,0.5),
(601,0.214286),
(602,0.198113),
(603,0.183161),
(604,0.169337),
(605,0.156557),
(606,0.144741),
(607,0.133817),
(608,0.123718),
(609,0.114381),
(610,0.105748),
(611,0.097767),
(612,0.090388),
(613,0.083566),
(614,0.07726),
(615,0.071429),
(616,0.071429),
(617,0.071429),
(618,0.071429),
(619,0.071429),
(620,0.071429),
(621,0.071429),
(622,0.071429),
(623,0.071429),
(624,0.071429),
(625,0.071429),
(626,0.071429),
(627,0.071429),
(628,0.071429),
(629,0.071429),
(630,0.071429),
(631,0.071429),
(632,0.071429),
(633,0.071429),
(634,0.071429),
(635,0.071429),
(636,0.071429),
(637,0.071429),
(638,0.071429),
(639,0.071429),
(640,0.071429),
(641,0.071429),
(642,0.071429),
(643,0.071429),
(644,0.071429),
(645,0.071429),
(646,0.071429),
(647,0.071429),
(648,0.071429),
(649,0.071429),
(650,0.071429),
(651,0.071429),
(652,0.071429),
(653,0.071429),
(654,0.071429),
(655,0.071429),
(656,0.071429),
(657,0.071429),
(658,0.071429),
(659,0.071429),
(660,0.071429),
(661,0.071429),
(662,0.071429),
(663,0.071429),
(664,0.071429),
(665,0.071429),
(666,0.071429),
(667,0.071429),
(668,0.071429),
(669,0.071429),
(670,0.071429),
(671,0.071429),
(672,0.071429),
(673,0.071429),
(674,0.071429),
(675,0.071429),
(676,0.071429),
(677,0.071429),
(678,0.071429),
(679,0.071429),
(680,0.071429),
(681,0.071429),
(682,0.071429),
(683,0.071429),
(684,0.071429),
(685,0.071429),
(686,0.071429),
(687,0.071429),
(688,0.071429),
(689,0.071429),
(690,0.071429),
(691,0.071429),
(692,0.071429),
(693,0.071429),
(694,0.071429),
(695,0.071429),
(696,0.071429),
(697,0.071429),
(698,0.071429),
(699,0.071429),
(700,0.071429),
(701,0.125),
(702,0.115566),
(703,0.106844),
(704,0.09878),
(705,0.091325),
(706,0.084432),
(707,0.07806),
(708,0.072169),
(709,0.066722),
(710,0.061686),
(711,0.057031),
(712,0.052727),
(713,0.048747),
(714,0.045068),
(715,0.041667),
(716,0.041667),
(717,0.041667),
(718,0.041667),
(719,0.041667),
(720,0.041667),
(721,0.041667),
(722,0.041667),
(723,0.041667),
(724,0.041667),
(725,0.041667),
(726,0.041667),
(727,0.041667),
(728,0.041667),
(729,0.041667),
(730,0.041667),
(731,0.041667),
(732,0.041667),
(733,0.041667),
(734,0.041667),
(735,0.041667),
(736,0.041667),
(737,0.041667),
(738,0.041667),
(739,0.041667),
(740,0.041667),
(741,0.041667),
(742,0.041667),
(743,0.041667),
(744,0.041667),
(745,0.041667),
(746,0.041667),
(747,0.041667),
(748,0.041667),
(749,0.041667),
(750,0.041667),
(751,0.041667),
(752,0.041667),
(753,0.041667),
(754,0.041667),
(755,0.041667),
(756,0.041667),
(757,0.041667),
(758,0.041667),
(759,0.041667),
(760,0.041667),
(761,0.041667),
(762,0.041667),
(763,0.041667),
(764,0.041667),
(765,0.041667),
(766,0.041667),
(767,0.041667),
(768,0.041667),
(769,0.041667),
(770,0.041667),
(771,0.041667),
(772,0.041667),
(773,0.041667),
(774,0.041667),
(775,0.041667),
(776,0.041667),
(777,0.041667),
(778,0.041667),
(779,0.041667),
(780,0.041667),
(781,0.041667),
(782,0.041667),
(783,0.041667),
(784,0.041667),
(785,0.041667),
(786,0.041667),
(787,0.041667),
(788,0.041667),
(789,0.041667),
(790,0.041667),
(791,0.041667),
(792,0.041667),
(793,0.041667),
(794,0.041667),
(795,0.041667),
(796,0.041667),
(797,0.041667),
(798,0.041667),
(799,0.041667),
(800,0.041667),
(801,0.136364),
(802,0.126072),
(803,0.116557),
(804,0.10776),
(805,0.099627),
(806,0.092108),
(807,0.085157),
(808,0.07873),
(809,0.072788),
(810,0.067294),
(811,0.062215),
(812,0.05752),
(813,0.053179),
(814,0.049165),
(815,0.045455),
(816,0.045455),
(817,0.045455),
(818,0.045455),
(819,0.045455),
(820,0.045455),
(821,0.045455),
(822,0.045455),
(823,0.045455),
(824,0.045455),
(825,0.045455),
(826,0.045455),
(827,0.045455),
(828,0.045455),
(829,0.045455),
(830,0.045455),
(831,0.045455),
(832,0.045455),
(833,0.045455),
(834,0.045455),
(835,0.045455),
(836,0.045455),
(837,0.045455),
(838,0.045455),
(839,0.045455),
(840,0.045455),
(841,0.045455),
(842,0.045455),
(843,0.045455),
(844,0.045455),
(845,0.045455),
(846,0.045455),
(847,0.045455),
(848,0.045455),
(849,0.045455),
(850,0.045455),
(851,0.045455),
(852,0.045455),
(853,0.045455),
(854,0.045455),
(855,0.045455),
(856,0.045455),
(857,0.045455),
(858,0.045455),
(859,0.045455),
(860,0.045455),
(861,0.045455),
(862,0.045455),
(863,0.045455),
(864,0.045455),
(865,0.045455),
(866,0.045455),
(867,0.045455),
(868,0.045455),
(869,0.045455),
(870,0.045455),
(871,0.045455),
(872,0.045455),
(873,0.045455),
(874,0.045455),
(875,0.045455),
(876,0.045455),
(877,0.045455),
(878,0.045455),
(879,0.045455),
(880,0.045455),
(881,0.045455),
(882,0.045455),
(883,0.045455),
(884,0.045455),
(885,0.045455),
(886,0.045455),
(887,0.045455),
(888,0.045455),
(889,0.045455),
(890,0.045455),
(891,0.045455),
(892,0.045455),
(893,0.045455),
(894,0.045455),
(895,0.045455),
(896,0.045455),
(897,0.045455),
(898,0.045455),
(899,0.045455),
(900,0.045455),
(901,0),
(902,0),
(903,0),
(904,0),
(905,0),
(906,0),
(907,0),
(908,0),
(909,0),
(910,0),
(911,0),
(912,0),
(913,0),
(914,0),
(915,0),
(916,0),
(917,0),
(918,0),
(919,0),
(920,0),
(921,0),
(922,0),
(923,0),
(924,0),
(925,0),
(926,0),
(927,0),
(928,0),
(929,0),
(930,0),
(931,0),
(932,0),
(933,0),
(934,0),
(935,0),
(936,0),
(937,0),
(938,0),
(939,0),
(940,0),
(941,0),
(942,0),
(943,0),
(944,0),
(945,0),
(946,0),
(947,0),
(948,0),
(949,0),
(950,0),
(951,0),
(952,0),
(953,0),
(954,0),
(955,0),
(956,0),
(957,0),
(958,0),
(959,0),
(960,0),
(961,0),
(962,0),
(963,0),
(964,0),
(965,0),
(966,0),
(967,0),
(968,0),
(969,0),
(970,0),
(971,0),
(972,0),
(973,0),
(974,0),
(975,0),
(976,0),
(977,0),
(978,0),
(979,0),
(980,0),
(981,0),
(982,0),
(983,0),
(984,0),
(985,0),
(986,0),
(987,0),
(988,0),
(989,0),
(990,0),
(991,0),
(992,0),
(993,0),
(994,0),
(995,0),
(996,0),
(997,0),
(998,0),
(999,0),
(1000,0),
(1001,0.1875),
(1002,0.173349),
(1003,0.160266),
(1004,0.14817),
(1005,0.136987),
(1006,0.126649),
(1007,0.11709),
(1008,0.108253),
(1009,0.100083),
(1010,0.09253),
(1011,0.085546),
(1012,0.07909),
(1013,0.073121),
(1014,0.067602),
(1015,0.0625),
(1016,0.0625),
(1017,0.0625),
(1018,0.0625),
(1019,0.0625),
(1020,0.0625),
(1021,0.0625),
(1022,0.0625),
(1023,0.0625),
(1024,0.0625),
(1025,0.0625),
(1026,0.0625),
(1027,0.0625),
(1028,0.0625),
(1029,0.0625),
(1030,0.0625),
(1031,0.0625),
(1032,0.0625),
(1033,0.0625),
(1034,0.0625),
(1035,0.0625),
(1036,0.0625),
(1037,0.0625),
(1038,0.0625),
(1039,0.0625),
(1040,0.0625),
(1041,0.0625),
(1042,0.0625),
(1043,0.0625),
(1044,0.0625),
(1045,0.0625),
(1046,0.0625),
(1047,0.0625),
(1048,0.0625),
(1049,0.0625),
(1050,0.0625),
(1051,0.0625),
(1052,0.0625),
(1053,0.0625),
(1054,0.0625),
(1055,0.0625),
(1056,0.0625),
(1057,0.0625),
(1058,0.0625),
(1059,0.0625),
(1060,0.0625),
(1061,0.0625),
(1062,0.0625),
(1063,0.0625),
(1064,0.0625),
(1065,0.0625),
(1066,0.0625),
(1067,0.0625),
(1068,0.0625),
(1069,0.0625),
(1070,0.0625),
(1071,0.0625),
(1072,0.0625),
(1073,0.0625),
(1074,0.0625),
(1075,0.0625),
(1076,0.0625),
(1077,0.0625),
(1078,0.0625),
(1079,0.0625),
(1080,0.0625),
(1081,0.0625),
(1082,0.0625),
(1083,0.0625),
(1084,0.0625),
(1085,0.0625),
(1086,0.0625),
(1087,0.0625),
(1088,0.0625),
(1089,0.0625),
(1090,0.0625),
(1091,0.0625),
(1092,0.0625),
(1093,0.0625),
(1094,0.0625),
(1095,0.0625),
(1096,0.0625),
(1097,0.0625),
(1098,0.0625),
(1099,0.0625),
(1100,0.0625);

-- ----------------------------
-- Table structure for gtregenmpperspt_dbc
-- ----------------------------
DROP TABLE IF EXISTS `gtregenmpperspt_dbc`; 
CREATE TABLE `gtregenmpperspt_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of gtregenmpperspt_dbc
-- ----------------------------
INSERT INTO `gtregenmpperspt_dbc` VALUES 
(1,0),
(2,0),
(3,0),
(4,0),
(5,0),
(6,0),
(7,0),
(8,0),
(9,0),
(10,0),
(11,0),
(12,0),
(13,0),
(14,0),
(15,0),
(16,0),
(17,0),
(18,0),
(19,0),
(20,0),
(21,0),
(22,0),
(23,0),
(24,0),
(25,0),
(26,0),
(27,0),
(28,0),
(29,0),
(30,0),
(31,0),
(32,0),
(33,0),
(34,0),
(35,0),
(36,0),
(37,0),
(38,0),
(39,0),
(40,0),
(41,0),
(42,0),
(43,0),
(44,0),
(45,0),
(46,0),
(47,0),
(48,0),
(49,0),
(50,0),
(51,0),
(52,0),
(53,0),
(54,0),
(55,0),
(56,0),
(57,0),
(58,0),
(59,0),
(60,0),
(61,0),
(62,0),
(63,0),
(64,0),
(65,0),
(66,0),
(67,0),
(68,0),
(69,0),
(70,0),
(71,0),
(72,0),
(73,0),
(74,0),
(75,0),
(76,0),
(77,0),
(78,0),
(79,0),
(80,0),
(81,0),
(82,0),
(83,0),
(84,0),
(85,0),
(86,0),
(87,0),
(88,0),
(89,0),
(90,0),
(91,0),
(92,0),
(93,0),
(94,0),
(95,0),
(96,0),
(97,0),
(98,0),
(99,0),
(100,0),
(101,0.062937),
(102,0.0569),
(103,0.051488),
(104,0.046267),
(105,0.041637),
(106,0.037784),
(107,0.034309),
(108,0.031172),
(109,0.028158),
(110,0.02546),
(111,0.022654),
(112,0.019904),
(113,0.017817),
(114,0.015771),
(115,0.014008),
(116,0.01365),
(117,0.013175),
(118,0.012832),
(119,0.012475),
(120,0.012073),
(121,0.01184),
(122,0.011494),
(123,0.011292),
(124,0.01099),
(125,0.010761),
(126,0.010546),
(127,0.010321),
(128,0.010151),
(129,0.009949),
(130,0.00974),
(131,0.009597),
(132,0.009425),
(133,0.009278),
(134,0.009123),
(135,0.008974),
(136,0.008847),
(137,0.008698),
(138,0.008581),
(139,0.008457),
(140,0.008338),
(141,0.008235),
(142,0.008113),
(143,0.008018),
(144,0.007906),
(145,0.007798),
(146,0.007713),
(147,0.007612),
(148,0.007524),
(149,0.00743),
(150,0.00734),
(151,0.007268),
(152,0.007184),
(153,0.007116),
(154,0.007029),
(155,0.006945),
(156,0.006884),
(157,0.006805),
(158,0.006747),
(159,0.006667),
(160,0.0066),
(161,0.006421),
(162,0.006314),
(163,0.006175),
(164,0.006072),
(165,0.005981),
(166,0.005885),
(167,0.005791),
(168,0.005732),
(169,0.005668),
(170,0.005596),
(171,0.005316),
(172,0.005049),
(173,0.004796),
(174,0.004555),
(175,0.004327),
(176,0.00411),
(177,0.003903),
(178,0.003708),
(179,0.003522),
(180,0.003345),
(181,0.003345),
(182,0.003345),
(183,0.003345),
(184,5.4E-05),
(185,5.4E-05),
(186,5.4E-05),
(187,5.4E-05),
(188,5.4E-05),
(189,5.4E-05),
(190,5.4E-05),
(191,5.4E-05),
(192,5.4E-05),
(193,5.4E-05),
(194,5.4E-05),
(195,5.4E-05),
(196,5.4E-05),
(197,5.4E-05),
(198,5.4E-05),
(199,5.4E-05),
(200,5.4E-05),
(201,0.062937),
(202,0.0569),
(203,0.051488),
(204,0.046267),
(205,0.041637),
(206,0.037784),
(207,0.034309),
(208,0.031172),
(209,0.028158),
(210,0.02546),
(211,0.022654),
(212,0.019904),
(213,0.017817),
(214,0.015771),
(215,0.014008),
(216,0.01365),
(217,0.013175),
(218,0.012832),
(219,0.012475),
(220,0.012073),
(221,0.01184),
(222,0.011494),
(223,0.011292),
(224,0.01099),
(225,0.010761),
(226,0.010546),
(227,0.010321),
(228,0.010151),
(229,0.009949),
(230,0.00974),
(231,0.009597),
(232,0.009425),
(233,0.009278),
(234,0.009123),
(235,0.008974),
(236,0.008847),
(237,0.008698),
(238,0.008581),
(239,0.008457),
(240,0.008338),
(241,0.008235),
(242,0.008113),
(243,0.008018),
(244,0.007906),
(245,0.007798),
(246,0.007713),
(247,0.007612),
(248,0.007524),
(249,0.00743),
(250,0.00734),
(251,0.007268),
(252,0.007184),
(253,0.007116),
(254,0.007029),
(255,0.006945),
(256,0.006884),
(257,0.006805),
(258,0.006747),
(259,0.006667),
(260,0.0066),
(261,0.006421),
(262,0.006314),
(263,0.006175),
(264,0.006072),
(265,0.005981),
(266,0.005885),
(267,0.005791),
(268,0.005732),
(269,0.005668),
(270,0.005596),
(271,0.005316),
(272,0.005049),
(273,0.004796),
(274,0.004555),
(275,0.004327),
(276,0.00411),
(277,0.003903),
(278,0.003708),
(279,0.003522),
(280,0.003345),
(281,0.003345),
(282,0.003345),
(283,0.003345),
(284,5.4E-05),
(285,5.4E-05),
(286,5.4E-05),
(287,5.4E-05),
(288,5.4E-05),
(289,5.4E-05),
(290,5.4E-05),
(291,5.4E-05),
(292,5.4E-05),
(293,5.4E-05),
(294,5.4E-05),
(295,5.4E-05),
(296,5.4E-05),
(297,5.4E-05),
(298,5.4E-05),
(299,5.4E-05),
(300,5.4E-05),
(301,0),
(302,0),
(303,0),
(304,0),
(305,0),
(306,0),
(307,0),
(308,0),
(309,0),
(310,0),
(311,0),
(312,0),
(313,0),
(314,0),
(315,0),
(316,0),
(317,0),
(318,0),
(319,0),
(320,0),
(321,0),
(322,0),
(323,0),
(324,0),
(325,0),
(326,0),
(327,0),
(328,0),
(329,0),
(330,0),
(331,0),
(332,0),
(333,0),
(334,0),
(335,0),
(336,0),
(337,0),
(338,0),
(339,0),
(340,0),
(341,0),
(342,0),
(343,0),
(344,0),
(345,0),
(346,0),
(347,0),
(348,0),
(349,0),
(350,0),
(351,0),
(352,0),
(353,0),
(354,0),
(355,0),
(356,0),
(357,0),
(358,0),
(359,0),
(360,0),
(361,0),
(362,0),
(363,0),
(364,0),
(365,0),
(366,0),
(367,0),
(368,0),
(369,0),
(370,0),
(371,0),
(372,0),
(373,0),
(374,0),
(375,0),
(376,0),
(377,0),
(378,0),
(379,0),
(380,0),
(381,0),
(382,0),
(383,0),
(384,0),
(385,0),
(386,0),
(387,0),
(388,0),
(389,0),
(390,0),
(391,0),
(392,0),
(393,0),
(394,0),
(395,0),
(396,0),
(397,0),
(398,0),
(399,0),
(400,0),
(401,0.062937),
(402,0.0569),
(403,0.051488),
(404,0.046267),
(405,0.041637),
(406,0.037784),
(407,0.034309),
(408,0.031172),
(409,0.028158),
(410,0.02546),
(411,0.022654),
(412,0.019904),
(413,0.017817),
(414,0.015771),
(415,0.014008),
(416,0.01365),
(417,0.013175),
(418,0.012832),
(419,0.012475),
(420,0.012073),
(421,0.01184),
(422,0.011494),
(423,0.011292),
(424,0.01099),
(425,0.010761),
(426,0.010546),
(427,0.010321),
(428,0.010151),
(429,0.009949),
(430,0.00974),
(431,0.009597),
(432,0.009425),
(433,0.009278),
(434,0.009123),
(435,0.008974),
(436,0.008847),
(437,0.008698),
(438,0.008581),
(439,0.008457),
(440,0.008338),
(441,0.008235),
(442,0.008113),
(443,0.008018),
(444,0.007906),
(445,0.007798),
(446,0.007713),
(447,0.007612),
(448,0.007524),
(449,0.00743),
(450,0.00734),
(451,0.007268),
(452,0.007184),
(453,0.007116),
(454,0.007029),
(455,0.006945),
(456,0.006884),
(457,0.006805),
(458,0.006747),
(459,0.006667),
(460,0.0066),
(461,0.006421),
(462,0.006314),
(463,0.006175),
(464,0.006072),
(465,0.005981),
(466,0.005885),
(467,0.005791),
(468,0.005732),
(469,0.005668),
(470,0.005596),
(471,0.005316),
(472,0.005049),
(473,0.004796),
(474,0.004555),
(475,0.004327),
(476,0.00411),
(477,0.003903),
(478,0.003708),
(479,0.003522),
(480,0.003345),
(481,0.003345),
(482,0.003345),
(483,0.003345),
(484,5.4E-05),
(485,5.4E-05),
(486,5.4E-05),
(487,5.4E-05),
(488,5.4E-05),
(489,5.4E-05),
(490,5.4E-05),
(491,5.4E-05),
(492,5.4E-05),
(493,5.4E-05),
(494,5.4E-05),
(495,5.4E-05),
(496,5.4E-05),
(497,5.4E-05),
(498,5.4E-05),
(499,5.4E-05),
(500,5.4E-05),
(501,0),
(502,0),
(503,0),
(504,0),
(505,0),
(506,0),
(507,0),
(508,0),
(509,0),
(510,0),
(511,0),
(512,0),
(513,0),
(514,0),
(515,0),
(516,0),
(517,0),
(518,0),
(519,0),
(520,0),
(521,0),
(522,0),
(523,0),
(524,0),
(525,0),
(526,0),
(527,0),
(528,0),
(529,0),
(530,0),
(531,0),
(532,0),
(533,0),
(534,0),
(535,0),
(536,0),
(537,0),
(538,0),
(539,0),
(540,0),
(541,0),
(542,0),
(543,0),
(544,0),
(545,0),
(546,0),
(547,0),
(548,0),
(549,0),
(550,0),
(551,0),
(552,0),
(553,0),
(554,0),
(555,0),
(556,0),
(557,0),
(558,0),
(559,0),
(560,0),
(561,0),
(562,0),
(563,0),
(564,0),
(565,0),
(566,0),
(567,0),
(568,0),
(569,0),
(570,0),
(571,0),
(572,0),
(573,0),
(574,0),
(575,0),
(576,0),
(577,0),
(578,0),
(579,0),
(580,0),
(581,0),
(582,0),
(583,0),
(584,0),
(585,0),
(586,0),
(587,0),
(588,0),
(589,0),
(590,0),
(591,0),
(592,0),
(593,0),
(594,0),
(595,0),
(596,0),
(597,0),
(598,0),
(599,0),
(600,0),
(601,0.062937),
(602,0.0569),
(603,0.051488),
(604,0.046267),
(605,0.041637),
(606,0.037784),
(607,0.034309),
(608,0.031172),
(609,0.028158),
(610,0.02546),
(611,0.022654),
(612,0.019904),
(613,0.017817),
(614,0.015771),
(615,0.014008),
(616,0.01365),
(617,0.013175),
(618,0.012832),
(619,0.012475),
(620,0.012073),
(621,0.01184),
(622,0.011494),
(623,0.011292),
(624,0.01099),
(625,0.010761),
(626,0.010546),
(627,0.010321),
(628,0.010151),
(629,0.009949),
(630,0.00974),
(631,0.009597),
(632,0.009425),
(633,0.009278),
(634,0.009123),
(635,0.008974),
(636,0.008847),
(637,0.008698),
(638,0.008581),
(639,0.008457),
(640,0.008338),
(641,0.008235),
(642,0.008113),
(643,0.008018),
(644,0.007906),
(645,0.007798),
(646,0.007713),
(647,0.007612),
(648,0.007524),
(649,0.00743),
(650,0.00734),
(651,0.007268),
(652,0.007184),
(653,0.007116),
(654,0.007029),
(655,0.006945),
(656,0.006884),
(657,0.006805),
(658,0.006747),
(659,0.006667),
(660,0.0066),
(661,0.006421),
(662,0.006314),
(663,0.006175),
(664,0.006072),
(665,0.005981),
(666,0.005885),
(667,0.005791),
(668,0.005732),
(669,0.005668),
(670,0.005596),
(671,0.005316),
(672,0.005049),
(673,0.004796),
(674,0.004555),
(675,0.004327),
(676,0.00411),
(677,0.003903),
(678,0.003708),
(679,0.003522),
(680,0.003345),
(681,0.003345),
(682,0.003345),
(683,0.003345),
(684,5.4E-05),
(685,5.4E-05),
(686,5.4E-05),
(687,5.4E-05),
(688,5.4E-05),
(689,5.4E-05),
(690,5.4E-05),
(691,5.4E-05),
(692,5.4E-05),
(693,5.4E-05),
(694,5.4E-05),
(695,5.4E-05),
(696,5.4E-05),
(697,5.4E-05),
(698,5.4E-05),
(699,5.4E-05),
(700,5.4E-05),
(701,0.062937),
(702,0.0569),
(703,0.051488),
(704,0.046267),
(705,0.041637),
(706,0.037784),
(707,0.034309),
(708,0.031172),
(709,0.028158),
(710,0.02546),
(711,0.022654),
(712,0.019904),
(713,0.017817),
(714,0.015771),
(715,0.014008),
(716,0.01365),
(717,0.013175),
(718,0.012832),
(719,0.012475),
(720,0.012073),
(721,0.01184),
(722,0.011494),
(723,0.011292),
(724,0.01099),
(725,0.010761),
(726,0.010546),
(727,0.010321),
(728,0.010151),
(729,0.009949),
(730,0.00974),
(731,0.009597),
(732,0.009425),
(733,0.009278),
(734,0.009123),
(735,0.008974),
(736,0.008847),
(737,0.008698),
(738,0.008581),
(739,0.008457),
(740,0.008338),
(741,0.008235),
(742,0.008113),
(743,0.008018),
(744,0.007906),
(745,0.007798),
(746,0.007713),
(747,0.007612),
(748,0.007524),
(749,0.00743),
(750,0.00734),
(751,0.007268),
(752,0.007184),
(753,0.007116),
(754,0.007029),
(755,0.006945),
(756,0.006884),
(757,0.006805),
(758,0.006747),
(759,0.006667),
(760,0.0066),
(761,0.006421),
(762,0.006314),
(763,0.006175),
(764,0.006072),
(765,0.005981),
(766,0.005885),
(767,0.005791),
(768,0.005732),
(769,0.005668),
(770,0.005596),
(771,0.005316),
(772,0.005049),
(773,0.004796),
(774,0.004555),
(775,0.004327),
(776,0.00411),
(777,0.003903),
(778,0.003708),
(779,0.003522),
(780,0.003345),
(781,0.003345),
(782,0.003345),
(783,0.003345),
(784,5.4E-05),
(785,5.4E-05),
(786,5.4E-05),
(787,5.4E-05),
(788,5.4E-05),
(789,5.4E-05),
(790,5.4E-05),
(791,5.4E-05),
(792,5.4E-05),
(793,5.4E-05),
(794,5.4E-05),
(795,5.4E-05),
(796,5.4E-05),
(797,5.4E-05),
(798,5.4E-05),
(799,5.4E-05),
(800,5.4E-05),
(801,0.062937),
(802,0.0569),
(803,0.051488),
(804,0.046267),
(805,0.041637),
(806,0.037784),
(807,0.034309),
(808,0.031172),
(809,0.028158),
(810,0.02546),
(811,0.022654),
(812,0.019904),
(813,0.017817),
(814,0.015771),
(815,0.014008),
(816,0.01365),
(817,0.013175),
(818,0.012832),
(819,0.012475),
(820,0.012073),
(821,0.01184),
(822,0.011494),
(823,0.011292),
(824,0.01099),
(825,0.010761),
(826,0.010546),
(827,0.010321),
(828,0.010151),
(829,0.009949),
(830,0.00974),
(831,0.009597),
(832,0.009425),
(833,0.009278),
(834,0.009123),
(835,0.008974),
(836,0.008847),
(837,0.008698),
(838,0.008581),
(839,0.008457),
(840,0.008338),
(841,0.008235),
(842,0.008113),
(843,0.008018),
(844,0.007906),
(845,0.007798),
(846,0.007713),
(847,0.007612),
(848,0.007524),
(849,0.00743),
(850,0.00734),
(851,0.007268),
(852,0.007184),
(853,0.007116),
(854,0.007029),
(855,0.006945),
(856,0.006884),
(857,0.006805),
(858,0.006747),
(859,0.006667),
(860,0.0066),
(861,0.006421),
(862,0.006314),
(863,0.006175),
(864,0.006072),
(865,0.005981),
(866,0.005885),
(867,0.005791),
(868,0.005732),
(869,0.005668),
(870,0.005596),
(871,0.005316),
(872,0.005049),
(873,0.004796),
(874,0.004555),
(875,0.004327),
(876,0.00411),
(877,0.003903),
(878,0.003708),
(879,0.003522),
(880,0.003345),
(881,0.003345),
(882,0.003345),
(883,0.003345),
(884,5.4E-05),
(885,5.4E-05),
(886,5.4E-05),
(887,5.4E-05),
(888,5.4E-05),
(889,5.4E-05),
(890,5.4E-05),
(891,5.4E-05),
(892,5.4E-05),
(893,5.4E-05),
(894,5.4E-05),
(895,5.4E-05),
(896,5.4E-05),
(897,5.4E-05),
(898,5.4E-05),
(899,5.4E-05),
(900,5.4E-05),
(901,0),
(902,0),
(903,0),
(904,0),
(905,0),
(906,0),
(907,0),
(908,0),
(909,0),
(910,0),
(911,0),
(912,0),
(913,0),
(914,0),
(915,0),
(916,0),
(917,0),
(918,0),
(919,0),
(920,0),
(921,0),
(922,0),
(923,0),
(924,0),
(925,0),
(926,0),
(927,0),
(928,0),
(929,0),
(930,0),
(931,0),
(932,0),
(933,0),
(934,0),
(935,0),
(936,0),
(937,0),
(938,0),
(939,0),
(940,0),
(941,0),
(942,0),
(943,0),
(944,0),
(945,0),
(946,0),
(947,0),
(948,0),
(949,0),
(950,0),
(951,0),
(952,0),
(953,0),
(954,0),
(955,0),
(956,0),
(957,0),
(958,0),
(959,0),
(960,0),
(961,0),
(962,0),
(963,0),
(964,0),
(965,0),
(966,0),
(967,0),
(968,0),
(969,0),
(970,0),
(971,0),
(972,0),
(973,0),
(974,0),
(975,0),
(976,0),
(977,0),
(978,0),
(979,0),
(980,0),
(981,0),
(982,0),
(983,0),
(984,0),
(985,0),
(986,0),
(987,0),
(988,0),
(989,0),
(990,0),
(991,0),
(992,0),
(993,0),
(994,0),
(995,0),
(996,0),
(997,0),
(998,0),
(999,0),
(1000,0),
(1001,0.062937),
(1002,0.0569),
(1003,0.051488),
(1004,0.046267),
(1005,0.041637),
(1006,0.037784),
(1007,0.034309),
(1008,0.031172),
(1009,0.028158),
(1010,0.02546),
(1011,0.022654),
(1012,0.019904),
(1013,0.017817),
(1014,0.015771),
(1015,0.014008),
(1016,0.01365),
(1017,0.013175),
(1018,0.012832),
(1019,0.012475),
(1020,0.012073),
(1021,0.01184),
(1022,0.011494),
(1023,0.011292),
(1024,0.01099),
(1025,0.010761),
(1026,0.010546),
(1027,0.010321),
(1028,0.010151),
(1029,0.009949),
(1030,0.00974),
(1031,0.009597),
(1032,0.009425),
(1033,0.009278),
(1034,0.009123),
(1035,0.008974),
(1036,0.008847),
(1037,0.008698),
(1038,0.008581),
(1039,0.008457),
(1040,0.008338),
(1041,0.008235),
(1042,0.008113),
(1043,0.008018),
(1044,0.007906),
(1045,0.007798),
(1046,0.007713),
(1047,0.007612),
(1048,0.007524),
(1049,0.00743),
(1050,0.00734),
(1051,0.007268),
(1052,0.007184),
(1053,0.007116),
(1054,0.007029),
(1055,0.006945),
(1056,0.006884),
(1057,0.006805),
(1058,0.006747),
(1059,0.006667),
(1060,0.0066),
(1061,0.006421),
(1062,0.006314),
(1063,0.006175),
(1064,0.006072),
(1065,0.005981),
(1066,0.005885),
(1067,0.005791),
(1068,0.005732),
(1069,0.005668),
(1070,0.005596),
(1071,0.005316),
(1072,0.005049),
(1073,0.004796),
(1074,0.004555),
(1075,0.004327),
(1076,0.00411),
(1077,0.003903),
(1078,0.003708),
(1079,0.003522),
(1080,0.003345),
(1081,0.003345),
(1082,0.003345),
(1083,0.003345),
(1084,0),
(1085,0),
(1086,0),
(1087,0),
(1088,0),
(1089,0),
(1090,0),
(1091,0),
(1092,0),
(1093,0),
(1094,0),
(1095,0),
(1096,0),
(1097,0),
(1098,0),
(1099,0),
(1100,0);

-- ----------------------------
-- Table structure for holidays_dbc
-- ----------------------------
DROP TABLE IF EXISTS `holidays_dbc`; 
CREATE TABLE `holidays_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Duration_1` INT NOT NULL DEFAULT '0',
 `Duration_2` INT NOT NULL DEFAULT '0',
 `Duration_3` INT NOT NULL DEFAULT '0',
 `Duration_4` INT NOT NULL DEFAULT '0',
 `Duration_5` INT NOT NULL DEFAULT '0',
 `Duration_6` INT NOT NULL DEFAULT '0',
 `Duration_7` INT NOT NULL DEFAULT '0',
 `Duration_8` INT NOT NULL DEFAULT '0',
 `Duration_9` INT NOT NULL DEFAULT '0',
 `Duration_10` INT NOT NULL DEFAULT '0',
 `Date_1` INT NOT NULL DEFAULT '0',
 `Date_2` INT NOT NULL DEFAULT '0',
 `Date_3` INT NOT NULL DEFAULT '0',
 `Date_4` INT NOT NULL DEFAULT '0',
 `Date_5` INT NOT NULL DEFAULT '0',
 `Date_6` INT NOT NULL DEFAULT '0',
 `Date_7` INT NOT NULL DEFAULT '0',
 `Date_8` INT NOT NULL DEFAULT '0',
 `Date_9` INT NOT NULL DEFAULT '0',
 `Date_10` INT NOT NULL DEFAULT '0',
 `Date_11` INT NOT NULL DEFAULT '0',
 `Date_12` INT NOT NULL DEFAULT '0',
 `Date_13` INT NOT NULL DEFAULT '0',
 `Date_14` INT NOT NULL DEFAULT '0',
 `Date_15` INT NOT NULL DEFAULT '0',
 `Date_16` INT NOT NULL DEFAULT '0',
 `Date_17` INT NOT NULL DEFAULT '0',
 `Date_18` INT NOT NULL DEFAULT '0',
 `Date_19` INT NOT NULL DEFAULT '0',
 `Date_20` INT NOT NULL DEFAULT '0',
 `Date_21` INT NOT NULL DEFAULT '0',
 `Date_22` INT NOT NULL DEFAULT '0',
 `Date_23` INT NOT NULL DEFAULT '0',
 `Date_24` INT NOT NULL DEFAULT '0',
 `Date_25` INT NOT NULL DEFAULT '0',
 `Date_26` INT NOT NULL DEFAULT '0',
 `Region` INT NOT NULL DEFAULT '0',
 `Looping` INT NOT NULL DEFAULT '0',
 `CalendarFlags_1` INT NOT NULL DEFAULT '0',
 `CalendarFlags_2` INT NOT NULL DEFAULT '0',
 `CalendarFlags_3` INT NOT NULL DEFAULT '0',
 `CalendarFlags_4` INT NOT NULL DEFAULT '0',
 `CalendarFlags_5` INT NOT NULL DEFAULT '0',
 `CalendarFlags_6` INT NOT NULL DEFAULT '0',
 `CalendarFlags_7` INT NOT NULL DEFAULT '0',
 `CalendarFlags_8` INT NOT NULL DEFAULT '0',
 `CalendarFlags_9` INT NOT NULL DEFAULT '0',
 `CalendarFlags_10` INT NOT NULL DEFAULT '0',
 `HolidayNameID` INT NOT NULL DEFAULT '0',
 `HolidayDescriptionID` INT NOT NULL DEFAULT '0',
 `TextureFilename` varchar(100) NULL,
 `Priority` INT NOT NULL DEFAULT '0',
 `CalendarFilterType` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for itembagfamily_dbc
-- ----------------------------
DROP TABLE IF EXISTS `itembagfamily_dbc`; 
CREATE TABLE `itembagfamily_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for itemdisplayinfo_dbc
-- ----------------------------
DROP TABLE IF EXISTS `itemdisplayinfo_dbc`; 
CREATE TABLE `itemdisplayinfo_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ModelName_1` varchar(100) NULL,
 `ModelName_2` varchar(100) NULL,
 `ModelTexture_1` varchar(100) NULL,
 `ModelTexture_2` varchar(100) NULL,
 `InventoryIcon_1` varchar(100) NULL,
 `InventoryIcon_2` varchar(100) NULL,
 `GeosetGroup_1` INT NOT NULL DEFAULT '0',
 `GeosetGroup_2` INT NOT NULL DEFAULT '0',
 `GeosetGroup_3` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `SpellVisualID` INT NOT NULL DEFAULT '0',
 `GroupSoundIndex` INT NOT NULL DEFAULT '0',
 `HelmetGeosetVis_1` INT NOT NULL DEFAULT '0',
 `HelmetGeosetVis_2` INT NOT NULL DEFAULT '0',
 `Texture_1` varchar(100) NULL,
 `Texture_2` varchar(100) NULL,
 `Texture_3` varchar(100) NULL,
 `Texture_4` varchar(100) NULL,
 `Texture_5` varchar(100) NULL,
 `Texture_6` varchar(100) NULL,
 `Texture_7` varchar(100) NULL,
 `Texture_8` varchar(100) NULL,
 `ItemVisual` INT NOT NULL DEFAULT '0',
 `ParticleColorID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for itemextendedcost_dbc
-- ----------------------------
DROP TABLE IF EXISTS `itemextendedcost_dbc`; 
CREATE TABLE `itemextendedcost_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `HonorPoints` INT NOT NULL DEFAULT '0',
 `ArenaPoints` INT NOT NULL DEFAULT '0',
 `ArenaBracket` INT NOT NULL DEFAULT '0',
 `ItemID_1` INT NOT NULL DEFAULT '0',
 `ItemID_2` INT NOT NULL DEFAULT '0',
 `ItemID_3` INT NOT NULL DEFAULT '0',
 `ItemID_4` INT NOT NULL DEFAULT '0',
 `ItemID_5` INT NOT NULL DEFAULT '0',
 `ItemCount_1` INT NOT NULL DEFAULT '0',
 `ItemCount_2` INT NOT NULL DEFAULT '0',
 `ItemCount_3` INT NOT NULL DEFAULT '0',
 `ItemCount_4` INT NOT NULL DEFAULT '0',
 `ItemCount_5` INT NOT NULL DEFAULT '0',
 `RequiredArenaRating` INT NOT NULL DEFAULT '0',
 `ItemPurchaseGroup` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for itemlimitcategory_dbc
-- ----------------------------
DROP TABLE IF EXISTS `itemlimitcategory_dbc`; 
CREATE TABLE `itemlimitcategory_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Quantity` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for itemrandomproperties_dbc
-- ----------------------------
DROP TABLE IF EXISTS `itemrandomproperties_dbc`; 
CREATE TABLE `itemrandomproperties_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name` varchar(100) NULL,
 `Enchantment_1` INT NOT NULL DEFAULT '0',
 `Enchantment_2` INT NOT NULL DEFAULT '0',
 `Enchantment_3` INT NOT NULL DEFAULT '0',
 `Enchantment_4` INT NOT NULL DEFAULT '0',
 `Enchantment_5` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for itemrandomsuffix_dbc
-- ----------------------------
DROP TABLE IF EXISTS `itemrandomsuffix_dbc`; 
CREATE TABLE `itemrandomsuffix_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `InternalName` varchar(100) NULL,
 `Enchantment_1` INT NOT NULL DEFAULT '0',
 `Enchantment_2` INT NOT NULL DEFAULT '0',
 `Enchantment_3` INT NOT NULL DEFAULT '0',
 `Enchantment_4` INT NOT NULL DEFAULT '0',
 `Enchantment_5` INT NOT NULL DEFAULT '0',
 `AllocationPct_1` INT NOT NULL DEFAULT '0',
 `AllocationPct_2` INT NOT NULL DEFAULT '0',
 `AllocationPct_3` INT NOT NULL DEFAULT '0',
 `AllocationPct_4` INT NOT NULL DEFAULT '0',
 `AllocationPct_5` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for itemset_dbc
-- ----------------------------
DROP TABLE IF EXISTS `itemset_dbc`; 
CREATE TABLE `itemset_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `ItemID_1` INT NOT NULL DEFAULT '0',
 `ItemID_2` INT NOT NULL DEFAULT '0',
 `ItemID_3` INT NOT NULL DEFAULT '0',
 `ItemID_4` INT NOT NULL DEFAULT '0',
 `ItemID_5` INT NOT NULL DEFAULT '0',
 `ItemID_6` INT NOT NULL DEFAULT '0',
 `ItemID_7` INT NOT NULL DEFAULT '0',
 `ItemID_8` INT NOT NULL DEFAULT '0',
 `ItemID_9` INT NOT NULL DEFAULT '0',
 `ItemID_10` INT NOT NULL DEFAULT '0',
 `ItemID_11` INT NOT NULL DEFAULT '0',
 `ItemID_12` INT NOT NULL DEFAULT '0',
 `ItemID_13` INT NOT NULL DEFAULT '0',
 `ItemID_14` INT NOT NULL DEFAULT '0',
 `ItemID_15` INT NOT NULL DEFAULT '0',
 `ItemID_16` INT NOT NULL DEFAULT '0',
 `ItemID_17` INT NOT NULL DEFAULT '0',
 `SetSpellID_1` INT NOT NULL DEFAULT '0',
 `SetSpellID_2` INT NOT NULL DEFAULT '0',
 `SetSpellID_3` INT NOT NULL DEFAULT '0',
 `SetSpellID_4` INT NOT NULL DEFAULT '0',
 `SetSpellID_5` INT NOT NULL DEFAULT '0',
 `SetSpellID_6` INT NOT NULL DEFAULT '0',
 `SetSpellID_7` INT NOT NULL DEFAULT '0',
 `SetSpellID_8` INT NOT NULL DEFAULT '0',
 `SetThreshold_1` INT NOT NULL DEFAULT '0',
 `SetThreshold_2` INT NOT NULL DEFAULT '0',
 `SetThreshold_3` INT NOT NULL DEFAULT '0',
 `SetThreshold_4` INT NOT NULL DEFAULT '0',
 `SetThreshold_5` INT NOT NULL DEFAULT '0',
 `SetThreshold_6` INT NOT NULL DEFAULT '0',
 `SetThreshold_7` INT NOT NULL DEFAULT '0',
 `SetThreshold_8` INT NOT NULL DEFAULT '0',
 `RequiredSkill` INT NOT NULL DEFAULT '0',
 `RequiredSkillRank` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for lfgdungeons_dbc
-- ----------------------------
DROP TABLE IF EXISTS `lfgdungeons_dbc`; 
CREATE TABLE `lfgdungeons_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `MinLevel` INT NOT NULL DEFAULT '0',
 `MaxLevel` INT NOT NULL DEFAULT '0',
 `Target_Level` INT NOT NULL DEFAULT '0',
 `Target_Level_Min` INT NOT NULL DEFAULT '0',
 `Target_Level_Max` INT NOT NULL DEFAULT '0',
 `MapID` INT NOT NULL DEFAULT '0',
 `Difficulty` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `TypeID` INT NOT NULL DEFAULT '0',
 `Faction` INT NOT NULL DEFAULT '0',
 `TextureFilename` varchar(100) NULL,
 `ExpansionLevel` INT NOT NULL DEFAULT '0',
 `Order_Index` INT NOT NULL DEFAULT '0',
 `Group_Id` INT NOT NULL DEFAULT '0',
 `Description_Lang_enUS` varchar(100) NULL,
 `Description_Lang_enGB` varchar(100) NULL,
 `Description_Lang_koKR` varchar(100) NULL,
 `Description_Lang_frFR` varchar(100) NULL,
 `Description_Lang_deDE` varchar(100) NULL,
 `Description_Lang_enCN` varchar(100) NULL,
 `Description_Lang_zhCN` varchar(100) NULL,
 `Description_Lang_enTW` varchar(100) NULL,
 `Description_Lang_zhTW` varchar(100) NULL,
 `Description_Lang_esES` varchar(100) NULL,
 `Description_Lang_esMX` varchar(100) NULL,
 `Description_Lang_ruRU` varchar(100) NULL,
 `Description_Lang_ptPT` varchar(100) NULL,
 `Description_Lang_ptBR` varchar(100) NULL,
 `Description_Lang_itIT` varchar(100) NULL,
 `Description_Lang_Unk` varchar(100) NULL,
 `Description_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for light_dbc
-- ----------------------------
DROP TABLE IF EXISTS `light_dbc`; 
CREATE TABLE `light_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ContinentID` INT NOT NULL DEFAULT '0',
 `X` FLOAT NOT NULL DEFAULT '0',
 `Y` FLOAT NOT NULL DEFAULT '0',
 `Z` FLOAT NOT NULL DEFAULT '0',
 `FalloffStart` FLOAT NOT NULL DEFAULT '0',
 `FalloffEnd` FLOAT NOT NULL DEFAULT '0',
 `LightParamsID_1` INT NOT NULL DEFAULT '0',
 `LightParamsID_2` INT NOT NULL DEFAULT '0',
 `LightParamsID_3` INT NOT NULL DEFAULT '0',
 `LightParamsID_4` INT NOT NULL DEFAULT '0',
 `LightParamsID_5` INT NOT NULL DEFAULT '0',
 `LightParamsID_6` INT NOT NULL DEFAULT '0',
 `LightParamsID_7` INT NOT NULL DEFAULT '0',
 `LightParamsID_8` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for liquidtype_dbc
-- ----------------------------
DROP TABLE IF EXISTS `liquidtype_dbc`; 
CREATE TABLE `liquidtype_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name` varchar(100) NULL,
 `Flags` INT NOT NULL DEFAULT '0',
 `Type` INT NOT NULL DEFAULT '0',
 `SoundID` INT NOT NULL DEFAULT '0',
 `SpellID` INT NOT NULL DEFAULT '0',
 `MaxDarkenDepth` FLOAT NOT NULL DEFAULT '0',
 `FogDarkenintensity` FLOAT NOT NULL DEFAULT '0',
 `AmbDarkenintensity` FLOAT NOT NULL DEFAULT '0',
 `DirDarkenintensity` FLOAT NOT NULL DEFAULT '0',
 `LightID` INT NOT NULL DEFAULT '0',
 `ParticleScale` FLOAT NOT NULL DEFAULT '0',
 `ParticleMovement` INT NOT NULL DEFAULT '0',
 `ParticleTexSlots` INT NOT NULL DEFAULT '0',
 `MaterialID` INT NOT NULL DEFAULT '0',
 `Texture_1` varchar(100) NULL,
 `Texture_2` varchar(100) NULL,
 `Texture_3` varchar(100) NULL,
 `Texture_4` varchar(100) NULL,
 `Texture_5` varchar(100) NULL,
 `Texture_6` varchar(100) NULL,
 `Color_1` INT NOT NULL DEFAULT '0',
 `Color_2` INT NOT NULL DEFAULT '0',
 `Float_1` FLOAT NOT NULL DEFAULT '0',
 `Float_2` FLOAT NOT NULL DEFAULT '0',
 `Float_3` FLOAT NOT NULL DEFAULT '0',
 `Float_4` FLOAT NOT NULL DEFAULT '0',
 `Float_5` FLOAT NOT NULL DEFAULT '0',
 `Float_6` FLOAT NOT NULL DEFAULT '0',
 `Float_7` FLOAT NOT NULL DEFAULT '0',
 `Float_8` FLOAT NOT NULL DEFAULT '0',
 `Float_9` FLOAT NOT NULL DEFAULT '0',
 `Float_10` FLOAT NOT NULL DEFAULT '0',
 `Float_11` FLOAT NOT NULL DEFAULT '0',
 `Float_12` FLOAT NOT NULL DEFAULT '0',
 `Float_13` FLOAT NOT NULL DEFAULT '0',
 `Float_14` FLOAT NOT NULL DEFAULT '0',
 `Float_15` FLOAT NOT NULL DEFAULT '0',
 `Float_16` FLOAT NOT NULL DEFAULT '0',
 `Float_17` FLOAT NOT NULL DEFAULT '0',
 `Float_18` FLOAT NOT NULL DEFAULT '0',
 `Int_1` INT NOT NULL DEFAULT '0',
 `Int_2` INT NOT NULL DEFAULT '0',
 `Int_3` INT NOT NULL DEFAULT '0',
 `Int_4` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for lock_dbc
-- ----------------------------
DROP TABLE IF EXISTS `lock_dbc`; 
CREATE TABLE `lock_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Type_1` INT NOT NULL DEFAULT '0',
 `Type_2` INT NOT NULL DEFAULT '0',
 `Type_3` INT NOT NULL DEFAULT '0',
 `Type_4` INT NOT NULL DEFAULT '0',
 `Type_5` INT NOT NULL DEFAULT '0',
 `Type_6` INT NOT NULL DEFAULT '0',
 `Type_7` INT NOT NULL DEFAULT '0',
 `Type_8` INT NOT NULL DEFAULT '0',
 `Index_1` INT NOT NULL DEFAULT '0',
 `Index_2` INT NOT NULL DEFAULT '0',
 `Index_3` INT NOT NULL DEFAULT '0',
 `Index_4` INT NOT NULL DEFAULT '0',
 `Index_5` INT NOT NULL DEFAULT '0',
 `Index_6` INT NOT NULL DEFAULT '0',
 `Index_7` INT NOT NULL DEFAULT '0',
 `Index_8` INT NOT NULL DEFAULT '0',
 `Skill_1` INT NOT NULL DEFAULT '0',
 `Skill_2` INT NOT NULL DEFAULT '0',
 `Skill_3` INT NOT NULL DEFAULT '0',
 `Skill_4` INT NOT NULL DEFAULT '0',
 `Skill_5` INT NOT NULL DEFAULT '0',
 `Skill_6` INT NOT NULL DEFAULT '0',
 `Skill_7` INT NOT NULL DEFAULT '0',
 `Skill_8` INT NOT NULL DEFAULT '0',
 `Action_1` INT NOT NULL DEFAULT '0',
 `Action_2` INT NOT NULL DEFAULT '0',
 `Action_3` INT NOT NULL DEFAULT '0',
 `Action_4` INT NOT NULL DEFAULT '0',
 `Action_5` INT NOT NULL DEFAULT '0',
 `Action_6` INT NOT NULL DEFAULT '0',
 `Action_7` INT NOT NULL DEFAULT '0',
 `Action_8` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for mailtemplate_dbc
-- ----------------------------
DROP TABLE IF EXISTS `mailtemplate_dbc`; 
CREATE TABLE `mailtemplate_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Subject_Lang_enUS` varchar(100) NULL,
 `Subject_Lang_enGB` varchar(100) NULL,
 `Subject_Lang_koKR` varchar(100) NULL,
 `Subject_Lang_frFR` varchar(100) NULL,
 `Subject_Lang_deDE` varchar(100) NULL,
 `Subject_Lang_enCN` varchar(100) NULL,
 `Subject_Lang_zhCN` varchar(100) NULL,
 `Subject_Lang_enTW` varchar(100) NULL,
 `Subject_Lang_zhTW` varchar(100) NULL,
 `Subject_Lang_esES` varchar(100) NULL,
 `Subject_Lang_esMX` varchar(100) NULL,
 `Subject_Lang_ruRU` varchar(100) NULL,
 `Subject_Lang_ptPT` varchar(100) NULL,
 `Subject_Lang_ptBR` varchar(100) NULL,
 `Subject_Lang_itIT` varchar(100) NULL,
 `Subject_Lang_Unk` varchar(100) NULL,
 `Subject_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Body_Lang_enUS` varchar(100) NULL,
 `Body_Lang_enGB` varchar(100) NULL,
 `Body_Lang_koKR` varchar(100) NULL,
 `Body_Lang_frFR` varchar(100) NULL,
 `Body_Lang_deDE` varchar(100) NULL,
 `Body_Lang_enCN` varchar(100) NULL,
 `Body_Lang_zhCN` varchar(100) NULL,
 `Body_Lang_enTW` varchar(100) NULL,
 `Body_Lang_zhTW` varchar(100) NULL,
 `Body_Lang_esES` varchar(100) NULL,
 `Body_Lang_esMX` varchar(100) NULL,
 `Body_Lang_ruRU` varchar(100) NULL,
 `Body_Lang_ptPT` varchar(100) NULL,
 `Body_Lang_ptBR` varchar(100) NULL,
 `Body_Lang_itIT` varchar(100) NULL,
 `Body_Lang_Unk` varchar(100) NULL,
 `Body_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for map_dbc
-- ----------------------------
DROP TABLE IF EXISTS `map_dbc`; 
CREATE TABLE `map_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Directory` varchar(100) NULL,
 `InstanceType` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `PVP` INT NOT NULL DEFAULT '0',
 `MapName_Lang_enUS` varchar(100) NULL,
 `MapName_Lang_enGB` varchar(100) NULL,
 `MapName_Lang_koKR` varchar(100) NULL,
 `MapName_Lang_frFR` varchar(100) NULL,
 `MapName_Lang_deDE` varchar(100) NULL,
 `MapName_Lang_enCN` varchar(100) NULL,
 `MapName_Lang_zhCN` varchar(100) NULL,
 `MapName_Lang_enTW` varchar(100) NULL,
 `MapName_Lang_zhTW` varchar(100) NULL,
 `MapName_Lang_esES` varchar(100) NULL,
 `MapName_Lang_esMX` varchar(100) NULL,
 `MapName_Lang_ruRU` varchar(100) NULL,
 `MapName_Lang_ptPT` varchar(100) NULL,
 `MapName_Lang_ptBR` varchar(100) NULL,
 `MapName_Lang_itIT` varchar(100) NULL,
 `MapName_Lang_Unk` varchar(100) NULL,
 `MapName_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `AreaTableID` INT NOT NULL DEFAULT '0',
 `MapDescription0_Lang_enUS` varchar(100) NULL,
 `MapDescription0_Lang_enGB` varchar(100) NULL,
 `MapDescription0_Lang_koKR` varchar(100) NULL,
 `MapDescription0_Lang_frFR` varchar(100) NULL,
 `MapDescription0_Lang_deDE` varchar(100) NULL,
 `MapDescription0_Lang_enCN` varchar(100) NULL,
 `MapDescription0_Lang_zhCN` varchar(100) NULL,
 `MapDescription0_Lang_enTW` varchar(100) NULL,
 `MapDescription0_Lang_zhTW` varchar(100) NULL,
 `MapDescription0_Lang_esES` varchar(100) NULL,
 `MapDescription0_Lang_esMX` varchar(100) NULL,
 `MapDescription0_Lang_ruRU` varchar(100) NULL,
 `MapDescription0_Lang_ptPT` varchar(100) NULL,
 `MapDescription0_Lang_ptBR` varchar(100) NULL,
 `MapDescription0_Lang_itIT` varchar(100) NULL,
 `MapDescription0_Lang_Unk` varchar(100) NULL,
 `MapDescription0_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `MapDescription1_Lang_enUS` varchar(100) NULL,
 `MapDescription1_Lang_enGB` varchar(100) NULL,
 `MapDescription1_Lang_koKR` varchar(100) NULL,
 `MapDescription1_Lang_frFR` varchar(100) NULL,
 `MapDescription1_Lang_deDE` varchar(100) NULL,
 `MapDescription1_Lang_enCN` varchar(100) NULL,
 `MapDescription1_Lang_zhCN` varchar(100) NULL,
 `MapDescription1_Lang_enTW` varchar(100) NULL,
 `MapDescription1_Lang_zhTW` varchar(100) NULL,
 `MapDescription1_Lang_esES` varchar(100) NULL,
 `MapDescription1_Lang_esMX` varchar(100) NULL,
 `MapDescription1_Lang_ruRU` varchar(100) NULL,
 `MapDescription1_Lang_ptPT` varchar(100) NULL,
 `MapDescription1_Lang_ptBR` varchar(100) NULL,
 `MapDescription1_Lang_itIT` varchar(100) NULL,
 `MapDescription1_Lang_Unk` varchar(100) NULL,
 `MapDescription1_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `LoadingScreenID` INT NOT NULL DEFAULT '0',
 `MinimapIconScale` FLOAT NOT NULL DEFAULT '0',
 `CorpseMapID` INT NOT NULL DEFAULT '0',
 `CorpseX` FLOAT NOT NULL DEFAULT '0',
 `CorpseY` FLOAT NOT NULL DEFAULT '0',
 `TimeOfDayOverride` INT NOT NULL DEFAULT '0',
 `ExpansionID` INT NOT NULL DEFAULT '0',
 `RaidOffset` INT NOT NULL DEFAULT '0',
 `MaxPlayers` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for mapdifficulty_dbc
-- ----------------------------
DROP TABLE IF EXISTS `mapdifficulty_dbc`; 
CREATE TABLE `mapdifficulty_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `MapID` INT NOT NULL DEFAULT '0',
 `Difficulty` INT NOT NULL DEFAULT '0',
 `Message_Lang_enUS` varchar(100) NULL,
 `Message_Lang_enGB` varchar(100) NULL,
 `Message_Lang_koKR` varchar(100) NULL,
 `Message_Lang_frFR` varchar(100) NULL,
 `Message_Lang_deDE` varchar(100) NULL,
 `Message_Lang_enCN` varchar(100) NULL,
 `Message_Lang_zhCN` varchar(100) NULL,
 `Message_Lang_enTW` varchar(100) NULL,
 `Message_Lang_zhTW` varchar(100) NULL,
 `Message_Lang_esES` varchar(100) NULL,
 `Message_Lang_esMX` varchar(100) NULL,
 `Message_Lang_ruRU` varchar(100) NULL,
 `Message_Lang_ptPT` varchar(100) NULL,
 `Message_Lang_ptBR` varchar(100) NULL,
 `Message_Lang_itIT` varchar(100) NULL,
 `Message_Lang_Unk` varchar(100) NULL,
 `Message_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `RaidDuration` INT NOT NULL DEFAULT '0',
 `MaxPlayers` INT NOT NULL DEFAULT '0',
 `Difficultystring` varchar(100) NULL,
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for movie_dbc
-- ----------------------------
DROP TABLE IF EXISTS `movie_dbc`; 
CREATE TABLE `movie_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Filename` varchar(100) NULL,
 `Volume` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for overridespelldata_dbc
-- ----------------------------
DROP TABLE IF EXISTS `overridespelldata_dbc`; 
CREATE TABLE `overridespelldata_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Spells_1` INT NOT NULL DEFAULT '0',
 `Spells_2` INT NOT NULL DEFAULT '0',
 `Spells_3` INT NOT NULL DEFAULT '0',
 `Spells_4` INT NOT NULL DEFAULT '0',
 `Spells_5` INT NOT NULL DEFAULT '0',
 `Spells_6` INT NOT NULL DEFAULT '0',
 `Spells_7` INT NOT NULL DEFAULT '0',
 `Spells_8` INT NOT NULL DEFAULT '0',
 `Spells_9` INT NOT NULL DEFAULT '0',
 `Spells_10` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for powerdisplay_dbc
-- ----------------------------
DROP TABLE IF EXISTS `powerdisplay_dbc`; 
CREATE TABLE `powerdisplay_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ActualType` INT NOT NULL DEFAULT '0',
 `GlobalstringBaseTag` varchar(100) NULL,
 `Red` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Green` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Blue` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for pvpdifficulty_dbc
-- ----------------------------
DROP TABLE IF EXISTS `pvpdifficulty_dbc`; 
CREATE TABLE `pvpdifficulty_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `MapID` INT NOT NULL DEFAULT '0',
 `RangeIndex` INT NOT NULL DEFAULT '0',
 `MinLevel` INT NOT NULL DEFAULT '0',
 `MaxLevel` INT NOT NULL DEFAULT '0',
 `Difficulty` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for questxp_dbc
-- ----------------------------
DROP TABLE IF EXISTS `questxp_dbc`; 
CREATE TABLE `questxp_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Difficulty_1` INT NOT NULL DEFAULT '0',
 `Difficulty_2` INT NOT NULL DEFAULT '0',
 `Difficulty_3` INT NOT NULL DEFAULT '0',
 `Difficulty_4` INT NOT NULL DEFAULT '0',
 `Difficulty_5` INT NOT NULL DEFAULT '0',
 `Difficulty_6` INT NOT NULL DEFAULT '0',
 `Difficulty_7` INT NOT NULL DEFAULT '0',
 `Difficulty_8` INT NOT NULL DEFAULT '0',
 `Difficulty_9` INT NOT NULL DEFAULT '0',
 `Difficulty_10` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for questfactionreward_dbc
-- ----------------------------
DROP TABLE IF EXISTS `questfactionreward_dbc`; 
CREATE TABLE `questfactionreward_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Difficulty_1` INT NOT NULL DEFAULT '0',
 `Difficulty_2` INT NOT NULL DEFAULT '0',
 `Difficulty_3` INT NOT NULL DEFAULT '0',
 `Difficulty_4` INT NOT NULL DEFAULT '0',
 `Difficulty_5` INT NOT NULL DEFAULT '0',
 `Difficulty_6` INT NOT NULL DEFAULT '0',
 `Difficulty_7` INT NOT NULL DEFAULT '0',
 `Difficulty_8` INT NOT NULL DEFAULT '0',
 `Difficulty_9` INT NOT NULL DEFAULT '0',
 `Difficulty_10` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for questsort_dbc
-- ----------------------------
DROP TABLE IF EXISTS `questsort_dbc`; 
CREATE TABLE `questsort_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `SortName_Lang_enUS` varchar(100) NULL,
 `SortName_Lang_enGB` varchar(100) NULL,
 `SortName_Lang_koKR` varchar(100) NULL,
 `SortName_Lang_frFR` varchar(100) NULL,
 `SortName_Lang_deDE` varchar(100) NULL,
 `SortName_Lang_enCN` varchar(100) NULL,
 `SortName_Lang_zhCN` varchar(100) NULL,
 `SortName_Lang_enTW` varchar(100) NULL,
 `SortName_Lang_zhTW` varchar(100) NULL,
 `SortName_Lang_esES` varchar(100) NULL,
 `SortName_Lang_esMX` varchar(100) NULL,
 `SortName_Lang_ruRU` varchar(100) NULL,
 `SortName_Lang_ptPT` varchar(100) NULL,
 `SortName_Lang_ptBR` varchar(100) NULL,
 `SortName_Lang_itIT` varchar(100) NULL,
 `SortName_Lang_Unk` varchar(100) NULL,
 `SortName_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for randproppoints_dbc
-- ----------------------------
DROP TABLE IF EXISTS `randproppoints_dbc`; 
CREATE TABLE `randproppoints_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Epic_1` INT NOT NULL DEFAULT '0',
 `Epic_2` INT NOT NULL DEFAULT '0',
 `Epic_3` INT NOT NULL DEFAULT '0',
 `Epic_4` INT NOT NULL DEFAULT '0',
 `Epic_5` INT NOT NULL DEFAULT '0',
 `Superior_1` INT NOT NULL DEFAULT '0',
 `Superior_2` INT NOT NULL DEFAULT '0',
 `Superior_3` INT NOT NULL DEFAULT '0',
 `Superior_4` INT NOT NULL DEFAULT '0',
 `Superior_5` INT NOT NULL DEFAULT '0',
 `Good_1` INT NOT NULL DEFAULT '0',
 `Good_2` INT NOT NULL DEFAULT '0',
 `Good_3` INT NOT NULL DEFAULT '0',
 `Good_4` INT NOT NULL DEFAULT '0',
 `Good_5` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for scalingstatdistribution_dbc
-- ----------------------------
DROP TABLE IF EXISTS `scalingstatdistribution_dbc`; 
CREATE TABLE `scalingstatdistribution_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `StatID_1` INT NOT NULL DEFAULT '0',
 `StatID_2` INT NOT NULL DEFAULT '0',
 `StatID_3` INT NOT NULL DEFAULT '0',
 `StatID_4` INT NOT NULL DEFAULT '0',
 `StatID_5` INT NOT NULL DEFAULT '0',
 `StatID_6` INT NOT NULL DEFAULT '0',
 `StatID_7` INT NOT NULL DEFAULT '0',
 `StatID_8` INT NOT NULL DEFAULT '0',
 `StatID_9` INT NOT NULL DEFAULT '0',
 `StatID_10` INT NOT NULL DEFAULT '0',
 `Bonus_1` INT NOT NULL DEFAULT '0',
 `Bonus_2` INT NOT NULL DEFAULT '0',
 `Bonus_3` INT NOT NULL DEFAULT '0',
 `Bonus_4` INT NOT NULL DEFAULT '0',
 `Bonus_5` INT NOT NULL DEFAULT '0',
 `Bonus_6` INT NOT NULL DEFAULT '0',
 `Bonus_7` INT NOT NULL DEFAULT '0',
 `Bonus_8` INT NOT NULL DEFAULT '0',
 `Bonus_9` INT NOT NULL DEFAULT '0',
 `Bonus_10` INT NOT NULL DEFAULT '0',
 `Maxlevel` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for scalingstatvalues_dbc
-- ----------------------------
DROP TABLE IF EXISTS `scalingstatvalues_dbc`; 
CREATE TABLE `scalingstatvalues_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Charlevel` INT NOT NULL DEFAULT '0',
 `ShoulderBudget` INT NOT NULL DEFAULT '0',
 `TrinketBudget` INT NOT NULL DEFAULT '0',
 `WeaponBudget1H` INT NOT NULL DEFAULT '0',
 `RangedBudget` INT NOT NULL DEFAULT '0',
 `ClothShoulderArmor` INT NOT NULL DEFAULT '0',
 `LeatherShoulderArmor` INT NOT NULL DEFAULT '0',
 `MailShoulderArmor` INT NOT NULL DEFAULT '0',
 `PlateShoulderArmor` INT NOT NULL DEFAULT '0',
 `WeaponDPS1H` INT NOT NULL DEFAULT '0',
 `WeaponDPS2H` INT NOT NULL DEFAULT '0',
 `SpellcasterDPS1H` INT NOT NULL DEFAULT '0',
 `SpellcasterDPS2H` INT NOT NULL DEFAULT '0',
 `RangedDPS` INT NOT NULL DEFAULT '0',
 `WandDPS` INT NOT NULL DEFAULT '0',
 `SpellPower` INT NOT NULL DEFAULT '0',
 `PrimaryBudget` INT NOT NULL DEFAULT '0',
 `TertiaryBudget` INT NOT NULL DEFAULT '0',
 `ClothCloakArmor` INT NOT NULL DEFAULT '0',
 `ClothChestArmor` INT NOT NULL DEFAULT '0',
 `LeatherChestArmor` INT NOT NULL DEFAULT '0',
 `MailChestArmor` INT NOT NULL DEFAULT '0',
 `PlateChestArmor` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for skillline_dbc
-- ----------------------------
DROP TABLE IF EXISTS `skillline_dbc`; 
CREATE TABLE `skillline_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `CategoryID` INT NOT NULL DEFAULT '0',
 `SkillCostsID` INT NOT NULL DEFAULT '0',
 `DisplayName_Lang_enUS` varchar(100) NULL,
 `DisplayName_Lang_enGB` varchar(100) NULL,
 `DisplayName_Lang_koKR` varchar(100) NULL,
 `DisplayName_Lang_frFR` varchar(100) NULL,
 `DisplayName_Lang_deDE` varchar(100) NULL,
 `DisplayName_Lang_enCN` varchar(100) NULL,
 `DisplayName_Lang_zhCN` varchar(100) NULL,
 `DisplayName_Lang_enTW` varchar(100) NULL,
 `DisplayName_Lang_zhTW` varchar(100) NULL,
 `DisplayName_Lang_esES` varchar(100) NULL,
 `DisplayName_Lang_esMX` varchar(100) NULL,
 `DisplayName_Lang_ruRU` varchar(100) NULL,
 `DisplayName_Lang_ptPT` varchar(100) NULL,
 `DisplayName_Lang_ptBR` varchar(100) NULL,
 `DisplayName_Lang_itIT` varchar(100) NULL,
 `DisplayName_Lang_Unk` varchar(100) NULL,
 `DisplayName_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Description_Lang_enUS` varchar(100) NULL,
 `Description_Lang_enGB` varchar(100) NULL,
 `Description_Lang_koKR` varchar(100) NULL,
 `Description_Lang_frFR` varchar(100) NULL,
 `Description_Lang_deDE` varchar(100) NULL,
 `Description_Lang_enCN` varchar(100) NULL,
 `Description_Lang_zhCN` varchar(100) NULL,
 `Description_Lang_enTW` varchar(100) NULL,
 `Description_Lang_zhTW` varchar(100) NULL,
 `Description_Lang_esES` varchar(100) NULL,
 `Description_Lang_esMX` varchar(100) NULL,
 `Description_Lang_ruRU` varchar(100) NULL,
 `Description_Lang_ptPT` varchar(100) NULL,
 `Description_Lang_ptBR` varchar(100) NULL,
 `Description_Lang_itIT` varchar(100) NULL,
 `Description_Lang_Unk` varchar(100) NULL,
 `Description_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `SpellIconID` INT NOT NULL DEFAULT '0',
 `AlternateVerb_Lang_enUS` varchar(100) NULL,
 `AlternateVerb_Lang_enGB` varchar(100) NULL,
 `AlternateVerb_Lang_koKR` varchar(100) NULL,
 `AlternateVerb_Lang_frFR` varchar(100) NULL,
 `AlternateVerb_Lang_deDE` varchar(100) NULL,
 `AlternateVerb_Lang_enCN` varchar(100) NULL,
 `AlternateVerb_Lang_zhCN` varchar(100) NULL,
 `AlternateVerb_Lang_enTW` varchar(100) NULL,
 `AlternateVerb_Lang_zhTW` varchar(100) NULL,
 `AlternateVerb_Lang_esES` varchar(100) NULL,
 `AlternateVerb_Lang_esMX` varchar(100) NULL,
 `AlternateVerb_Lang_ruRU` varchar(100) NULL,
 `AlternateVerb_Lang_ptPT` varchar(100) NULL,
 `AlternateVerb_Lang_ptBR` varchar(100) NULL,
 `AlternateVerb_Lang_itIT` varchar(100) NULL,
 `AlternateVerb_Lang_Unk` varchar(100) NULL,
 `AlternateVerb_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `CanLink` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for skilllineability_dbc
-- ----------------------------
DROP TABLE IF EXISTS `skilllineability_dbc`; 
CREATE TABLE `skilllineability_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `SkillLine` INT NOT NULL DEFAULT '0',
 `Spell` INT NOT NULL DEFAULT '0',
 `RaceMask` INT NOT NULL DEFAULT '0',
 `ClassMask` INT NOT NULL DEFAULT '0',
 `MinSkillLineRank` INT NOT NULL DEFAULT '0',
 `SupercededBySpell` INT NOT NULL DEFAULT '0',
 `AcquireMethod` INT NOT NULL DEFAULT '0',
 `TrivialSkillLineRankHigh` INT NOT NULL DEFAULT '0',
 `TrivialSkillLineRankLow` INT NOT NULL DEFAULT '0',
 `CharacterPoints_1` INT NOT NULL DEFAULT '0',
 `CharacterPoints_2` INT NOT NULL DEFAULT '0',
 `TradeSkillCategoryID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for soundentries_dbc
-- ----------------------------
DROP TABLE IF EXISTS `soundentries_dbc`; 
CREATE TABLE `soundentries_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `SoundType` INT NOT NULL DEFAULT '0',
 `Name` varchar(100) NULL,
 `File_1` varchar(100) NULL,
 `File_2` varchar(100) NULL,
 `File_3` varchar(100) NULL,
 `File_4` varchar(100) NULL,
 `File_5` varchar(100) NULL,
 `File_6` varchar(100) NULL,
 `File_7` varchar(100) NULL,
 `File_8` varchar(100) NULL,
 `File_9` varchar(100) NULL,
 `File_10` varchar(100) NULL,
 `Freq_1` INT NOT NULL DEFAULT '0',
 `Freq_2` INT NOT NULL DEFAULT '0',
 `Freq_3` INT NOT NULL DEFAULT '0',
 `Freq_4` INT NOT NULL DEFAULT '0',
 `Freq_5` INT NOT NULL DEFAULT '0',
 `Freq_6` INT NOT NULL DEFAULT '0',
 `Freq_7` INT NOT NULL DEFAULT '0',
 `Freq_8` INT NOT NULL DEFAULT '0',
 `Freq_9` INT NOT NULL DEFAULT '0',
 `Freq_10` INT NOT NULL DEFAULT '0',
 `DirectoryBase` varchar(100) NULL,
 `Volumefloat` FLOAT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `MinDistance` FLOAT NOT NULL DEFAULT '0',
 `DistanceCutoff` FLOAT NOT NULL DEFAULT '0',
 `EAXDef` INT NOT NULL DEFAULT '0',
 `SoundEntriesAdvancedID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for spell_dbc
-- ----------------------------

-- Create temp db table
DROP TABLE IF EXISTS `_spell_dbc`;
CREATE TABLE `_spell_dbc` (
  `Id` int(10) unsigned NOT NULL,
  `Dispel` int(10) unsigned NOT NULL DEFAULT 0,
  `Mechanic` int(10) unsigned NOT NULL DEFAULT 0,
  `Attributes` int(10) unsigned NOT NULL DEFAULT 0,
  `AttributesEx` int(10) unsigned NOT NULL DEFAULT 0,
  `AttributesEx2` int(10) unsigned NOT NULL DEFAULT 0,
  `AttributesEx3` int(10) unsigned NOT NULL DEFAULT 0,
  `AttributesEx4` int(10) unsigned NOT NULL DEFAULT 0,
  `AttributesEx5` int(10) unsigned NOT NULL DEFAULT 0,
  `AttributesEx6` int(10) unsigned NOT NULL DEFAULT 0,
  `AttributesEx7` int(10) unsigned NOT NULL DEFAULT 0,
  `Stances` int(10) unsigned NOT NULL DEFAULT 0,
  `StancesNot` int(10) unsigned NOT NULL DEFAULT 0,
  `Targets` int(10) unsigned NOT NULL DEFAULT 0,
  `CastingTimeIndex` int(10) unsigned NOT NULL DEFAULT 1,
  `AuraInterruptFlags` int(10) unsigned NOT NULL DEFAULT 0,
  `ProcFlags` int(10) unsigned NOT NULL DEFAULT 0,
  `ProcChance` int(10) unsigned NOT NULL DEFAULT 0,
  `ProcCharges` int(10) unsigned NOT NULL DEFAULT 0,
  `MaxLevel` int(10) unsigned NOT NULL DEFAULT 0,
  `BaseLevel` int(10) unsigned NOT NULL DEFAULT 0,
  `SpellLevel` int(10) unsigned NOT NULL DEFAULT 0,
  `DurationIndex` int(10) unsigned NOT NULL DEFAULT 0,
  `RangeIndex` int(10) unsigned NOT NULL DEFAULT 1,
  `StackAmount` int(10) unsigned NOT NULL DEFAULT 0,
  `EquippedItemClass` int(11) NOT NULL DEFAULT -1,
  `EquippedItemSubClassMask` int(11) NOT NULL DEFAULT 0,
  `EquippedItemInventoryTypeMask` int(11) NOT NULL DEFAULT 0,
  `Effect1` int(10) unsigned NOT NULL DEFAULT 0,
  `Effect2` int(10) unsigned NOT NULL DEFAULT 0,
  `Effect3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectDieSides1` int(11) NOT NULL DEFAULT 0,
  `EffectDieSides2` int(11) NOT NULL DEFAULT 0,
  `EffectDieSides3` int(11) NOT NULL DEFAULT 0,
  `EffectRealPointsPerLevel1` float NOT NULL DEFAULT 0,
  `EffectRealPointsPerLevel2` float NOT NULL DEFAULT 0,
  `EffectRealPointsPerLevel3` float NOT NULL DEFAULT 0,
  `EffectBasePoints1` int(11) NOT NULL DEFAULT 0,
  `EffectBasePoints2` int(11) NOT NULL DEFAULT 0,
  `EffectBasePoints3` int(11) NOT NULL DEFAULT 0,
  `EffectMechanic1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectMechanic2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectMechanic3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectImplicitTargetA1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectImplicitTargetA2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectImplicitTargetA3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectImplicitTargetB1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectImplicitTargetB2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectImplicitTargetB3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectRadiusIndex1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectRadiusIndex2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectRadiusIndex3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectApplyAuraName1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectApplyAuraName2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectApplyAuraName3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectAmplitude1` int(11) NOT NULL DEFAULT 0,
  `EffectAmplitude2` int(11) NOT NULL DEFAULT 0,
  `EffectAmplitude3` int(11) NOT NULL DEFAULT 0,
  `EffectMultipleValue1` float NOT NULL DEFAULT 0,
  `EffectMultipleValue2` float NOT NULL DEFAULT 0,
  `EffectMultipleValue3` float NOT NULL DEFAULT 0,
  `EffectItemType1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectItemType2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectItemType3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectMiscValue1` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValue2` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValue3` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValueB1` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValueB2` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValueB3` int(11) NOT NULL DEFAULT 0,
  `EffectTriggerSpell1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectTriggerSpell2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectTriggerSpell3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskA1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskA2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskA3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskB1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskB2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskB3` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskC1` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskC2` int(10) unsigned NOT NULL DEFAULT 0,
  `EffectSpellClassMaskC3` int(10) unsigned NOT NULL DEFAULT 0,
  `MaxTargetLevel` int(10) unsigned NOT NULL DEFAULT 0,
  `SpellFamilyName` int(10) unsigned NOT NULL DEFAULT 0,
  `SpellFamilyFlags1` int(10) unsigned NOT NULL DEFAULT 0,
  `SpellFamilyFlags2` int(10) unsigned NOT NULL DEFAULT 0,
  `SpellFamilyFlags3` int(10) unsigned NOT NULL DEFAULT 0,
  `MaxAffectedTargets` int(10) unsigned NOT NULL DEFAULT 0,
  `DmgClass` int(10) unsigned NOT NULL DEFAULT 0,
  `PreventionType` int(10) unsigned NOT NULL DEFAULT 0,
  `DmgMultiplier1` float NOT NULL DEFAULT 0,
  `DmgMultiplier2` float NOT NULL DEFAULT 0,
  `DmgMultiplier3` float NOT NULL DEFAULT 0,
  `AreaGroupId` int(11) NOT NULL DEFAULT 0,
  `SchoolMask` int(10) unsigned NOT NULL DEFAULT 0,
  `Comment` text NOT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED COMMENT='Custom spell.dbc entries';

-- instert temp data to temp db table
INSERT INTO `_spell_dbc` SELECT * FROM `spell_dbc`;

DROP TABLE IF EXISTS `spell_dbc`;
CREATE TABLE `spell_dbc`  (
  `ID` int(11) NOT NULL DEFAULT 0,
  `Category` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `DispelType` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Mechanic` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Attributes` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AttributesEx` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AttributesExB` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AttributesExC` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AttributesExD` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AttributesExE` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AttributesExF` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AttributesExG` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ShapeshiftMask` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `unk_320_2` int(10) NOT NULL DEFAULT 0,
  `ShapeshiftExclude` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `unk_320_3` int(10) NOT NULL DEFAULT 0,
  `Targets` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `TargetCreatureType` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RequiresSpellFocus` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `FacingCasterFlags` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `CasterAuraState` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `TargetAuraState` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ExcludeCasterAuraState` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ExcludeTargetAuraState` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `CasterAuraSpell` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `TargetAuraSpell` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ExcludeCasterAuraSpell` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ExcludeTargetAuraSpell` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `CastingTimeIndex` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RecoveryTime` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `CategoryRecoveryTime` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `InterruptFlags` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AuraInterruptFlags` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ChannelInterruptFlags` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ProcTypeMask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ProcChance` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ProcCharges` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `MaxLevel` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `BaseLevel` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellLevel` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `DurationIndex` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `PowerType` int(11) NOT NULL DEFAULT 0,
  `ManaCost` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ManaCostPerLevel` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ManaPerSecond` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ManaPerSecondPerLevel` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RangeIndex` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Speed` float NOT NULL DEFAULT 0,
  `ModalNextSpell` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `CumulativeAura` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Totem_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Totem_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Reagent_1` int(11) NOT NULL DEFAULT 0,
  `Reagent_2` int(11) NOT NULL DEFAULT 0,
  `Reagent_3` int(11) NOT NULL DEFAULT 0,
  `Reagent_4` int(11) NOT NULL DEFAULT 0,
  `Reagent_5` int(11) NOT NULL DEFAULT 0,
  `Reagent_6` int(11) NOT NULL DEFAULT 0,
  `Reagent_7` int(11) NOT NULL DEFAULT 0,
  `Reagent_8` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_1` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_2` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_3` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_4` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_5` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_6` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_7` int(11) NOT NULL DEFAULT 0,
  `ReagentCount_8` int(11) NOT NULL DEFAULT 0,
  `EquippedItemClass` int(11) NOT NULL DEFAULT 0,
  `EquippedItemSubclass` int(11) NOT NULL DEFAULT 0,
  `EquippedItemInvTypes` int(11) NOT NULL DEFAULT 0,
  `Effect_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Effect_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Effect_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectDieSides_1` int(11) NOT NULL DEFAULT 0,
  `EffectDieSides_2` int(11) NOT NULL DEFAULT 0,
  `EffectDieSides_3` int(11) NOT NULL DEFAULT 0,
  `EffectRealPointsPerLevel_1` float NOT NULL DEFAULT 0,
  `EffectRealPointsPerLevel_2` float NOT NULL DEFAULT 0,
  `EffectRealPointsPerLevel_3` float NOT NULL DEFAULT 0,
  `EffectBasePoints_1` int(11) NOT NULL DEFAULT 0,
  `EffectBasePoints_2` int(11) NOT NULL DEFAULT 0,
  `EffectBasePoints_3` int(11) NOT NULL DEFAULT 0,
  `EffectMechanic_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectMechanic_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectMechanic_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ImplicitTargetA_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ImplicitTargetA_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ImplicitTargetA_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ImplicitTargetB_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ImplicitTargetB_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ImplicitTargetB_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectRadiusIndex_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectRadiusIndex_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectRadiusIndex_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectAura_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectAura_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectAura_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectAuraPeriod_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectAuraPeriod_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectAuraPeriod_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectMultipleValue_1` float NOT NULL DEFAULT 0,
  `EffectMultipleValue_2` float NOT NULL DEFAULT 0,
  `EffectMultipleValue_3` float NOT NULL DEFAULT 0,
  `EffectChainTargets_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectChainTargets_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectChainTargets_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectItemType_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectItemType_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectItemType_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectMiscValue_1` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValue_2` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValue_3` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValueB_1` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValueB_2` int(11) NOT NULL DEFAULT 0,
  `EffectMiscValueB_3` int(11) NOT NULL DEFAULT 0,
  `EffectTriggerSpell_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectTriggerSpell_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectTriggerSpell_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectPointsPerCombo_1` float NOT NULL DEFAULT 0,
  `EffectPointsPerCombo_2` float NOT NULL DEFAULT 0,
  `EffectPointsPerCombo_3` float NOT NULL DEFAULT 0,
  `EffectSpellClassMaskA_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskA_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskA_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskB_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskB_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskB_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskC_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskC_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectSpellClassMaskC_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellVisualID_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellVisualID_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellIconID` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ActiveIconID` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellPriority` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Name_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Name_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `NameSubtext_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `NameSubtext_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `Description_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Description_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `AuraDescription_Lang_enUS` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_enGB` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_koKR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_frFR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_deDE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_enCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_zhCN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_enTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_zhTW` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_esES` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_esMX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_ruRU` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_ptPT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_ptBR` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_itIT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_Unk` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AuraDescription_Lang_Mask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ManaCostPct` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `StartRecoveryCategory` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `StartRecoveryTime` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `MaxTargetLevel` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellClassSet` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellClassMask_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellClassMask_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellClassMask_3` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `MaxTargets` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `DefenseType` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `PreventionType` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `StanceBarOrder` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `EffectChainAmplitude_1` float NOT NULL DEFAULT 0,
  `EffectChainAmplitude_2` float NOT NULL DEFAULT 0,
  `EffectChainAmplitude_3` float NOT NULL DEFAULT 0,
  `MinFactionID` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `MinReputation` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RequiredAuraVision` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RequiredTotemCategoryID_1` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RequiredTotemCategoryID_2` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RequiredAreasID` int(11) NOT NULL DEFAULT 0,
  `SchoolMask` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `RuneCostID` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellMissileID` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `PowerDisplayID` int(11) NOT NULL DEFAULT 0,
  `Field227` float NOT NULL DEFAULT 0,
  `Field228` float NOT NULL DEFAULT 0,
  `Field229` float NOT NULL DEFAULT 0,
  `SpellDescriptionVariableID` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `SpellDifficultyID` int(10) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Insert data from temp
-- ----------------------------
INSERT INTO `spell_dbc`(
  `ID`, 
  `DispelType`,
  `Mechanic`,
  `Attributes`,
  `AttributesEx`,
  `AttributesExB`,
  `AttributesExC`,
  `AttributesExD`,
  `AttributesExE`,
  `AttributesExF`, 
  `AttributesExG`, 
  `ShapeshiftMask`, 
  `ShapeshiftExclude`, 
  `Targets`, 
  `CastingTimeIndex`, 
  `AuraInterruptFlags`, 
  `ProcTypeMask`, 
  `ProcChance`, 
  `ProcCharges`, 
  `MaxLevel`, 
  `BaseLevel`, 
  `SpellLevel`, 
  `DurationIndex`, 
  `RangeIndex`, 
  `CumulativeAura`, 
  `EquippedItemClass`, 
  `EquippedItemSubclass`, 
  `EquippedItemInvTypes`, 
  `Effect_1`, 
  `Effect_2`,
  `Effect_3`, 
  `EffectDieSides_1`, 
  `EffectDieSides_2`, 
  `EffectDieSides_3`, 
  `EffectRealPointsPerLevel_1`, 
  `EffectRealPointsPerLevel_2`, 
  `EffectRealPointsPerLevel_3`,
  `EffectBasePoints_1`, 
  `EffectBasePoints_2`, 
  `EffectBasePoints_3`, 
  `EffectMechanic_1`, 
  `EffectMechanic_2`, 
  `EffectMechanic_3`, 
  `ImplicitTargetA_1`, 
  `ImplicitTargetA_2`,
  `ImplicitTargetA_3`, 
  `ImplicitTargetB_1`, 
  `ImplicitTargetB_2`, 
  `ImplicitTargetB_3`, 
  `EffectRadiusIndex_1`, 
  `EffectRadiusIndex_2`, 
  `EffectRadiusIndex_3`, 
  `EffectAura_1`,
  `EffectAura_2`, 
  `EffectAura_3`, 
  `EffectAuraPeriod_1`, 
  `EffectAuraPeriod_2`, 
  `EffectAuraPeriod_3`, 
  `EffectMultipleValue_1`,
  `EffectMultipleValue_2`,
  `EffectMultipleValue_3`,
  `EffectItemType_1`,
  `EffectItemType_2`,
  `EffectItemType_3`,
  `EffectMiscValue_1`,
  `EffectMiscValue_2`,
  `EffectMiscValue_3`,
  `EffectMiscValueB_1`,
  `EffectMiscValueB_2`,
  `EffectMiscValueB_3`,
  `EffectTriggerSpell_1`,
  `EffectTriggerSpell_2`,
  `EffectTriggerSpell_3`,
  `EffectSpellClassMaskA_1`,
  `EffectSpellClassMaskA_2`,
  `EffectSpellClassMaskA_3`,
  `EffectSpellClassMaskB_1`,
  `EffectSpellClassMaskB_2`,
  `EffectSpellClassMaskB_3`,
  `EffectSpellClassMaskC_1`,
  `EffectSpellClassMaskC_2`,
  `EffectSpellClassMaskC_3`,
  `MaxTargetLevel`,
  `SpellClassSet`,
  `SpellClassMask_1`,
  `SpellClassMask_2`,
  `SpellClassMask_3`,
  `MaxTargets`,
  `DefenseType`,
  `PreventionType`,
  `EffectChainAmplitude_1`,
  `EffectChainAmplitude_2`,
  `EffectChainAmplitude_3`,
  `RequiredAreasID`,
  `SchoolMask`,
  `Name_Lang_enUS`)
(SELECT 
  `Id`,
  `Dispel`,
  `Mechanic`,
  `Attributes`,
  `AttributesEx`,
  `AttributesEx2`,
  `AttributesEx3`,
  `AttributesEx4`,
  `AttributesEx5`,
  `AttributesEx6`,
  `AttributesEx7`,
  `Stances`,
  `StancesNot`,
  `Targets`,
  `CastingTimeIndex`,
  `AuraInterruptFlags`,
  `ProcFlags`,
  `ProcChance`,
  `ProcCharges`,
  `MaxLevel`,
  `BaseLevel`,
  `SpellLevel`,
  `DurationIndex`,
  `RangeIndex`,
  `StackAmount`,
  `EquippedItemClass`,
  `EquippedItemSubClassMask`,
  `EquippedItemInventoryTypeMask`,
  `Effect1`,
  `Effect2`,
  `Effect3`,
  `EffectDieSides1`,
  `EffectDieSides2`,
  `EffectDieSides3`,
  `EffectRealPointsPerLevel1`,
  `EffectRealPointsPerLevel2`,
  `EffectRealPointsPerLevel3`,
  `EffectBasePoints1`,
  `EffectBasePoints2`,
  `EffectBasePoints3`,
  `EffectMechanic1`,
  `EffectMechanic2`,
  `EffectMechanic3`,
  `EffectImplicitTargetA1`,
  `EffectImplicitTargetA2`,
  `EffectImplicitTargetA3`,
  `EffectImplicitTargetB1`,
  `EffectImplicitTargetB2`,
  `EffectImplicitTargetB3`,
  `EffectRadiusIndex1`,
  `EffectRadiusIndex2`,
  `EffectRadiusIndex3`,
  `EffectApplyAuraName1`,
  `EffectApplyAuraName2`,
  `EffectApplyAuraName3`,
  `EffectAmplitude1`,
  `EffectAmplitude2`,
  `EffectAmplitude3`,
  `EffectMultipleValue1`,
  `EffectMultipleValue2`,
  `EffectMultipleValue3`,
  `EffectItemType1`,
  `EffectItemType2`,
  `EffectItemType3`,
  `EffectMiscValue1`,
  `EffectMiscValue2`,
  `EffectMiscValue3`,
  `EffectMiscValueB1`,
  `EffectMiscValueB2`,
  `EffectMiscValueB3`,
  `EffectTriggerSpell1`,
  `EffectTriggerSpell2`,
  `EffectTriggerSpell3`,
  `EffectSpellClassMaskA1`,
  `EffectSpellClassMaskA2`,
  `EffectSpellClassMaskA3`,
  `EffectSpellClassMaskB1`,
  `EffectSpellClassMaskB2`,
  `EffectSpellClassMaskB3`,
  `EffectSpellClassMaskC1`,
  `EffectSpellClassMaskC2`,
  `EffectSpellClassMaskC3`,
  `MaxTargetLevel`,
  `SpellFamilyName`,
  `SpellFamilyFlags1`,
  `SpellFamilyFlags2`,
  `SpellFamilyFlags3`,
  `MaxAffectedTargets`,
  `DmgClass`,
  `PreventionType`,
  `DmgMultiplier1`,
  `DmgMultiplier2`,
  `DmgMultiplier3`,
  `AreaGroupId`,
  `SchoolMask`,
  `Comment` FROM `_spell_dbc`);

-- detele temp table
DROP TABLE IF EXISTS `_spell_dbc`;

-- ----------------------------
-- Table structure for spellcasttimes_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellcasttimes_dbc`; 
CREATE TABLE `spellcasttimes_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Base` INT NOT NULL DEFAULT '0',
 `PerLevel` INT NOT NULL DEFAULT '0',
 `Minimum` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for spellcategory_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellcategory_dbc`; 
CREATE TABLE `spellcategory_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for spelldifficulty_dbc
-- ----------------------------
-- DROP TABLE IF EXISTS `spelldifficulty_dbc`; 
-- CREATE TABLE `spelldifficulty_dbc` ( `ID` INT NOT NULL DEFAULT '0',
--  `DifficultySpellID_1` INT NOT NULL DEFAULT '0',
--  `DifficultySpellID_2` INT NOT NULL DEFAULT '0',
--  `DifficultySpellID_3` INT NOT NULL DEFAULT '0',
--  `DifficultySpellID_4` INT NOT NULL DEFAULT '0',
--  PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 
ALTER TABLE `spelldifficulty_dbc`
CHANGE COLUMN `id` `ID` int(11) UNSIGNED NOT NULL DEFAULT 0 FIRST,
CHANGE COLUMN `spellid0` `DifficultySpellID_1` int(11) UNSIGNED NOT NULL DEFAULT 0 AFTER `ID`,
CHANGE COLUMN `spellid1` `DifficultySpellID_2` int(11) UNSIGNED NOT NULL DEFAULT 0 AFTER `DifficultySpellID_1`,
CHANGE COLUMN `spellid2` `DifficultySpellID_3` int(11) UNSIGNED NOT NULL DEFAULT 0 AFTER `DifficultySpellID_2`,
CHANGE COLUMN `spellid3` `DifficultySpellID_4` int(11) UNSIGNED NOT NULL DEFAULT 0 AFTER `DifficultySpellID_3`;

-- ----------------------------
-- Table structure for spellduration_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellduration_dbc`; 
CREATE TABLE `spellduration_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Duration` INT NOT NULL DEFAULT '0',
 `DurationPerLevel` INT NOT NULL DEFAULT '0',
 `MaxDuration` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for spellfocusobject_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellfocusobject_dbc`; 
CREATE TABLE `spellfocusobject_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for spellitemenchantment_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellitemenchantment_dbc`; 
CREATE TABLE `spellitemenchantment_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Charges` INT NOT NULL DEFAULT '0',
 `Effect_1` INT NOT NULL DEFAULT '0',
 `Effect_2` INT NOT NULL DEFAULT '0',
 `Effect_3` INT NOT NULL DEFAULT '0',
 `EffectPointsMin_1` INT NOT NULL DEFAULT '0',
 `EffectPointsMin_2` INT NOT NULL DEFAULT '0',
 `EffectPointsMin_3` INT NOT NULL DEFAULT '0',
 `EffectPointsMax_1` INT NOT NULL DEFAULT '0',
 `EffectPointsMax_2` INT NOT NULL DEFAULT '0',
 `EffectPointsMax_3` INT NOT NULL DEFAULT '0',
 `EffectArg_1` INT NOT NULL DEFAULT '0',
 `EffectArg_2` INT NOT NULL DEFAULT '0',
 `EffectArg_3` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `ItemVisual` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `Src_ItemID` INT NOT NULL DEFAULT '0',
 `Condition_Id` INT NOT NULL DEFAULT '0',
 `RequiredSkillID` INT NOT NULL DEFAULT '0',
 `RequiredSkillRank` INT NOT NULL DEFAULT '0',
 `MinLevel` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for spellitemenchantmentcondition_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellitemenchantmentcondition_dbc`; 
CREATE TABLE `spellitemenchantmentcondition_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Lt_OperandType_1` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Lt_OperandType_2` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Lt_OperandType_3` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Lt_OperandType_4` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Lt_OperandType_5` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Lt_Operand_1` INT NOT NULL DEFAULT '0',
 `Lt_Operand_2` INT NOT NULL DEFAULT '0',
 `Lt_Operand_3` INT NOT NULL DEFAULT '0',
 `Lt_Operand_4` INT NOT NULL DEFAULT '0',
 `Lt_Operand_5` INT NOT NULL DEFAULT '0',
 `Operator_1` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Operator_2` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Operator_3` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Operator_4` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Operator_5` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Rt_OperandType_1` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Rt_OperandType_2` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Rt_OperandType_3` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Rt_OperandType_4` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Rt_OperandType_5` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Rt_Operand_1` INT NOT NULL DEFAULT '0',
 `Rt_Operand_2` INT NOT NULL DEFAULT '0',
 `Rt_Operand_3` INT NOT NULL DEFAULT '0',
 `Rt_Operand_4` INT NOT NULL DEFAULT '0',
 `Rt_Operand_5` INT NOT NULL DEFAULT '0',
 `Logic_1` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Logic_2` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Logic_3` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Logic_4` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 `Logic_5` TINYINT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for spellradius_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellradius_dbc`; 
CREATE TABLE `spellradius_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Radius` FLOAT NOT NULL DEFAULT '0',
 `RadiusPerLevel` FLOAT NOT NULL DEFAULT '0',
 `RadiusMax` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for spellrange_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellrange_dbc`; 
CREATE TABLE `spellrange_dbc` (
  `ID` int(11) NOT NULL DEFAULT 0,
  `RangeMin_1` float NOT NULL DEFAULT 0,
  `RangeMin_2` float NOT NULL DEFAULT 0,
  `RangeMax_1` float NOT NULL DEFAULT 0,
  `RangeMax_2` float NOT NULL DEFAULT 0,
  `Flags` int(11) NOT NULL DEFAULT 0,
  `DisplayName_Lang_enUS` text DEFAULT NULL,
  `DisplayName_Lang_enGB` text DEFAULT NULL,
  `DisplayName_Lang_koKR` text DEFAULT NULL,
  `DisplayName_Lang_frFR` text DEFAULT NULL,
  `DisplayName_Lang_deDE` text DEFAULT NULL,
  `DisplayName_Lang_enCN` text DEFAULT NULL,
  `DisplayName_Lang_zhCN` text DEFAULT NULL,
  `DisplayName_Lang_enTW` text DEFAULT NULL,
  `DisplayName_Lang_zhTW` text DEFAULT NULL,
  `DisplayName_Lang_esES` text DEFAULT NULL,
  `DisplayName_Lang_esMX` text DEFAULT NULL,
  `DisplayName_Lang_ruRU` text DEFAULT NULL,
  `DisplayName_Lang_ptPT` text DEFAULT NULL,
  `DisplayName_Lang_ptBR` text DEFAULT NULL,
  `DisplayName_Lang_itIT` text DEFAULT NULL,
  `DisplayName_Lang_Unk` text DEFAULT NULL,
  `DisplayName_Lang_Mask` int(10) unsigned NOT NULL DEFAULT 0,
  `DisplayNameShort_Lang_enUS` text DEFAULT NULL,
  `DisplayNameShort_Lang_enGB` text DEFAULT NULL,
  `DisplayNameShort_Lang_koKR` text DEFAULT NULL,
  `DisplayNameShort_Lang_frFR` text DEFAULT NULL,
  `DisplayNameShort_Lang_deDE` text DEFAULT NULL,
  `DisplayNameShort_Lang_enCN` text DEFAULT NULL,
  `DisplayNameShort_Lang_zhCN` text DEFAULT NULL,
  `DisplayNameShort_Lang_enTW` text DEFAULT NULL,
  `DisplayNameShort_Lang_zhTW` text DEFAULT NULL,
  `DisplayNameShort_Lang_esES` text DEFAULT NULL,
  `DisplayNameShort_Lang_esMX` text DEFAULT NULL,
  `DisplayNameShort_Lang_ruRU` text DEFAULT NULL,
  `DisplayNameShort_Lang_ptPT` text DEFAULT NULL,
  `DisplayNameShort_Lang_ptBR` text DEFAULT NULL,
  `DisplayNameShort_Lang_itIT` text DEFAULT NULL,
  `DisplayNameShort_Lang_Unk` text DEFAULT NULL,
  `DisplayNameShort_Lang_Mask` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for spellrunecost_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellrunecost_dbc`; 
CREATE TABLE `spellrunecost_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Blood` INT NOT NULL DEFAULT '0',
 `Unholy` INT NOT NULL DEFAULT '0',
 `Frost` INT NOT NULL DEFAULT '0',
 `RunicPower` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for spellshapeshiftform_dbc
-- ----------------------------
DROP TABLE IF EXISTS `spellshapeshiftform_dbc`; 
CREATE TABLE `spellshapeshiftform_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `BonusActionBar` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `CreatureType` INT NOT NULL DEFAULT '0',
 `AttackIconID` INT NOT NULL DEFAULT '0',
 `CombatRoundTime` INT NOT NULL DEFAULT '0',
 `CreatureDisplayID_1` INT NOT NULL DEFAULT '0',
 `CreatureDisplayID_2` INT NOT NULL DEFAULT '0',
 `CreatureDisplayID_3` INT NOT NULL DEFAULT '0',
 `CreatureDisplayID_4` INT NOT NULL DEFAULT '0',
 `PresetSpellID_1` INT NOT NULL DEFAULT '0',
 `PresetSpellID_2` INT NOT NULL DEFAULT '0',
 `PresetSpellID_3` INT NOT NULL DEFAULT '0',
 `PresetSpellID_4` INT NOT NULL DEFAULT '0',
 `PresetSpellID_5` INT NOT NULL DEFAULT '0',
 `PresetSpellID_6` INT NOT NULL DEFAULT '0',
 `PresetSpellID_7` INT NOT NULL DEFAULT '0',
 `PresetSpellID_8` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for stableslotprices_dbc
-- ----------------------------
DROP TABLE IF EXISTS `stableslotprices_dbc`; 
CREATE TABLE `stableslotprices_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Cost` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for summonproperties_dbc
-- ----------------------------
DROP TABLE IF EXISTS `summonproperties_dbc`; 
CREATE TABLE `summonproperties_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Control` INT NOT NULL DEFAULT '0',
 `Faction` INT NOT NULL DEFAULT '0',
 `Title` INT NOT NULL DEFAULT '0',
 `Slot` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for talent_dbc
-- ----------------------------
DROP TABLE IF EXISTS `talent_dbc`; 
CREATE TABLE `talent_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `TabID` INT NOT NULL DEFAULT '0',
 `TierID` INT NOT NULL DEFAULT '0',
 `ColumnIndex` INT NOT NULL DEFAULT '0',
 `SpellRank_1` INT NOT NULL DEFAULT '0',
 `SpellRank_2` INT NOT NULL DEFAULT '0',
 `SpellRank_3` INT NOT NULL DEFAULT '0',
 `SpellRank_4` INT NOT NULL DEFAULT '0',
 `SpellRank_5` INT NOT NULL DEFAULT '0',
 `SpellRank_6` INT NOT NULL DEFAULT '0',
 `SpellRank_7` INT NOT NULL DEFAULT '0',
 `SpellRank_8` INT NOT NULL DEFAULT '0',
 `SpellRank_9` INT NOT NULL DEFAULT '0',
 `PrereqTalent_1` INT NOT NULL DEFAULT '0',
 `PrereqTalent_2` INT NOT NULL DEFAULT '0',
 `PrereqTalent_3` INT NOT NULL DEFAULT '0',
 `PrereqRank_1` INT NOT NULL DEFAULT '0',
 `PrereqRank_2` INT NOT NULL DEFAULT '0',
 `PrereqRank_3` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `RequiredSpellID` INT NOT NULL DEFAULT '0',
 `CategoryMask_1` INT NOT NULL DEFAULT '0',
 `CategoryMask_2` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for talenttab_dbc
-- ----------------------------
DROP TABLE IF EXISTS `talenttab_dbc`; 
CREATE TABLE `talenttab_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `SpellIconID` INT NOT NULL DEFAULT '0',
 `RaceMask` INT NOT NULL DEFAULT '0',
 `ClassMask` INT NOT NULL DEFAULT '0',
 `PetTalentMask` INT NOT NULL DEFAULT '0',
 `OrderIndex` INT NOT NULL DEFAULT '0',
 `BackgroundFile` varchar(100) NULL,
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for taxinodes_dbc
-- ----------------------------
DROP TABLE IF EXISTS `taxinodes_dbc`; 
CREATE TABLE `taxinodes_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `ContinentID` INT NOT NULL DEFAULT '0',
 `X` FLOAT NOT NULL DEFAULT '0',
 `Y` FLOAT NOT NULL DEFAULT '0',
 `Z` FLOAT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `MountCreatureID_1` INT NOT NULL DEFAULT '0',
 `MountCreatureID_2` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for taxipath_dbc
-- ----------------------------
DROP TABLE IF EXISTS `taxipath_dbc`; 
CREATE TABLE `taxipath_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `FromTaxiNode` INT NOT NULL DEFAULT '0',
 `ToTaxiNode` INT NOT NULL DEFAULT '0',
 `Cost` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for taxipathnode_dbc
-- ----------------------------
DROP TABLE IF EXISTS `taxipathnode_dbc`; 
CREATE TABLE `taxipathnode_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `PathID` INT NOT NULL DEFAULT '0',
 `NodeIndex` INT NOT NULL DEFAULT '0',
 `ContinentID` INT NOT NULL DEFAULT '0',
 `LocX` FLOAT NOT NULL DEFAULT '0',
 `LocY` FLOAT NOT NULL DEFAULT '0',
 `LocZ` FLOAT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `Delay` INT NOT NULL DEFAULT '0',
 `ArrivalEventID` INT NOT NULL DEFAULT '0',
 `DepartureEventID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for teamcontributionpoints_dbc
-- ----------------------------
DROP TABLE IF EXISTS `teamcontributionpoints_dbc`; 
CREATE TABLE `teamcontributionpoints_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Data` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for totemcategory_dbc
-- ----------------------------
DROP TABLE IF EXISTS `totemcategory_dbc`; 
CREATE TABLE `totemcategory_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Name_Lang_enUS` varchar(100) NULL,
 `Name_Lang_enGB` varchar(100) NULL,
 `Name_Lang_koKR` varchar(100) NULL,
 `Name_Lang_frFR` varchar(100) NULL,
 `Name_Lang_deDE` varchar(100) NULL,
 `Name_Lang_enCN` varchar(100) NULL,
 `Name_Lang_zhCN` varchar(100) NULL,
 `Name_Lang_enTW` varchar(100) NULL,
 `Name_Lang_zhTW` varchar(100) NULL,
 `Name_Lang_esES` varchar(100) NULL,
 `Name_Lang_esMX` varchar(100) NULL,
 `Name_Lang_ruRU` varchar(100) NULL,
 `Name_Lang_ptPT` varchar(100) NULL,
 `Name_Lang_ptBR` varchar(100) NULL,
 `Name_Lang_itIT` varchar(100) NULL,
 `Name_Lang_Unk` varchar(100) NULL,
 `Name_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 `TotemCategoryType` INT NOT NULL DEFAULT '0',
 `TotemCategoryMask` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 
 
-- ----------------------------
-- Table structure for transportanimation_dbc
-- ----------------------------
DROP TABLE IF EXISTS `transportanimation_dbc`; 
CREATE TABLE `transportanimation_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `TransportID` INT NOT NULL DEFAULT '0',
 `TimeIndex` INT NOT NULL DEFAULT '0',
 `PosX` FLOAT NOT NULL DEFAULT '0',
 `PosY` FLOAT NOT NULL DEFAULT '0',
 `PosZ` FLOAT NOT NULL DEFAULT '0',
 `SequenceID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for transportrotation_dbc
-- ----------------------------
DROP TABLE IF EXISTS `transportrotation_dbc`; 
CREATE TABLE `transportrotation_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `GameObjectsID` INT NOT NULL DEFAULT '0',
 `TimeIndex` INT NOT NULL DEFAULT '0',
 `RotX` FLOAT NOT NULL DEFAULT '0',
 `RotY` FLOAT NOT NULL DEFAULT '0',
 `RotZ` FLOAT NOT NULL DEFAULT '0',
 `RotW` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for vehicle_dbc
-- ----------------------------
DROP TABLE IF EXISTS `vehicle_dbc`; 
CREATE TABLE `vehicle_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `TurnSpeed` FLOAT NOT NULL DEFAULT '0',
 `PitchSpeed` FLOAT NOT NULL DEFAULT '0',
 `PitchMin` FLOAT NOT NULL DEFAULT '0',
 `PitchMax` FLOAT NOT NULL DEFAULT '0',
 `SeatID_1` INT NOT NULL DEFAULT '0',
 `SeatID_2` INT NOT NULL DEFAULT '0',
 `SeatID_3` INT NOT NULL DEFAULT '0',
 `SeatID_4` INT NOT NULL DEFAULT '0',
 `SeatID_5` INT NOT NULL DEFAULT '0',
 `SeatID_6` INT NOT NULL DEFAULT '0',
 `SeatID_7` INT NOT NULL DEFAULT '0',
 `SeatID_8` INT NOT NULL DEFAULT '0',
 `MouseLookOffsetPitch` FLOAT NOT NULL DEFAULT '0',
 `CameraFadeDistScalarMin` FLOAT NOT NULL DEFAULT '0',
 `CameraFadeDistScalarMax` FLOAT NOT NULL DEFAULT '0',
 `CameraPitchOffset` FLOAT NOT NULL DEFAULT '0',
 `FacingLimitRight` FLOAT NOT NULL DEFAULT '0',
 `FacingLimitLeft` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtTurnLingering` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtPitchLingering` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtMouseLingering` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtEndOpacity` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtArcSpeed` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtArcRepeat` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtArcWidth` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtImpactRadius_1` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtImpactRadius_2` FLOAT NOT NULL DEFAULT '0',
 `MsslTrgtArcTexture` varchar(100) NULL,
 `MsslTrgtImpactTexture` varchar(100) NULL,
 `MsslTrgtImpactModel_1` varchar(100) NULL,
 `MsslTrgtImpactModel_2` varchar(100) NULL,
 `CameraYawOffset` FLOAT NOT NULL DEFAULT '0',
 `UilocomotionType` INT NOT NULL DEFAULT '0',
 `MsslTrgtImpactTexRadius` FLOAT NOT NULL DEFAULT '0',
 `VehicleUIIndicatorID` INT NOT NULL DEFAULT '0',
 `PowerDisplayID_1` INT NOT NULL DEFAULT '0',
 `PowerDisplayID_2` INT NOT NULL DEFAULT '0',
 `PowerDisplayID_3` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for vehicleseat_dbc
-- ----------------------------
DROP TABLE IF EXISTS `vehicleseat_dbc`; 
CREATE TABLE `vehicleseat_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `AttachmentID` INT NOT NULL DEFAULT '0',
 `AttachmentOffsetX` FLOAT NOT NULL DEFAULT '0',
 `AttachmentOffsetY` FLOAT NOT NULL DEFAULT '0',
 `AttachmentOffsetZ` FLOAT NOT NULL DEFAULT '0',
 `EnterPreDelay` FLOAT NOT NULL DEFAULT '0',
 `EnterSpeed` FLOAT NOT NULL DEFAULT '0',
 `EnterGravity` FLOAT NOT NULL DEFAULT '0',
 `EnterMinDuration` FLOAT NOT NULL DEFAULT '0',
 `EnterMaxDuration` FLOAT NOT NULL DEFAULT '0',
 `EnterMinArcHeight` FLOAT NOT NULL DEFAULT '0',
 `EnterMaxArcHeight` FLOAT NOT NULL DEFAULT '0',
 `EnterAnimStart` INT NOT NULL DEFAULT '0',
 `EnterAnimLoop` INT NOT NULL DEFAULT '0',
 `RideAnimStart` INT NOT NULL DEFAULT '0',
 `RideAnimLoop` INT NOT NULL DEFAULT '0',
 `RideUpperAnimStart` INT NOT NULL DEFAULT '0',
 `RideUpperAnimLoop` INT NOT NULL DEFAULT '0',
 `ExitPreDelay` FLOAT NOT NULL DEFAULT '0',
 `ExitSpeed` FLOAT NOT NULL DEFAULT '0',
 `ExitGravity` FLOAT NOT NULL DEFAULT '0',
 `ExitMinDuration` FLOAT NOT NULL DEFAULT '0',
 `ExitMaxDuration` FLOAT NOT NULL DEFAULT '0',
 `ExitMinArcHeight` FLOAT NOT NULL DEFAULT '0',
 `ExitMaxArcHeight` FLOAT NOT NULL DEFAULT '0',
 `ExitAnimStart` INT NOT NULL DEFAULT '0',
 `ExitAnimLoop` INT NOT NULL DEFAULT '0',
 `ExitAnimEnd` INT NOT NULL DEFAULT '0',
 `PassengerYaw` FLOAT NOT NULL DEFAULT '0',
 `PassengerPitch` FLOAT NOT NULL DEFAULT '0',
 `PassengerRoll` FLOAT NOT NULL DEFAULT '0',
 `PassengerAttachmentID` INT NOT NULL DEFAULT '0',
 `VehicleEnterAnim` INT NOT NULL DEFAULT '0',
 `VehicleExitAnim` INT NOT NULL DEFAULT '0',
 `VehicleRideAnimLoop` INT NOT NULL DEFAULT '0',
 `VehicleEnterAnimBone` INT NOT NULL DEFAULT '0',
 `VehicleExitAnimBone` INT NOT NULL DEFAULT '0',
 `VehicleRideAnimLoopBone` INT NOT NULL DEFAULT '0',
 `VehicleEnterAnimDelay` FLOAT NOT NULL DEFAULT '0',
 `VehicleExitAnimDelay` FLOAT NOT NULL DEFAULT '0',
 `VehicleAbilityDisplay` INT NOT NULL DEFAULT '0',
 `EnterUISoundID` INT NOT NULL DEFAULT '0',
 `ExitUISoundID` INT NOT NULL DEFAULT '0',
 `UiSkin` INT NOT NULL DEFAULT '0',
 `FlagsB` INT NOT NULL DEFAULT '0',
 `CameraEnteringDelay` FLOAT NOT NULL DEFAULT '0',
 `CameraEnteringDuration` FLOAT NOT NULL DEFAULT '0',
 `CameraExitingDelay` FLOAT NOT NULL DEFAULT '0',
 `CameraExitingDuration` FLOAT NOT NULL DEFAULT '0',
 `CameraOffsetX` FLOAT NOT NULL DEFAULT '0',
 `CameraOffsetY` FLOAT NOT NULL DEFAULT '0',
 `CameraOffsetZ` FLOAT NOT NULL DEFAULT '0',
 `CameraPosChaseRate` FLOAT NOT NULL DEFAULT '0',
 `CameraFacingChaseRate` FLOAT NOT NULL DEFAULT '0',
 `CameraEnteringZoom` FLOAT NOT NULL DEFAULT '0',
 `CameraSeatZoomMin` FLOAT NOT NULL DEFAULT '0',
 `CameraSeatZoomMax` FLOAT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for wmoareatable_dbc
-- ----------------------------
DROP TABLE IF EXISTS `wmoareatable_dbc`; 
CREATE TABLE `wmoareatable_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `WMOID` INT NOT NULL DEFAULT '0',
 `NameSetID` INT NOT NULL DEFAULT '0',
 `WMOGroupID` INT NOT NULL DEFAULT '0',
 `SoundProviderPref` INT NOT NULL DEFAULT '0',
 `SoundProviderPrefUnderwater` INT NOT NULL DEFAULT '0',
 `AmbienceID` INT NOT NULL DEFAULT '0',
 `ZoneMusic` INT NOT NULL DEFAULT '0',
 `IntroSound` INT NOT NULL DEFAULT '0',
 `Flags` INT NOT NULL DEFAULT '0',
 `AreaTableID` INT NOT NULL DEFAULT '0',
 `AreaName_Lang_enUS` varchar(100) NULL,
 `AreaName_Lang_enGB` varchar(100) NULL,
 `AreaName_Lang_koKR` varchar(100) NULL,
 `AreaName_Lang_frFR` varchar(100) NULL,
 `AreaName_Lang_deDE` varchar(100) NULL,
 `AreaName_Lang_enCN` varchar(100) NULL,
 `AreaName_Lang_zhCN` varchar(100) NULL,
 `AreaName_Lang_enTW` varchar(100) NULL,
 `AreaName_Lang_zhTW` varchar(100) NULL,
 `AreaName_Lang_esES` varchar(100) NULL,
 `AreaName_Lang_esMX` varchar(100) NULL,
 `AreaName_Lang_ruRU` varchar(100) NULL,
 `AreaName_Lang_ptPT` varchar(100) NULL,
 `AreaName_Lang_ptBR` varchar(100) NULL,
 `AreaName_Lang_itIT` varchar(100) NULL,
 `AreaName_Lang_Unk` varchar(100) NULL,
 `AreaName_Lang_Mask` INT UNSIGNED NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for worldmaparea_dbc
-- ----------------------------
DROP TABLE IF EXISTS `worldmaparea_dbc`; 
CREATE TABLE `worldmaparea_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `MapID` INT NOT NULL DEFAULT '0',
 `AreaID` INT NOT NULL DEFAULT '0',
 `AreaName` varchar(100) NULL,
 `LocLeft` FLOAT NOT NULL DEFAULT '0',
 `LocRight` FLOAT NOT NULL DEFAULT '0',
 `LocTop` FLOAT NOT NULL DEFAULT '0',
 `LocBottom` FLOAT NOT NULL DEFAULT '0',
 `DisplayMapID` INT NOT NULL DEFAULT '0',
 `DefaultDungeonFloor` INT NOT NULL DEFAULT '0',
 `ParentWorldMapID` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 

-- ----------------------------
-- Table structure for worldmapoverlay_dbc 
-- ----------------------------
DROP TABLE IF EXISTS `worldmapoverlay_dbc`; 
CREATE TABLE `worldmapoverlay_dbc` ( `ID` INT NOT NULL DEFAULT '0',
 `MapAreaID` INT NOT NULL DEFAULT '0',
 `AreaID_1` INT NOT NULL DEFAULT '0',
 `AreaID_2` INT NOT NULL DEFAULT '0',
 `AreaID_3` INT NOT NULL DEFAULT '0',
 `AreaID_4` INT NOT NULL DEFAULT '0',
 `MapPointX` INT NOT NULL DEFAULT '0',
 `MapPointY` INT NOT NULL DEFAULT '0',
 `TextureName` varchar(100) NULL,
 `TextureWidth` INT NOT NULL DEFAULT '0',
 `TextureHeight` INT NOT NULL DEFAULT '0',
 `OffsetX` INT NOT NULL DEFAULT '0',
 `OffsetY` INT NOT NULL DEFAULT '0',
 `HitRectTop` INT NOT NULL DEFAULT '0',
 `HitRectLeft` INT NOT NULL DEFAULT '0',
 `HitRectBottom` INT NOT NULL DEFAULT '0',
 `HitRectRight` INT NOT NULL DEFAULT '0',
 PRIMARY KEY (`ID`)) ENGINE=MyISAM DEFAULT CHARSET=utf8; 
--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
