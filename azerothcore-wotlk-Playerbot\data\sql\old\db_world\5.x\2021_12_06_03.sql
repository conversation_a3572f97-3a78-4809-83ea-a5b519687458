-- DB update 2021_12_06_02 -> 2021_12_06_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_06_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_06_02 2021_12_06_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1638546663868479700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1638546663868479700');

-- @@@ Copper clean up @@@
-- Delete updated pooling
SET @FREE_POOL_TEMPLATE = 11660;
SET @FREE_GAMEOBJECT = 93000;
DELETE FROM `pool_template` WHERE `entry` BETWEEN @FREE_POOL_TEMPLATE AND @FREE_POOL_TEMPLATE + 17;
DELETE FROM `pool_gameobject` WHERE `pool_entry`BETWEEN @FREE_POOL_TEMPLATE AND @FREE_POOL_TEMPLATE + 17;

-- Queries to restore old copper nodes
DELETE FROM `gameobject` where `id` IN (1731, 2055, 3763, 103713, 181248);
DELETE `pool_template` FROM `pool_template` WHERE `entry` IN (2009, 2008, 4400, 4401, 4402, 4403, 4404, 4405, 4406, 4407, 4408, 4409, 4410, 4411, 4412, 4413, 4415, 4416, 4417, 4418, 4419, 4420, 4421, 4422, 4423, 4424, 4425, 4426, 4427, 4428, 4429, 4430, 4431, 4432, 4433, 4434, 4435, 4436, 4437, 4438, 4439, 4440, 4441, 4442, 4443, 4444, 4445, 4446, 4447, 4448, 4449, 4450, 4451, 4452, 4453, 4454, 4455, 4456, 4457, 4458, 4459, 4460, 4461, 4462, 4463, 4464, 4465, 4466, 4467, 4468, 4469, 4470, 4471, 4472, 4473, 4474, 4475, 4476, 4477, 4478, 4479, 4480, 4481, 4482, 4483, 4484, 4485, 4487, 4488, 4491, 4492, 4493, 4494, 4496, 4497, 4499, 4500, 4501, 4502, 4503, 4504, 4505, 4506, 4509, 4510, 4511, 4512, 2011, 11644, 4700, 4701, 4702, 4703, 4704, 4705, 4706, 4707, 4708, 4709, 4710, 4711, 4712, 4713, 4714, 4715, 4716, 4717, 4718, 4719, 4720, 4721, 4722, 4723, 4724, 4725, 4726, 4727, 4728, 4729, 4730, 4731, 4732, 4733, 4734, 4735, 4736, 4737, 4738, 4739, 4750, 4751, 4752, 4753, 4754, 4755, 4756, 4757, 4758, 4759, 4760, 4761, 4762, 4764, 4765, 4766, 4767, 4768, 4770, 4771, 4772, 4773, 4774, 4775, 4776, 4777, 4778, 4779, 4780, 4781, 4782, 4783, 4784, 4785, 4786, 4787, 4788, 4789, 4790, 4791, 4792, 4793, 4794, 4795, 4796, 4797, 4798, 4799, 4800, 4801, 4802, 4803, 4804, 4805, 4806, 4807, 4808, 4809, 4810, 4811, 4812, 4813, 4814, 4815, 4816, 4817, 4818, 4880, 4881, 4882, 4883, 4884, 4885, 4886, 4887, 4888, 4889, 4890, 4891, 4892, 4893, 4894, 4895, 4896, 4897, 4898, 4899, 4900, 4901, 4902, 4903, 4904, 4905, 4906, 4907, 4908, 4909, 4910, 4911, 4912, 4913, 4914, 4915, 4916, 4917, 4918, 4919, 4920, 4921, 4922, 4923, 4924, 4925, 4926, 4927, 4928, 4929, 4930, 4931, 4932, 4933, 4935, 4936, 4937, 4938, 4939, 4940, 4941, 4942, 4943, 4944, 4945, 4946, 4947, 4948, 4949, 4950, 4951, 4952, 4953, 4954, 4955, 4956, 4957, 4958, 4959, 4960, 4961, 4962, 4963, 4964, 4965, 4966, 4967, 4968, 4969, 4970, 4971, 4972, 4973, 4974, 4975, 4976, 4977, 4978, 4979, 4980, 4981, 4982, 4983, 4984, 4985, 4986, 4987, 4988, 4989, 4990, 4992, 11643, 11646);
DELETE `pool_gameobject` FROM `pool_gameobject` WHERE `pool_entry` IN (2008, 4400, 4401, 4402, 4403, 4404, 4405, 4406, 4407, 4408, 4409, 4410, 4411, 4412, 4413, 4415, 4416, 4417, 4418, 4419, 4420, 4421, 4422, 4423, 4424, 4425, 4426, 4427, 4428, 4429, 4430, 4431, 4432, 4433, 4434, 4435, 4436, 4437, 4438, 4439, 4440, 4441, 4442, 4443, 4444, 4445, 4446, 4447, 4448, 4449, 4450, 4451, 4452, 4453, 4454, 4455, 4456, 4457, 4458, 4459, 4460, 4461, 4462, 4463, 4464, 4465, 4466, 4467, 4468, 4469, 4470, 4471, 4472, 4473, 4474, 4475, 4476, 4477, 4478, 4479, 4480, 4481, 4482, 4483, 4484, 4485, 4487, 4488, 4491, 4492, 4493, 4494, 4496, 4497, 4499, 4500, 4501, 4502, 4503, 4504, 4505, 4506, 4509, 4510, 4511, 4512, 2011, 11644, 4700, 4701, 4702, 4703, 4704, 4705, 4706, 4707, 4708, 4709, 4710, 4711, 4712, 4713, 4714, 4715, 4716, 4717, 4718, 4719, 4720, 4721, 4722, 4723, 4724, 4725, 4726, 4727, 4728, 4729, 4730, 4731, 4732, 4733, 4734, 4735, 4736, 4737, 4738, 4739, 4750, 4751, 4752, 4753, 4754, 4755, 4756, 4757, 4758, 4759, 4760, 4761, 4762, 4764, 4765, 4766, 4767, 4768, 4770, 4771, 4772, 4773, 4774, 4775, 4776, 4777, 4778, 4779, 4780, 4781, 4782, 4783, 4784, 4785, 4786, 4787, 4788, 4789, 4790, 4791, 4792, 4793, 4794, 4795, 4796, 4797, 4798, 4799, 4800, 4801, 4802, 4803, 4804, 4805, 4806, 4807, 4808, 4809, 4810, 4811, 4812, 4813, 4814, 4815, 4816, 4817, 4818, 4880, 4881, 4882, 4883, 4884, 4885, 4886, 4887, 4888, 4889, 4890, 4891, 4892, 4893, 4894, 4895, 4896, 4897, 4898, 4899, 4900, 4901, 4902, 4903, 4904, 4905, 4906, 4907, 4908, 4909, 4910, 4911, 4912, 4913, 4914, 4915, 4916, 4917, 4918, 4919, 4920, 4921, 4922, 4923, 4924, 4925, 4926, 4927, 4928, 4929, 4930, 4931, 4932, 4933, 4935, 4936, 4937, 4938, 4939, 4940, 4941, 4942, 4943, 4944, 4945, 4946, 4947, 4948, 4949, 4950, 4951, 4952, 4953, 4954, 4955, 4956, 4957, 4958, 4959, 4960, 4961, 4962, 4963, 4964, 4965, 4966, 4967, 4968, 4969, 4970, 4971, 4972, 4973, 4974, 4975, 4976, 4977, 4978, 4979, 4980, 4981, 4982, 4983, 4984, 4985, 4986, 4987, 4988, 4989, 4990, 4992, 11643, 11646);

-- @@@ Duskwood clean up @@@
-- Delete updated pooling
SET @FREE_POOL_TEMPLATE_ENTRY = 500;
SET @FREE_GAMEOBJECT_ENTRY = 10230;
DELETE FROM `gameobject` WHERE `guid` >= @FREE_GAMEOBJECT_ENTRY AND `guid` <= @FREE_GAMEOBJECT_ENTRY + 57;
DELETE FROM `pool_gameobject` WHERE `pool_entry` >= @FREE_POOL_TEMPLATE_ENTRY AND `pool_entry` <= @FREE_POOL_TEMPLATE_ENTRY + 3;
DELETE FROM `pool_template` WHERE `entry` >= @FREE_POOL_TEMPLATE_ENTRY AND `entry` <= @FREE_POOL_TEMPLATE_ENTRY + 3;

-- Queries to restore old duskwood nodes
DELETE `gameobject` FROM `gameobject` WHERE `guid` IN(73600, 73601, 73602, 73603, 73604, 73605, 73606, 73607, 73608, 73609, 73610, 73611, 73612, 73613, 73614, 73615, 73616, 73617, 73618, 73619, 73620, 73621, 73622, 73623, 73624, 73625, 73626, 73627, 73628, 73629, 73630, 73631, 73632, 73633, 73634, 73635, 73636, 73637, 73638, 73639, 73640, 73641, 73642, 73643, 73644, 73645, 73646, 73647, 73648, 73649, 73655, 73656, 73657, 73658, 73659, 73660, 73661, 73662, 73663, 73664, 73665, 73666, 73667, 73668, 73669, 73675, 73676, 73677, 73678, 73679, 73680, 73681, 73682, 73683, 73684, 73685, 73686, 73687, 73688, 73689, 73690, 73691, 73692, 73693, 73694, 73695, 73696, 73697, 73698, 73699, 73700, 73701, 73702, 73703, 73704, 73705, 73706, 73707, 73708, 73709, 73710, 73711, 73712, 73713, 73714, 73715, 73716, 73717, 73718, 73719, 73720, 73721, 73722, 73723, 73724, 73725, 73726, 73727, 73728, 73729, 73730, 73731, 73732, 73733, 73734, 73735, 73736, 73737, 73738, 73739, 73740, 73741, 73742, 73743, 73744, 73745, 73746, 73747, 73748, 73749, 73750, 73751, 73752, 73753, 73754, 73760, 73761, 73762, 73763, 73764, 73765, 73766, 73767, 73768, 73769, 73770, 73771, 73772, 73773, 73774, 73775, 73776, 73777, 73778, 73779, 73780, 73781, 73782, 73783, 73784, 73785, 73786, 73787, 73788, 73789, 73790, 73791, 73792, 73793, 73794, 73795, 73796, 73797, 73798, 73799, 73800, 73801, 73802, 73803, 73804, 73805, 73806, 73807, 73808, 73809, 73810, 73811, 73812, 73813, 73814, 73815, 73816, 73817, 73818, 73819, 73820, 73821, 73822, 73823, 73824, 73825, 73826, 73827, 73828, 73829, 73830, 73831, 73832, 73833, 73834, 73835, 73836, 73837, 73838, 73839, 73840, 73841, 73842, 73843, 73844, 73845, 73846, 73847, 73848, 73849, 73850, 73851, 73852, 73853, 73854, 73855, 73856, 73857, 73858, 73859, 73860, 73861, 73862, 73863, 73864, 73865, 73866, 73867, 73868, 73869, 73875, 73876, 73877, 73878, 73879, 73880, 73881, 73882, 73883, 73884, 73885, 73886, 73887, 73888, 73889, 73890, 73891, 73892, 73893, 73894, 73895, 73896, 73897, 73898, 73899, 73900, 73901, 73902, 73903, 73904, 73905, 73906, 73907, 73908, 73909, 73910, 73911, 73912, 73913, 73914, 73915, 73916, 73917, 73918, 73919, 73920, 73921, 73922, 73923, 73924, 73925, 73926, 73927, 73928, 73929, 73930, 73931, 73932, 73933, 73934, 73935, 73936, 73937, 73938, 73939, 73945, 73946, 73947, 73948, 73949, 73950, 73951, 73952, 73953, 73954, 73955, 73956, 73957, 73958, 73959, 73960, 73961, 73962, 73963, 73964, 73965, 73966, 73967, 73968, 73969, 73970, 73971, 73972, 73973, 73974, 73975, 73976, 73977, 73978, 73979, 73980, 73981, 73982, 73983, 73984, 73985, 73986, 73987, 73988, 73989, 73990, 73991, 73992, 73993, 73994, 73995, 73996, 73997, 73998, 73999, 74000, 74001, 74002, 74003, 74004, 74005, 74006, 74007, 74008, 74009, 74010, 74011, 74012, 74013, 74014, 74015, 74016, 74017, 74018, 74019, 74020, 74021, 74022, 74023, 74024, 74025, 74026, 74027, 74028, 74029, 74035, 74036, 74037, 74038, 74039, 74040, 74041, 74042, 74043, 74044, 74055, 74056, 74057, 74058, 74059, 74060, 74061, 74062, 74063, 74064, 74065, 74066, 74067, 74068, 74069, 74070, 74071, 74072, 74073, 74074, 74085, 74086, 74087, 74088, 74089, 74095, 74096, 74097, 74098, 74099, 74100, 74101, 74102, 74103, 74104, 74105, 74106, 74107, 74108, 74109, 74110, 74111, 74112, 74113, 74114, 74115, 74116, 74117, 74118, 74119, 74120, 74121, 74122, 74123, 74124, 74130, 74131, 74132, 74133, 74134, 74145, 74146, 74147, 74148, 74149, 74155, 74156, 74157, 74158, 74159, 74160, 74161, 74162, 74163, 74164);
DELETE `pool_template` FROM `pool_template` WHERE `entry` IN (4468, 4508, 4400, 4401, 4402, 4403, 4404, 4405, 4406, 4407, 4408, 4409, 4411, 4412, 4413, 4415, 4416, 4417, 4418, 4419, 4420, 4421, 4422, 4423, 4424, 4425, 4426, 4427, 4428, 4429, 4430, 4432, 4433, 4434, 4435, 4436, 4437, 4438, 4439, 4440, 4441, 4442, 4443, 4444, 4445, 4446, 4447, 4448, 4449, 4450, 4451, 4452, 4453, 4455, 4456, 4457, 4458, 4459, 4460, 4461, 4462, 4463, 4464, 4465, 4466, 4467, 4469, 4470, 4471, 4472, 4473, 4474, 4475, 4476, 4477, 4478, 4479, 4480, 4481, 4482, 4483, 4484, 4485, 4486, 4487, 4488, 4489, 4490, 4491, 4492, 4493, 4494, 4495, 4497, 4498, 4499, 4500, 4501, 4502, 4503, 4504, 4506, 4507, 4509, 4511, 4512); 
DELETE `pool_gameobject` FROM `pool_gameobject` WHERE `pool_entry` IN (4400, 4401, 4402, 4403, 4404, 4405, 4406, 4407, 4408, 4409, 4411, 4412, 4413, 4415, 4416, 4417, 4418, 4419, 4420, 4421, 4422, 4423, 4424, 4425, 4426, 4427, 4428, 4429, 4430, 4432, 4433, 4434, 4435, 4436, 4437, 4438, 4439, 4440, 4441, 4442, 4443, 4444, 4445, 4446, 4447, 4448, 4449, 4450, 4451, 4452, 4453, 4455, 4456, 4457, 4458, 4459, 4460, 4461, 4462, 4463, 4464, 4465, 4466, 4467, 4469, 4470, 4471, 4472, 4473, 4474, 4475, 4476, 4477, 4478, 4479, 4480, 4481, 4482, 4483, 4484, 4485, 4486, 4487, 4488, 4489, 4490, 4491, 4492, 4493, 4494, 4495, 4497, 4498, 4499, 4500, 4501, 4502, 4503, 4504, 4506, 4507, 4509, 4511, 4512); 
DELETE `pool_pool` FROM `pool_pool` WHERE (`mother_pool` = 2009) OR (`pool_id` IN (4400,4401,4402,4403,4404,4405,4406,4407,4408,4409,4411,4412,4413,4415,4416,4417,4418,4419,4420,4421,4422,4423,4424,4425,4426,4427,4428,4429,4430,4468,4432,4433,4434,4435,4436,4437,4438,4439,4440,4441,4442,4443,4444,4445,4446,4447,4448,4449,4450,4451,4452,4453,4455,4456,4457,4458,4459,4460,4461,4462,4463,4464,4465,4466,4467,4454,4469,4470,4471,4472,4473,4474,4475,4476,4477,4478,4479,4480,4481,4482,4483,4484,4485,4486,4487,4488,4489,4490,4491,4492,4493,4494,4495,4497,4498,4499,4500,4501,4502,4503,4504,4431,4506,4507,4509,4410,4511,4512,4496,4505,4508,4510));

-- Recovering nodes
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES 
(2008, 15, 'Master Mineral Pool - Dun Morogh'),
(2011, 32, 'Master Mineral Pool - Elwynn Forest'),
(11644, 30, 'Copper Vein - Eversong Woods'),
(4700, 1, 'Spawn Point 1 - Ghostlands'),
(4701, 1, 'Spawn Point 2 - Ghostlands'),
(4702, 1, 'Spawn Point 3 - Ghostlands'),
(4703, 1, 'Spawn Point 4 - Ghostlands'),
(4704, 1, 'Spawn Point 5 - Ghostlands'),
(4705, 1, 'Spawn Point 6 - Ghostlands'),
(4706, 1, 'Spawn Point 7 - Ghostlands'),
(4707, 1, 'Spawn Point 8 - Ghostlands'),
(4708, 1, 'Spawn Point 9 - Ghostlands'),
(4709, 1, 'Spawn Point 10 - Ghostlands'),
(4710, 1, 'Spawn Point 11 - Ghostlands'),
(4711, 1, 'Spawn Point 12 - Ghostlands'),
(4712, 1, 'Spawn Point 13 - Ghostlands'),
(4713, 1, 'Spawn Point 14 - Ghostlands'),
(4714, 1, 'Spawn Point 15 - Ghostlands'),
(4715, 1, 'Spawn Point 16 - Ghostlands'),
(4716, 1, 'Spawn Point 17 - Ghostlands'),
(4717, 1, 'Spawn Point 18 - Ghostlands'),
(4718, 1, 'Spawn Point 19 - Ghostlands'),
(4719, 1, 'Spawn Point 20 - Ghostlands'),
(4720, 1, 'Spawn Point 21 - Ghostlands'),
(4721, 1, 'Spawn Point 22 - Ghostlands'),
(4722, 1, 'Spawn Point 23 - Ghostlands'),
(4723, 1, 'Spawn Point 24 - Ghostlands'),
(4724, 1, 'Spawn Point 25 - Ghostlands'),
(4725, 1, 'Spawn Point 26 - Ghostlands'),
(4726, 1, 'Spawn Point 27 - Ghostlands'),
(4727, 1, 'Spawn Point 28 - Ghostlands'),
(4728, 1, 'Spawn Point 29 - Ghostlands'),
(4729, 1, 'Spawn Point 30 - Ghostlands'),
(4730, 1, 'Spawn Point 31 - Ghostlands'),
(4731, 1, 'Spawn Point 32 - Ghostlands'),
(4732, 1, 'Spawn Point 33 - Ghostlands'),
(4733, 1, 'Spawn Point 34 - Ghostlands'),
(4734, 1, 'Spawn Point 35 - Ghostlands'),
(4735, 1, 'Spawn Point 36 - Ghostlands'),
(4736, 1, 'Spawn Point 37 - Ghostlands'),
(4737, 1, 'Spawn Point 38 - Ghostlands'),
(4738, 1, 'Spawn Point 39 - Ghostlands'),
(4739, 1, 'Spawn Point 40 - Ghostlands'),
(4750, 1, 'Spawn Point 1 - Hillsbrad Foothills'),
(4751, 1, 'Spawn Point 2 - Hillsbrad Foothills'),
(4752, 1, 'Spawn Point 3 - Hillsbrad Foothills'),
(4753, 1, 'Spawn Point 4 - Hillsbrad Foothills'),
(4754, 1, 'Spawn Point 5 - Hillsbrad Foothills'),
(4755, 1, 'Spawn Point 6 - Hillsbrad Foothills'),
(4756, 1, 'Spawn Point 7 - Hillsbrad Foothills'),
(4757, 1, 'Spawn Point 8 - Hillsbrad Foothills'),
(4758, 1, 'Spawn Point 9 - Hillsbrad Foothills'),
(4759, 1, 'Spawn Point 10 - Hillsbrad Foothills'),
(4760, 1, 'Spawn Point 11 - Hillsbrad Foothills'),
(4761, 1, 'Spawn Point 12 - Hillsbrad Foothills'),
(4762, 1, 'Spawn Point 13 - Hillsbrad Foothills'),
(4764, 1, 'Spawn Point 15 - Hillsbrad Foothills'),
(4765, 1, 'Spawn Point 16 - Hillsbrad Foothills'),
(4766, 1, 'Spawn Point 17 - Hillsbrad Foothills'),
(4767, 1, 'Spawn Point 18 - Hillsbrad Foothills'),
(4768, 1, 'Spawn Point 19 - Hillsbrad Foothills'),
(4770, 1, 'Spawn Point 21 - Hillsbrad Foothills'),
(4771, 1, 'Spawn Point 22 - Hillsbrad Foothills'),
(4772, 1, 'Spawn Point 23 - Hillsbrad Foothills'),
(4773, 1, 'Spawn Point 24 - Hillsbrad Foothills'),
(4774, 1, 'Spawn Point 25 - Hillsbrad Foothills'),
(4775, 1, 'Spawn Point 26 - Hillsbrad Foothills'),
(4776, 1, 'Spawn Point 27 - Hillsbrad Foothills'),
(4777, 1, 'Spawn Point 28 - Hillsbrad Foothills'),
(4778, 1, 'Spawn Point 29 - Hillsbrad Foothills'),
(4779, 1, 'Spawn Point 30 - Hillsbrad Foothills'),
(4780, 1, 'Spawn Point 31 - Hillsbrad Foothills'),
(4781, 1, 'Spawn Point 32 - Hillsbrad Foothills'),
(4782, 1, 'Spawn Point 33 - Hillsbrad Foothills'),
(4783, 1, 'Spawn Point 34 - Hillsbrad Foothills'),
(4784, 1, 'Spawn Point 35 - Hillsbrad Foothills'),
(4785, 1, 'Spawn Point 36 - Hillsbrad Foothills'),
(4786, 1, 'Spawn Point 37 - Hillsbrad Foothills'),
(4787, 1, 'Spawn Point 38 - Hillsbrad Foothills'),
(4788, 1, 'Spawn Point 39 - Hillsbrad Foothills'),
(4789, 1, 'Spawn Point 40 - Hillsbrad Foothills'),
(4790, 1, 'Spawn Point 41 - Hillsbrad Foothills'),
(4791, 1, 'Spawn Point 42 - Hillsbrad Foothills'),
(4792, 1, 'Spawn Point 43 - Hillsbrad Foothills'),
(4793, 1, 'Spawn Point 44 - Hillsbrad Foothills'),
(4794, 1, 'Spawn Point 45 - Hillsbrad Foothills'),
(4795, 1, 'Spawn Point 46 - Hillsbrad Foothills'),
(4796, 1, 'Spawn Point 47 - Hillsbrad Foothills'),
(4797, 1, 'Spawn Point 48 - Hillsbrad Foothills'),
(4798, 1, 'Spawn Point 49 - Hillsbrad Foothills'),
(4799, 1, 'Spawn Point 50 - Hillsbrad Foothills'),
(4800, 1, 'Spawn Point 51 - Hillsbrad Foothills'),
(4801, 1, 'Spawn Point 52 - Hillsbrad Foothills'),
(4802, 1, 'Spawn Point 53 - Hillsbrad Foothills'),
(4803, 1, 'Spawn Point 54 - Hillsbrad Foothills'),
(4804, 1, 'Spawn Point 55 - Hillsbrad Foothills'),
(4805, 1, 'Spawn Point 56 - Hillsbrad Foothills'),
(4806, 1, 'Spawn Point 57 - Hillsbrad Foothills'),
(4807, 1, 'Spawn Point 58 - Hillsbrad Foothills'),
(4808, 1, 'Spawn Point 59 - Hillsbrad Foothills'),
(4809, 1, 'Spawn Point 60 - Hillsbrad Foothills'),
(4810, 1, 'Spawn Point 61 - Hillsbrad Foothills'),
(4811, 1, 'Spawn Point 62 - Hillsbrad Foothills'),
(4812, 1, 'Spawn Point 63 - Hillsbrad Foothills'),
(4813, 1, 'Spawn Point 64 - Hillsbrad Foothills'),
(4814, 1, 'Spawn Point 65 - Hillsbrad Foothills'),
(4815, 1, 'Spawn Point 66 - Hillsbrad Foothills'),
(4816, 1, 'Spawn Point 67 - Hillsbrad Foothills'),
(4817, 1, 'Spawn Point 68 - Hillsbrad Foothills'),
(4818, 1, 'Spawn Point 69 - Hillsbrad Foothills'),
(4880, 1, 'Spawn Point 1 - Loch Modan'),
(4881, 1, 'Spawn Point 2 - Loch Modan'),
(4882, 1, 'Spawn Point 3 - Loch Modan'),
(4883, 1, 'Spawn Point 4 - Loch Modan'),
(4884, 1, 'Spawn Point 5 - Loch Modan'),
(4885, 1, 'Spawn Point 6 - Loch Modan'),
(4886, 1, 'Spawn Point 7 - Loch Modan'),
(4887, 1, 'Spawn Point 8 - Loch Modan'),
(4888, 1, 'Spawn Point 9 - Loch Modan'),
(4889, 1, 'Spawn Point 10 - Loch Modan'),
(4890, 1, 'Spawn Point 11 - Loch Modan'),
(4891, 1, 'Spawn Point 12 - Loch Modan'),
(4892, 1, 'Spawn Point 13 - Loch Modan'),
(4893, 1, 'Spawn Point 14 - Loch Modan'),
(4894, 1, 'Spawn Point 15 - Loch Modan'),
(4895, 1, 'Spawn Point 16 - Loch Modan'),
(4896, 1, 'Spawn Point 17 - Loch Modan'),
(4897, 1, 'Spawn Point 18 - Loch Modan'),
(4898, 1, 'Spawn Point 19 - Loch Modan'),
(4899, 1, 'Spawn Point 20 - Loch Modan'),
(4900, 1, 'Spawn Point 21 - Loch Modan'),
(4901, 1, 'Spawn Point 22 - Loch Modan'),
(4902, 1, 'Spawn Point 23 - Loch Modan'),
(4903, 1, 'Spawn Point 24 - Loch Modan'),
(4904, 1, 'Spawn Point 25 - Loch Modan'),
(4905, 1, 'Spawn Point 26 - Loch Modan'),
(4906, 1, 'Spawn Point 27 - Loch Modan'),
(4907, 1, 'Spawn Point 28 - Loch Modan'),
(4908, 1, 'Spawn Point 29 - Loch Modan'),
(4909, 1, 'Spawn Point 30 - Loch Modan'),
(4910, 1, 'Spawn Point 31 - Loch Modan'),
(4911, 1, 'Spawn Point 32 - Loch Modan'),
(4912, 1, 'Spawn Point 33 - Loch Modan'),
(4913, 1, 'Spawn Point 34 - Loch Modan'),
(4914, 1, 'Spawn Point 35 - Loch Modan'),
(4915, 1, 'Spawn Point 36 - Loch Modan'),
(4916, 1, 'Spawn Point 37 - Loch Modan'),
(4917, 1, 'Spawn Point 38 - Loch Modan'),
(4918, 1, 'Spawn Point 39 - Loch Modan'),
(4919, 1, 'Spawn Point 40 - Loch Modan'),
(4920, 1, 'Spawn Point 41 - Loch Modan'),
(4921, 1, 'Spawn Point 42 - Loch Modan'),
(4922, 1, 'Spawn Point 43 - Loch Modan'),
(4923, 1, 'Spawn Point 44 - Loch Modan'),
(4924, 1, 'Spawn Point 45 - Loch Modan'),
(4925, 1, 'Spawn Point 46 - Loch Modan'),
(4926, 1, 'Spawn Point 47 - Loch Modan'),
(4927, 1, 'Spawn Point 48 - Loch Modan'),
(4928, 1, 'Spawn Point 49 - Loch Modan'),
(4929, 1, 'Spawn Point 50 - Loch Modan'),
(4930, 1, 'Spawn Point 51 - Loch Modan'),
(4931, 1, 'Spawn Point 52 - Loch Modan'),
(4932, 1, 'Spawn Point 53 - Loch Modan'),
(4933, 1, 'Spawn Point 54 - Loch Modan'),
(4935, 1, 'Spawn Point 56 - Loch Modan'),
(4936, 1, 'Spawn Point 57 - Loch Modan'),
(4937, 1, 'Spawn Point 58 - Loch Modan'),
(4938, 1, 'Spawn Point 59 - Loch Modan'),
(4939, 1, 'Spawn Point 60 - Loch Modan'),
(4940, 1, 'Spawn Point 61 - Loch Modan'),
(4941, 1, 'Spawn Point 62 - Loch Modan'),
(4942, 1, 'Spawn Point 63 - Loch Modan'),
(4943, 1, 'Spawn Point 64 - Loch Modan'),
(4944, 1, 'Spawn Point 65 - Loch Modan'),
(4945, 1, 'Spawn Point 66 - Loch Modan'),
(4946, 1, 'Spawn Point 67 - Loch Modan'),
(4947, 1, 'Spawn Point 68 - Loch Modan'),
(4948, 1, 'Spawn Point 69 - Loch Modan'),
(4949, 1, 'Spawn Point 70 - Loch Modan'),
(4950, 1, 'Spawn Point 71 - Loch Modan'),
(4951, 1, 'Spawn Point 72 - Loch Modan'),
(4952, 1, 'Spawn Point 73 - Loch Modan'),
(4953, 1, 'Spawn Point 74 - Loch Modan'),
(4954, 1, 'Spawn Point 75 - Loch Modan'),
(4955, 1, 'Spawn Point 76 - Loch Modan'),
(4956, 1, 'Spawn Point 77 - Loch Modan'),
(4957, 1, 'Spawn Point 78 - Loch Modan'),
(4958, 1, 'Spawn Point 79 - Loch Modan'),
(4959, 1, 'Spawn Point 80 - Loch Modan'),
(4960, 1, 'Spawn Point 81 - Loch Modan'),
(4961, 1, 'Spawn Point 82 - Loch Modan'),
(4962, 1, 'Spawn Point 83 - Loch Modan'),
(4963, 1, 'Spawn Point 84 - Loch Modan'),
(4964, 1, 'Spawn Point 85 - Loch Modan'),
(4965, 1, 'Spawn Point 86 - Loch Modan'),
(4966, 1, 'Spawn Point 87 - Loch Modan'),
(4967, 1, 'Spawn Point 88 - Loch Modan'),
(4968, 1, 'Spawn Point 89 - Loch Modan'),
(4969, 1, 'Spawn Point 90 - Loch Modan'),
(4970, 1, 'Spawn Point 91 - Loch Modan'),
(4971, 1, 'Spawn Point 92 - Loch Modan'),
(4972, 1, 'Spawn Point 93 - Loch Modan'),
(4973, 1, 'Spawn Point 94 - Loch Modan'),
(4974, 1, 'Spawn Point 95 - Loch Modan'),
(4975, 1, 'Spawn Point 96 - Loch Modan'),
(4976, 1, 'Spawn Point 97 - Loch Modan'),
(4977, 1, 'Spawn Point 98 - Loch Modan'),
(4978, 1, 'Spawn Point 99 - Loch Modan'),
(4979, 1, 'Spawn Point 100 - Loch Modan'),
(4980, 1, 'Spawn Point 101 - Loch Modan'),
(4981, 1, 'Spawn Point 102 - Loch Modan'),
(4982, 1, 'Spawn Point 103 - Loch Modan'),
(4983, 1, 'Spawn Point 104 - Loch Modan'),
(4984, 1, 'Spawn Point 105 - Loch Modan'),
(4985, 1, 'Spawn Point 106 - Loch Modan'),
(4986, 1, 'Spawn Point 107 - Loch Modan'),
(4987, 1, 'Spawn Point 108 - Loch Modan'),
(4988, 1, 'Spawn Point 109 - Loch Modan'),
(4989, 1, 'Spawn Point 110 - Loch Modan'),
(4990, 1, 'Spawn Point 111 - Loch Modan'),
(4992, 1, 'Spawn Point 113 - Loch Modan'),
(11643, 20, 'Copper Vein - Azuremyst Isle'),
(11646, 30, 'Mineral Veins - Bloodmyst Isle'),
(11676, 1, 'Spawn Point 1 - Redridge Mountains - Zone 44'),
(11677, 1, 'Spawn Point 1 - Stranglethorn Vale - Zone 33');

INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES 
(73500, 2008, 0, 'Spawn Point 1 - Copper'),
(73501, 2008, 0, 'Spawn Point 2 - Copper'),
(73502, 2008, 0, 'Spawn Point 3 - Copper'),
(73503, 2008, 0, 'Spawn Point 4 - Copper'),
(73504, 2008, 0, 'Spawn Point 5 - Copper'),
(73505, 2008, 0, 'Spawn Point 6 - Copper'),
(73506, 2008, 0, 'Spawn Point 7 - Copper'),
(73507, 2008, 0, 'Spawn Point 8 - Copper'),
(73508, 2008, 0, 'Spawn Point 9 - Copper'),
(73509, 2008, 0, 'Spawn Point 10 - Copper'),
(73510, 2008, 0, 'Spawn Point 11 - Copper'),
(73511, 2008, 0, 'Spawn Point 12 - Copper'),
(73512, 2008, 0, 'Spawn Point 13 - Copper'),
(73513, 2008, 0, 'Spawn Point 14 - Copper'),
(73514, 2008, 0, 'Spawn Point 15 - Copper'),
(73515, 2008, 0, 'Spawn Point 16 - Copper'),
(73516, 2008, 0, 'Spawn Point 17 - Copper'),
(73517, 2008, 0, 'Spawn Point 18 - Copper'),
(73518, 2008, 0, 'Spawn Point 19 - Copper'),
(73519, 2008, 0, 'Spawn Point 20 - Copper'),
(73520, 2008, 0, 'Spawn Point 21 - Copper'),
(73521, 2008, 0, 'Spawn Point 22 - Copper'),
(73522, 2008, 0, 'Spawn Point 23 - Copper'),
(73523, 2008, 0, 'Spawn Point 24 - Copper'),
(73524, 2008, 0, 'Spawn Point 25 - Copper'),
(73525, 2008, 0, 'Spawn Point 26 - Copper'),
(73526, 2008, 0, 'Spawn Point 27 - Copper'),
(73527, 2008, 0, 'Spawn Point 28 - Copper'),
(73528, 2008, 0, 'Spawn Point 29 - Copper'),
(73529, 2008, 0, 'Spawn Point 30 - Copper'),
(73530, 2008, 0, 'Spawn Point 31 - Copper'),
(73531, 2008, 0, 'Spawn Point 32 - Copper'),
(73532, 2008, 0, 'Spawn Point 33 - Copper'),
(73533, 2008, 0, 'Spawn Point 34 - Copper'),
(73534, 2008, 0, 'Spawn Point 35 - Copper'),
(73535, 2008, 0, 'Spawn Point 36 - Copper'),
(73536, 2008, 0, 'Spawn Point 37 - Copper'),
(73537, 2008, 0, 'Spawn Point 38 - Copper'),
(73538, 2008, 0, 'Spawn Point 39 - Copper'),
(73539, 2008, 0, 'Spawn Point 40 - Copper'),
(73540, 2008, 0, 'Spawn Point 41 - Copper'),
(73541, 2008, 0, 'Spawn Point 42 - Copper'),
(73542, 2008, 0, 'Spawn Point 43 - Copper'),
(73543, 2008, 0, 'Spawn Point 44 - Copper'),
(73544, 2008, 0, 'Spawn Point 45 - Copper'),
(73545, 2008, 0, 'Spawn Point 46 - Copper'),
(73546, 2008, 0, 'Spawn Point 47 - Copper'),
(73547, 2008, 0, 'Spawn Point 48 - Copper'),
(73548, 2008, 0, 'Spawn Point 49 - Copper'),
(73549, 2008, 0, 'Spawn Point 50 - Copper'),
(73550, 2008, 0, 'Spawn Point 51 - Copper'),
(73551, 2008, 0, 'Spawn Point 52 - Copper'),
(73552, 2008, 0, 'Spawn Point 53 - Copper'),
(73553, 2008, 0, 'Spawn Point 54 - Copper'),
(73554, 2008, 0, 'Spawn Point 55 - Copper'),
(73555, 2008, 0, 'Spawn Point 56 - Copper'),
(73556, 2008, 0, 'Spawn Point 57 - Copper'),
(73557, 2008, 0, 'Spawn Point 58 - Copper'),
(73558, 2008, 0, 'Spawn Point 59 - Copper'),
(73559, 2008, 0, 'Spawn Point 60 - Copper'),
(73560, 2008, 0, 'Spawn Point 61 - Copper'),
(73561, 2008, 0, 'Spawn Point 62 - Copper'),
(73562, 2008, 0, 'Spawn Point 63 - Copper'),
(73563, 2008, 0, 'Spawn Point 64 - Copper'),
(73564, 2008, 0, 'Spawn Point 65 - Copper'),
(73565, 2008, 0, 'Spawn Point 66 - Copper'),
(73566, 2008, 0, 'Spawn Point 67 - Copper'),
(73567, 2008, 0, 'Spawn Point 68 - Copper'),
(73568, 2008, 0, 'Spawn Point 69 - Copper'),
(73569, 2008, 0, 'Spawn Point 70 - Copper'),
(73570, 2008, 0, 'Spawn Point 71 - Copper'),
(73571, 2008, 0, 'Spawn Point 72 - Copper'),
(73572, 2008, 0, 'Spawn Point 73 - Copper'),
(73573, 2008, 0, 'Spawn Point 74 - Copper'),
(73574, 2008, 0, 'Spawn Point 75 - Copper'),
(73575, 2008, 0, 'Spawn Point 76 - Copper'),
(73576, 2008, 0, 'Spawn Point 77 - Copper'),
(73577, 2008, 0, 'Spawn Point 78 - Copper'),
(73578, 2008, 0, 'Spawn Point 79 - Copper'),
(73579, 2008, 0, 'Spawn Point 80 - Copper'),
(73580, 2008, 0, 'Spawn Point 81 - Copper'),
(73581, 2008, 0, 'Spawn Point 82 - Copper'),
(73582, 2008, 0, 'Spawn Point 83 - Copper'),
(73583, 2008, 0, 'Spawn Point 84 - Copper'),
(73584, 2008, 0, 'Spawn Point 85 - Copper'),
(73585, 2008, 0, 'Spawn Point 86 - Copper'),
(73586, 2008, 0, 'Spawn Point 87 - Copper'),
(73600, 4400, 0, 'Spawn Point 1 - Copper'),
(73605, 4401, 0, 'Spawn Point 2 - Copper'),
(73610, 4402, 0, 'Spawn Point 3 - Copper'),
(73615, 4403, 0, 'Spawn Point 4 - Copper'),
(73620, 4404, 0, 'Spawn Point 5 - Copper'),
(73625, 4405, 0, 'Spawn Point 6 - Copper'),
(73630, 4406, 0, 'Spawn Point 7 - Copper'),
(73635, 4407, 0, 'Spawn Point 8 - Copper'),
(73640, 4408, 0, 'Spawn Point 9 - Copper'),
(73645, 4409, 0, 'Spawn Point 10 - Copper'),
(73650, 4410, 0, 'Spawn Point 11 - Copper'),
(73655, 4411, 0, 'Spawn Point 12 - Copper'),
(73660, 4412, 0, 'Spawn Point 13 - Copper'),
(73665, 4413, 0, 'Spawn Point 14 - Copper'),
(73675, 4415, 0, 'Spawn Point 16 - Copper'),
(73680, 4416, 0, 'Spawn Point 17 - Copper'),
(73685, 4417, 0, 'Spawn Point 18 - Copper'),
(73690, 4418, 0, 'Spawn Point 19 - Copper'),
(73695, 4419, 0, 'Spawn Point 20 - Copper'),
(73700, 4420, 0, 'Spawn Point 21 - Copper'),
(73705, 4421, 0, 'Spawn Point 22 - Copper'),
(73710, 4422, 0, 'Spawn Point 23 - Copper'),
(73715, 4423, 0, 'Spawn Point 24 - Copper'),
(73720, 4424, 0, 'Spawn Point 25 - Copper'),
(73725, 4425, 0, 'Spawn Point 26 - Copper'),
(73730, 4426, 0, 'Spawn Point 27 - Copper'),
(73735, 4427, 0, 'Spawn Point 28 - Copper'),
(73740, 4428, 0, 'Spawn Point 29 - Copper'),
(73745, 4429, 0, 'Spawn Point 30 - Copper'),
(73750, 4430, 0, 'Spawn Point 31 - Copper'),
(73755, 4431, 0, 'Spawn Point 32 - Copper'),
(73760, 4432, 0, 'Spawn Point 33 - Copper'),
(73765, 4433, 0, 'Spawn Point 34 - Copper'),
(73770, 4434, 0, 'Spawn Point 35 - Copper'),
(73775, 4435, 0, 'Spawn Point 36 - Copper'),
(73780, 4436, 0, 'Spawn Point 37 - Copper'),
(73785, 4437, 0, 'Spawn Point 38 - Copper'),
(73790, 4438, 0, 'Spawn Point 39 - Copper'),
(73795, 4439, 0, 'Spawn Point 40 - Copper'),
(73800, 4440, 0, 'Spawn Point 41 - Copper'),
(73805, 4441, 0, 'Spawn Point 42 - Copper'),
(73810, 4442, 0, 'Spawn Point 43 - Copper'),
(73815, 4443, 0, 'Spawn Point 44 - Copper'),
(73820, 4444, 0, 'Spawn Point 45 - Copper'),
(73825, 4445, 0, 'Spawn Point 46 - Copper'),
(73830, 4446, 0, 'Spawn Point 47 - Copper'),
(73835, 4447, 0, 'Spawn Point 48 - Copper'),
(73840, 4448, 0, 'Spawn Point 49 - Copper'),
(73845, 4449, 0, 'Spawn Point 50 - Copper'),
(73850, 4450, 0, 'Spawn Point 51 - Copper'),
(73855, 4451, 0, 'Spawn Point 52 - Copper'),
(73860, 4452, 0, 'Spawn Point 53 - Copper'),
(73865, 4453, 0, 'Spawn Point 54 - Copper'),
(73870, 4454, 0, 'Spawn Point 55 - Copper'),
(73875, 4455, 0, 'Spawn Point 56 - Copper'),
(73880, 4456, 0, 'Spawn Point 57 - Copper'),
(73885, 4457, 0, 'Spawn Point 58 - Copper'),
(73890, 4458, 0, 'Spawn Point 59 - Copper'),
(73895, 4459, 0, 'Spawn Point 60 - Copper'),
(73900, 4460, 0, 'Spawn Point 61 - Copper'),
(73905, 4461, 0, 'Spawn Point 62 - Copper'),
(73910, 4462, 0, 'Spawn Point 63 - Copper'),
(73915, 4463, 0, 'Spawn Point 64 - Copper'),
(73920, 4464, 0, 'Spawn Point 65 - Copper'),
(73925, 4465, 0, 'Spawn Point 66 - Copper'),
(73930, 4466, 0, 'Spawn Point 67 - Copper'),
(73935, 4467, 0, 'Spawn Point 68 - Copper'),
(73940, 4468, 0, 'Spawn Point 69 - Copper'),
(73945, 4469, 0, 'Spawn Point 70 - Copper'),
(73950, 4470, 0, 'Spawn Point 71 - Copper'),
(73955, 4471, 0, 'Spawn Point 72 - Copper'),
(73960, 4472, 0, 'Spawn Point 73 - Copper'),
(73965, 4473, 0, 'Spawn Point 74 - Copper'),
(73970, 4474, 0, 'Spawn Point 75 - Copper'),
(73975, 4475, 0, 'Spawn Point 76 - Copper'),
(73980, 4476, 0, 'Spawn Point 77 - Copper'),
(73985, 4477, 0, 'Spawn Point 78 - Copper'),
(73990, 4478, 0, 'Spawn Point 79 - Copper'),
(73995, 4479, 0, 'Spawn Point 80 - Copper'),
(74000, 4480, 0, 'Spawn Point 81 - Copper'),
(74005, 4481, 0, 'Spawn Point 82 - Copper'),
(74010, 4482, 0, 'Spawn Point 83 - Copper'),
(74015, 4483, 0, 'Spawn Point 84 - Copper'),
(74020, 4484, 0, 'Spawn Point 85 - Copper'),
(74025, 4485, 0, 'Spawn Point 86 - Copper'),
(74035, 4487, 0, 'Spawn Point 88 - Copper'),
(74040, 4488, 0, 'Spawn Point 89 - Copper'),
(74055, 4491, 0, 'Spawn Point 92 - Copper'),
(74060, 4492, 0, 'Spawn Point 93 - Copper'),
(74065, 4493, 0, 'Spawn Point 94 - Copper'),
(74070, 4494, 0, 'Spawn Point 95 - Copper'),
(74080, 4496, 0, 'Spawn Point 97 - Copper'),
(74085, 4497, 0, 'Spawn Point 98 - Copper'),
(74095, 4499, 0, 'Spawn Point 100 - Copper'),
(74100, 4500, 0, 'Spawn Point 101 - Copper'),
(74105, 4501, 0, 'Spawn Point 102 - Copper'),
(74110, 4502, 0, 'Spawn Point 103 - Copper'),
(74115, 4503, 0, 'Spawn Point 104 - Copper'),
(74120, 4504, 0, 'Spawn Point 105 - Copper'),
(74125, 4505, 0, 'Spawn Point 106 - Copper'),
(74130, 4506, 0, 'Spawn Point 107 - Copper'),
(74145, 4509, 0, 'Spawn Point 110 - Copper'),
(74150, 4510, 0, 'Spawn Point 111 - Copper'),
(74155, 4511, 0, 'Spawn Point 112 - Copper'),
(74160, 4512, 0, 'Spawn Point 113 - Copper'),
(74800, 2011, 0, 'Spawn Point 1 - Copper'),
(74801, 2011, 0, 'Spawn Point 2 - Copper'),
(74802, 2011, 0, 'Spawn Point 3 - Copper'),
(74803, 2011, 0, 'Spawn Point 4 - Copper'),
(74804, 2011, 0, 'Spawn Point 5 - Copper'),
(74805, 2011, 0, 'Spawn Point 6 - Copper'),
(74806, 2011, 0, 'Spawn Point 7 - Copper'),
(74807, 2011, 0, 'Spawn Point 8 - Copper'),
(74808, 2011, 0, 'Spawn Point 9 - Copper'),
(74809, 2011, 0, 'Spawn Point 10 - Copper'),
(74810, 2011, 0, 'Spawn Point 11 - Copper'),
(74811, 2011, 0, 'Spawn Point 12 - Copper'),
(74812, 2011, 0, 'Spawn Point 13 - Copper'),
(74813, 2011, 0, 'Spawn Point 14 - Copper'),
(74814, 2011, 0, 'Spawn Point 15 - Copper'),
(74815, 2011, 0, 'Spawn Point 16 - Copper'),
(74816, 2011, 0, 'Spawn Point 17 - Copper'),
(74817, 2011, 0, 'Spawn Point 18 - Copper'),
(74818, 2011, 0, 'Spawn Point 19 - Copper'),
(74819, 2011, 0, 'Spawn Point 20 - Copper'),
(74820, 2011, 0, 'Spawn Point 21 - Copper'),
(74821, 2011, 0, 'Spawn Point 22 - Copper'),
(74822, 2011, 0, 'Spawn Point 23 - Copper'),
(74823, 2011, 0, 'Spawn Point 24 - Copper'),
(74824, 2011, 0, 'Spawn Point 25 - Copper'),
(74825, 2011, 0, 'Spawn Point 26 - Copper'),
(74826, 2011, 0, 'Spawn Point 27 - Copper'),
(74827, 2011, 0, 'Spawn Point 28 - Copper'),
(74828, 2011, 0, 'Spawn Point 29 - Copper'),
(74829, 2011, 0, 'Spawn Point 30 - Copper'),
(74830, 2011, 0, 'Spawn Point 31 - Copper'),
(74831, 2011, 0, 'Spawn Point 32 - Copper'),
(74832, 2011, 0, 'Spawn Point 33 - Copper'),
(74833, 2011, 0, 'Spawn Point 34 - Copper'),
(74834, 2011, 0, 'Spawn Point 35 - Copper'),
(74835, 2011, 0, 'Spawn Point 36 - Copper'),
(74836, 2011, 0, 'Spawn Point 37 - Copper'),
(74837, 2011, 0, 'Spawn Point 38 - Copper'),
(74838, 2011, 0, 'Spawn Point 39 - Copper'),
(74839, 2011, 0, 'Spawn Point 40 - Copper'),
(74840, 2011, 0, 'Spawn Point 41 - Copper'),
(74841, 2011, 0, 'Spawn Point 42 - Copper'),
(74842, 2011, 0, 'Spawn Point 43 - Copper'),
(74843, 2011, 0, 'Spawn Point 44 - Copper'),
(74844, 2011, 0, 'Spawn Point 45 - Copper'),
(74845, 2011, 0, 'Spawn Point 46 - Copper'),
(74846, 2011, 0, 'Spawn Point 47 - Copper'),
(74847, 2011, 0, 'Spawn Point 48 - Copper'),
(74848, 2011, 0, 'Spawn Point 49 - Copper'),
(74849, 2011, 0, 'Spawn Point 50 - Copper'),
(74850, 2011, 0, 'Spawn Point 51 - Copper'),
(74851, 2011, 0, 'Spawn Point 52 - Copper'),
(74852, 2011, 0, 'Spawn Point 53 - Copper'),
(74853, 2011, 0, 'Spawn Point 54 - Copper'),
(74854, 2011, 0, 'Spawn Point 55 - Copper'),
(74855, 2011, 0, 'Spawn Point 56 - Copper'),
(74856, 2011, 0, 'Spawn Point 57 - Copper'),
(74857, 2011, 0, 'Spawn Point 58 - Copper'),
(74858, 2011, 0, 'Spawn Point 59 - Copper'),
(74859, 2011, 0, 'Spawn Point 60 - Copper'),
(74860, 2011, 0, 'Spawn Point 61 - Copper'),
(74861, 2011, 0, 'Spawn Point 62 - Copper'),
(74862, 2011, 0, 'Spawn Point 63 - Copper'),
(74863, 2011, 0, 'Spawn Point 64 - Copper'),
(74864, 2011, 0, 'Spawn Point 65 - Copper'),
(74865, 2011, 0, 'Spawn Point 66 - Copper'),
(74866, 2011, 0, 'Spawn Point 67 - Copper'),
(74867, 2011, 0, 'Spawn Point 68 - Copper'),
(74868, 2011, 0, 'Spawn Point 69 - Copper'),
(74869, 2011, 0, 'Spawn Point 70 - Copper'),
(74870, 2011, 0, 'Spawn Point 71 - Copper'),
(74871, 2011, 0, 'Spawn Point 72 - Copper'),
(74872, 2011, 0, 'Spawn Point 73 - Copper'),
(74873, 2011, 0, 'Spawn Point 74 - Copper'),
(74874, 2011, 0, 'Spawn Point 75 - Copper'),
(74875, 2011, 0, 'Spawn Point 76 - Copper'),
(74876, 2011, 0, 'Spawn Point 77 - Copper'),
(74877, 2011, 0, 'Spawn Point 78 - Copper'),
(74878, 2011, 0, 'Spawn Point 79 - Copper'),
(74879, 2011, 0, 'Spawn Point 80 - Copper'),
(74880, 2011, 0, 'Spawn Point 81 - Copper'),
(74881, 2011, 0, 'Spawn Point 82 - Copper'),
(74882, 2011, 0, 'Spawn Point 83 - Copper'),
(74883, 2011, 0, 'Spawn Point 84 - Copper'),
(74884, 2011, 0, 'Spawn Point 85 - Copper'),
(74885, 2011, 0, 'Spawn Point 86 - Copper'),
(74886, 2011, 0, 'Spawn Point 87 - Copper'),
(74887, 2011, 0, 'Spawn Point 88 - Copper'),
(74888, 2011, 0, 'Spawn Point 89 - Copper'),
(74889, 2011, 0, 'Spawn Point 90 - Copper'),
(74890, 2011, 0, 'Spawn Point 91 - Copper'),
(74891, 2011, 0, 'Spawn Point 92 - Copper'),
(74892, 2011, 0, 'Spawn Point 93 - Copper'),
(74893, 2011, 0, 'Spawn Point 94 - Copper'),
(74894, 2011, 0, 'Spawn Point 95 - Copper'),
(74895, 2011, 0, 'Spawn Point 96 - Copper'),
(74896, 2011, 0, 'Spawn Point 97 - Copper'),
(74897, 2011, 0, 'Spawn Point 98 - Copper'),
(74898, 2011, 0, 'Spawn Point 99 - Copper'),
(74899, 2011, 0, 'Spawn Point 100 - Copper'),
(74900, 2011, 0, 'Spawn Point 101 - Copper'),
(74958, 11644, 0, 'Eversong Woods, Copper Vein, spawn 29'),
(74957, 11644, 0, 'Eversong Woods, Copper Vein, spawn 28'),
(74956, 11644, 0, 'Eversong Woods, Copper Vein, spawn 27'),
(74955, 11644, 0, 'Eversong Woods, Copper Vein, spawn 26'),
(74954, 11644, 0, 'Eversong Woods, Copper Vein, spawn 25'),
(74953, 11644, 0, 'Eversong Woods, Copper Vein, spawn 24'),
(74952, 11644, 0, 'Eversong Woods, Copper Vein, spawn 23'),
(74951, 11644, 0, 'Eversong Woods, Copper Vein, spawn 22'),
(74950, 11644, 0, 'Eversong Woods, Copper Vein, spawn 21'),
(74949, 11644, 0, 'Eversong Woods, Copper Vein, spawn 20'),
(74948, 11644, 0, 'Eversong Woods, Copper Vein, spawn 19'),
(74947, 11644, 0, 'Eversong Woods, Copper Vein, spawn 18'),
(74946, 11644, 0, 'Eversong Woods, Copper Vein, spawn 17'),
(74945, 11644, 0, 'Eversong Woods, Copper Vein, spawn 16'),
(74944, 11644, 0, 'Eversong Woods, Copper Vein, spawn 15'),
(74943, 11644, 0, 'Eversong Woods, Copper Vein, spawn 14'),
(74942, 11644, 0, 'Eversong Woods, Copper Vein, spawn 13'),
(74941, 11644, 0, 'Eversong Woods, Copper Vein, spawn 12'),
(74940, 11644, 0, 'Eversong Woods, Copper Vein, spawn 11'),
(74939, 11644, 0, 'Eversong Woods, Copper Vein, spawn 10'),
(74938, 11644, 0, 'Eversong Woods, Copper Vein, spawn 9'),
(74937, 11644, 0, 'Eversong Woods, Copper Vein, spawn 8'),
(74936, 11644, 0, 'Eversong Woods, Copper Vein, spawn 7'),
(74935, 11644, 0, 'Eversong Woods, Copper Vein, spawn 6'),
(74934, 11644, 0, 'Eversong Woods, Copper Vein, spawn 5'),
(74933, 11644, 0, 'Eversong Woods, Copper Vein, spawn 4'),
(74932, 11644, 0, 'Eversong Woods, Copper Vein, spawn 3'),
(74931, 11644, 0, 'Eversong Woods, Copper Vein, spawn 2'),
(74930, 11644, 0, 'Eversong Woods, Copper Vein, spawn 1'),
(75000, 4700, 60, 'Ghostlands Spawn Point 1 (Copper)'),
(75001, 4700, 30, 'Ghostlands Spawn Point 1 (Tin)'),
(75002, 4700, 10, 'Ghostlands Spawn Point 1 (Silver)'),
(75003, 4701, 60, 'Ghostlands Spawn Point 2 (Copper)'),
(75004, 4701, 30, 'Ghostlands Spawn Point 2 (Tin)'),
(75005, 4701, 10, 'Ghostlands Spawn Point 2 (Silver)'),
(75006, 4702, 60, 'Ghostlands Spawn Point 3 (Copper)'),
(75007, 4702, 30, 'Ghostlands Spawn Point 3 (Tin)'),
(75008, 4702, 10, 'Ghostlands Spawn Point 3 (Silver)'),
(75009, 4703, 60, 'Ghostlands Spawn Point 4 (Copper)'),
(75010, 4703, 30, 'Ghostlands Spawn Point 4 (Tin)'),
(75011, 4703, 10, 'Ghostlands Spawn Point 4 (Silver)'),
(75012, 4704, 60, 'Ghostlands Spawn Point 5 (Copper)'),
(75013, 4704, 30, 'Ghostlands Spawn Point 5 (Tin)'),
(75014, 4704, 10, 'Ghostlands Spawn Point 5 (Silver)'),
(75015, 4705, 60, 'Ghostlands Spawn Point 6 (Copper)'),
(75016, 4705, 30, 'Ghostlands Spawn Point 6 (Tin)'),
(75017, 4705, 10, 'Ghostlands Spawn Point 6 (Silver)'),
(75018, 4706, 60, 'Ghostlands Spawn Point 7 (Copper)'),
(75019, 4706, 30, 'Ghostlands Spawn Point 7 (Tin)'),
(75020, 4706, 10, 'Ghostlands Spawn Point 7 (Silver)'),
(75021, 4707, 60, 'Ghostlands Spawn Point 8 (Copper)'),
(75022, 4707, 30, 'Ghostlands Spawn Point 8 (Tin)'),
(75023, 4707, 10, 'Ghostlands Spawn Point 8 (Silver)'),
(75024, 4708, 60, 'Ghostlands Spawn Point 9 (Copper)'),
(75025, 4708, 30, 'Ghostlands Spawn Point 9 (Tin)'),
(75026, 4708, 10, 'Ghostlands Spawn Point 9 (Silver)'),
(75027, 4709, 60, 'Ghostlands Spawn Point 10 (Copper)'),
(75028, 4709, 30, 'Ghostlands Spawn Point 10 (Tin)'),
(75029, 4709, 10, 'Ghostlands Spawn Point 10 (Silver)'),
(75030, 4710, 60, 'Ghostlands Spawn Point 11 (Copper)'),
(75031, 4710, 30, 'Ghostlands Spawn Point 11 (Tin)'),
(75032, 4710, 10, 'Ghostlands Spawn Point 11 (Silver)'),
(75033, 4711, 60, 'Ghostlands Spawn Point 12 (Copper)'),
(75034, 4711, 30, 'Ghostlands Spawn Point 12 (Tin)'),
(75035, 4711, 10, 'Ghostlands Spawn Point 12 (Silver)'),
(75036, 4712, 60, 'Ghostlands Spawn Point 13 (Copper)'),
(75037, 4712, 30, 'Ghostlands Spawn Point 13 (Tin)'),
(75038, 4712, 10, 'Ghostlands Spawn Point 13 (Silver)'),
(75039, 4713, 60, 'Ghostlands Spawn Point 14 (Copper)'),
(75040, 4713, 30, 'Ghostlands Spawn Point 14 (Tin)'),
(75041, 4713, 10, 'Ghostlands Spawn Point 14 (Silver)'),
(75042, 4714, 60, 'Ghostlands Spawn Point 15 (Copper)'),
(75043, 4714, 30, 'Ghostlands Spawn Point 15 (Tin)'),
(75044, 4714, 10, 'Ghostlands Spawn Point 15 (Silver)'),
(75045, 4715, 60, 'Ghostlands Spawn Point 16 (Copper)'),
(75046, 4715, 30, 'Ghostlands Spawn Point 16 (Tin)'),
(75047, 4715, 10, 'Ghostlands Spawn Point 16 (Silver)'),
(75048, 4716, 60, 'Ghostlands Spawn Point 17 (Copper)'),
(75049, 4716, 30, 'Ghostlands Spawn Point 17 (Tin)'),
(75050, 4716, 10, 'Ghostlands Spawn Point 17 (Silver)'),
(75051, 4717, 60, 'Ghostlands Spawn Point 18 (Copper)'),
(75052, 4717, 30, 'Ghostlands Spawn Point 18 (Tin)'),
(75053, 4717, 10, 'Ghostlands Spawn Point 18 (Silver)'),
(75054, 4718, 60, 'Ghostlands Spawn Point 19 (Copper)'),
(75055, 4718, 30, 'Ghostlands Spawn Point 19 (Tin)'),
(75056, 4718, 10, 'Ghostlands Spawn Point 19 (Silver)'),
(75057, 4719, 60, 'Ghostlands Spawn Point 20 (Copper)'),
(75058, 4719, 30, 'Ghostlands Spawn Point 20 (Tin)'),
(75059, 4719, 10, 'Ghostlands Spawn Point 20 (Silver)'),
(75060, 4720, 60, 'Ghostlands Spawn Point 21 (Copper)'),
(75061, 4720, 30, 'Ghostlands Spawn Point 21 (Tin)'),
(75062, 4720, 10, 'Ghostlands Spawn Point 21 (Silver)'),
(75063, 4721, 60, 'Ghostlands Spawn Point 22 (Copper)'),
(75064, 4721, 30, 'Ghostlands Spawn Point 22 (Tin)'),
(75065, 4721, 10, 'Ghostlands Spawn Point 22 (Silver)'),
(75066, 4722, 60, 'Ghostlands Spawn Point 23 (Copper)'),
(75067, 4722, 30, 'Ghostlands Spawn Point 23 (Tin)'),
(75068, 4722, 10, 'Ghostlands Spawn Point 23 (Silver)'),
(75069, 4723, 60, 'Ghostlands Spawn Point 24 (Copper)'),
(75070, 4723, 30, 'Ghostlands Spawn Point 24 (Tin)'),
(75071, 4723, 10, 'Ghostlands Spawn Point 24 (Silver)'),
(75072, 4724, 60, 'Ghostlands Spawn Point 25 (Copper)'),
(75073, 4724, 30, 'Ghostlands Spawn Point 25 (Tin)'),
(75074, 4724, 10, 'Ghostlands Spawn Point 25 (Silver)'),
(75075, 4725, 60, 'Ghostlands Spawn Point 26 (Copper)'),
(75076, 4725, 30, 'Ghostlands Spawn Point 26 (Tin)'),
(75077, 4725, 10, 'Ghostlands Spawn Point 26 (Silver)'),
(75078, 4726, 60, 'Ghostlands Spawn Point 27 (Copper)'),
(75079, 4726, 30, 'Ghostlands Spawn Point 27 (Tin)'),
(75080, 4726, 10, 'Ghostlands Spawn Point 27 (Silver)'),
(75081, 4727, 60, 'Ghostlands Spawn Point 28 (Copper)'),
(75082, 4727, 30, 'Ghostlands Spawn Point 28 (Tin)'),
(75083, 4727, 10, 'Ghostlands Spawn Point 28 (Silver)'),
(75084, 4728, 60, 'Ghostlands Spawn Point 29 (Copper)'),
(75085, 4728, 30, 'Ghostlands Spawn Point 29 (Tin)'),
(75086, 4728, 10, 'Ghostlands Spawn Point 29 (Silver)'),
(75087, 4729, 60, 'Ghostlands Spawn Point 30 (Copper)'),
(75088, 4729, 30, 'Ghostlands Spawn Point 30 (Tin)'),
(75089, 4729, 10, 'Ghostlands Spawn Point 30 (Silver)'),
(75090, 4730, 60, 'Ghostlands Spawn Point 31 (Copper)'),
(75091, 4730, 30, 'Ghostlands Spawn Point 31 (Tin)'),
(75092, 4730, 10, 'Ghostlands Spawn Point 31 (Silver)'),
(75093, 4731, 60, 'Ghostlands Spawn Point 32 (Copper)'),
(75094, 4731, 30, 'Ghostlands Spawn Point 32 (Tin)'),
(75095, 4731, 10, 'Ghostlands Spawn Point 32 (Silver)'),
(75096, 4732, 60, 'Ghostlands Spawn Point 33 (Copper)'),
(75097, 4732, 30, 'Ghostlands Spawn Point 33 (Tin)'),
(75098, 4732, 10, 'Ghostlands Spawn Point 33 (Silver)'),
(75099, 4733, 60, 'Ghostlands Spawn Point 34 (Copper)'),
(75100, 4733, 30, 'Ghostlands Spawn Point 34 (Tin)'),
(75101, 4733, 10, 'Ghostlands Spawn Point 34 (Silver)'),
(75102, 4734, 60, 'Ghostlands Spawn Point 35 (Copper)'),
(75103, 4734, 30, 'Ghostlands Spawn Point 35 (Tin)'),
(75104, 4734, 10, 'Ghostlands Spawn Point 35 (Silver)'),
(75105, 4735, 60, 'Ghostlands Spawn Point 36 (Copper)'),
(75106, 4735, 30, 'Ghostlands Spawn Point 36 (Tin)'),
(75107, 4735, 10, 'Ghostlands Spawn Point 36 (Silver)'),
(75108, 4736, 60, 'Ghostlands Spawn Point 37 (Copper)'),
(75109, 4736, 30, 'Ghostlands Spawn Point 37 (Tin)'),
(75110, 4736, 10, 'Ghostlands Spawn Point 37 (Silver)'),
(75111, 4737, 60, 'Ghostlands Spawn Point 38 (Copper)'),
(75112, 4737, 30, 'Ghostlands Spawn Point 38 (Tin)'),
(75113, 4737, 10, 'Ghostlands Spawn Point 38 (Silver)'),
(75114, 4738, 60, 'Ghostlands Spawn Point 39 (Copper)'),
(75115, 4738, 30, 'Ghostlands Spawn Point 39 (Tin)'),
(75116, 4738, 10, 'Ghostlands Spawn Point 39 (Silver)'),
(75117, 4739, 60, 'Ghostlands Spawn Point 40 (Copper)'),
(75118, 4739, 30, 'Ghostlands Spawn Point 40 (Tin)'),
(75119, 4739, 10, 'Ghostlands Spawn Point 40 (Silver)'),
(75200, 4750, 0, 'Spawn Point 1 - Copper'),
(75203, 4751, 0, 'Spawn Point 2 - Copper'),
(75206, 4752, 0, 'Spawn Point 3 - Copper'),
(75209, 4753, 0, 'Spawn Point 4 - Copper'),
(75212, 4754, 0, 'Spawn Point 5 - Copper'),
(75215, 4755, 0, 'Spawn Point 6 - Copper'),
(75218, 4756, 0, 'Spawn Point 7 - Copper'),
(75221, 4757, 0, 'Spawn Point 8 - Copper'),
(75224, 4758, 0, 'Spawn Point 9 - Copper'),
(75227, 4759, 0, 'Spawn Point 10 - Copper'),
(75230, 4760, 0, 'Spawn Point 11 - Copper'),
(75233, 4761, 0, 'Spawn Point 12 - Copper'),
(75236, 4762, 0, 'Spawn Point 13 - Copper'),
(75242, 4764, 0, 'Spawn Point 15 - Copper'),
(75245, 4765, 0, 'Spawn Point 16 - Copper'),
(75248, 4766, 0, 'Spawn Point 17 - Copper'),
(75251, 4767, 0, 'Spawn Point 18 - Copper'),
(75254, 4768, 0, 'Spawn Point 19 - Copper'),
(75260, 4770, 0, 'Spawn Point 21 - Copper'),
(75263, 4771, 0, 'Spawn Point 22 - Copper'),
(75266, 4772, 0, 'Spawn Point 23 - Copper'),
(75269, 4773, 0, 'Spawn Point 24 - Copper'),
(75272, 4774, 0, 'Spawn Point 25 - Copper'),
(75275, 4775, 0, 'Spawn Point 26 - Copper'),
(75278, 4776, 0, 'Spawn Point 27 - Copper'),
(75281, 4777, 0, 'Spawn Point 28 - Copper'),
(75284, 4778, 0, 'Spawn Point 29 - Copper'),
(75287, 4779, 0, 'Spawn Point 30 - Copper'),
(75290, 4780, 0, 'Spawn Point 31 - Copper'),
(75293, 4781, 0, 'Spawn Point 32 - Copper'),
(75296, 4782, 0, 'Spawn Point 33 - Copper'),
(75299, 4783, 0, 'Spawn Point 34 - Copper'),
(75302, 4784, 0, 'Spawn Point 35 - Copper'),
(75305, 4785, 0, 'Spawn Point 36 - Copper'),
(75308, 4786, 0, 'Spawn Point 37 - Copper'),
(75311, 4787, 0, 'Spawn Point 38 - Copper'),
(75314, 4788, 0, 'Spawn Point 39 - Copper'),
(75317, 4789, 0, 'Spawn Point 40 - Copper'),
(75320, 4790, 0, 'Spawn Point 41 - Copper'),
(75323, 4791, 0, 'Spawn Point 42 - Copper'),
(75326, 4792, 0, 'Spawn Point 43 - Copper'),
(75329, 4793, 0, 'Spawn Point 44 - Copper'),
(75332, 4794, 0, 'Spawn Point 45 - Copper'),
(75335, 4795, 0, 'Spawn Point 46 - Copper'),
(75338, 4796, 0, 'Spawn Point 47 - Copper'),
(75341, 4797, 0, 'Spawn Point 48 - Copper'),
(75344, 4798, 0, 'Spawn Point 49 - Copper'),
(75347, 4799, 0, 'Spawn Point 50 - Copper'),
(75350, 4800, 0, 'Spawn Point 51 - Copper'),
(75353, 4801, 0, 'Spawn Point 52 - Copper'),
(75356, 4802, 0, 'Spawn Point 53 - Copper'),
(75359, 4803, 0, 'Spawn Point 54 - Copper'),
(75362, 4804, 0, 'Spawn Point 55 - Copper'),
(75365, 4805, 0, 'Spawn Point 56 - Copper'),
(75368, 4806, 0, 'Spawn Point 57 - Copper'),
(75371, 4807, 0, 'Spawn Point 58 - Copper'),
(75374, 4808, 0, 'Spawn Point 59 - Copper'),
(75377, 4809, 0, 'Spawn Point 60 - Copper'),
(75380, 4810, 0, 'Spawn Point 61 - Copper'),
(75383, 4811, 0, 'Spawn Point 62 - Copper'),
(75386, 4812, 0, 'Spawn Point 63 - Copper'),
(75389, 4813, 0, 'Spawn Point 64 - Copper'),
(75392, 4814, 0, 'Spawn Point 65 - Copper'),
(75395, 4815, 0, 'Spawn Point 66 - Copper'),
(75398, 4816, 0, 'Spawn Point 67 - Copper'),
(75401, 4817, 0, 'Spawn Point 68 - Copper'),
(75404, 4818, 0, 'Spawn Point 69 - Copper'),
(75600, 4880, 0, 'Spawn Point 1 - Copper'),
(75603, 4881, 0, 'Spawn Point 2 - Copper'),
(75606, 4882, 0, 'Spawn Point 3 - Copper'),
(75609, 4883, 0, 'Spawn Point 4 - Copper'),
(75612, 4884, 0, 'Spawn Point 5 - Copper'),
(75615, 4885, 0, 'Spawn Point 6 - Copper'),
(75618, 4886, 0, 'Spawn Point 7 - Copper'),
(75621, 4887, 0, 'Spawn Point 8 - Copper'),
(75624, 4888, 0, 'Spawn Point 9 - Copper'),
(75627, 4889, 0, 'Spawn Point 10 - Copper'),
(75630, 4890, 0, 'Spawn Point 11 - Copper'),
(75633, 4891, 0, 'Spawn Point 12 - Copper'),
(75636, 4892, 0, 'Spawn Point 13 - Copper'),
(75639, 4893, 0, 'Spawn Point 14 - Copper'),
(75642, 4894, 0, 'Spawn Point 15 - Copper'),
(75645, 4895, 0, 'Spawn Point 16 - Copper'),
(75648, 4896, 0, 'Spawn Point 17 - Copper'),
(75651, 4897, 0, 'Spawn Point 18 - Copper'),
(75654, 4898, 0, 'Spawn Point 19 - Copper'),
(75657, 4899, 0, 'Spawn Point 20 - Copper'),
(75660, 4900, 0, 'Spawn Point 21 - Copper'),
(75663, 4901, 0, 'Spawn Point 22 - Copper'),
(75666, 4902, 0, 'Spawn Point 23 - Copper'),
(75669, 4903, 0, 'Spawn Point 24 - Copper'),
(75672, 4904, 0, 'Spawn Point 25 - Copper'),
(75675, 4905, 0, 'Spawn Point 26 - Copper'),
(75678, 4906, 0, 'Spawn Point 27 - Copper'),
(75681, 4907, 0, 'Spawn Point 28 - Copper'),
(75684, 4908, 0, 'Spawn Point 29 - Copper'),
(75687, 4909, 0, 'Spawn Point 30 - Copper'),
(75690, 4910, 0, 'Spawn Point 31 - Copper'),
(75693, 4911, 0, 'Spawn Point 32 - Copper'),
(75696, 4912, 0, 'Spawn Point 33 - Copper'),
(75699, 4913, 0, 'Spawn Point 34 - Copper'),
(75702, 4914, 0, 'Spawn Point 35 - Copper'),
(75705, 4915, 0, 'Spawn Point 36 - Copper'),
(75708, 4916, 0, 'Spawn Point 37 - Copper'),
(75711, 4917, 0, 'Spawn Point 38 - Copper'),
(75714, 4918, 0, 'Spawn Point 39 - Copper'),
(75717, 4919, 0, 'Spawn Point 40 - Copper'),
(75720, 4920, 0, 'Spawn Point 41 - Copper'),
(75723, 4921, 0, 'Spawn Point 42 - Copper'),
(75726, 4922, 0, 'Spawn Point 43 - Copper'),
(75729, 4923, 0, 'Spawn Point 44 - Copper'),
(75732, 4924, 0, 'Spawn Point 45 - Copper'),
(75735, 4925, 0, 'Spawn Point 46 - Copper'),
(75738, 4926, 0, 'Spawn Point 47 - Copper'),
(75741, 4927, 0, 'Spawn Point 48 - Copper'),
(75744, 4928, 0, 'Spawn Point 49 - Copper'),
(75747, 4929, 0, 'Spawn Point 50 - Copper'),
(75750, 4930, 0, 'Spawn Point 51 - Copper'),
(75753, 4931, 0, 'Spawn Point 52 - Copper'),
(75756, 4932, 0, 'Spawn Point 53 - Copper'),
(75759, 4933, 0, 'Spawn Point 54 - Copper'),
(75765, 4935, 0, 'Spawn Point 56 - Copper'),
(75768, 4936, 0, 'Spawn Point 57 - Copper'),
(75771, 4937, 0, 'Spawn Point 58 - Copper'),
(75774, 4938, 0, 'Spawn Point 59 - Copper'),
(75777, 4939, 0, 'Spawn Point 60 - Copper'),
(75780, 4940, 0, 'Spawn Point 61 - Copper'),
(75783, 4941, 0, 'Spawn Point 62 - Copper'),
(75786, 4942, 0, 'Spawn Point 63 - Copper'),
(75789, 4943, 0, 'Spawn Point 64 - Copper'),
(75792, 4944, 0, 'Spawn Point 65 - Copper'),
(75795, 4945, 0, 'Spawn Point 66 - Copper'),
(75798, 4946, 0, 'Spawn Point 67 - Copper'),
(75801, 4947, 0, 'Spawn Point 68 - Copper'),
(75804, 4948, 0, 'Spawn Point 69 - Copper'),
(75807, 4949, 0, 'Spawn Point 70 - Copper'),
(75810, 4950, 0, 'Spawn Point 71 - Copper'),
(75813, 4951, 0, 'Spawn Point 72 - Copper'),
(75816, 4952, 0, 'Spawn Point 73 - Copper'),
(75819, 4953, 0, 'Spawn Point 74 - Copper'),
(75822, 4954, 0, 'Spawn Point 75 - Copper'),
(75825, 4955, 0, 'Spawn Point 76 - Copper'),
(75828, 4956, 0, 'Spawn Point 77 - Copper'),
(75831, 4957, 0, 'Spawn Point 78 - Copper'),
(75834, 4958, 0, 'Spawn Point 79 - Copper'),
(75837, 4959, 0, 'Spawn Point 80 - Copper'),
(75840, 4960, 0, 'Spawn Point 81 - Copper'),
(75843, 4961, 0, 'Spawn Point 82 - Copper'),
(75846, 4962, 0, 'Spawn Point 83 - Copper'),
(75849, 4963, 0, 'Spawn Point 84 - Copper'),
(75852, 4964, 0, 'Spawn Point 85 - Copper'),
(75855, 4965, 0, 'Spawn Point 86 - Copper'),
(75858, 4966, 0, 'Spawn Point 87 - Copper'),
(75861, 4967, 0, 'Spawn Point 88 - Copper'),
(75864, 4968, 0, 'Spawn Point 89 - Copper'),
(75867, 4969, 0, 'Spawn Point 90 - Copper'),
(75870, 4970, 0, 'Spawn Point 91 - Copper'),
(75873, 4971, 0, 'Spawn Point 92 - Copper'),
(75876, 4972, 0, 'Spawn Point 93 - Copper'),
(75879, 4973, 0, 'Spawn Point 94 - Copper'),
(75882, 4974, 0, 'Spawn Point 95 - Copper'),
(75885, 4975, 0, 'Spawn Point 96 - Copper'),
(75888, 4976, 0, 'Spawn Point 97 - Copper'),
(75891, 4977, 0, 'Spawn Point 98 - Copper'),
(75894, 4978, 0, 'Spawn Point 99 - Copper'),
(75897, 4979, 0, 'Spawn Point 100 - Copper'),
(75900, 4980, 0, 'Spawn Point 101 - Copper'),
(75903, 4981, 0, 'Spawn Point 102 - Copper'),
(75906, 4982, 0, 'Spawn Point 103 - Copper'),
(75909, 4983, 0, 'Spawn Point 104 - Copper'),
(75912, 4984, 0, 'Spawn Point 105 - Copper'),
(75915, 4985, 0, 'Spawn Point 106 - Copper'),
(75918, 4986, 0, 'Spawn Point 107 - Copper'),
(75921, 4987, 0, 'Spawn Point 108 - Copper'),
(75924, 4988, 0, 'Spawn Point 109 - Copper'),
(75927, 4989, 0, 'Spawn Point 110 - Copper'),
(75930, 4990, 0, 'Spawn Point 111 - Copper'),
(75936, 4992, 0, 'Spawn Point 113 - Copper'),
(150129, 2011, 0, 'Spawn Point 102 - Copper'),
(150130, 2011, 0, 'Spawn Point 103 - Copper'),
(150138, 2011, 0, 'Spawn Point 111 - Copper'),
(150139, 2011, 0, 'Spawn Point 112 - Copper'),
(5290, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 1'),
(5291, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 2'),
(5292, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 3'),
(5293, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 4'),
(5294, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 5'),
(5295, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 6'),
(5296, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 7'),
(5297, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 8'),
(5298, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 9'),
(5299, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 10'),
(5300, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 11'),
(5301, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 12'),
(5302, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 13'),
(5303, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 14'),
(5304, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 15'),
(5305, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 16'),
(5306, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 17'),
(5307, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 18'),
(5308, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 19'),
(5309, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 20'),
(5310, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 21'),
(5311, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 22'),
(5312, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 23'),
(5313, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 24'),
(30444, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 25'),
(30478, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 26'),
(30486, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 27'),
(30498, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 28'),
(30507, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 29'),
(30512, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 30'),
(30514, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 31'),
(30531, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 32'),
(39948, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 33'),
(39949, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 34'),
(39950, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 35'),
(120291, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 36'),
(120340, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 37'),
(120362, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 38'),
(120791, 11643, 0, 'Azuremyst Isle, Copper Vein, spawn 39'),
(74959, 11644, 0, 'Eversong Woods, Copper Vein, spawn 30'),
(74960, 11644, 0, 'Eversong Woods, Copper Vein, spawn 31'),
(74961, 11644, 0, 'Eversong Woods, Copper Vein, spawn 32'),
(74962, 11644, 0, 'Eversong Woods, Copper Vein, spawn 33'),
(74963, 11644, 0, 'Eversong Woods, Copper Vein, spawn 34'),
(74964, 11644, 0, 'Eversong Woods, Copper Vein, spawn 35'),
(74965, 11644, 0, 'Eversong Woods, Copper Vein, spawn 36'),
(74966, 11644, 0, 'Eversong Woods, Copper Vein, spawn 37'),
(74967, 11644, 0, 'Eversong Woods, Copper Vein, spawn 38'),
(74968, 11644, 0, 'Eversong Woods, Copper Vein, spawn 39'),
(74969, 11644, 0, 'Eversong Woods, Copper Vein, spawn 40'),
(74970, 11644, 0, 'Eversong Woods, Copper Vein, spawn 41'),
(74971, 11644, 0, 'Eversong Woods, Copper Vein, spawn 42'),
(74972, 11644, 0, 'Eversong Woods, Copper Vein, spawn 43'),
(74973, 11644, 0, 'Eversong Woods, Copper Vein, spawn 44'),
(74974, 11644, 0, 'Eversong Woods, Copper Vein, spawn 45'),
(74975, 11644, 0, 'Eversong Woods, Copper Vein, spawn 46'),
(74976, 11644, 0, 'Eversong Woods, Copper Vein, spawn 47'),
(74977, 11644, 0, 'Eversong Woods, Copper Vein, spawn 48'),
(74978, 11644, 0, 'Eversong Woods, Copper Vein, spawn 49'),
(74979, 11644, 0, 'Eversong Woods, Copper Vein, spawn 50'),
(74980, 11644, 0, 'Eversong Woods, Copper Vein, spawn 51'),
(74981, 11644, 0, 'Eversong Woods, Copper Vein, spawn 52'),
(74982, 11644, 0, 'Eversong Woods, Copper Vein, spawn 53'),
(74983, 11644, 0, 'Eversong Woods, Copper Vein, spawn 54'),
(74984, 11644, 0, 'Eversong Woods, Copper Vein, spawn 55'),
(74985, 11644, 0, 'Eversong Woods, Copper Vein, spawn 56'),
(74986, 11644, 0, 'Eversong Woods, Copper Vein, spawn 57'),
(74987, 11644, 0, 'Eversong Woods, Copper Vein, spawn 58'),
(120618, 11644, 0, 'Eversong Woods, Copper Vein, spawn 59'),
(120325, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(120324, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134521, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134623, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134624, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134625, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134626, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134627, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134631, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134632, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134633, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134634, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134635, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134636, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134637, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134638, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134639, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134640, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134643, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134645, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134647, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134648, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134649, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134651, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134652, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134653, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134654, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134656, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134658, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134659, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134661, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134662, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134663, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134664, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134665, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134667, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134669, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134670, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134673, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134674, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134675, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134676, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134678, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134680, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134681, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134682, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134683, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134684, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134685, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134689, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134690, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134691, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134693, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134695, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134696, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134700, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134702, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134704, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134705, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134706, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134707, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134708, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134711, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134712, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134713, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134714, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134715, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134716, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134720, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134721, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134722, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134723, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134724, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134725, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134726, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134728, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134729, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134731, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134732, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134733, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134734, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134735, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134736, 11646, 0, 'Mineral Veins - Bloodmyst Isle'),
(2134737, 11646, 0, 'Mineral Veins - Bloodmyst Isle');

INSERT INTO `gameobject` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`, `ScriptName`, `VerifiedBuild`) VALUES 
(4648, 3763, 1, 0, 0, 1, 1, -1709, -3472, 96.624, -1, 0, 0, -0.636078, 0.771625, 900, 100, 1, '', 0),
(4649, 1731, 1, 0, 0, 1, 1, -2000.81, -1117.73, 83.8292, -0.453786, 0, 0, 0.224951, -0.97437, 900, 100, 1, '', 0),
(4650, 1731, 0, 0, 0, 1, 1, 895.387, 1604.67, 44.542, 5.46, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4652, 1731, 1, 0, 0, 1, 1, 6061.28, 89.874, 44.249, 0, 0, 0, -0.233445, 0.97237, 900, 100, 1, '', 0),
(4653, 1731, 0, 0, 0, 1, 1, 306.395, 1558.8, 134.429, 1.798, 0, 0, 0.782608, 0.622515, 900, 100, 1, '', 0),
(4661, 1731, 1, 0, 0, 1, 1, -315.871, -5035.62, 28.281, 0.637474, 0, 0, 0.449502, 0.893279, 900, 100, 1, '', 0),
(4662, 1731, 0, 0, 0, 1, 1, 2400.78, 569.456, 42.212, 2.74, 0, 0, 0.979925, 0.199368, 900, 100, 1, '', 0),
(4663, 1731, 0, 0, 0, 1, 1, -4035, -2924, 9.719, 5.223, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4664, 1731, 1, 0, 0, 1, 1, 497.183, -4427, 47.452, 1.484, 0, 0, 0.67559, 0.737277, 900, 100, 1, '', 0),
(4665, 3763, 1, 0, 0, 1, 1, -3415, -2304, 114.538, 1.03, 0, 0, 0.492424, 0.870356, 900, 100, 1, '', 0),
(4667, 1731, 1, 0, 0, 1, 1, 647.757, -4755.22, 22.2529, -1.69297, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4668, 1731, 0, 0, 0, 1, 1, -10782.3, 1400.45, 23.0415, 1.71042, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4669, 2055, 0, 0, 0, 1, 1, -8804, -1930, 125.48, 3.048, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4670, 1731, 1, 0, 0, 1, 1, 463.742, -4203, 26.989, 5.652, 0, 0, 0.31042, -0.9506, 900, 100, 1, '', 0),
(4671, 1731, 0, 0, 0, 1, 1, -10008.9, 878.734, 38.924, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(4674, 1731, 0, 0, 0, 1, 1, 847.164, 369.173, 22.683, -3, 0, 0, -0.999048, 0.04362, 900, 100, 1, '', 0),
(4675, 1731, 1, 0, 0, 1, 1, 193.139, -4449, 32.768, 1.497, 0, 0, 0.680605, 0.73265, 900, 100, 1, '', 0),
(4677, 1731, 1, 0, 0, 1, 1, 110.429, -4593.55, 69.8251, 2.75761, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4681, 3763, 1, 0, 0, 1, 1, -1088, -2223, 67.857, -2, 0, 0, -0.891007, 0.453991, 900, 100, 1, '', 0),
(4682, 1731, 1, 0, 0, 1, 1, -1530.36, -3934.37, 11.9037, -2.77507, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4685, 3763, 1, 0, 0, 1, 1, -1019.65, -2055.23, 63.3198, -1.39626, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4687, 1731, 0, 0, 0, 1, 1, 1144.8, 1857.25, 27.916, 2.087, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4688, 1731, 1, 0, 0, 1, 1, 6636.36, -84.0922, 30.0194, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1, '', 0),
(4689, 1731, 0, 0, 0, 1, 1, -2877, -1713, 8.906, 3.341, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4690, 1731, 1, 0, 0, 1, 1, -4977, -1838, -38, 5.275, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4692, 3763, 1, 0, 0, 1, 1, -2751, -1867, 93.906, 2.478, 0, 0, 0.945519, 0.325568, 900, 100, 1, '', 0),
(4693, 1731, 1, 0, 0, 1, 1, 1127.29, -4691.03, 19.824, -2.28638, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4694, 1731, 0, 0, 0, 1, 1, 1196.7, 1185.35, 49.815, 2.492, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4696, 1731, 1, 0, 0, 1, 1, -3050.33, 316.995, 151.141, 2.859, 0, 0, 0.990066, 0.140601, 900, 100, 1, '', 0),
(4697, 1731, 0, 0, 0, 1, 1, -3049, -1892, 7.422, 0.56, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4698, 1731, 1, 0, 0, 1, 1, -4937, -1566, -26, 5.799, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4701, 1731, 1, 0, 0, 1, 1, -5576.58, -3465.93, -46.912, -0.715585, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4702, 1731, 1, 0, 0, 1, 1, 1178.94, -4858.16, 24.753, 0.645772, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4703, 1731, 0, 0, 0, 1, 1, -4337, -2650, 101.786, 3.08, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4705, 1731, 0, 0, 0, 1, 1, 1022.67, 1784.11, 20.243, 2.374, 0, 0, 0.927184, 0.374607, 900, 100, 1, '', 0),
(4706, 3763, 1, 0, 0, 1, 1, -3307.13, -2266.09, 93.2678, 1.83259, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4711, 1731, 1, 0, 0, 1, 1, 5665.6, 352.15, 17.621, 0.698, 0, 0, 0.34202, 0.939693, 900, 100, 1, '', 0),
(4714, 1731, 1, 0, 0, 1, 1, 696.49, 313.62, 72.7516, 3.726, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4715, 1731, 1, 0, 0, 1, 1, -4629, -1709, -27, 5.148, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4716, 1731, 0, 0, 0, 1, 1, -3066.62, -1932.85, 4.93504, 2.33874, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4717, 1731, 0, 0, 0, 1, 1, 1730.85, 994.12, 56.659, 1.466, 0, 0, 0.669131, 0.743145, 900, 100, 1, '', 0),
(4722, 1731, 1, 0, 0, 1, 1, -507, -5222, 13.401, 5.888, 0, 0, 0.196252, -0.980553, 900, 100, 1, '', 0),
(4723, 3763, 1, 0, 0, 1, 1, 94.9738, -1945.36, 79.6132, -2.25147, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4724, 1731, 1, 0, 0, 1, 1, 1466.95, -4775, 8.583, 0.244, 0, 0, 0.121869, 0.992546, 900, 100, 1, '', 0),
(4725, 1731, 1, 0, 0, 1, 1, 1199.76, -4642, 19.916, 3.019, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4726, 3763, 1, 0, 0, 1, 1, -419, -2187, 143.579, -1, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4728, 1731, 1, 0, 0, 1, 1, -5079, -2066, -51, 3.397, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4732, 1731, 0, 0, 0, 1, 1, 2219.76, 607.936, 29.159, -2, 0, 0, -0.766044, 0.642788, 900, 100, 1, '', 0),
(4733, 1731, 1, 0, 0, 1, 1, -9, -4221, 94.547, 2.251, 0, 0, 0.902585, 0.430511, 900, 100, 1, '', 0),
(4734, 1731, 1, 0, 0, 1, 1, -4624, -1722, -29, -1, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4735, 1731, 1, 0, 0, 1, 1, -5073, -1271, -30, 4.463, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4736, 1731, 1, 0, 0, 1, 1, 48.07, -281, 22.871, 4.64, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4737, 1731, 1, 0, 0, 1, 1, -191.015, 1279.91, 96.0695, -0.383972, 0, 0, 0.190809, -0.981627, 900, 100, 1, '', 0),
(4740, 1731, 1, 0, 0, 1, 1, 37.761, -4547, 83.901, 2.107, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4741, 1731, 0, 0, 0, 1, 1, -9767.09, -2316.49, 70.4724, 2.26893, 0, 0, 0.906308, 0.422618, 900, 100, 1, '', 0),
(4742, 3763, 1, 0, 0, 1, 1, -426, -3247, 158.574, 0.96, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4745, 1731, 1, 0, 0, 1, 1, 389.877, -4049, 38.833, 0, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4746, 1731, 1, 0, 0, 1, 1, 5215.59, 316.743, 42.787, 0.75, 0, 0, 0.366501, 0.930417, 900, 100, 1, '', 0),
(4747, 1731, 1, 0, 0, 1, 1, 5130.03, 146.779, 48.287, 0, 0, 0, -0.190809, 0.981627, 900, 100, 1, '', 0),
(4748, 1731, 0, 0, 0, 1, 1, -4361, -2925, 34.976, 1.875, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4749, 1731, 1, 0, 0, 1, 1, 1563.38, -309, 64.214, 6.082, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4750, 1731, 0, 0, 0, 1, 1, -142.785, 1280.16, 54.9085, -2.58308, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4754, 1731, 1, 0, 0, 1, 1, 1226.43, 1375.26, 100.911, 4.113, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4755, 1731, 0, 0, 0, 1, 1, -8999.95, -3221.55, 112.057, 0.591, 0, 0, 0.29099, 0.956726, 900, 100, 1, '', 0),
(4757, 1731, 0, 0, 0, 1, 1, 96.613, 1030.55, 124.76, 3.651, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4758, 1731, 0, 0, 0, 1, 1, 1300.18, 1395.15, 61.724, 1.515, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4759, 1731, 1, 0, 0, 1, 1, -59.494, -4808.29, 25.549, 6.08592, 0, 0, 0.793363, 0.608748, 900, 100, 1, '', 0),
(4760, 1731, 1, 0, 0, 1, 1, -4481, -932, -50, 3.672, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4761, 1731, 0, 0, 0, 1, 1, -9052.53, -2680.67, 132.473, 1.8675, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4762, 1731, 1, 0, 0, 1, 1, -5053, -2034, -51, 2.644, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4763, 1731, 1, 0, 0, 1, 1, -879, -4727, 29.001, 6.179, 0, 0, 0.051822, -0.998656, 900, 100, 1, '', 0),
(4764, 1731, 1, 0, 0, 1, 1, -877, -4736, 29.884, 1.937, 0, 0, 0.824126, 0.566406, 900, 100, 1, '', 0),
(4765, 1731, 0, 0, 0, 1, 1, -9186, -2912, 112.139, 2.217, 0, 0, 0.894934, 0.446198, 900, 100, 1, '', 0),
(4766, 1731, 1, 0, 0, 1, 1, -1996.73, 423.48, 133.59, 1.25664, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4767, 1731, 0, 0, 0, 1, 1, -9116.97, -2785.5, 104.123, -1.11701, 0, 0, 0.529919, -0.848048, 900, 100, 1, '', 0),
(4768, 1731, 1, 0, 0, 1, 1, -4917, -1993, -22, 3.779, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4769, 3763, 1, 0, 0, 1, 1, 405.748, -2479, 95.441, 0.698, 0, 0, 0.34202, 0.939693, 900, 100, 1, '', 0),
(4770, 1731, 1, 0, 0, 1, 1, 646.033, -4629, 11.574, 4.39, 0, 0, 0.811522, -0.584322, 900, 100, 1, '', 0),
(4772, 1731, 1, 0, 0, 1, 1, 1417.42, -4711.25, -0.304713, 0.541051, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4774, 1731, 1, 0, 0, 1, 1, -5208.44, -1578.95, -38.6579, -0.767944, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4778, 3763, 1, 0, 0, 1, 1, -1900, -1748, 92.571, -2, 0, 0, -0.824126, 0.566406, 900, 100, 1, '', 0),
(4780, 3763, 1, 0, 0, 1, 1, -1959, -2685, 95.0178, -2, 0, 0, -0.909961, 0.414694, 900, 100, 1, '', 0),
(4781, 1731, 0, 0, 0, 1, 1, -11450, 1723.14, 11.694, 2.304, 0, 0, 0.913545, 0.406737, 900, 100, 1, '', 0),
(4782, 1731, 1, 0, 0, 1, 1, 4573.88, 559.401, 1.293, 1.069, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4783, 1731, 1, 0, 0, 1, 1, -2170, -1180, 39.063, -3, 0, 0, -0.983255, 0.182235, 900, 100, 1, '', 0),
(4784, 1731, 1, 0, 0, 1, 1, 922.413, -4280, -5, 3.482, 0, 0, 0.985543, -0.169428, 900, 100, 1, '', 0),
(4785, 1731, 1, 0, 0, 1, 1, -4846, -1692, -33, -1, 0, 0, -0.587785, 0.809017, 900, 100, 1, '', 0),
(4786, 1731, 1, 0, 0, 1, 1, 4594.8, 576.503, 1.253, 2.757, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4787, 1731, 0, 0, 0, 1, 1, 2782.9, -830, 155.883, 2.234, 0, 0, 0.898794, 0.438371, 900, 100, 1, '', 0),
(4788, 1731, 1, 0, 0, 1, 1, 1125.93, -4498, 20.325, 4.77728, 0, 0, 0.704938, 0.709268, 900, 100, 1, '', 0),
(4794, 1731, 1, 0, 0, 1, 1, -2716.08, -1351.25, 54.7605, 0.10472, 0, 0, 0.052336, 0.99863, 900, 100, 1, '', 0),
(4797, 1731, 1, 0, 0, 1, 1, 4602.73, 597.9, 1.681, 2.529, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4799, 1731, 1, 0, 0, 1, 1, 6314.4, 83.032, 44.354, 1.745, 0, 0, 0.766044, 0.642788, 900, 100, 1, '', 0),
(4800, 1731, 1, 0, 0, 1, 1, 919.639, 47.729, 75.064, 2.97, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4801, 1731, 0, 0, 0, 1, 1, -4140, -2456, 185.147, 1.03, 0, 0, 0.492423, 0.870356, 900, 100, 1, '', 0),
(4802, 2055, 0, 0, 0, 1, 1, -8835, -1927, 125.082, 2.03, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4803, 1731, 1, 0, 0, 1, 1, -840.207, 175.43, -2.1451, -0.820303, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4804, 1731, 1, 0, 0, 1, 1, -758.024, 151.076, 19.3412, -1.72788, 0, 0, 0.760406, -0.649448, 900, 100, 1, '', 0),
(4805, 1731, 1, 0, 0, 1, 1, -5682.08, -3400.04, -39.1371, 2.02458, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4807, 1731, 1, 0, 0, 1, 1, 4562.35, 820.458, 3.31582, 2.70526, 0, 0, 0.976296, 0.21644, 900, 100, 1, '', 0),
(4809, 1731, 1, 0, 0, 1, 1, 6608.39, 233.555, 45.378, -1, 0, 0, -0.601815, 0.798635, 900, 100, 1, '', 0),
(4810, 1731, 0, 0, 0, 1, 1, -4060, -2416, 121.647, 5.448, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4811, 3763, 1, 0, 0, 1, 1, -601.688, -2027.34, 145.97, 1.37881, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4812, 1731, 1, 0, 0, 1, 1, -2508.05, -1484.73, 67.8085, -1.76278, 0, 0, 0.771625, -0.636078, 900, 100, 1, '', 0),
(4813, 1731, 1, 0, 0, 1, 1, -993, -4497, 29.336, 2.604, 0, 0, 0.964043, 0.265746, 900, 100, 1, '', 0),
(4815, 1731, 1, 0, 0, 1, 1, -246.69, -5237.25, 2.6372, 0, 0, 0, -0.087156, 0.996195, 900, 100, 1, '', 0),
(4816, 1731, 1, 0, 0, 1, 1, 1465.76, -104, 33.935, 0.157, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4818, 3763, 1, 0, 0, 1, 1, -3172.37, -1849.55, 99.9321, -2.72271, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4822, 1731, 0, 0, 0, 1, 1, -9138, -3187, 107.33, -2, 0, 0, -0.766044, 0.642788, 900, 100, 1, '', 0),
(4824, 1731, 1, 0, 0, 1, 1, 6354.29, 119.985, 22.103, 1.588, 0, 0, 0.71325, 0.700909, 900, 100, 1, '', 0),
(4825, 3763, 1, 0, 0, 1, 1, -592, -2116, 186.799, 1.653, 0, 0, 0.735462, 0.677566, 900, 100, 1, '', 0),
(4826, 1731, 1, 0, 0, 1, 1, -5113, -1689, -34, 5.623, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4829, 1731, 1, 0, 0, 1, 1, -961, -183, 22.172, 0.454, 0, 0, 0.224951, 0.97437, 900, 100, 1, '', 0),
(4830, 1731, 1, 0, 0, 1, 1, -1385, 326.601, 33.147, 2.688, 0, 0, 0.97437, 0.224951, 900, 100, 1, '', 0),
(4831, 1731, 1, 0, 0, 1, 1, 806.486, -1391, 92.108, 4.69, 0, 0, 0.714839, -0.699289, 900, 100, 1, '', 0),
(4832, 1731, 1, 0, 0, 1, 1, 770.491, 336.971, 75.4982, 0.322, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4836, 1731, 1, 0, 0, 1, 1, -1118, -485, -40, 0, 0, 0, -0.207912, 0.978148, 900, 100, 1, '', 0),
(4843, 1731, 0, 0, 0, 1, 1, -9256, -3097, 100.701, 1.972, 0, 0, 0.833886, 0.551937, 900, 100, 1, '', 0),
(4844, 1731, 1, 0, 0, 1, 1, -2037, 299.28, 127.223, 0.336, 0, 0, 0.16728, 0.985909, 900, 100, 1, '', 0),
(4846, 1731, 0, 0, 0, 1, 1, -581, 1097.44, 90.111, 1.239, 0, 0, 0.580703, 0.814115, 900, 100, 1, '', 0),
(4848, 1731, 0, 0, 0, 1, 1, -3153, -1198, 7.912, 2.401, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4850, 1731, 1, 0, 0, 1, 1, -5090.03, -1587.31, -25.4564, 2.80997, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4851, 1731, 1, 0, 0, 1, 1, -4384.31, -929.796, -53.7144, -2.26892, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4854, 1731, 1, 0, 0, 1, 1, -516, 78.965, 61.133, 1.449, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4855, 1731, 1, 0, 0, 1, 1, -2089, -1113, 38.546, 5.599, 0, 0, 0.33526, -0.942126, 900, 100, 1, '', 0),
(4856, 1731, 1, 0, 0, 1, 1, -5656, -2561, -38, 0.713, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4861, 1731, 0, 0, 0, 1, 1, -473.576, 1463.86, 27.1123, 0.261798, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4863, 1731, 0, 0, 0, 1, 1, -9637.25, -2812.56, 60.8158, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1, '', 0),
(4864, 1731, 1, 0, 0, 1, 1, -2820.62, -438.43, 24.2419, -0.977384, 0, 0, 0.469472, -0.882948, 900, 100, 1, '', 0),
(4865, 1731, 1, 0, 0, 1, 1, 3767.14, 637.685, 9.60146, 5.38385, 0, 0, 0.434666, -0.900592, 900, 100, 1, '', 0),
(4866, 1731, 1, 0, 0, 1, 1, 3414.91, 880.762, 21.9763, 4.46493, 0, 0, 0.788967, -0.614435, 900, 100, 1, '', 0),
(4867, 1731, 1, 0, 0, 1, 1, 2991.59, 467.605, 17.3735, 2.77397, 0, 0, 0.983154, 0.182779, 900, 100, 1, '', 0),
(4868, 1731, 1, 0, 0, 1, 1, 3982.12, 209.46, 10.8324, 0.869379, 0, 0, 0.421129, 0.907001, 900, 100, 1, '', 0),
(4869, 1731, 1, 0, 0, 1, 1, 4032.55, 121.854, 8.0798, 5.1286, 0, 0, 0.545759, -0.837942, 900, 100, 1, '', 0),
(4870, 1731, 1, 0, 0, 1, 1, 3836.78, -228.029, 9.44121, 4.12256, 0, 0, 0.882106, -0.471051, 900, 100, 1, '', 0),
(4871, 1731, 1, 0, 0, 1, 1, 3680.38, -171.198, 8.77558, 4.91188, 0, 0, 0.633177, -0.774007, 900, 100, 1, '', 0),
(4872, 1731, 1, 0, 0, 1, 1, 3413.55, -12.685, 8.58239, 4.70611, 0, 0, 0.709324, -0.704883, 900, 100, 1, '', 0),
(4873, 1731, 1, 0, 0, 1, 1, 3166.67, 142.232, 10.8116, 3.55393, 0, 0, 0.978823, -0.204711, 900, 100, 1, '', 0),
(4874, 1731, 1, 0, 0, 1, 1, 2527.21, 122.553, 98.3699, 2.20776, 0, 0, 0.89296, 0.450136, 900, 100, 1, '', 0),
(4875, 1731, 1, 0, 0, 1, 1, 2602.06, 81.6833, 97.8526, 0.468889, 0, 0, 0.232303, 0.972643, 900, 100, 1, '', 0),
(4876, 1731, 1, 0, 0, 1, 1, 2417.39, 389.26, 108.253, 5.13337, 0, 0, 0.543758, -0.839242, 900, 100, 1, '', 0),
(4877, 1731, 1, 0, 0, 1, 1, 2420.83, -137.195, 97.6321, 5.83002, 0, 0, 0.224651, -0.974439, 900, 100, 1, '', 0),
(4878, 1731, 1, 0, 0, 1, 1, 2372.5, -232.726, 101.306, 2.72848, 0, 0, 0.978743, 0.205091, 900, 100, 1, '', 0),
(4879, 1731, 1, 0, 0, 1, 1, 2471.51, -334.146, 103.695, 0.581204, 0, 0, 0.286529, 0.958072, 900, 100, 1, '', 0),
(4880, 1731, 1, 0, 0, 1, 1, 2381.85, -494.339, 111.571, 5.06818, 0, 0, 0.570817, -0.821077, 900, 100, 1, '', 0),
(4881, 1731, 1, 0, 0, 1, 1, 3024.65, -233.572, 132.981, 4.8145, 0, 0, 0.670099, -0.742271, 900, 100, 1, '', 0),
(4882, 1731, 1, 0, 0, 1, 1, 3196.77, -124.978, 107.313, 0.822322, 0, 0, 0.399674, 0.916657, 900, 100, 1, '', 0),
(4883, 1731, 1, 0, 0, 1, 1, 3116.76, -326.704, 131.087, 2.99787, 0, 0, 0.997419, 0.071797, 900, 100, 1, '', 0),
(4884, 1731, 1, 0, 0, 1, 1, 3143.46, -606.541, 165.709, 5.18442, 0, 0, 0.522159, -0.852848, 900, 100, 1, '', 0),
(4885, 1731, 1, 0, 0, 1, 1, 2973.86, -691.858, 173.951, 1.55039, 0, 0, 0.699854, 0.714286, 900, 100, 1, '', 0),
(4886, 1731, 1, 0, 0, 1, 1, 2885.72, -790.483, 167.812, 5.21741, 0, 0, 0.508022, -0.861344, 900, 100, 1, '', 0),
(4887, 1731, 1, 0, 0, 1, 1, 2727.19, -805.128, 151.909, 2.15043, 0, 0, 0.879692, 0.475543, 900, 100, 1, '', 0),
(4888, 1731, 1, 0, 0, 1, 1, 2622.01, -966.571, 133.405, 1.54882, 0, 0, 0.699293, 0.714835, 900, 100, 1, '', 0),
(4889, 1731, 1, 0, 0, 1, 1, 2586.05, -1105.28, 131.529, 5.20799, 0, 0, 0.512076, -0.85894, 900, 100, 1, '', 0),
(4890, 1731, 1, 0, 0, 1, 1, 2730.42, -1262.76, 181.695, -1.79769, 0, 0, 0.782608, -0.622515, 900, 100, 1, '', 0),
(4891, 1731, 1, 0, 0, 1, 1, 2548.3, -1332.13, 160.075, 4.3684, 0, 0, 0.817694, -0.575652, 900, 100, 1, '', 0),
(4892, 1731, 1, 0, 0, 1, 1, 1796.47, -1630.64, 91.2559, 1.77658, 0, 0, 0.775996, 0.630738, 900, 100, 1, '', 0),
(4893, 1731, 1, 0, 0, 1, 1, 1439.69, -1940.5, 106.255, 1.71925, 0, 0, 0.757597, 0.652722, 900, 100, 1, '', 0),
(4894, 1731, 1, 0, 0, 1, 1, 1900.28, -2425.91, 88.1028, 1.5661, 0, 0, 0.705445, 0.708765, 900, 100, 1, '', 0),
(4895, 1731, 1, 0, 0, 1, 1, 2743.54, -2850.19, 152.638, 1.05638, 0, 0, 0.50397, 0.863721, 900, 100, 1, '', 0),
(4896, 1731, 1, 0, 0, 1, 1, 2755.11, -3065.72, 161.034, 5.98789, 0, 0, 0.14711, -0.98912, 900, 100, 1, '', 0),
(4897, 1731, 1, 0, 0, 1, 1, 2840.93, -2999.24, 171.869, 0.625194, 0, 0, 0.307531, 0.951538, 900, 100, 1, '', 0),
(4898, 3763, 1, 0, 0, 1, 1, 107.599, -1835.43, 102.923, 1.27706, 0, 0, 0.596016, 0.802972, 900, 100, 1, '', 0),
(4899, 3763, 1, 0, 0, 1, 1, 80.1229, -1813.46, 100.563, 0.566275, 0, 0, 0.27937, 0.960184, 900, 100, 1, '', 0),
(4900, 3763, 1, 0, 0, 1, 1, 52.4541, -1773.29, 104.064, 1.17653, 0, 0, 0.554917, 0.831905, 900, 100, 1, '', 0),
(4901, 3763, 1, 0, 0, 1, 1, 163.719, -1906.58, 96.5737, 4.81135, 0, 0, 0.671268, -0.741215, 900, 100, 1, '', 0),
(4902, 3763, 1, 0, 0, 1, 1, 79.3855, -2183.33, 99.2929, 5.02576, 0, 0, 0.588104, -0.808785, 900, 100, 1, '', 0),
(4903, 3763, 1, 0, 0, 1, 1, 84.5532, -2268.01, 106.783, 0.543493, 0, 0, 0.268414, 0.963304, 900, 100, 1, '', 0),
(4904, 3763, 1, 0, 0, 1, 1, -11.7106, -2513.13, 106.123, 3.00964, 0, 0, 0.997824, 0.0659279, 900, 100, 1, '', 0),
(4905, 3763, 1, 0, 0, 1, 1, 151.739, -2540.53, 103.42, 1.22679, 0, 0, 0.575646, 0.817699, 900, 100, 1, '', 0),
(4906, 3763, 1, 0, 0, 1, 1, 286.965, -2491.21, 96.9229, 0.727275, 0, 0, 0.355676, 0.934609, 900, 100, 1, '', 0),
(4907, 3763, 1, 0, 0, 1, 1, 424.006, -2508.96, 99.3446, 0.198702, 0, 0, 0.0991876, 0.995069, 900, 100, 1, '', 0),
(4908, 3763, 1, 0, 0, 1, 1, -105.957, -3021.48, 94.3172, 5.22132, 0, 0, 0.506339, -0.862335, 900, 100, 1, '', 0),
(4909, 3763, 1, 0, 0, 1, 1, -127.005, -3231.17, 117.079, 4.87417, 0, 0, 0.647658, -0.761931, 900, 100, 1, '', 0),
(4910, 3763, 1, 0, 0, 1, 1, -294.109, -3269.11, 169.451, 3.76676, 0, 0, 0.951542, -0.307517, 900, 100, 1, '', 0),
(4911, 3763, 1, 0, 0, 1, 1, 18.0536, -3379.66, 100.804, 0.173563, 0, 0, 0.0866726, 0.996237, 900, 100, 1, '', 0),
(4912, 3763, 1, 0, 0, 1, 1, -75.023, -3419.94, 98.4463, 3.79425, 0, 0, 0.947226, -0.320567, 900, 100, 1, '', 0),
(4913, 3763, 1, 0, 0, 1, 1, -423.354, -3601.65, 97.3639, 6.13159, 0, 0, 0.0757235, -0.997129, 900, 100, 1, '', 0),
(4914, 1731, 1, 0, 0, 1, 1, -5630, -2083, -50, 4.229, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4915, 1731, 1, 0, 0, 1, 1, -4647.86, -1294.27, -40.6488, -0.558504, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4916, 1731, 1, 0, 0, 1, 1, 871.858, -4230, -11, 3.379, 0, 0, 0.992971, -0.118355, 900, 100, 1, '', 0),
(4918, 3763, 1, 0, 0, 1, 1, -1345, -2233, 98.721, -2, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4919, 1731, 1, 0, 0, 1, 1, -85, -354, 29.077, 1.785, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4920, 1731, 1, 0, 0, 1, 1, 2092.82, 1024.63, 237.424, 1.41372, 0, 0, 0.649448, 0.760406, 900, 100, 1, '', 0),
(4925, 1731, 1, 0, 0, 1, 1, 713.16, -4010.18, 9.61455, -0.698132, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4926, 1731, 0, 0, 0, 1, 1, 2478.94, 56.65, 12.432, 1.292, 0, 0, 0.601815, 0.798635, 900, 100, 1, '', 0),
(4927, 3763, 1, 0, 0, 1, 1, -1556, -2081, 87.885, 0, 0, 0, -0.087156, 0.996195, 900, 100, 1, '', 0),
(4929, 1731, 0, 0, 0, 1, 1, 2133.66, -315.844, 57.8635, 2.21656, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4930, 1731, 0, 0, 0, 1, 1, -9602.69, 969.846, 39.3992, -2.67035, 0, 0, 0, 1, 900, 100, 1, '', 0),
(4931, 3763, 1, 0, 0, 1, 1, -3307.45, -1786.93, 102.86, 0.663223, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4933, 1731, 1, 0, 0, 1, 1, -4980.47, -1215.82, -45.5943, -0.122173, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4934, 1731, 1, 0, 0, 1, 1, 1458.08, -4808, 11.859, -2, 0, 0, -0.85264, 0.522499, 900, 100, 1, '', 0),
(4936, 1731, 1, 0, 0, 1, 1, 4298.69, 940.355, 62.732, -3, 0, 0, -0.992546, 0.121869, 900, 100, 1, '', 0),
(4937, 3763, 1, 0, 0, 1, 1, -2636.49, -1761.06, 105.159, -2.70526, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4938, 1731, 1, 0, 0, 1, 1, 6799.74, 231.752, 25.522, -1, 0, 0, -0.333807, 0.942641, 900, 100, 1, '', 0),
(4941, 1731, 0, 0, 0, 1, 1, 2224.62, -332, 71.859, 0, 0, 0, -0.017453, 0.999848, 900, 100, 1, '', 0),
(4942, 1731, 1, 0, 0, 1, 1, 66.278, -4533, 66.611, 2.471, 0, 0, 0.94432, 0.329028, 900, 100, 1, '', 0),
(4943, 1731, 1, 0, 0, 1, 1, 765.985, -3999, 24.124, -3, 0, 0, 0.981627, -0.190809, 900, 100, 1, '', 0),
(4945, 1731, 1, 0, 0, 1, 1, -784, -5564, 16.693, 0.681, 0, 0, 0.333807, 0.942641, 900, 100, 1, '', 0),
(4948, 1731, 1, 0, 0, 1, 1, 919.19, -4031, -13, 4.714, 0, 0, 0.706456, -0.707757, 900, 100, 1, '', 0),
(4949, 1731, 1, 0, 0, 1, 1, 7149.5, -285, 36.695, -1, 0, 0, -0.507538, 0.861629, 900, 100, 1, '', 0),
(4950, 1731, 1, 0, 0, 1, 1, 4031.87, 116.379, 10.8511, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1, '', 0),
(4952, 1731, 0, 0, 0, 1, 1, -2761, -1638, 8.36136, 2.571, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4954, 1731, 1, 0, 0, 1, 1, 207.442, -3865, 45.509, 4.674, 0, 0, 0.720517, -0.693438, 900, 100, 1, '', 0),
(4955, 1731, 1, 0, 0, 1, 1, 1141.85, -4684, 17.7031, 5.94, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4956, 1731, 1, 0, 0, 1, 1, -2290.48, 262.298, 84.6144, -0.872665, 0, 0, 0.422618, -0.906308, 900, 100, 1, '', 0),
(4959, 1731, 1, 0, 0, 1, 1, -420, -4898, 59.501, 4.375, 0, 0, 0.81589, -0.578208, 900, 100, 1, '', 0),
(4961, 1731, 0, 0, 0, 1, 1, -4034, -2472, 151.041, 4.721, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4962, 1731, 1, 0, 0, 1, 1, 7052.75, 297.974, 0.323, -1, 0, 0, -0.34202, 0.939693, 900, 100, 1, '', 0),
(4963, 1731, 1, 0, 0, 1, 1, 24.325, -3989, 49.571, 3.016, 0, 0, 0.998045, 0.062506, 900, 100, 1, '', 0),
(4965, 1731, 1, 0, 0, 1, 1, 7470.96, -1033.38, 16.8551, -2.96704, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4966, 1731, 1, 0, 0, 1, 1, 1117.3, -4850, 18.723, 2.619, 0, 0, 0.966114, 0.258117, 900, 100, 1, '', 0),
(4967, 1731, 1, 0, 0, 1, 1, 1572.45, 1415.66, 194.04, 0.401425, 0, 0, 0, 1, 900, 100, 1, '', 0),
(4968, 1731, 1, 0, 0, 1, 1, -5675, -3390, -34, 3.022, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4969, 1731, 1, 0, 0, 1, 1, 6867.76, -610, 54.861, 1.522, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4970, 1731, 1, 0, 0, 1, 1, 751.688, -4683.93, 30.1805, 1.72787, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4971, 1731, 1, 0, 0, 1, 1, 1555.19, 1329.2, 170.396, 0.516, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4973, 1731, 0, 0, 0, 1, 1, 784.642, 1691.71, 33.4731, -0.855211, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4974, 1731, 1, 0, 0, 1, 1, -5599, -3014, -46.7644, 0.809, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4975, 1731, 0, 0, 0, 1, 1, 2245.48, 1332.42, 38.1899, -2.93214, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4976, 1731, 1, 0, 0, 1, 1, 879.885, -3867.51, 34.1144, 2.82743, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4977, 1731, 0, 0, 0, 1, 1, -9414.11, -1916.16, 97.5622, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1, '', 0),
(4978, 1731, 1, 0, 0, 1, 1, -1391.02, -3936.81, 20.6872, 0.890117, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4979, 1731, 0, 0, 0, 1, 1, 383.233, 1054.9, 106.53, 2.618, 0, 0, 0.965926, 0.258819, 900, 100, 1, '', 0),
(4981, 1731, 1, 0, 0, 1, 1, -2437, -1487, 38.715, 0.698, 0, 0, 0.34202, 0.939693, 900, 100, 1, '', 0),
(4983, 1731, 1, 0, 0, 1, 1, 1056.59, -4808.49, 21.7389, -2.04204, 0, 0, 0, 1, 900, 255, 1, '', 0),
(4987, 1731, 0, 0, 0, 1, 1, -447, 1648.44, 13.009, 2.867, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4988, 1731, 1, 0, 0, 1, 1, -2587, -1468, 107.091, 1.564, 0, 0, 0.70483, 0.709376, 900, 100, 1, '', 0),
(4989, 1731, 1, 0, 0, 1, 1, 1780.35, 999.612, 158.326, 0.816, 0, 0, 0, 0, 900, 100, 1, '', 0),
(4991, 1731, 1, 0, 0, 1, 1, -999, -4500, 28.246, 0.698, 0, 0, 0.34202, 0.939693, 900, 100, 1, '', 0),
(4992, 1731, 1, 0, 0, 1, 1, 4896.26, 44.194, 60.931, 1.641, 0, 0, 0.731354, 0.681998, 900, 100, 1, '', 0),
(5005, 1731, 0, 0, 0, 1, 1, -9737.25, -2440.96, 70.0273, -1.79769, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5019, 1731, 1, 0, 0, 1, 1, 413.661, -4258.74, 32.9778, -1.39626, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5020, 3763, 1, 0, 0, 1, 1, -1823, -1876, 97.795, 0, 0, 0, -0.017453, 0.999848, 900, 100, 1, '', 0),
(5022, 1731, 1, 0, 0, 1, 1, 356.217, -4973, 27.051, 6.106, 0, 0, 0.088544, -0.996072, 900, 100, 1, '', 0),
(5023, 1731, 0, 0, 0, 1, 1, 912.864, 623.534, 53.645, 0.69, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5024, 1731, 0, 0, 0, 1, 1, -123, 1353.89, 94.635, 2.726, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5026, 1731, 0, 0, 0, 1, 1, -9623.88, -2158.55, 69.8939, -2.93214, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5027, 1731, 0, 0, 0, 1, 1, -9352.8, -1881.89, 74.445, 1.01316, 0, 0, 0.915428, 0.402483, 900, 100, 1, '', 0),
(5028, 1731, 0, 0, 0, 1, 1, -312, 944.819, 131.909, 6.117, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5029, 1731, 1, 0, 0, 1, 1, 67.037, -217, 20.389, 2.451, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5031, 1731, 1, 0, 0, 1, 1, -225, -547, 41.729, 3.401, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5035, 3763, 1, 0, 0, 1, 1, -1297.31, -3028.46, 72.4677, -1.44862, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5037, 3763, 1, 0, 0, 1, 1, -1110, -2139, 80.593, 5.06, 0, 0, 0.57422, -0.818701, 900, 100, 1, '', 0),
(5038, 1731, 1, 0, 0, 1, 1, 1241.87, -4940, 14.829, -3, 0, 0, -0.97237, 0.233445, 900, 100, 1, '', 0),
(5039, 1731, 0, 0, 0, 1, 1, 622.258, 1211.16, 86.023, 5.952, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5040, 1731, 1, 0, 0, 1, 1, -605, -4948, 48.027, 4.451, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5041, 1731, 1, 0, 0, 1, 1, 503.216, -3923.43, 23.0152, -2.80997, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5043, 1731, 0, 0, 0, 1, 1, -3123, -1427, 8.889, 0.32, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5044, 1731, 1, 0, 0, 1, 1, -1042, -4566, 45.508, 1.606, 0, 0, 0.71934, 0.694658, 900, 100, 1, '', 0),
(5045, 1731, 1, 0, 0, 1, 1, 1000.78, -4682, 33.097, 0.107, 0, 0, 0.053395, 0.998573, 900, 100, 1, '', 0),
(5046, 3763, 1, 0, 0, 1, 1, 814.53, -3039.36, 98.4465, -1.53589, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5047, 1731, 1, 0, 0, 1, 1, 26.8517, 1824.61, 127.604, -1.20428, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5048, 1731, 0, 0, 0, 1, 1, -3715, -2880, 5.163, 1.937, 0, 0, 0.824126, 0.566406, 900, 100, 1, '', 0),
(5053, 1731, 0, 0, 0, 1, 1, -9440.52, -2880.75, 76.799, -0.628319, 0, 0, 0.309017, -0.951056, 900, 100, 1, '', 0),
(5059, 1731, 0, 0, 0, 1, 1, 180.635, 1062.78, 92.608, 4.751, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5061, 1731, 1, 0, 0, 1, 1, -943, -5110, -9, 4.681, 0, 0, 0.71815, -0.695888, 900, 100, 1, '', 0),
(5063, 1731, 0, 0, 0, 1, 1, 2805.09, 741.591, 138.367, -1, 0, 0, -0.300706, 0.953717, 900, 100, 1, '', 0),
(5065, 1731, 0, 0, 0, 1, 1, -349, 992.667, 126.913, 2.678, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5066, 1731, 1, 0, 0, 1, 1, 6825.55, -664, 86.337, -1, 0, 0, -0.67559, 0.737277, 900, 100, 1, '', 0),
(5067, 3763, 1, 0, 0, 1, 1, -1349, -2965, 97.854, -3, 0, 0, -0.984808, 0.173648, 900, 100, 1, '', 0),
(5068, 1731, 0, 0, 0, 1, 1, 887.186, 1642.41, 34.843, 5.909, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5069, 1731, 0, 0, 0, 1, 1, -2739, -1694, 7.936, 0.58, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5070, 3763, 1, 0, 0, 1, 1, -1300, -3262, 97.866, -3, 0, 0, -0.989016, 0.147809, 900, 100, 1, '', 0),
(5072, 1731, 1, 0, 0, 1, 1, 2368.37, -502.473, 116.287, -1.22173, 0, 0, 0, 1, 180, 100, 1, '', 0),
(5073, 1731, 1, 0, 0, 1, 1, 6573.71, -219, 48.386, 2.95, 0, 0, 0.995396, 0.095846, 900, 100, 1, '', 0),
(5075, 3763, 1, 0, 0, 1, 1, -704, -2119, 170.004, 2.635, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5077, 3763, 1, 0, 0, 1, 1, -1066.33, -2085.51, 57.133, 2.44346, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5080, 1731, 1, 0, 0, 1, 1, -149.711, -5155.12, 22.047, 0.279, 0, 0, 0.139173, 0.990268, 900, 100, 1, '', 0),
(5083, 1731, 1, 0, 0, 1, 1, -710.721, -4951.73, 29.1461, 0.349065, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5084, 1731, 1, 0, 0, 1, 1, 243.719, 359.159, 69.786, -3, 0, 0, -0.997564, 0.069757, 900, 100, 1, '', 0),
(5085, 1731, 1, 0, 0, 1, 1, 1299.81, 1362.38, 159.663, -1.48353, 0, 0, 0.67559, -0.737277, 900, 100, 1, '', 0),
(5087, 1731, 0, 0, 0, 1, 1, 2208.11, 553.019, 34.0019, 0.523598, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5088, 1731, 1, 0, 0, 1, 1, -4911, -1282, -29.3489, 1.323, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5089, 1731, 1, 0, 0, 1, 1, 4560.8, 630.978, 28.017, 3.521, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5091, 1731, 0, 0, 0, 1, 1, 298.83, 1183.47, 90.898, 4.294, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5092, 1731, 0, 0, 0, 1, 1, -3195, -1538, 7.937, 0.892, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5093, 1731, 0, 0, 0, 1, 1, 970.928, 1637.48, 44.789, 1.986, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5094, 1731, 1, 0, 0, 1, 1, -615.527, -775.294, 56.9064, 1.41372, 0, 0, 0.649448, 0.760406, 900, 100, 1, '', 0),
(5096, 103713, 1, 0, 0, 1, 1, -4093.85, -2122.36, 55.9365, 1.74533, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5098, 1731, 0, 0, 0, 1, 1, 1046.06, 1433.04, 42.641, 0.469, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5100, 3763, 1, 0, 0, 1, 1, -1790, -1930, 122.051, -2, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5101, 1731, 0, 0, 0, 1, 1, 2659.03, 802.917, 114.841, 0.314, 0, 0, 0.156434, 0.987688, 900, 100, 1, '', 0),
(5102, 1731, 0, 0, 0, 1, 1, 1274.62, 1971.04, 17.53, 1.326, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5103, 1731, 1, 0, 0, 1, 1, 2761.35, -3058.57, 164.029, -0.488691, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5104, 1731, 0, 0, 0, 1, 1, 1235.25, 1540.25, 47.696, 5.078, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5106, 1731, 1, 0, 0, 1, 1, 900.322, -4608, 18.3222, 2.264, 0, 0, 0.905198, 0.424991, 900, 100, 1, '', 0),
(5108, 1731, 1, 0, 0, 1, 1, 2354.04, 1204.05, 338.593, 2.534, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5109, 1731, 0, 0, 0, 1, 1, -8961, -2664, 152.968, 4.575, 0, 0, 0.754015, -0.656857, 900, 100, 1, '', 0),
(5110, 1731, 1, 0, 0, 1, 1, 945.301, -4123, -12, 3.856, 0, 0, 0.936927, -0.349526, 900, 100, 1, '', 0),
(5111, 1731, 0, 0, 0, 1, 1, 831.482, 604.969, 34.86, 1.969, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5112, 1731, 0, 0, 0, 1, 1, -10507, 1976.45, 9.944, 1.018, 0, 0, 0.487484, 0.873132, 900, 100, 1, '', 0),
(5114, 1731, 1, 0, 0, 1, 1, -947, -4608, 25.595, 0.297, 0, 0, 0.147809, 0.989016, 900, 100, 1, '', 0),
(5127, 1731, 1, 0, 0, 1, 1, -1164.81, 609.84, 83.9524, 2.9147, 0, 0, 0.993572, 0.113203, 900, 100, 1, '', 0),
(5128, 1731, 0, 0, 0, 1, 1, -11134, 1440.32, 60.008, -2, 0, 0, -0.848048, 0.529919, 900, 100, 1, '', 0),
(5129, 3763, 1, 0, 0, 1, 1, -1069.42, -2112.45, 56.0496, 1.5708, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5131, 1731, 0, 0, 0, 1, 1, -710, 1204.41, 90.622, 0.044, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5132, 1731, 1, 0, 0, 1, 1, 791.334, -4036, -6, 3.273, 0, 0, 0.997842, -0.065666, 900, 100, 1, '', 0),
(5134, 3763, 1, 0, 0, 1, 1, -2056, -2826, 95.313, -2, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5135, 1731, 1, 0, 0, 1, 1, -5720.24, -3154.99, -28.4684, -0.593412, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5137, 1731, 1, 0, 0, 1, 1, 5161.25, 31.9684, 44.8402, 2.25148, 0, 0, 0.902585, 0.430511, 900, 100, 1, '', 0),
(5138, 1731, 1, 0, 0, 1, 1, 1447.74, 214.272, 16.954, 0.903, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5142, 1731, 1, 0, 0, 1, 1, 7277.69, -842, 37.668, 1.169, 0, 0, 0.551937, 0.833886, 900, 100, 1, '', 0),
(5145, 1731, 0, 0, 0, 1, 1, -10677.6, 863.392, 42.8536, -2.93215, 0, 0, 0.994522, -0.104529, 900, 100, 1, '', 0),
(5146, 1731, 1, 0, 0, 1, 1, -4981, -1205, -37, 3.21, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5148, 1731, 1, 0, 0, 1, 1, 1233.14, -275, 2.302, 4.548, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5149, 1731, 0, 0, 0, 1, 1, -9144, -2078, 125, 3.369, 0, 0, 0.094207, -0.995553, 900, 100, 1, '', 0),
(5150, 1731, 1, 0, 0, 1, 1, 1267.43, 759.141, 193.445, 0.767, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5151, 1731, 0, 0, 0, 1, 1, -3008, -1530, 6.995, 4.785, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5152, 1731, 1, 0, 0, 1, 1, 833.743, -4211, -1, 5.17, 0, 0, 0.528468, -0.848953, 900, 100, 1, '', 0),
(5153, 1731, 1, 0, 0, 1, 1, -2767, -205, 23.503, 1.414, 0, 0, 0.649448, 0.760406, 900, 100, 1, '', 0),
(5154, 1731, 0, 0, 0, 1, 1, 2716.62, -540, 106.939, 0.14, 0, 0, 0.069756, 0.997564, 900, 100, 1, '', 0),
(5155, 1731, 0, 0, 0, 1, 1, 1234.75, 2052.94, 10.635, 0.541, 0, 0, 0.267238, 0.96363, 900, 100, 1, '', 0),
(5156, 1731, 1, 0, 0, 1, 1, 6657.02, 89.426, 34.761, 0.122, 0, 0, 0.061049, 0.998135, 900, 100, 1, '', 0),
(5157, 1731, 1, 0, 0, 1, 1, -815.609, -868.47, 26.8822, -2.84489, 0, 0, 0.989016, -0.147809, 900, 100, 1, '', 0),
(5159, 1731, 0, 0, 0, 1, 1, -9141, -3080, 123.024, 3.054, 0, 0, 0.999048, 0.04362, 900, 100, 1, '', 0),
(5162, 3763, 1, 0, 0, 1, 1, -576, -2394, 119.873, 2.031, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5163, 1731, 1, 0, 0, 1, 1, -5255, -1554, -37, 6.202, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5164, 3763, 1, 0, 0, 1, 1, -176.022, -3579.29, 47.931, 0.209439, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5166, 1731, 1, 0, 0, 1, 1, 6725.17, 90.662, 29.634, 4.67, 0, 0, 0.722008, -0.691884, 900, 100, 1, '', 0),
(5167, 1731, 1, 0, 0, 1, 1, -317.643, -4847.5, 40.5401, -0.453785, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5168, 1731, 1, 0, 0, 1, 1, 1177.25, -4769, 18.966, 0, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5171, 1731, 0, 0, 0, 1, 1, -262, 1496.29, 47.785, 0.859, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5172, 1731, 0, 0, 0, 1, 1, -9741, -2102, 62.484, 2.845, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5173, 1731, 1, 0, 0, 1, 1, 1268.36, 1499.91, 92.6485, 1.276, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5174, 1731, 1, 0, 0, 1, 1, -5154, -2351, -43, 3.886, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5175, 3763, 1, 0, 0, 1, 1, -2595.26, -2242.73, 109.43, 1.43117, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5176, 1731, 1, 0, 0, 1, 1, -550, -4910, 45.558, 0, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5177, 1731, 1, 0, 0, 1, 1, -2806, -144, 22.177, 4.521, 0, 0, 0.771557, -0.636161, 900, 100, 1, '', 0),
(5180, 1731, 1, 0, 0, 1, 1, 2411.54, -856.929, 147.678, -0.069812, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5184, 1731, 0, 0, 0, 1, 1, -10462, 1951.66, 9.521, 4.751, 0, 0, 0.693435, -0.720519, 900, 100, 1, '', 0),
(5186, 1731, 1, 0, 0, 1, 1, 2287.31, 1126.64, 316.395, 2.828, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5187, 1731, 1, 0, 0, 1, 1, 546.318, -4874.02, 37.2483, -1.69297, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5188, 1731, 1, 0, 0, 1, 1, -5173, -2426, -50, -3, 0, 0, -0.961262, 0.275638, 900, 100, 1, '', 0),
(5189, 1731, 1, 0, 0, 1, 1, 7148.24, -934.898, 75.9276, 0.977384, 0, 0, 0.469472, 0.882948, 900, 100, 1, '', 0),
(5191, 1731, 1, 0, 0, 1, 1, 923.991, -3979, 27.253, -2, 0, 0, -0.913545, 0.406737, 900, 100, 1, '', 0),
(5199, 1731, 0, 0, 0, 1, 1, -9041.66, -3307.45, 107.945, 4.673, 0, 0, 0.720786, -0.693158, 900, 100, 1, '', 0),
(5200, 1731, 1, 0, 0, 1, 1, 7637.88, -1134, 72.186, 5.297, 0, 0, 0.473286, -0.880909, 900, 100, 1, '', 0),
(5201, 1731, 1, 0, 0, 1, 1, 5788.55, 180.098, 41.5897, -0.034907, 0, 0, 0.017452, -0.999848, 900, 100, 1, '', 0),
(5202, 1731, 1, 0, 0, 1, 1, 712.542, -4512.38, 19.8831, -1.71042, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5204, 1731, 1, 0, 0, 1, 1, 924.105, 1489.77, 14.2401, 0.575957, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5207, 1731, 0, 0, 0, 1, 1, -3834, -2867, 3.535, -3, 0, 0, 0.99863, -0.052336, 900, 100, 1, '', 0),
(5208, 1731, 0, 0, 0, 1, 1, -4153, -3039, 2.21, -2, 0, 0, -0.927184, 0.374607, 900, 100, 1, '', 0),
(5209, 1731, 0, 0, 0, 1, 1, -9756, 1008.48, 35.189, -1, 0, 0, -0.515038, 0.857167, 900, 100, 1, '', 0),
(5210, 1731, 0, 0, 0, 1, 1, 900.318, 1704.4, 29.177, -1, 0, 0, -0.5373, 0.843391, 900, 100, 1, '', 0),
(5211, 1731, 1, 0, 0, 1, 1, -5396.93, -1709.76, -38.7407, 2.87979, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5212, 1731, 0, 0, 0, 1, 1, -11018.8, 960.273, 38.1789, 0.90757, 0, 0, 0, 1, 900, 100, 1, '', 0),
(5213, 1731, 1, 0, 0, 1, 1, 845.625, -4164.17, -8.82255, -0.994837, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5214, 1731, 1, 0, 0, 1, 1, 358.909, -4596, 60.359, 4.485, 0, 0, 0.782814, -0.622256, 900, 100, 1, '', 0),
(5215, 1731, 0, 0, 0, 1, 1, -9484.96, -1976.89, 94.0196, 0.890117, 0, 0, 0, 1, 180, 100, 1, '', 0),
(5217, 1731, 0, 0, 0, 1, 1, 266, 1074.96, 96.534, 5.809, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5218, 1731, 1, 0, 0, 1, 1, 193.885, -4771, 14.324, 0.164, 0, 0, 0.081855, 0.996644, 900, 100, 1, '', 0),
(5219, 1731, 1, 0, 0, 1, 1, -2762, -634, 19.881, 0.541, 0, 0, 0.267238, 0.96363, 900, 100, 1, '', 0),
(5220, 3763, 1, 0, 0, 1, 1, -970.368, -3106.75, 114.669, -1.78023, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5221, 1731, 1, 0, 0, 1, 1, 4560.07, 587.813, 1.126, 5.591, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5223, 1731, 1, 0, 0, 1, 1, 188.034, -4793, 14.51, 3.147, 0, 0, 0.999997, -0.002613, 900, 100, 1, '', 0),
(5225, 1731, 1, 0, 0, 1, 1, -309, -5194, 22.23, 0, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5226, 1731, 1, 0, 0, 1, 1, 1245.91, 315.511, 27.965, 3.092, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5227, 1731, 0, 0, 0, 1, 1, -11309, 1854.9, 16.912, -2, 0, 0, -0.700909, 0.71325, 900, 100, 1, '', 0),
(5228, 1731, 0, 0, 0, 1, 1, -9049.53, -2379.63, 134.05, 0.117, 0, 0, 0.058555, 0.998284, 900, 100, 1, '', 0),
(5229, 1731, 0, 0, 0, 1, 1, 3100.4, -592, 126.385, 2.496, 0, 0, 0.948324, 0.317305, 900, 100, 1, '', 0),
(5230, 1731, 0, 0, 0, 1, 1, -249, 1431.17, 40.295, 0, 0, 0, -0.21644, 0.976296, 900, 100, 1, '', 0),
(5232, 1731, 0, 0, 0, 1, 1, 638.894, 1222.03, 85.574, -2, 0, 0, -0.681998, 0.731354, 900, 100, 1, '', 0),
(5233, 1731, 1, 0, 0, 1, 1, -713, -763, 44.168, -2, 0, 0, -0.681998, 0.731354, 900, 100, 1, '', 0),
(5235, 1731, 1, 0, 0, 1, 1, 7605.1, -1025, 40.266, -1, 0, 0, -0.382683, 0.92388, 900, 100, 1, '', 0),
(5236, 1731, 0, 0, 0, 1, 1, -9200.57, -2441.51, 60.5342, 0.538, 0, 0, 0.266006, 0.963971, 900, 100, 1, '', 0),
(5237, 1731, 1, 0, 0, 1, 1, -278, 967.098, 94.647, -3, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5238, 1731, 0, 0, 0, 1, 1, -4100, -2385, 117.557, 2.81, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5240, 1731, 1, 0, 0, 1, 1, -117.992, -4889.76, 20.688, 4.42659, 0, 0, 0.6018, 0.798647, 900, 100, 1, '', 0),
(5241, 1731, 1, 0, 0, 1, 1, 1153.52, -268, -9, 4.029, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5242, 1731, 0, 0, 0, 1, 1, -9668.72, -2031.46, 60.258, 0.480889, 0, 0, 0.778983, -0.627045, 900, 100, 1, '', 0),
(5244, 1731, 1, 0, 0, 1, 1, -552, -4832, 36.636, 4.135, 0, 0, 0.879262, -0.476338, 900, 100, 1, '', 0),
(5245, 1731, 1, 0, 0, 1, 1, -1844.6, -1004.5, 84.088, 2.74017, 0, 0, 0.979925, 0.199368, 900, 100, 1, '', 0),
(5246, 2055, 0, 0, 0, 1, 1, -8958, -1990, 137.616, 5.848, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5247, 1731, 1, 0, 0, 1, 1, -2808, -792, 18.084, 1.917, 0, 0, 0.818442, 0.574589, 900, 100, 1, '', 0),
(5248, 1731, 0, 0, 0, 1, 1, -9912, 1116.01, 37.625, -1, 0, 0, -0.267238, 0.96363, 900, 100, 1, '', 0),
(5250, 1731, 0, 0, 0, 1, 1, -4080.01, -2911.18, 4.63452, 0.977383, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5251, 1731, 0, 0, 0, 1, 1, -9233, -3283, 104.756, -2, 0, 0, -0.793353, 0.608761, 900, 100, 1, '', 0),
(5252, 1731, 0, 0, 0, 1, 1, -10326.8, 1437.81, 41.681, 1.55334, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5253, 3763, 1, 0, 0, 1, 1, -101.218, -2886.92, 91.6667, -2.07694, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5254, 1731, 1, 0, 0, 1, 1, -817, -4629, 44.872, 3.991, 0, 0, 0.911245, -0.411865, 900, 100, 1, '', 0),
(5255, 1731, 0, 0, 0, 1, 1, 825.814, 1157.32, 36.97, 0.281, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5256, 3763, 1, 0, 0, 1, 1, -2726, -2025, 113.39, 2.88, 0, 0, 0.991445, 0.130526, 900, 100, 1, '', 0),
(5257, 3763, 1, 0, 0, 1, 1, -738, -3626, 94.504, -2, 0, 0, -0.688354, 0.725375, 900, 100, 1, '', 0),
(5290, 1731, 530, 0, 0, 1, 1, -3198.77, -12358.7, 18.8168, -0.663225, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5291, 1731, 530, 0, 0, 1, 1, -3205.95, -12416.9, 3.10401, 0.959931, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5292, 1731, 530, 0, 0, 1, 1, -3127.35, -12482.8, 2.25965, -1.15192, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5293, 1731, 530, 0, 0, 1, 1, -3356.8, -12177.6, 41.1204, -1.32645, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5294, 1731, 530, 0, 0, 1, 1, -3875.6, -12775, 25.1323, 2.77507, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5295, 1731, 530, 0, 0, 1, 1, -3961.57, -12711.4, 82.02, 1.02974, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5296, 1731, 530, 0, 0, 1, 1, -4295.92, -12735.8, 21.8414, -1.0472, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5297, 1731, 530, 0, 0, 1, 1, -4302, -12927.6, 9.3698, -2.14675, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5298, 1731, 530, 0, 0, 1, 1, -4778.81, -11573.7, -2.75845, -0.820305, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5299, 1731, 530, 0, 0, 1, 1, -4774.76, -11635.7, -36.7166, -1.23918, 0, 0, 0, 1, 60, 255, 1, '', 0),
(5300, 1731, 530, 0, 0, 1, 1, -4702.54, -11540.2, -24.777, -0.733038, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5301, 1731, 530, 0, 0, 1, 1, -4773.14, -11467.5, 24.643, -1.91986, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5302, 1731, 530, 0, 0, 1, 1, -5045.83, -11072.7, 29.4929, 1.8675, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5303, 1731, 530, 0, 0, 1, 1, -5167.42, -11033.6, 24.5044, 1.6057, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5304, 1731, 530, 0, 0, 1, 1, -5118.29, -10943.1, 16.0907, 0.174533, 0, 0, 0, 1, 60, 100, 1, '', 0),
(5305, 1731, 530, 0, 0, 1, 1, -4874.49, -11278.6, 2.6214, -0.488692, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5306, 1731, 530, 0, 0, 1, 1, -4890.08, -11146.4, 7.22636, 1.0472, 0, 0, 1, 1, 60, 100, 1, '', 0),
(5307, 1731, 530, 0, 0, 1, 1, -4977.74, -11441.8, -36.9993, 2.75762, 0, 0, 1, 0, 60, 100, 1, '', 0),
(5308, 1731, 530, 0, 0, 1, 1, -4816.09, -11605.3, -42.3396, -1.0821, 0, 0, 1, 0, 60, 100, 1, '', 0),
(5309, 1731, 530, 0, 0, 1, 1, -5083.06, -11658.3, -12.9082, 2.82743, 0, 0, 1, 0, 60, 100, 1, '', 0),
(5310, 1731, 530, 0, 0, 1, 1, -4961.75, -11727.3, 12.8604, 0.541052, 0, 0, 0, 1, 60, 100, 1, '', 0),
(5311, 1731, 530, 0, 0, 1, 1, -4890.44, -12042.3, 22.4669, 0.733038, 0, 0, 0, 1, 60, 100, 1, '', 0),
(5312, 1731, 530, 0, 0, 1, 1, -4323.69, -12851.5, 11.673, -1.15192, 0, 0, 1, 0, 60, 100, 1, '', 0),
(5313, 1731, 530, 0, 0, 1, 1, -4178.29, -12910.6, 6.36836, -1.01229, 0, 0, 0, 0, 60, 100, 1, '', 0),
(5314, 1731, 1, 0, 0, 1, 1, 5242.39, 40.943, 61.728, 0, 0, 0, -0.199368, 0.979925, 900, 100, 1, '', 0),
(5316, 1731, 1, 0, 0, 1, 1, 958.429, -4298, -6, 4.213, 0, 0, 0.859792, -0.510644, 900, 100, 1, '', 0),
(5317, 1731, 1, 0, 0, 1, 1, 6715.34, -360.81, 47.1552, -1.39626, 0, 0, 0.642788, -0.766044, 900, 100, 1, '', 0),
(5318, 1731, 1, 0, 0, 1, 1, 1133.49, -4584.18, 28.9594, 2.79252, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5320, 1731, 1, 0, 0, 1, 1, 1601.27, 102.149, 21.912, 0.677, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5321, 1731, 1, 0, 0, 1, 1, 801.919, -4835, 38.039, -3, 0, 0, -0.984808, 0.173648, 900, 100, 1, '', 0),
(5323, 1731, 1, 215, 215, 1, 1, -1665, 355.18, 109.988, 2.531, 0, 0, 0.953717, 0.300706, 900, 100, 1, '', 0),
(5325, 3763, 1, 17, 17, 1, 1, -1078.73, -2743.5, 105.44, 1.11819, 0, 0, 0.34202, 0.939693, 900, 100, 1, '', 0),
(5326, 1731, 0, 11, 1024, 1, 1, -2982, -1308, 7.72, 1.31, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5327, 1731, 1, 14, 14, 1, 1, 544.077, -4581.3, 49.0066, -2.18166, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5329, 1731, 1, 406, 464, 1, 1, 1752.61, 689.183, 161.426, 3.006, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5330, 3763, 1, 17, 17, 1, 1, -1387.82, -2392.19, 130.659, 2.3911, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5331, 1731, 0, 1581, 1581, 1, 1, -11227, 1537.94, 35.716, 0, 0, 0, -0.069756, 0.997564, 900, 100, 1, '', 0),
(5332, 1731, 0, 130, 130, 1, 1, 1364.66, 1138.66, 81.29, 3.329, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5333, 1731, 1, 148, 2078, 1, 1, 4854.45, 534.633, 10.662, -1, 0, 0, -0.390731, 0.920505, 900, 100, 1, '', 0),
(5334, 1731, 0, 130, 130, 1, 1, 229.32, 1514.23, 153.349, 2.077, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5335, 1731, 1, 148, 148, 1, 1, 6176.48, 463.119, 24.8209, -1.20428, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5336, 3763, 1, 17, 17, 1, 1, -493, -2338, 130.473, 4.645, 0, 0, 0.730471, -0.682943, 900, 100, 1, '', 0),
(5337, 1731, 1, 14, 14, 1, 1, 418.688, -4938, 37.499, -1, 0, 0, -0.430511, 0.902585, 900, 100, 1, '', 0),
(5339, 1731, 0, 130, 130, 1, 1, 762.282, 1271.56, 69.5199, -2.77507, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5341, 1731, 0, 130, 227, 1, 1, 1011.34, 1184.54, 56.9709, -1.11701, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5342, 1731, 1, 215, 215, 1, 1, -2673, 343.263, 137.52, -1, 0, 0, -0.446198, 0.894934, 900, 100, 1, '', 0),
(5343, 1731, 1, 14, 14, 1, 1, 251.159, -3871.59, 39.2736, 0.331611, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5344, 1731, 1, 14, 369, 1, 1, 951.524, -4226, -6, 2.417, 0, 0, 0.935001, 0.354646, 900, 100, 1, '', 0),
(5345, 1731, 0, 130, 230, 1, 1, -766, 1328.19, 77.905, 3.858, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5346, 1731, 0, 40, 919, 1, 1, -10795.9, 1266.73, 34.3962, 1.32645, 0, 0, 0.615662, 0.788011, 900, 100, 1, '', 0),
(5347, 1731, 1, 400, 2097, 1, 1, -4979, -1856, -38, 2.635, 0, 0, 0.968148, 0.25038, 900, 100, 1, '', 0),
(5349, 1731, 0, 130, 130, 1, 1, 511.705, 1353.7, 88.843, 1.868, 0, 0, 0.803857, 0.594823, 900, 100, 1, '', 0),
(5350, 1731, 0, 130, 130, 1, 1, 646.375, 1415.15, 83.733, 1.228, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5354, 1731, 1, 14, 816, 1, 1, 82.093, -4522, 70.473, 2.467, 0, 0, 0.943659, 0.330919, 900, 100, 1, '', 0),
(5355, 1731, 1, 148, 148, 1, 1, 6835.45, -291, 40.423, 1.92, 0, 0, 0.819152, 0.573577, 900, 100, 1, '', 0),
(5356, 1731, 1, 14, 14, 1, 1, -708, -4690, 35.181, 6.181, 0, 0, 0.051245, -0.998686, 900, 100, 1, '', 0),
(5357, 1731, 0, 130, 130, 1, 1, 710.541, 1538.18, 69.106, 1.466, 0, 0, 0.669131, 0.743145, 900, 100, 1, '', 0),
(5358, 1731, 1, 406, 465, 1, 1, 829.24, 1452.84, -5, 0, 0, 0, -0.113203, 0.993572, 900, 100, 1, '', 0),
(5359, 1731, 0, 85, 173, 1, 1, 2613.72, -503, 92.281, 3.054, 0, 0, 0.999048, 0.04362, 900, 100, 1, '', 0),
(5360, 1731, 1, 400, 400, 1, 1, -4663, -1376, -32, 3.686, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5361, 1731, 1, 14, 369, 1, 1, 857.312, -4118.45, 26.625, 2.8618, 0, 0, -0.522499, 0.85264, 900, 100, 1, '', 0),
(5365, 1731, 1, 400, 2097, 1, 1, -4859.25, -1798.27, -41.88, 3.037, 0, 0, 0.99863, 0.052336, 900, 100, 1, '', 0),
(5366, 1731, 1, 14, 14, 1, 1, 1316.97, -4828.23, 24.0255, -0.575957, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5367, 1731, 0, 130, 130, 1, 1, 978.403, 1369.48, 46.028, 6.095, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5368, 1731, 1, 14, 373, 1, 1, 71.799, -5185, -6, 1.129, 0, 0, 0.534839, 0.844954, 900, 100, 1, '', 0),
(5369, 1731, 0, 85, 85, 1, 1, 2485.92, 581.391, 34.6939, -2.07694, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5370, 1731, 1, 406, 1076, 1, 1, 6.07, -34, 49.14, 4.863, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5371, 1731, 1, 400, 483, 1, 1, -5306, -1632, -38, 1.264, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5373, 1731, 0, 130, 130, 1, 1, 271.289, 1564.88, 149.205, 1.426, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5374, 1731, 1, 406, 636, 1, 1, 1225.42, -103, -2, 5.961, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5375, 1731, 1, 14, 817, 1, 1, 1509.55, -4858.73, 9.38298, 1.85, 0, 0, 0.798635, 0.601815, 900, 100, 1, '', 0),
(5376, 1731, 1, 14, 14, 1, 1, 1067.8, -4590.99, 27.041, -2.74016, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5377, 1731, 1, 148, 452, 1, 1, 7325.42, -523, 8.434, 1.553, 0, 0, 0.700909, 0.71325, 900, 100, 1, '', 0),
(5378, 1731, 0, 40, 111, 1, 1, -9938, 1438.88, 39.449, 4.51, 0, 0, 0.775039, -0.631913, 900, 100, 1, '', 0),
(5383, 3763, 1, 17, 17, 1, 1, 213.075, -2059, 123.965, 0.855211, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5387, 2055, 0, 44, 98, 1, 1, -8782, -2048, 128.154, 2.199, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5388, 2055, 0, 44, 98, 1, 1, -8842, -2074, 128.179, 3.142, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5389, 1731, 0, 40, 113, 1, 1, -10518, 1953.73, 5.183, 1.485, 0, 0, 0.676104, 0.736807, 900, 100, 1, '', 0),
(5390, 1731, 0, 40, 113, 1, 1, -10530, 1940.12, 3.946, 5.535, 0, 0, 0.36566, -0.930748, 900, 100, 1, '', 0),
(5391, 1731, 0, 40, 920, 1, 1, -11322, 1601.41, 37.152, 3.142, 0, 0, 1, 0, 900, 100, 1, '', 0),
(5393, 1731, 0, 44, 95, 1, 1, -9119, -2165, 124.142, 0.089, 0, 0, 0.044716, 0.999, 900, 100, 1, '', 0),
(5395, 1731, 0, 130, 231, 1, 1, -319, 990.09, 124.668, 2.398, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5397, 1731, 0, 130, 130, 1, 1, 512.472, 1052.08, 107.543, 0.244, 0, 0, 0.121869, 0.992546, 900, 100, 1, '', 0),
(5399, 1731, 1, 148, 452, 1, 1, 6888.3, 396.644, 15.7299, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1, '', 0),
(5400, 1731, 1, 14, 14, 1, 1, 590.341, -4278, 15.473, 1.777, 0, 0, 0.776019, 0.63071, 900, 100, 1, '', 0),
(5401, 1731, 1, 14, 370, 1, 1, 972.06, -4718.87, 29.4653, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5402, 1731, 0, 130, 172, 1, 1, 914.508, 758.553, 56.683, 1.557, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5406, 1731, 1, 400, 480, 1, 1, -4546, -1195, -48, 3.132, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5407, 3763, 1, 17, 17, 1, 1, -727.292, -2078.22, 147.917, 2.594, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5408, 1731, 1, 14, 14, 1, 1, 1430.12, -4664.15, 46.1002, -0.418879, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5409, 1731, 1, 148, 446, 1, 1, 6770.72, 0.317, 27.224, -1, 0, 0, -0.267238, 0.96363, 900, 100, 1, '', 0),
(5410, 1731, 0, 40, 113, 1, 1, -10498, 1985.95, 4.486, 2.477, 0, 0, 0.945272, 0.326285, 900, 100, 1, '', 0),
(5411, 1731, 0, 40, 113, 1, 1, -10320, 1884.29, 38.209, 3.721, 0, 0, 0.95836, -0.285563, 900, 100, 1, '', 0),
(5415, 1731, 1, 406, 406, 1, 1, 2059.95, 1124.83, 270.65, 1.197, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5416, 1731, 1, 405, 405, 1, 1, -114.647, 1298.89, 93.6003, 0.122173, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5418, 1731, 0, 44, 44, 1, 1, -9430, -1973, 81.649, 5.14, 0, 0, 0.541043, -0.840995, 900, 100, 1, '', 0),
(5420, 1731, 1, 406, 469, 1, 1, 36.035, -197, 27.172, 0.288, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5421, 3763, 1, 17, 388, 1, 1, -1136, -3019, 104.508, -1, 0, 0, -0.350207, 0.936672, 900, 100, 1, '', 0),
(5432, 1731, 0, 44, 1000, 1, 1, -9285.47, -3419.89, 105.121, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1, '', 0),
(5433, 1731, 0, 130, 230, 1, 1, -635, 1599.98, 16.259, 1.866, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5434, 3763, 1, 17, 1701, 1, 1, -3710, -1559, 92.09, 2.772, 0, 0, 0.982941, 0.183923, 900, 100, 1, '', 0),
(5436, 1731, 0, 40, 40, 1, 1, -9917.85, 884.944, 33.5319, -2.54818, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5437, 2055, 0, 44, 98, 1, 1, -8789, -2046, 127.599, 2.199, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5438, 1731, 0, 44, 44, 1, 1, -8840, -2071, 128.304, 3.142, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5440, 1731, 0, 44, 996, 1, 1, -8839.28, -2528.36, 142.069, 2.98451, 0, 0, 0.996917, 0.078459, 900, 100, 1, '', 0),
(5443, 1731, 0, 130, 232, 1, 1, 1098.15, 228.977, 35.23, 1.187, 0, 0, 0.559193, 0.829038, 900, 100, 1, '', 0),
(5444, 1731, 1, 148, 454, 1, 1, 5122.07, 354.769, 11.8237, -2.9147, 0, 0, 0.993572, -0.113203, 900, 100, 1, '', 0),
(5445, 1731, 1, 148, 449, 1, 1, 4451.96, 413.918, 67.525, -1, 0, 0, -0.469471, 0.882948, 900, 100, 1, '', 0),
(5446, 1731, 0, 130, 130, 1, 1, 612.784, 1429.8, 102.685, -1, 0, 0, -0.573576, 0.819152, 900, 100, 1, '', 0),
(5447, 1731, 1, 14, 816, 1, 1, 5.7781, -4578.69, 53.9976, 0.314158, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5448, 1731, 0, 130, 130, 1, 1, 378.417, 1341.24, 92.854, 2.775, 0, 0, 0.983255, 0.182235, 900, 100, 1, '', 0),
(5449, 1731, 0, 130, 130, 1, 1, -234, 1297.04, 46.842, 1.451, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5450, 1731, 1, 215, 820, 1, 1, -1594, -874, 37.908, 2.723, 0, 0, 0.978148, 0.207912, 900, 100, 1, '', 0),
(5451, 1731, 0, 44, 68, 1, 1, -9591.81, -2403.29, 64.4106, -2.68781, 0, 0, 0, 1, 180, 100, 1, '', 0),
(5452, 1731, 1, 148, 148, 1, 1, 6135.04, 17.2649, 44.6068, 2.16421, 0, 0, 0.882948, 0.469472, 900, 100, 1, '', 0),
(5454, 3763, 1, 17, 17, 1, 1, -1437.73, -3085.67, 111.734, 0.655799, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5455, 1731, 1, 14, 369, 1, 1, 835.805, -4055, -9, 3.073, 0, 0, 0.999408, 0.034417, 900, 100, 1, '', 0),
(5456, 1731, 0, 130, 237, 1, 1, 723.705, 1126, 64.147, -2, 0, 0, -0.788011, 0.615661, 900, 100, 1, '', 0),
(5457, 1731, 0, 130, 227, 1, 1, 908.113, 1309.6, 49.069, -1, 0, 0, -0.48481, 0.87462, 900, 100, 1, '', 0),
(5458, 1731, 1, 406, 465, 1, 1, 1000.95, 1509.58, 19.5043, 5.281, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5460, 1731, 0, 130, 237, 1, 1, 742.489, 1032.72, 56.424, 1.414, 0, 0, 0.649448, 0.760406, 900, 100, 1, '', 0),
(5461, 1731, 0, 11, 1020, 1, 1, -3881, -2862, 6.556, 3.882, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5462, 1731, 0, 130, 130, 1, 1, 476.727, 1412.82, 116.182, 3.369, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5463, 1731, 1, 400, 2097, 1, 1, -4762, -1768, -33, 2.236, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5464, 3763, 1, 17, 17, 1, 1, 47.076, -1726, 114.984, -1, 0, 0, -0.594823, 0.803857, 900, 100, 1, '', 0),
(5466, 1731, 1, 400, 2303, 1, 1, -5403, -2922, -50, 4.191, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5467, 1731, 1, 400, 400, 1, 1, -5126, -2436, -52, 4.652, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5468, 1731, 1, 14, 369, 1, 1, 719.195, -4127, 1.455, 2.869, 0, 0, 0.990746, 0.135728, 900, 100, 1, '', 0),
(5470, 1731, 0, 11, 1024, 1, 1, -3021, -1509, 7.241, 4.472, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5471, 1731, 1, 14, 14, 1, 1, -244.725, -4848.6, 31.135, 0.131648, 0, 0, 0.997643, 0.068625, 900, 100, 1, '', 0),
(5472, 1731, 1, 14, 393, 1, 1, -642, -5100, 21.157, 0.379, 0, 0, 0.18853, 0.982067, 900, 100, 1, '', 0),
(5473, 1731, 0, 44, 999, 1, 1, -9343.75, -2926.38, 109.87, 1.58825, 0, 0, 0, 1, 900, 255, 1, '', 0),
(5474, 1731, 1, 14, 14, 1, 1, 517.774, -4761, 30.16, 0.785, 0, 0, 0.382683, 0.92388, 900, 100, 1, '', 0),
(5475, 1731, 1, 215, 215, 1, 1, -1583, 276.251, 22.789, -2, 0, 0, -0.902585, 0.430511, 900, 100, 1, '', 0),
(5477, 1731, 1, 406, 1076, 1, 1, 508.831, 275.832, 69.153, 5.665, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5478, 1731, 0, 130, 239, 1, 1, 1353.66, 1243.58, 47.166, 1.571, 0, 0, 0.707107, 0.707107, 900, 100, 1, '', 0),
(5480, 1731, 0, 130, 226, 1, 1, 1339.78, 1960.68, 13.496, -1, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5481, 1731, 0, 130, 229, 1, 1, 227.436, 1485.74, 143.122, 2.797, 0, 0, 0, 0, 900, 100, 1, '', 0),
(5482, 1731, 0, 85, 85, 1, 1, 2656.42, 1194.57, 74.715, -2, 0, 0, -0.793353, 0.608761, 900, 100, 1, '', 0),
(5483, 1731, 0, 85, 811, 1, 1, 1790.83, 884.56, 33.848, 2.304, 0, 0, 0.913545, 0.406737, 900, 100, 1, '', 0),
(12374, 1731, 1, 14, 369, 1, 1, 987.733, -4279.1, 20.6213, -2.02458, 0, 0, 0.848048, -0.529919, 900, 100, 1, '', 0),
(12375, 1731, 1, 14, 14, 1, 1, -666.219, -4858.36, 39.6105, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(12376, 1731, 1, 14, 372, 1, 1, -369.761, -5142.03, 25.3783, -0.174533, 0, 0, 0.087156, -0.996195, 900, 100, 1, '', 0),
(12377, 1731, 1, 14, 369, 1, 1, 733.337, -4104.11, -9.99905, 1.27409, 0, 0, 0.594823, 0.803857, 900, 100, 1, '', 0),
(12378, 1731, 1, 14, 369, 1, 1, 833.363, -4095.24, -12.8436, -1.09956, 0, 0, 0.522499, -0.85264, 900, 100, 1, '', 0),
(12379, 1731, 1, 14, 14, 1, 1, -145.402, -4681.56, 32.4146, 1.3439, 0, 0, 0.622515, 0.782608, 900, 100, 1, '', 0),
(12380, 1731, 1, 14, 14, 1, 1, 612.375, -4124.88, 25.6376, -1.18682, 0, 0, 0.559193, -0.829037, 900, 100, 1, '', 0),
(12381, 1731, 1, 14, 1296, 1, 1, 1234.41, -4135.12, 26.184, 0.517706, 0, 0, 0.996195, 0.087156, 900, 100, 1, '', 0),
(12382, 1731, 1, 14, 366, 1, 1, -940.237, -4518.29, 36.6469, -0.349066, 0, 0, 0.173648, -0.984808, 900, 100, 1, '', 0),
(12385, 1731, 1, 14, 410, 1, 1, 829.248, -4702.52, 12.2974, -2.21657, 0, 0, 0.894934, -0.446198, 900, 100, 1, '', 0),
(12386, 1731, 1, 14, 410, 1, 1, 877.604, -4602.6, 14.8535, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1, '', 0),
(12387, 1731, 1, 14, 369, 1, 1, 980.647, -4085.28, -5.86554, 0.383972, 0, 0, 0.190809, 0.981627, 900, 100, 1, '', 0),
(12482, 1731, 1, 14, 14, 1, 1, 1057.03, -4889.49, 25.6804, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1, '', 0),
(12483, 1731, 1, 14, 14, 1, 1, 1166.65, -4762.67, 22.9298, -0.2618, 0, 0, 0.130526, -0.991445, 900, 100, 1, '', 0),
(12486, 1731, 1, 14, 368, 1, 1, -651.845, -5585.51, 12.682, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1, '', 0),
(2135425, 1731, 1, 0, 0, 1, 1, -402.363, -4745.78, 38.7069, 0.0534247, 0, 0, -0.0267091, -0.999643, 300, 0, 1, '', 0),
(12491, 1731, 1, 14, 14, 1, 1, 1299.94, -4054.1, 39.2247, -0.20944, 0, 0, 0.104528, -0.994522, 900, 100, 1, '', 0),
(12493, 1731, 1, 14, 14, 1, 1, 793.832, -4771.2, 38.5628, -2.33874, 0, 0, 0.920505, -0.390731, 900, 100, 1, '', 0),
(12494, 1731, 1, 14, 817, 1, 1, 1465.41, -4865.19, 13.0559, 1.76278, 0, 0, 0.771625, 0.636078, 900, 100, 1, '', 0),
(12495, 1731, 1, 14, 14, 1, 1, -416.801, -4633.31, 50.701, -1.20428, 0, 0, 0.566406, -0.824126, 900, 100, 1, '', 0),
(12534, 1731, 1, 14, 14, 1, 1, -111.785, -4017.52, 67.2261, -1.41372, 0, 0, 0.649448, -0.760406, 900, 100, 1, '', 0),
(12535, 1731, 1, 14, 816, 1, 1, 58.4049, -4381.18, 74.8717, 0.383972, 0, 0, 0.190809, 0.981627, 900, 100, 1, '', 0),
(12537, 1731, 1, 14, 14, 1, 1, 359.069, -5093.58, 12.6732, -0.506145, 0, 0, 0.25038, -0.968148, 900, 100, 1, '', 0),
(12539, 1731, 1, 14, 14, 1, 1, 701.043, -4386.61, 27.4552, 2.53073, 0, 0, 0.953717, 0.300706, 900, 100, 1, '', 0),
(12540, 1731, 1, 14, 374, 1, 1, 1181.44, -5106.4, 8.4284, -2.96706, 0, 0, 0.996195, -0.087156, 900, 100, 1, '', 0),
(12541, 1731, 1, 14, 816, 1, 1, 121.802, -4321.91, 60.3641, 1.44862, 0, 0, 0.66262, 0.748956, 900, 100, 1, '', 0),
(12542, 1731, 1, 14, 816, 1, 1, 31.9014, -4346.94, 76.9537, -1.32645, 0, 0, 0.615661, -0.788011, 900, 100, 1, '', 0),
(12543, 1731, 1, 14, 371, 1, 1, 932.9, -4749.66, 21.0103, -1.62316, 0, 0, 0.725374, -0.688354, 900, 100, 1, '', 0),
(12544, 1731, 1, 14, 14, 1, 1, 790.85, -3870.64, 21.5309, -2.33874, 0, 0, 0.920505, -0.390731, 900, 100, 1, '', 0),
(12545, 1731, 1, 14, 817, 1, 1, 1513.36, -4712.86, 12.2831, -0.418879, 0, 0, 0.207912, -0.978148, 900, 100, 1, '', 0),
(12601, 1731, 1, 14, 14, 1, 1, 620.868, -3887.83, 29.8135, 1.23918, 0, 0, 0.580703, 0.814116, 900, 100, 1, '', 0),
(12616, 1731, 1, 14, 14, 1, 1, -281.428, -4741.94, 39.3108, -2.70526, 0, 0, 0.976296, -0.21644, 900, 100, 1, '', 0),
(12617, 1731, 1, 14, 14, 1, 1, -111.012, -4599, 54.0679, -0.680679, 0, 0, 0.333807, -0.942641, 900, 100, 1, '', 0),
(13067, 103713, 1, 17, 359, 1, 1, -4203.78, -2233.75, 55.3837, 0.20944, 0, 0, 0.104528, 0.994522, 900, 100, 1, '', 0),
(14564, 1731, 0, 11, 836, 1, 1, -4144.73, -2474.29, 185.1, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1, '', 0),
(14862, 1731, 0, 11, 1018, 1, 1, -3800.21, -1383.16, 39.8308, 1.90241, 0, 0, 0.814116, 0.580703, 900, 100, 1, '', 0),
(14870, 1731, 0, 11, 1024, 1, 1, -3024.83, -1356.18, 0.228389, -2.16421, 0, 0, 0.882948, -0.469472, 900, 100, 1, '', 0),
(14894, 1731, 0, 11, 836, 1, 1, -4159.18, -2369.03, 212.793, -2.18166, 0, 0, 0.887011, -0.461749, 900, 100, 1, '', 0),
(14900, 1731, 0, 11, 1023, 1, 1, -2757.12, -1669.96, -0.252495, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(14906, 1731, 0, 11, 1018, 1, 1, -3644.19, -1439.96, 11.7144, 0.034907, 0, 0, 0.017452, 0.999848, 900, 100, 1, '', 0),
(14960, 3763, 1, 17, 383, 1, 1, 635.223, -1433.38, 92.741, 0.436332, 0, 0, 0.21644, 0.976296, 900, 100, 1, '', 0),
(14966, 3763, 1, 17, 383, 1, 1, 336.791, -1647.45, 105.217, 2.40855, 0, 0, 0.93358, 0.358368, 900, 100, 1, '', 0),
(14967, 3763, 1, 17, 1701, 1, 1, -3925.95, -1621.06, 91.8752, 0.279253, 0, 0, 0.139173, 0.990268, 900, 100, 1, '', 0),
(14969, 3763, 1, 17, 1701, 1, 1, -3980.38, -1706.46, 94.8775, 2.42601, 0, 0, 0.936672, 0.350207, 900, 100, 1, '', 0),
(14970, 3763, 1, 17, 1701, 1, 1, -3701.07, -1621.85, 96.2469, -2.37365, 0, 0, 0.927184, -0.374607, 900, 100, 1, '', 0),
(14971, 3763, 1, 17, 1701, 1, 1, -3720.6, -1553.41, 96.1831, -1.79769, 0, 0, 0.782608, -0.622515, 900, 100, 1, '', 0),
(15004, 3763, 1, 17, 1698, 1, 1, -2287.27, -2533.56, 95.5277, 0.541052, 0, 0, 0.267238, 0.96363, 900, 100, 1, '', 0),
(15005, 3763, 1, 17, 1698, 1, 1, -1919.06, -2721.38, 91.8313, -2.1293, 0, 0, 0.87462, -0.48481, 900, 100, 1, '', 0),
(15009, 3763, 1, 17, 1698, 1, 1, -1958.45, -2686.12, 94.873, -2.28638, 0, 0, 0.909961, -0.414693, 900, 100, 1, '', 0),
(15010, 3763, 1, 17, 1700, 1, 1, -1432.43, -1536.64, 94.439, 0.244346, 0, 0, 0.121869, 0.992546, 900, 100, 1, '', 0),
(15011, 3763, 1, 17, 1700, 1, 1, -1468.31, -1519.67, 95.6948, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1, '', 0),
(15016, 3763, 1, 17, 1699, 1, 1, -116.853, -3193.63, 93.3306, 3.10669, 0, 0, 0.999848, 0.017452, 900, 100, 1, '', 0),
(15017, 3763, 1, 17, 1699, 1, 1, -197.368, -3267.02, 95.4545, 3.08923, 0, 0, 0.999657, 0.026177, 900, 100, 1, '', 0),
(15018, 3763, 1, 17, 1699, 1, 1, -261.772, -3343.72, 99.7645, 1.81514, 0, 0, 0.788011, 0.615662, 900, 100, 1, '', 0),
(15019, 3763, 1, 17, 1700, 1, 1, -1844.55, -1955.4, 94.9395, -0.139626, 0, 0, 0.069756, -0.997564, 900, 100, 1, '', 0),
(15020, 3763, 1, 17, 1700, 1, 1, -2067.9, -1745.81, 105.385, -0.855212, 0, 0, 0.414693, -0.909961, 900, 100, 1, '', 0),
(15021, 3763, 1, 17, 1699, 1, 1, -141.477, -3034.67, 91.7, -1.02974, 0, 0, 0.492424, -0.870356, 900, 100, 1, '', 0),
(15030, 1731, 0, 11, 1022, 1, 1, -3118.42, -1245, 2.63742, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(15132, 3763, 1, 17, 1156, 1, 1, -2215.77, -1631.75, 105.657, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1, '', 0),
(15133, 3763, 1, 17, 1701, 1, 1, -3738.37, -1542.1, 109.619, 0.593412, 0, 0, 0.292372, 0.956305, 900, 100, 1, '', 0),
(15135, 3763, 1, 17, 391, 1, 1, -1356.53, -3763.91, 58.7635, -2.84489, 0, 0, 0.989016, -0.147809, 900, 100, 1, '', 0),
(15136, 1731, 1, 17, 391, 1, 1, -1273.68, -3803, 31.564, -0.575959, 0, 0, 0.284015, -0.95882, 900, 100, 1, '', 0),
(15137, 3763, 1, 17, 17, 1, 1, -1723.62, -3260.31, 96.8265, 0.418879, 0, 0, 0.207912, 0.978148, 900, 100, 1, '', 0),
(15140, 3763, 1, 17, 390, 1, 1, -3193.65, -1641.74, 91.8558, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1, '', 0),
(15150, 3763, 1, 17, 17, 1, 1, 730.945, -2801.62, 102.139, 3.01942, 0, 0, 0.998135, 0.061049, 900, 100, 1, '', 0),
(15155, 3763, 1, 17, 17, 1, 1, -1376.74, -3570.06, 97.8136, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1, '', 0),
(15160, 3763, 1, 17, 17, 1, 1, -514.753, -3016.06, 100.401, -2.93215, 0, 0, 0.994522, -0.104529, 900, 100, 1, '', 0),
(15163, 3763, 1, 17, 384, 1, 1, 381.943, -2117.46, 141.642, -0.733038, 0, 0, 0.358368, -0.93358, 900, 100, 1, '', 0),
(15164, 3763, 1, 17, 17, 1, 1, 405.053, -1977.74, 103.468, -2.1293, 0, 0, 0.87462, -0.48481, 900, 100, 1, '', 0),
(15165, 3763, 1, 17, 17, 1, 1, 319.669, -1761.75, 103.019, 1.36136, 0, 0, 0.62932, 0.777146, 900, 100, 1, '', 0),
(15167, 3763, 1, 17, 17, 1, 1, 81.6544, -2316.57, 107.679, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1, '', 0),
(15169, 3763, 1, 17, 1156, 1, 1, -3815.96, -2400.9, 105.543, -1.16937, 0, 0, 0.551937, -0.833886, 900, 100, 1, '', 0),
(15214, 1731, 0, 11, 11, 1, 1, -4028.35, -2945.35, 6.08753, 1.06465, 0, 0, 0.507538, 0.861629, 900, 100, 1, '', 0),
(15400, 3763, 1, 17, 17, 1, 1, -1594.93, -3509.24, 135.792, -1.3439, 0, 0, 0.622515, -0.782608, 900, 100, 1, '', 0),
(15452, 1731, 1, 17, 381, 1, 1, 1385.21, -3485.35, 95.0849, 1.27409, 0, 0, 0.594823, 0.803857, 900, 100, 1, '', 0),
(15453, 1731, 1, 17, 1700, 1, 1, -1553.23, -1500.86, 143.728, -2.72271, 0, 0, 0.978148, -0.207912, 900, 100, 1, '', 0),
(15454, 3763, 1, 17, 17, 1, 1, -770.842, -1545.78, 148.305, -0.436333, 0, 0, 0.21644, -0.976296, 900, 100, 1, '', 0),
(15458, 3763, 1, 17, 17, 1, 1, -631.387, -2378.14, 135.454, -1.11701, 0, 0, 0.529919, -0.848048, 900, 100, 1, '', 0),
(15483, 1731, 1, 17, 1702, 1, 1, -88.5203, -1416.21, 100.186, 2.98451, 0, 0, 0.996917, 0.078459, 900, 100, 1, '', 0),
(15484, 1731, 1, 17, 17, 1, 1, -271.54, -1471.81, 104.117, 0.907571, 0, 0, 0.438371, 0.898794, 900, 100, 1, '', 0),
(15485, 3763, 1, 17, 1700, 1, 1, -1386.23, -1625.25, 120.082, -3.10669, 0, 0, 0.999848, -0.017452, 900, 100, 1, '', 0),
(15486, 3763, 1, 17, 17, 1, 1, 887.47, -3138.51, 118.462, -0.785398, 0, 0, 0.382683, -0.92388, 900, 100, 1, '', 0),
(15487, 3763, 1, 17, 17, 1, 1, -952.934, -1583.11, 94.7977, 2.86234, 0, 0, 0.990268, 0.139173, 900, 100, 1, '', 0),
(15488, 3763, 1, 17, 17, 1, 1, -1013.44, -1702.45, 102.384, -0.715585, 0, 0, 0.350207, -0.936672, 900, 100, 1, '', 0),
(15489, 3763, 1, 17, 17, 1, 1, 1236.18, -3317.72, 101.758, -0.506145, 0, 0, 0.25038, -0.968148, 900, 100, 1, '', 0),
(15491, 3763, 1, 17, 17, 1, 1, -1261.57, -2591.49, 102.075, 0.488692, 0, 0, 0.241922, 0.970296, 900, 100, 1, '', 0),
(15492, 3763, 1, 17, 17, 1, 1, -1060.9, -2632.01, 101.019, 0.698132, 0, 0, 0.34202, 0.939693, 900, 100, 1, '', 0),
(15493, 3763, 1, 17, 17, 1, 1, -1190.27, -3345.26, 99.7463, -2.70526, 0, 0, 0.976296, -0.21644, 900, 100, 1, '', 0),
(15494, 3763, 1, 17, 385, 1, 1, -1844.02, -3356.48, 78.6834, 2.46091, 0, 0, 0.942641, 0.333807, 900, 100, 1, '', 0),
(15495, 3763, 1, 17, 385, 1, 1, -2041.03, -3471.56, 99.7707, 1.8675, 0, 0, 0.803857, 0.594823, 900, 100, 1, '', 0),
(15496, 3763, 1, 17, 388, 1, 1, -1348.71, -2963.96, 98.7622, -2.79253, 0, 0, 0.984808, -0.173648, 900, 100, 1, '', 0),
(15502, 3763, 1, 17, 17, 1, 1, 591.822, -3281.41, 187.322, -2.07694, 0, 0, 0.861629, -0.507538, 900, 100, 1, '', 0),
(15504, 3763, 1, 17, 17, 1, 1, 840.727, -3667.57, 32.224, 2.30383, 0, 0, 0.913545, 0.406737, 900, 100, 1, '', 0),
(15509, 3763, 1, 17, 17, 1, 1, 450.51, -3281.45, 98.8256, -2.11185, 0, 0, 0.870356, -0.492423, 900, 100, 1, '', 0),
(15510, 3763, 1, 17, 17, 1, 1, -621.845, -2219.93, 215.434, -0.10472, 0, 0, 0.052336, -0.99863, 900, 100, 1, '', 0),
(15511, 3763, 1, 17, 17, 1, 1, -1268.61, -1925.52, 89.2233, 2.09439, 0, 0, 0.866025, 0.5, 900, 100, 1, '', 0),
(15673, 3763, 1, 17, 1699, 1, 1, -202.882, -3173.86, 178.712, 1.79769, 0, 0, 0.782608, 0.622515, 900, 100, 1, '', 0),
(15674, 3763, 1, 17, 1699, 1, 1, -342.159, -3227.42, 187.558, 0.575959, 0, 0, 0.284015, 0.95882, 900, 100, 1, '', 0),
(15675, 3763, 1, 17, 1697, 1, 1, -1980.27, -3299.42, 118.611, 2.44346, 0, 0, 0.939693, 0.34202, 900, 100, 1, '', 0),
(15676, 3763, 1, 17, 17, 1, 1, -590.947, -3261.27, 100.938, -0.523599, 0, 0, 0.258819, -0.965926, 900, 100, 1, '', 0),
(15677, 3763, 1, 17, 17, 1, 1, 728.463, -3063.25, 93.5535, 0.331613, 0, 0, 0.165048, 0.986286, 900, 100, 1, '', 0),
(15678, 3763, 1, 17, 17, 1, 1, 660.177, -3168.27, 183.398, -2.75762, 0, 0, 0.981627, -0.190809, 900, 100, 1, '', 0),
(15679, 3763, 1, 17, 17, 1, 1, 788.479, -3309.57, 230.934, 1.67552, 0, 0, 0.743145, 0.669131, 900, 100, 1, '', 0),
(15680, 3763, 1, 17, 1700, 1, 1, -2063.11, -2160.76, 117.148, 2.98451, 0, 0, 0.996917, 0.078459, 900, 100, 1, '', 0),
(15681, 3763, 1, 17, 1699, 1, 1, -367.45, -3333.21, 94.927, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(15682, 3763, 1, 17, 1156, 1, 1, -2575.43, -2387.76, 104.146, -0.453786, 0, 0, 0.224951, -0.97437, 900, 100, 1, '', 0),
(15684, 3763, 1, 17, 390, 1, 1, -3034.66, -1600.33, 109.809, 1.29154, 0, 0, 0.601815, 0.798636, 900, 100, 1, '', 0),
(17090, 1731, 1, 400, 400, 1, 1, -4454.48, -849.572, -51.5708, 2.86234, 0, 0, 0.990268, 0.139173, 900, 100, 1, '', 0),
(17091, 1731, 1, 400, 2303, 1, 1, -5365.58, -3033.83, -37.2912, 2.9147, 0, 0, 0.993572, 0.113203, 900, 100, 1, '', 0),
(17094, 1731, 1, 400, 400, 1, 1, -5638.07, -2211.79, -56.7211, -1.11701, 0, 0, 0.529919, -0.848048, 900, 100, 1, '', 0),
(17096, 1731, 1, 400, 400, 1, 1, -4873.66, -1104.55, -55.7532, 2.40855, 0, 0, 0.93358, 0.358368, 900, 100, 1, '', 0),
(17097, 1731, 1, 400, 400, 1, 1, -4784.1, -1731.71, -37.375, 3.03687, 0, 0, 0.99863, 0.052336, 900, 100, 1, '', 0),
(17098, 1731, 1, 400, 480, 1, 1, -4494.19, -1153.58, -56.3431, -2.72271, 0, 0, 0.978148, -0.207912, 900, 100, 1, '', 0),
(18601, 1731, 0, 44, 998, 1, 1, -8810.75, -2136.72, 150.143, 0.453786, 0, 0, 0.224951, 0.97437, 900, 100, 1, '', 0),
(18679, 1731, 0, 44, 998, 1, 1, -8747.79, -2256.47, 153.35, 0.855903, 0, 0, 0.224951, 0.97437, 900, 100, 1, '', 0),
(18913, 1731, 0, 44, 71, 1, 1, -9565.56, -3240.22, 50.891, 2.3911, 0, 0, 0.930418, 0.366501, 900, 100, 1, '', 0),
(20454, 2055, 0, 44, 98, 1, 1, -8822.76, -1975.59, 124.41, -0.767945, 0, 0, 0.374607, -0.927184, 900, 100, 1, '', 0),
(20460, 1731, 0, 44, 998, 1, 1, -8688.33, -2170.14, 156.791, 2.21657, 0, 0, 0.894934, 0.446198, 900, 100, 1, '', 0),
(20641, 1731, 1, 215, 215, 1, 1, -1413.5, -1023.59, 142.286, 0.715585, 0, 0, 0.350207, 0.936672, 900, 100, 1, '', 0),
(20642, 1731, 1, 215, 215, 1, 1, -1541.22, -991.637, 154.309, -1.8675, 0, 0, 0.803857, -0.594823, 900, 100, 1, '', 0),
(20643, 1731, 1, 215, 215, 1, 1, -1385.24, -1081.32, 142.678, 2.67035, 0, 0, 0.97237, 0.233445, 900, 100, 1, '', 0),
(20644, 1731, 1, 215, 215, 1, 1, -1382.94, -1171.94, 163.176, -0.942478, 0, 0, 0.453991, -0.891006, 900, 100, 1, '', 0),
(20645, 1731, 1, 405, 405, 1, 1, 212.447, 1588.1, 174.453, -2.53073, 0, 0, 0.953717, -0.300706, 900, 100, 1, '', 0),
(20646, 1731, 1, 405, 2404, 1, 1, 170.665, 1985.24, 179.044, -0.05236, 0, 0, 0.026177, -0.999657, 900, 100, 1, '', 0),
(20647, 1731, 1, 405, 405, 1, 1, -201.552, 1178.94, 97.313, -1.50098, 0, 0, 0.681998, -0.731354, 900, 100, 1, '', 0),
(20648, 1731, 1, 215, 360, 1, 1, -1486.76, -1196.38, 129.623, -1.46608, 0, 0, 0.669131, -0.743145, 900, 100, 1, '', 0),
(20649, 1731, 1, 215, 215, 1, 1, -2881.33, 355.126, 127.839, 0.10472, 0, 0, 0.052336, 0.99863, 900, 100, 1, '', 0),
(20650, 1731, 1, 215, 215, 1, 1, -3002.64, 280.657, 110.877, -2.28638, 0, 0, 0.909961, -0.414693, 900, 100, 1, '', 0),
(20658, 1731, 1, 215, 215, 1, 1, -2658.6, 257.63, 96.5652, 2.26893, 0, 0, 0.906308, 0.422618, 900, 100, 1, '', 0),
(20661, 1731, 1, 215, 821, 1, 1, -2658.35, -1470.27, 80.4637, -2.53073, 0, 0, 0.953717, -0.300706, 900, 100, 1, '', 0),
(20662, 1731, 1, 215, 215, 1, 1, -500.561, -67.4469, 72.3764, -2.40855, 0, 0, 0.93358, -0.358368, 900, 100, 1, '', 0),
(20663, 1731, 1, 215, 220, 1, 1, -2834.42, -829.513, 28.5204, 0.977384, 0, 0, 0.469472, 0.882948, 900, 100, 1, '', 0),
(20664, 1731, 1, 215, 215, 1, 1, -1250.65, 488.017, 39.6058, 0.994838, 0, 0, 0.477159, 0.878817, 900, 100, 1, '', 0),
(20665, 1731, 1, 215, 818, 1, 1, -2395.6, 455.132, 74.6857, 2.02458, 0, 0, 0.848048, 0.529919, 900, 100, 1, '', 0),
(20666, 1731, 1, 215, 215, 1, 1, -2126.26, 342.359, 137.839, 0.20944, 0, 0, 0.104528, 0.994522, 900, 100, 1, '', 0),
(20667, 1731, 1, 215, 818, 1, 1, -2378.36, 391.934, 66.0987, 2.74017, 0, 0, 0.979925, 0.199368, 900, 100, 1, '', 0),
(20668, 1731, 1, 215, 215, 1, 1, -1499.49, 375.201, 67.0526, 0.087266, 0, 0, 0.043619, 0.999048, 900, 100, 1, '', 0),
(20669, 1731, 1, 215, 360, 1, 1, -1716.2, -1252.96, 114.492, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1, '', 0),
(20670, 1731, 1, 215, 360, 1, 1, -1598.59, -1179.21, 143.803, 1.48353, 0, 0, 0.67559, 0.737277, 900, 100, 1, '', 0),
(20671, 1731, 1, 215, 360, 1, 1, -1558.8, -1308.79, 135.994, -0.20944, 0, 0, 0.104528, -0.994522, 900, 100, 1, '', 0),
(20672, 1731, 1, 215, 360, 1, 1, -1554.69, -1083.31, 105.707, -1.64061, 0, 0, 0.731354, -0.681998, 900, 100, 1, '', 0),
(20673, 1731, 1, 215, 215, 1, 1, -2160.77, 246.023, 85.1964, 2.02458, 0, 0, 0.848048, 0.529919, 900, 100, 1, '', 0),
(20674, 1731, 1, 215, 819, 1, 1, -441.273, -634.283, 68.3678, 1.55334, 0, 0, 0.700909, 0.71325, 900, 100, 1, '', 0),
(20711, 1731, 1, 215, 819, 1, 1, -503.832, -529.76, 72.2556, 0.575959, 0, 0, 0.284015, 0.95882, 900, 100, 1, '', 0),
(20712, 1731, 1, 215, 404, 1, 1, -1886.84, 329.933, 110.194, 1.90241, 0, 0, 0.814116, 0.580703, 900, 100, 1, '', 0),
(20715, 1731, 1, 215, 818, 1, 1, -2412.37, 503.889, 64.3131, 4.71239, 0, 0, 0.707107, -0.707107, 900, 100, 1, '', 0),
(20716, 1731, 1, 215, 818, 1, 1, -2299.02, 385.609, 58.3093, 2.67035, 0, 0, 0.97237, 0.233445, 900, 100, 1, '', 0),
(20717, 1731, 1, 215, 818, 1, 1, -2415.56, 352.934, 65.7179, -2.77507, 0, 0, 0.983255, -0.182235, 900, 100, 1, '', 0),
(20718, 1731, 1, 215, 215, 1, 1, -1745.35, -912.952, 88.8484, -0.575959, 0, 0, 0.284015, -0.95882, 900, 100, 1, '', 0),
(20719, 1731, 1, 215, 225, 1, 1, -1301.52, -1039.44, 61.9713, -2.84489, 0, 0, 0.989016, -0.147809, 900, 100, 1, '', 0),
(20720, 1731, 1, 215, 225, 1, 1, -1271.72, -1073.83, 57.5627, 0, 0, 0, 0, 1, 900, 100, 1, '', 0),
(20721, 1731, 1, 215, 360, 1, 1, -1648.13, -1087.61, 127.155, 2.46091, 0, 0, 0.942641, 0.333807, 900, 100, 1, '', 0),
(20722, 1731, 1, 215, 360, 1, 1, -1810.58, -1214.9, 108.548, -0.488692, 0, 0, 0.241922, -0.970296, 900, 100, 1, '', 0),
(20723, 1731, 1, 215, 820, 1, 1, -1348.19, -948.489, 25.8795, -2.61799, 0, 0, 0.965926, -0.258819, 900, 100, 1, '', 0),
(20750, 1731, 0, 44, 71, 1, 1, -9440.46, -3395.88, 89.0594, -1.67552, 0, 0, 0.743145, -0.669131, 900, 100, 1, '', 0),
(20787, 1731, 0, 44, 44, 1, 1, -9737.6, -2925.08, 67.047, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1, '', 0),
(20860, 1731, 0, 44, 44, 1, 1, -9004.29, -2477.13, 147.16, -2.75762, 0, 0, 0.981627, -0.190809, 900, 100, 1, '', 0),
(20884, 1731, 0, 44, 1001, 1, 1, -9804.08, -2248.52, 73.8627, -3.03687, 0, 0, 0.99863, -0.052336, 900, 100, 1, '', 0),
(26186, 1731, 36, 1581, 1581, 1, 1, -81.0082, -394.329, 61.5258, 0.436332, 0, 0, 0.21644, 0.976296, 86400, 100, 1, '', 0),
(26199, 1731, 36, 1581, 1581, 1, 1, -286.104, -577.582, 50.9829, -0.069813, 0, 0, 0.034899, -0.999391, 86400, 100, 1, '', 0),
(29210, 3763, 1, 0, 0, 1, 1, -861.641, -3449.88, 93.6537, 0.855211, 0, 0, 0.414693, 0.909961, 900, 100, 1, '', 0),
(29211, 3763, 1, 0, 0, 1, 1, -1112.9, -3589.96, 109.39, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1, '', 0),
(29572, 1731, 0, 0, 0, 1, 1, -10673.2, 691.022, 43.2237, 0.733038, 0, 0, 0.358368, 0.93358, 900, 100, 1, '', 0),
(29661, 1731, 0, 0, 0, 1, 1, -9654.49, -2022.86, 57.1197, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1, '', 0),
(30179, 1731, 1, 0, 0, 1, 1, 4420.88, 826.078, 15.3331, -1.18682, 0, 0, 0.559193, -0.829037, 900, 100, 1, '', 0),
(30180, 1731, 1, 0, 0, 1, 1, 4535.69, 902.658, 7.67582, 2.18166, 0, 0, 0.887011, 0.461749, 900, 100, 1, '', 0),
(30194, 1731, 1, 0, 0, 1, 1, 2404.24, -705.668, 154.528, 0.663225, 0, 0, 0.325568, 0.945519, 900, 100, 1, '', 0),
(30195, 1731, 1, 0, 0, 1, 1, 2451.43, -997.505, 146.244, 1.01229, 0, 0, 0.48481, 0.87462, 900, 100, 1, '', 0),
(30198, 3763, 1, 0, 0, 1, 1, -484.619, -3073.22, 158.357, 0.15708, 0, 0, 0.078459, 0.996917, 900, 100, 1, '', 0),
(30444, 1731, 530, 0, 0, 1, 1, -4066.04, -12693.8, 14.8971, -0.872665, 0, 0, 0.422618, -0.906308, 60, 100, 1, '', 0),
(30478, 1731, 530, 0, 0, 1, 1, -4565.29, -12030.2, 42.5364, 0.331613, 0, 0, 0.165048, 0.986286, 60, 100, 1, '', 0),
(30486, 1731, 530, 0, 0, 1, 1, -5085.58, -11970, -2.40896, 1.8326, 0, 0, 0.793353, 0.608761, 60, 100, 1, '', 0),
(30498, 1731, 530, 0, 0, 1, 1, -4809.11, -11667.8, -40.5956, -1.43117, 0, 0, 0.656059, -0.75471, 60, 100, 1, '', 0),
(30507, 1731, 530, 0, 0, 1, 1, -3887.91, -12696.9, 94.0337, -2.70526, 0, 0, 0.976296, -0.21644, 60, 100, 1, '', 0),
(30512, 1731, 530, 0, 0, 1, 1, -4408.13, -11518.2, 14.1106, 1.46608, 0, 0, 0.669131, 0.743145, 60, 100, 1, '', 0),
(30514, 1731, 530, 0, 0, 1, 1, -4666.33, -11535.9, 22.4012, -2.11185, 0, 0, 0.870356, -0.492423, 60, 100, 1, '', 0),
(30531, 1731, 530, 0, 0, 1, 1, -3149.38, -12393.9, 12.1434, 3.00197, 0, 0, 0.997564, 0.069757, 60, 100, 1, '', 0),
(30588, 1731, 0, 0, 0, 1, 1, -9830.03, 1401.71, 48.2202, -2.75762, 0, 0, 0.981627, -0.190809, 900, 100, 1, '', 0),
(30590, 1731, 0, 0, 0, 1, 1, -9849.11, 1442.37, 40.4727, 2.63545, 0, 0, 0.968148, 0.25038, 900, 100, 1, '', 0),
(30592, 1731, 0, 0, 0, 1, 1, -9741.26, 1284.89, 41.5235, -2.11185, 0, 0, 0.870356, -0.492423, 900, 100, 1, '', 0),
(30600, 1731, 0, 0, 0, 1, 1, -10089.5, 1729.51, 39.0119, 1.97222, 0, 0, 0.833886, 0.551937, 900, 100, 1, '', 0),
(30617, 1731, 0, 0, 0, 1, 1, -10029.4, 2017.66, -15.1234, 3.01942, 0, 0, 0.998135, 0.061049, 900, 100, 1, '', 0),
(30619, 1731, 0, 0, 0, 1, 1, -9888.8, 1745.42, 18.9283, 1.67552, 0, 0, 0.743145, 0.669131, 900, 100, 1, '', 0),
(30630, 1731, 0, 0, 0, 1, 1, -10314.5, 1278.49, 45.8038, 2.25148, 0, 0, 0.902585, 0.430511, 900, 100, 1, '', 0),
(30896, 3763, 1, 0, 0, 1, 1, 958.728, -3689.37, 37.103, 0.122173, 0, 0, 0.061049, 0.998135, 900, 100, 1, '', 0),
(30929, 1731, 0, 0, 0, 1, 1, -9736.57, 1076.61, 17.0875, -0.872665, 0, 0, 0.422618, -0.906308, 900, 100, 1, '', 0),
(30996, 1731, 0, 0, 0, 1, 1, -10476.2, 1968.14, 9.30176, 1.97222, 0, 0, 0.833886, 0.551937, 900, 100, 1, '', 0),
(30997, 1731, 0, 0, 0, 1, 1, -10479, 1994.38, 12.0118, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(31002, 1731, 0, 0, 0, 1, 1, -10206.2, 1805.9, 38.0356, -0.2618, 0, 0, 0.130526, -0.991445, 900, 100, 1, '', 0),
(31011, 1731, 0, 0, 0, 1, 1, -9893.12, 1447.15, 81.8983, -0.767945, 0, 0, 0.374607, -0.927184, 900, 100, 1, '', 0),
(31012, 1731, 0, 0, 0, 1, 1, -9905.59, 1435.75, 40.0603, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1, '', 0),
(31020, 1731, 0, 0, 0, 1, 1, -9966.55, 1790.64, 20.2446, -1.8326, 0, 0, 0.793353, -0.608761, 900, 100, 1, '', 0),
(31022, 1731, 0, 0, 0, 1, 1, -10008.5, 1865.07, 20.2473, 2.74017, 0, 0, 0.979925, 0.199368, 900, 100, 1, '', 0),
(31026, 1731, 0, 0, 0, 1, 1, -10205.7, 1951.59, 21.2334, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1, '', 0),
(31031, 1731, 0, 0, 0, 1, 1, -10476.6, 1951.56, 11.4407, 0.139626, 0, 0, 0.069756, 0.997564, 900, 100, 1, '', 0),
(31033, 1731, 0, 0, 0, 1, 1, -10595.6, 1990.49, -4.30636, 1.64061, 0, 0, 0.731354, 0.681998, 900, 100, 1, '', 0),
(31034, 1731, 0, 0, 0, 1, 1, -10291.3, 1942.4, 35.3724, -1.13446, 0, 0, 0.5373, -0.843391, 900, 100, 1, '', 0),
(31038, 1731, 0, 0, 0, 1, 1, -10904, 2098.75, 13.2407, 0.523599, 0, 0, 0.258819, 0.965926, 900, 100, 1, '', 0),
(31051, 1731, 0, 0, 0, 1, 1, -11194.2, 1694.54, 45.6586, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1, '', 0),
(31053, 1731, 0, 0, 0, 1, 1, -11175.1, 1620.2, 29.506, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1, '', 0),
(31075, 1731, 0, 0, 0, 1, 1, -10466.7, 1001.84, 49.0825, -2.58309, 0, 0, 0.961262, -0.275637, 900, 100, 1, '', 0),
(31088, 1731, 0, 0, 0, 1, 1, -11238.7, 832.159, 51.1331, -3.03687, 0, 0, 0.99863, -0.052336, 900, 100, 1, '', 0),
(31091, 1731, 0, 0, 0, 1, 1, -9871.07, 1421.18, 45.4027, 0.733038, 0, 0, 0.358368, 0.93358, 900, 100, 1, '', 0),
(31100, 1731, 0, 0, 0, 1, 1, -10343.2, 773.869, 31.3173, 0.122173, 0, 0, 0.061049, 0.998135, 900, 100, 1, '', 0),
(31111, 1731, 0, 0, 0, 1, 1, -9562.95, -1836.39, 74.0232, -1.0821, 0, 0, 0.515038, -0.857167, 900, 100, 1, '', 0),
(31113, 1731, 0, 0, 0, 1, 1, -9372.14, -1915.85, 67.3032, -0.541052, 0, 0, 0.267238, -0.96363, 900, 100, 1, '', 0),
(31115, 1731, 0, 0, 0, 1, 1, -9697.58, -1754.86, 62.2563, -2.67035, 0, 0, 0.97237, -0.233445, 900, 100, 1, '', 0),
(31116, 1731, 0, 0, 0, 1, 1, -9675.05, -3188.28, 62.2984, 2.3911, 0, 0, 0.930418, 0.366501, 900, 100, 1, '', 0),
(31124, 2055, 0, 0, 0, 1, 1, -8774.09, -1973.63, 129.718, 2.11185, 0, 0, 0.870356, 0.492424, 900, 100, 1, '', 0),
(31131, 1731, 0, 0, 0, 1, 1, -9373.97, -2729.39, 57.8007, 2.56563, 0, 0, 0.95882, 0.284015, 900, 100, 1, '', 0),
(31133, 1731, 0, 0, 0, 1, 1, -8981.17, -2296.79, 136.786, -2.11185, 0, 0, 0.870356, -0.492423, 900, 100, 1, '', 0),
(31357, 1731, 1, 0, 0, 1, 1, 4787.53, 766.542, 8.5393, 1.20428, 0, 0, 0.566406, 0.824126, 900, 100, 1, '', 0),
(31358, 1731, 1, 0, 0, 1, 1, 4855.59, 722.665, 5.86654, -0.122173, 0, 0, 0.061049, -0.998135, 900, 100, 1, '', 0),
(31366, 1731, 1, 0, 0, 1, 1, 4348.59, 966.763, 15.5835, -2.89725, 0, 0, 0.992546, -0.121869, 900, 100, 1, '', 0),
(31370, 1731, 1, 0, 0, 1, 1, 3753.26, 618.903, 14.7077, -2.42601, 0, 0, 0.936672, -0.350207, 900, 100, 1, '', 0),
(31382, 1731, 1, 0, 0, 1, 1, 1795.4, -1623.78, 95.8916, 2.09439, 0, 0, 0.866025, 0.5, 900, 100, 1, '', 0),
(31946, 1731, 0, 0, 0, 1, 1, -11120.5, 1514.43, 24.9607, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1, '', 0),
(31947, 1731, 0, 0, 0, 1, 1, -11187.9, 1549.99, 20.5152, -0.471239, 0, 0, 0.233445, -0.97237, 900, 100, 1, '', 0),
(31952, 1731, 0, 0, 0, 1, 1, -11287.6, 1565.5, 37.6382, -1.0821, 0, 0, 0.515038, -0.857167, 900, 100, 1, '', 0),
(31953, 1731, 0, 0, 0, 1, 1, -11326.7, 1576.02, 35.6833, 0.226893, 0, 0, 0.113203, 0.993572, 900, 100, 1, '', 0),
(32016, 3763, 1, 0, 0, 1, 1, -3942.16, -2003.7, 108.563, -1.62316, 0, 0, 0.725374, -0.688354, 900, 100, 1, '', 0),
(32216, 1731, 0, 0, 0, 1, 1, 2115.94, -662.906, 79.4342, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(32218, 1731, 0, 0, 0, 1, 1, 2589.23, -673.345, 78.9289, 2.42601, 0, 0, 0.936672, 0.350207, 900, 100, 1, '', 0),
(32219, 1731, 0, 0, 0, 1, 1, 2686.04, -717.874, 129.438, 1.64061, 0, 0, 0.731354, 0.681998, 900, 100, 1, '', 0),
(32520, 1731, 1, 0, 0, 1, 1, -10.5625, -3934.51, 56.4291, -1.85005, 0, 0, 0.798635, -0.601815, 900, 100, 1, '', 0),
(32524, 1731, 1, 0, 0, 1, 1, 557.418, -3846.62, 30.4998, 2.35619, 0, 0, 0.92388, 0.382683, 900, 100, 1, '', 0),
(32657, 1731, 1, 0, 0, 1, 1, 5906.93, 616.413, 1.08114, -1.58825, 0, 0, 0.71325, -0.700909, 900, 100, 1, '', 0),
(32672, 1731, 1, 0, 0, 1, 1, 4580.22, 709.536, 25.268, -0.122173, 0, 0, 0.061049, -0.998135, 900, 100, 1, '', 0),
(32677, 1731, 1, 0, 0, 1, 1, 4470.7, 743.511, -1.83624, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1, '', 0),
(32678, 1731, 1, 0, 0, 1, 1, 4422.54, 774.582, 14.917, 1.309, 0, 0, 0.608761, 0.793353, 900, 100, 1, '', 0),
(33146, 1731, 0, 0, 0, 1, 1, -8705.2, -2598.11, 140.532, 2.77507, 0, 0, 0.983255, 0.182236, 900, 100, 1, '', 0),
(33545, 1731, 1, 0, 0, 1, 1, 2585.55, -1074.56, 131.414, 2.35619, 0, 0, 0.92388, 0.382683, 900, 100, 1, '', 0),
(33550, 3763, 1, 0, 0, 1, 1, -338.933, -2986.05, 100.969, 2.25148, 0, 0, 0.902585, 0.430511, 900, 100, 1, '', 0),
(33849, 1731, 0, 0, 0, 1, 1, -3338.99, -1702.82, 13.6847, 2.58309, 0, 0, 0.961262, 0.275637, 900, 100, 1, '', 0),
(34133, 1731, 0, 0, 0, 1, 1, -3438.17, -1643.46, 21.7195, -2.89725, 0, 0, 0.992546, -0.121869, 900, 100, 1, '', 0),
(34827, 1731, 0, 0, 0, 1, 1, -11051.7, 1347.31, 43.2865, 2.82743, 0, 0, 0.987688, 0.156434, 900, 100, 1, '', 0),
(34829, 1731, 0, 0, 0, 1, 1, -11082.8, 1519.41, 29.8961, -0.331612, 0, 0, 0.165048, -0.986286, 900, 100, 1, '', 0),
(34832, 1731, 0, 0, 0, 1, 1, -11149.5, 1547.8, 21.3038, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1, '', 0),
(34837, 1731, 0, 0, 0, 1, 1, -11264.5, 1643.5, 34.9335, -1.69297, 0, 0, 0.748956, -0.66262, 900, 100, 1, '', 0),
(34842, 1731, 0, 0, 0, 1, 1, -11320.6, 1559.97, 28.1727, 0.139626, 0, 0, 0.069756, 0.997564, 900, 100, 1, '', 0),
(34847, 1731, 36, 0, 0, 1, 1, -56.9023, -349.056, 57.4095, -1.06465, 0, 0, 0.507538, -0.861629, 86400, 100, 1, '', 0),
(34851, 1731, 36, 0, 0, 1, 1, -314.51, -592.77, 51.2438, 0.959931, 0, 0, 0.461749, 0.887011, 86400, 100, 1, '', 0),
(34853, 1731, 0, 0, 0, 1, 1, -11328.8, 1594.18, 36.9639, -1.81514, 0, 0, 0.788011, -0.615661, 900, 100, 1, '', 0),
(34854, 1731, 0, 0, 0, 1, 1, -11258, 1501.48, 38.3513, 0.10472, 0, 0, 0.052336, 0.99863, 900, 100, 1, '', 0),
(34859, 1731, 0, 0, 0, 1, 1, -10133.8, 793.8, 19.4021, 0.837758, 0, 0, 0.406737, 0.913545, 900, 100, 1, '', 0),
(34906, 1731, 0, 0, 0, 1, 1, -3531.56, -1456.55, 27.5651, -0.314159, 0, 0, 0.156434, -0.987688, 900, 100, 1, '', 0),
(35431, 1731, 0, 0, 0, 1, 1, 2228.82, 1288.9, 49.7022, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1, '', 0),
(35443, 1731, 0, 0, 0, 1, 1, 1415.12, 1940.35, 10.5989, 0.645772, 0, 0, 0.317305, 0.948324, 900, 100, 1, '', 0),
(35444, 1731, 0, 0, 0, 1, 1, -313.933, 936.846, 131.932, 2.14675, 0, 0, 0.878817, 0.477159, 900, 100, 1, '', 0),
(35455, 1731, 0, 0, 0, 1, 1, 1098.54, 229.932, 34.8875, 1.18682, 0, 0, 0.559193, 0.829038, 900, 100, 1, '', 0),
(35456, 1731, 0, 0, 0, 1, 1, 1841.24, 1133.81, 33.6349, -3.12414, 0, 0, 0.999962, -0.008727, 900, 100, 1, '', 0),
(35469, 1731, 0, 0, 0, 1, 1, 2524.39, -584.124, 83.0835, -0.890118, 0, 0, 0.430511, -0.902585, 900, 100, 1, '', 0),
(35477, 1731, 0, 0, 0, 1, 1, 2134.76, -457.173, 76.0097, -0.645772, 0, 0, 0.317305, -0.948324, 900, 100, 1, '', 0),
(35479, 1731, 0, 0, 0, 1, 1, 1222.31, 1578.68, 36.4169, 4.71239, 0, 0, 0.707107, -0.707107, 900, 100, 1, '', 0),
(35490, 1731, 0, 0, 0, 1, 1, 1277.66, 1963.54, 16.3876, 1.32645, 0, 0, 0.615662, 0.788011, 900, 100, 1, '', 0),
(35491, 1731, 0, 0, 0, 1, 1, 1389.75, 1970.95, 15.3534, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1, '', 0),
(35511, 1731, 0, 0, 0, 1, 1, 1120.89, 1749.98, 21.8891, -0.785398, 0, 0, 0.382683, -0.92388, 900, 100, 1, '', 0),
(35513, 1731, 0, 0, 0, 1, 1, 1147.87, 1983.14, 22.8906, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1, '', 0),
(35514, 1731, 0, 0, 0, 1, 1, 1093.2, 2060.97, 0.456879, 1.23918, 0, 0, 0.580703, 0.814116, 900, 100, 1, '', 0),
(35527, 1731, 0, 0, 0, 1, 1, 923.438, 1590.01, 36.9635, 1.72788, 0, 0, 0.760406, 0.649448, 900, 100, 1, '', 0),
(35534, 1731, 0, 0, 0, 1, 1, 1317.5, 1982.83, 15.0881, 3.01942, 0, 0, 0.998135, 0.061049, 900, 100, 1, '', 0),
(35535, 1731, 0, 0, 0, 1, 1, 1383.2, 1977.71, 15.059, -0.925024, 0, 0, 0.446198, -0.894934, 900, 100, 1, '', 0),
(35536, 1731, 0, 0, 0, 1, 1, 1438.32, 1980.49, 13.6133, -2.6529, 0, 0, 0.970296, -0.241922, 900, 100, 1, '', 0),
(35537, 1731, 0, 0, 0, 1, 1, 1132.6, 1902.35, 34.3818, -1.98968, 0, 0, 0.838671, -0.544639, 900, 100, 1, '', 0),
(35545, 1731, 0, 0, 0, 1, 1, 315.29, 1409.67, 136.68, 2.51327, 0, 0, 0.951057, 0.309017, 900, 100, 1, '', 0),
(35571, 1731, 0, 0, 0, 1, 1, 906.962, 1070.09, 44.2785, 0.314159, 0, 0, 0.156434, 0.987688, 900, 100, 1, '', 0),
(35596, 1731, 43, 0, 0, 1, 1, -161.053, 411.129, -72.5271, 3.01942, 0, 0, 0.998135, 0.061049, 86400, 100, 1, '', 0),
(35631, 1731, 43, 0, 0, 1, 1, -87.6443, -136.019, -65.0488, 0.523599, 0, 0, 0.258819, 0.965926, 86400, 100, 1, '', 0),
(35632, 1731, 43, 0, 0, 1, 1, -179.484, -190.661, -60.6078, -1.16937, 0, 0, 0.551937, -0.833886, 86400, 100, 1, '', 0),
(39948, 1731, 530, 0, 0, 1, 1, -3496.12, -11577, 14.9949, 0, 0, 0, 0, 0, 60, 0, 1, '', 0),
(39949, 1731, 530, 0, 0, 1, 1, -3703.19, -11466.1, 303.665, 2.53072, 0, 0, 0, 1, 60, 255, 1, '', 0),
(39950, 1731, 530, 0, 0, 1, 1, -3823.74, -12579.4, 2.17238, 0, 0, 0, 0, 0, 60, 0, 1, '', 0),
(40003, 1731, 0, 0, 0, 1, 1, -10468.5, 1341.91, 44.6808, 0, 0, 0, 0, 0, 900, 0, 1, '', 0),
(40133, 1731, 0, 0, 0, 1, 1, 805.129, 1164.21, 52.6026, 0.855211, 0, 0, 0.414693, 0.909961, 900, 100, 1, '', 0),
(41915, 1731, 0, 0, 0, 1, 1, -577.718, 1709.77, 22.0632, -1.13446, 0, 0, 0.5373, -0.843391, 900, 100, 1, '', 0),
(42062, 1731, 0, 0, 0, 1, 1, 129.166, 1097.66, 87.4431, 0.680678, 0, 0, 0.333807, 0.942641, 900, 100, 1, '', 0),
(42063, 1731, 0, 0, 0, 1, 1, 388.705, 1038.2, 107.54, 0.925024, 0, 0, 0.446198, 0.894934, 900, 100, 1, '', 0),
(42064, 1731, 0, 0, 0, 1, 1, -342.521, 1673.51, 25.3212, -0.471239, 0, 0, 0.233445, -0.97237, 900, 100, 1, '', 0),
(42664, 1731, 0, 0, 0, 1, 1, -11231.4, 1625.16, 28.2586, 1.20428, 0, 0, 0.566406, 0.824126, 900, 100, 1, '', 0),
(42665, 1731, 0, 0, 0, 1, 1, -11306.8, 1566.3, 37.7273, 1.67552, 0, 0, 0.743145, 0.669131, 900, 100, 1, '', 0),
(42670, 1731, 0, 0, 0, 1, 1, -9917.51, 1586.95, 43.3489, 2.21657, 0, 0, 0.894934, 0.446198, 900, 100, 1, '', 0),
(42671, 1731, 0, 0, 0, 1, 1, -10679.6, 2004.03, 17.5325, -1.74533, 0, 0, 0.766044, -0.642788, 900, 100, 1, '', 0),
(42672, 1731, 0, 0, 0, 1, 1, -11087.8, 2077.56, 5.49718, 2.3911, 0, 0, 0.930418, 0.366501, 900, 100, 1, '', 0),
(42706, 1731, 0, 0, 0, 1, 1, -11193.2, 1441.63, 89.3392, -3.12414, 0, 0, 0.999962, -0.008727, 900, 100, 1, '', 0),
(42707, 1731, 0, 0, 0, 1, 1, -11386.2, 1643.82, 72.648, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1, '', 0),
(42708, 1731, 0, 0, 0, 1, 1, -9939.02, 1473.09, 41.6971, -2.40855, 0, 0, 0.93358, -0.358368, 900, 100, 1, '', 0),
(42709, 1731, 0, 0, 0, 1, 1, -10738.4, 2188.22, 16.2193, -0.785398, 0, 0, 0.382683, -0.92388, 900, 100, 1, '', 0),
(42710, 1731, 0, 0, 0, 1, 1, -11278.8, 1137.58, 97.9341, 0.907571, 0, 0, 0.438371, 0.898794, 900, 100, 1, '', 0),
(42712, 1731, 0, 0, 0, 1, 1, -10557.3, 1950.03, -1.81455, 2.23402, 0, 0, 0.898794, 0.438371, 900, 100, 1, '', 0),
(42731, 1731, 0, 0, 0, 1, 1, -11194.9, 682.466, 36.5628, 2.84489, 0, 0, 0.989016, 0.147809, 900, 100, 1, '', 0),
(42732, 1731, 0, 0, 0, 1, 1, -11008.9, 835.871, 36.7729, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1, '', 0),
(42775, 1731, 0, 0, 0, 1, 1, -10411.3, 1948.61, 12.6105, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1, '', 0),
(45033, 1731, 0, 0, 0, 1, 1, 1572.09, -687.518, 57.2998, 1.37881, 0, 0, 0.636078, 0.771625, 900, 100, 1, '', 0),
(45034, 1731, 0, 0, 0, 1, 1, 2718.82, 1259.24, 46.958, 2.21657, 0, 0, 0.894934, 0.446198, 900, 100, 1, '', 0),
(45035, 1731, 0, 0, 0, 1, 1, 2114.57, 1213.39, 48.3358, 2.84489, 0, 0, 0.989016, 0.147809, 900, 100, 1, '', 0),
(45036, 1731, 0, 0, 0, 1, 1, 2861.2, -498.192, 102.199, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1, '', 0),
(45037, 1731, 0, 0, 0, 1, 1, 2745.86, -385.011, 85.5166, 0.261799, 0, 0, 0.130526, 0.991445, 900, 100, 1, '', 0),
(45038, 1731, 0, 0, 0, 1, 1, 2968.22, -349.844, 27.8641, 0.715585, 0, 0, 0.350207, 0.936672, 900, 100, 1, '', 0),
(45039, 1731, 0, 0, 0, 1, 1, 2850.04, -461.927, 82.1176, -1.8326, 0, 0, 0.793353, -0.608761, 900, 100, 1, '', 0),
(45040, 1731, 0, 0, 0, 1, 1, 2283.34, -1086.17, 83.1417, -2.09439, 0, 0, 0.866025, -0.5, 900, 100, 1, '', 0),
(45041, 1731, 0, 0, 0, 1, 1, 2453.47, 835.25, 85.8056, -2.58309, 0, 0, 0.961262, -0.275637, 900, 100, 1, '', 0),
(45073, 1731, 0, 0, 0, 1, 1, 2736.66, 1411.2, 2.68338, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1, '', 0),
(45074, 1731, 0, 0, 0, 1, 1, 2709.98, 1336.98, 42.0939, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1, '', 0),
(45075, 1731, 0, 0, 0, 1, 1, 2018.84, -628.099, 66.4502, 0.453786, 0, 0, 0.224951, 0.97437, 900, 100, 1, '', 0),
(45076, 1731, 0, 0, 0, 1, 1, 1732.7, 897.179, 60.023, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1, '', 0),
(45078, 1731, 0, 0, 0, 1, 1, 2551.51, -120.405, 25.5001, 2.67035, 0, 0, 0.97237, 0.233445, 900, 100, 1, '', 0),
(45079, 1731, 0, 0, 0, 1, 1, 2557.3, 1691.01, 8.38937, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1, '', 0),
(45080, 1731, 0, 0, 0, 1, 1, 2245.63, 1519.28, 53.4071, -2.58309, 0, 0, 0.961262, -0.275637, 900, 100, 1, '', 0),
(45081, 1731, 0, 0, 0, 1, 1, 3040.21, 619.111, 98.6797, -1.50098, 0, 0, 0.681998, -0.731354, 900, 100, 1, '', 0),
(45082, 1731, 0, 0, 0, 1, 1, 3023.28, 790.686, 87.5232, 0.436332, 0, 0, 0.21644, 0.976296, 900, 100, 1, '', 0),
(45083, 1731, 0, 0, 0, 1, 1, 2700.2, -846.233, 84.8905, 0.279253, 0, 0, 0.139173, 0.990268, 900, 100, 1, '', 0),
(45084, 1731, 0, 0, 0, 1, 1, 2721.52, -931.349, 81.0418, -1.06465, 0, 0, 0.507538, -0.861629, 900, 100, 1, '', 0),
(45086, 1731, 0, 0, 0, 1, 1, 2252.92, -636.266, 81.7184, 0.959931, 0, 0, 0.461749, 0.887011, 900, 100, 1, '', 0),
(45114, 1731, 0, 0, 0, 1, 1, 2985.67, -721.287, 161.564, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1, '', 0),
(45115, 1731, 0, 0, 0, 1, 1, 2373.29, 804.254, 47.0329, -1.72788, 0, 0, 0.760406, -0.649448, 900, 100, 1, '', 0),
(45171, 1731, 0, 0, 0, 1, 1, 1619.52, -560.97, 55.0243, 0.453786, 0, 0, 0.224951, 0.97437, 900, 100, 1, '', 0),
(45176, 1731, 0, 0, 0, 1, 1, 1702.92, 768.162, 69.0627, 2.3911, 0, 0, 0.930418, 0.366501, 900, 100, 1, '', 0),
(45177, 1731, 0, 0, 0, 1, 1, 3019.68, 162.956, -7.08108, -1.85005, 0, 0, 0.798635, -0.601815, 900, 100, 1, '', 0),
(45238, 1731, 0, 0, 0, 1, 1, 2575.05, 641.717, 31.4842, 0.977384, 0, 0, 0.469472, 0.882948, 900, 100, 1, '', 0),
(45239, 1731, 0, 0, 0, 1, 1, 2617.09, 564.733, 22.2648, 0.331613, 0, 0, 0.165048, 0.986286, 900, 100, 1, '', 0),
(45242, 1731, 0, 0, 0, 1, 1, 2927.08, 447.921, 37.3951, -2.60054, 0, 0, 0.96363, -0.267238, 900, 100, 1, '', 0),
(45245, 1731, 0, 0, 0, 1, 1, 2956.82, 412.312, 39.4448, -2.82743, 0, 0, 0.987688, -0.156434, 900, 100, 1, '', 0),
(47664, 1731, 1, 0, 0, 1, 1, 175.005, -458.132, 34.7948, 1.71042, 0, 0, 0.75471, 0.656059, 900, 100, 1, '', 0),
(47665, 1731, 1, 0, 0, 1, 1, 1570.61, 512.606, 180.156, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1, '', 0),
(47666, 1731, 1, 0, 0, 1, 1, 1357.7, 1544.49, 156.776, -2.1293, 0, 0, 0.87462, -0.48481, 900, 100, 1, '', 0),
(47667, 1731, 1, 0, 0, 1, 1, 198.019, -889.892, 20.2088, -3.12414, 0, 0, 0.999962, -0.008727, 900, 100, 1, '', 0),
(47668, 1731, 1, 0, 0, 1, 1, 2282.73, 1445.62, 281.699, -2.94961, 0, 0, 0.995396, -0.095846, 900, 100, 1, '', 0),
(47669, 1731, 1, 0, 0, 1, 1, 1199.96, -500.415, 10.4833, -1.88496, 0, 0, 0.809017, -0.587785, 900, 100, 1, '', 0),
(47670, 1731, 1, 0, 0, 1, 1, 1364.09, 615.343, 215.96, 0.069813, 0, 0, 0.034899, 0.999391, 900, 100, 1, '', 0),
(47671, 1731, 1, 0, 0, 1, 1, 1182.49, -112.109, 6.31108, -2.37365, 0, 0, 0.927184, -0.374607, 900, 100, 1, '', 0),
(47672, 1731, 1, 0, 0, 1, 1, 1329.93, 911.696, 189.129, 2.84489, 0, 0, 0.989016, 0.147809, 900, 100, 1, '', 0),
(47673, 1731, 1, 0, 0, 1, 1, 1237.52, 148.234, 12.0444, -2.37365, 0, 0, 0.927184, -0.374607, 900, 100, 1, '', 0),
(47674, 1731, 1, 0, 0, 1, 1, 1462.14, -88.5425, 36.7921, -0.383972, 0, 0, 0.190809, -0.981627, 900, 100, 1, '', 0),
(47675, 1731, 1, 0, 0, 1, 1, 1578.98, 70.4523, -2.32776, -0.314159, 0, 0, 0.156434, -0.987688, 900, 100, 1, '', 0),
(48108, 1731, 1, 0, 0, 1, 1, 2480.6, -324.883, 115.091, 0, 0, 0, 0, 1, 900, 100, 1, '', 0),
(48109, 1731, 1, 0, 0, 1, 1, 3944.9, 241.402, 23.5239, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1, '', 0),
(48110, 1731, 1, 0, 0, 1, 1, 2728.67, -879.718, 160.605, -2.35619, 0, 0, 0.92388, -0.382683, 900, 100, 1, '', 0),
(48111, 1731, 1, 0, 0, 1, 1, 3144.35, -610.987, 170.903, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1, '', 0),
(48112, 1731, 1, 0, 0, 1, 1, 2948.33, -697.115, 189.46, -0.069813, 0, 0, 0.034899, -0.999391, 900, 100, 1, '', 0),
(48113, 1731, 1, 0, 0, 1, 1, 2605.49, 83.8325, 99.1653, -1.0821, 0, 0, 0.515038, -0.857167, 900, 100, 1, '', 0),
(48114, 1731, 1, 0, 0, 1, 1, 2421.4, 386.887, 109.635, 2.61799, 0, 0, 0.965926, 0.258819, 900, 100, 1, '', 0),
(48115, 1731, 1, 0, 0, 1, 1, 1491.57, -2026.44, 117.112, -1.41372, 0, 0, 0.649448, -0.760406, 900, 100, 1, '', 0),
(48116, 1731, 1, 0, 0, 1, 1, 3120.77, -354.15, 137.784, -0.645772, 0, 0, 0.317305, -0.948324, 900, 100, 1, '', 0),
(48494, 1731, 1, 0, 0, 1, 1, 4606.25, -16.0594, 72.8562, 0.541052, 0, 0, 0.267238, 0.96363, 900, 100, 1, '', 0),
(48499, 1731, 1, 0, 0, 1, 1, 4608.91, 88.0124, 68.9622, -2.14675, 0, 0, 0.878817, -0.477159, 900, 100, 1, '', 0),
(48502, 1731, 1, 0, 0, 1, 1, 4374.9, 568.298, 58.7022, 1.41372, 0, 0, 0.649448, 0.760406, 900, 100, 1, '', 0),
(48505, 1731, 1, 0, 0, 1, 1, 4553.72, 134.689, 61.4659, -1.06465, 0, 0, 0.507538, -0.861629, 900, 100, 1, '', 0),
(48509, 1731, 1, 0, 0, 1, 1, 4792.63, 33.5088, 63.3617, 1.74533, 0, 0, 0.766044, 0.642788, 900, 100, 1, '', 0),
(48533, 1731, 1, 0, 0, 1, 1, 4776.8, 379.226, 46.0786, 2.44346, 0, 0, 0.939693, 0.34202, 900, 100, 1, '', 0),
(48570, 1731, 1, 0, 0, 1, 1, 6355.19, -94.2816, 28.0781, -3.01942, 0, 0, 0.998135, -0.061048, 900, 100, 1, '', 0),
(48575, 1731, 1, 0, 0, 1, 1, 6140.56, -124.413, 68.1804, -2.47837, 0, 0, 0.945519, -0.325568, 900, 100, 1, '', 0),
(48578, 1731, 1, 0, 0, 1, 1, 6140.81, -77.5832, 37.688, -1.46608, 0, 0, 0.669131, -0.743145, 900, 100, 1, '', 0),
(48581, 1731, 1, 0, 0, 1, 1, 5996.1, -116.554, 64.3843, 2.25148, 0, 0, 0.902585, 0.430511, 900, 100, 1, '', 0),
(48585, 1731, 1, 0, 0, 1, 1, 6004.19, -47.5299, 23.18, -2.94961, 0, 0, 0.995396, -0.095846, 900, 100, 1, '', 0),
(48654, 1731, 1, 0, 0, 1, 1, 7172.98, 299.393, -28.7123, 3.03687, 0, 0, 0.99863, 0.052336, 900, 100, 1, '', 0),
(48661, 1731, 1, 0, 0, 1, 1, 7148.67, 45.7817, 15.9786, -0.698132, 0, 0, 0.34202, -0.939693, 900, 100, 1, '', 0),
(48666, 1731, 1, 0, 0, 1, 1, 7358.84, 117.991, 12.6752, -2.93215, 0, 0, 0.994522, -0.104529, 900, 100, 1, '', 0),
(48668, 1731, 1, 0, 0, 1, 1, 7427.97, -258, 0.78858, 1.69297, 0, 0, 0.748956, 0.66262, 900, 100, 1, '', 0),
(48676, 1731, 1, 0, 0, 1, 1, 6827.71, -727.338, 61.7001, -2.02458, 0, 0, 0.848048, -0.529919, 900, 100, 1, '', 0),
(48681, 1731, 1, 0, 0, 1, 1, 6780.64, -702.896, 73.9283, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1, '', 0),
(48697, 1731, 1, 0, 0, 1, 1, 7116.58, -332.782, 36.5861, 2.16421, 0, 0, 0.882948, 0.469472, 900, 100, 1, '', 0),
(48704, 1731, 1, 0, 0, 1, 1, 7087.58, -685.904, 65.6651, 2.07694, 0, 0, 0.861629, 0.507538, 900, 100, 1, '', 0),
(48742, 1731, 1, 0, 0, 1, 1, 7123.89, -787.781, 73.2813, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1, '', 0),
(48748, 1731, 1, 0, 0, 1, 1, 7223.21, -1027.89, 71.9734, 2.51327, 0, 0, 0.951057, 0.309017, 900, 100, 1, '', 0),
(48761, 1731, 1, 0, 0, 1, 1, 7905.12, -1060.73, 37.5217, -0.034907, 0, 0, 0.017452, -0.999848, 900, 100, 1, '', 0),
(48762, 1731, 1, 0, 0, 1, 1, 7925.2, -1176.95, 57.2328, 2.84489, 0, 0, 0.989016, 0.147809, 900, 100, 1, '', 0),
(48764, 1731, 1, 0, 0, 1, 1, 7959.42, -1006.33, 38.6745, -0.226893, 0, 0, 0.113203, -0.993572, 900, 100, 1, '', 0),
(48766, 1731, 1, 0, 0, 1, 1, 7749.07, -955.581, 32.851, 2.19912, 0, 0, 0.891007, 0.453991, 900, 100, 1, '', 0),
(48770, 1731, 1, 0, 0, 1, 1, 7688.81, -814.313, 8.07843, -0.698132, 0, 0, 0.34202, -0.939693, 900, 100, 1, '', 0),
(48771, 1731, 1, 0, 0, 1, 1, 7475.87, -686.872, 4.20838, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1, '', 0),
(48773, 1731, 1, 0, 0, 1, 1, 7518.72, -495.795, -0.740971, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1, '', 0),
(63438, 3763, 1, 0, 0, 1, 1, -229.327, -2982.14, 92.0946, -2.53072, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63441, 3763, 1, 0, 0, 1, 1, 503.881, -3469.48, 104.275, -1.76278, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63442, 1731, 0, 0, 0, 1, 1, -11172, 1969.6, 21.3377, 2.3911, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63443, 1731, 0, 0, 0, 1, 1, -11257.2, 1054.57, 109.002, -0.122173, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63452, 1731, 0, 0, 0, 1, 1, -10524.5, 1949.69, 5.92688, -3.01941, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63453, 1731, 0, 0, 0, 1, 1, -10562.8, 2011.39, -6.07262, 3.12412, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63454, 1731, 1, 0, 0, 1, 1, 56.7158, 1562.73, 123.846, 0.017452, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63456, 3763, 1, 0, 0, 1, 1, 682.998, -3525.74, 100.477, -0.95993, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63461, 1731, 0, 0, 0, 1, 1, -9854.38, 1380.9, 38.568, -2.60053, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63466, 1731, 0, 0, 0, 1, 1, -10492.3, 1913.15, 41.5969, -1.67551, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63467, 1731, 0, 0, 0, 1, 1, -11298.1, 1636.16, 61.5966, 0.209439, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63470, 1731, 0, 0, 0, 1, 1, -10544, 1994.56, -8.69469, -0.645772, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63478, 1731, 0, 0, 0, 1, 1, -2923.04, -1608.39, 0.933756, 0.925024, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63481, 1731, 0, 0, 0, 1, 1, -3174.07, -1533.11, 2.15818, -2.25147, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63482, 1731, 0, 0, 0, 1, 1, -3037.39, -1525.7, 0.639823, 0.90757, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63483, 1731, 1, 0, 0, 1, 1, 4662.22, 766.788, 30.5175, -2.9845, 0, 0, 0, 1, 900, 100, 1, '', 0),
(63484, 1731, 0, 0, 0, 1, 1, 2781.51, -833.163, 154.953, 2.23402, 0, 0, 0, 1, 900, 100, 1, '', 0),
(64800, 181248, 530, 0, 0, 1, 1, 7116.52, -6233.31, 20.981, -1.55334, 0, 0, 0, 1, 180, 100, 1, '', 0),
(65136, 3763, 1, 0, 0, 1, 1, -2080.55, -2800.56, 97.923, 0.837757, 0, 0, 0, 1, 180, 100, 1, '', 0),
(65144, 1731, 0, 0, 0, 1, 1, -9519.65, -2103.17, 94.1483, -2.58308, 0, 0, 0, 1, 180, 100, 1, '', 0),
(65155, 1731, 0, 0, 0, 1, 1, -3132.9, -1450.79, -0.29859, 0.436332, 0, 0, 0, 1, 180, 100, 1, '', 0),
(65161, 1731, 0, 0, 0, 1, 1, -2899.95, -1715.63, 2.10021, -1.64061, 0, 0, 0, 1, 180, 100, 1, '', 0),
(73500, 1731, 0, 0, 0, 1, 1, -5811.24, -426.122, 370.75, 0.279253, 0, 0, 0.139173, 0.990268, 900, 255, 1, '', 0),
(73501, 1731, 0, 0, 0, 1, 1, -5148.34, -324.104, 402.391, 2.98451, 0, 0, 0.996917, 0.078459, 900, 255, 1, '', 0),
(73502, 1731, 0, 0, 0, 1, 1, -4962.52, -169.262, 386.505, -0.15708, 0, 0, 0.078459, -0.996917, 900, 255, 1, '', 0),
(73503, 1731, 0, 0, 0, 1, 1, -4827.8, -231.656, 406.364, 0.366519, 0, 0, 0.182236, 0.983255, 900, 255, 1, '', 0),
(73504, 1731, 0, 0, 0, 1, 1, -5018.81, 466.14, 419.118, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73505, 1731, 0, 0, 0, 1, 1, -5557.61, 649.328, 398.718, 0.855211, 0, 0, 0.414693, 0.909961, 900, 255, 1, '', 0),
(73506, 1731, 0, 0, 0, 1, 1, -5579.25, 745.874, 391.875, 1.39626, 0, 0, 0.642788, 0.766044, 900, 255, 1, '', 0),
(73507, 1731, 0, 0, 0, 1, 1, -5671.96, 758.951, 390.155, 0.767945, 0, 0, 0.374607, 0.927184, 900, 255, 1, '', 0),
(73508, 1731, 0, 0, 0, 1, 1, -5815.37, 224.161, 393.461, 2.18166, 0, 0, 0.887011, 0.461749, 900, 255, 1, '', 0),
(73509, 1731, 0, 0, 0, 1, 1, -5629.82, -64.7745, 420.671, -1.39626, 0, 0, 0.642788, -0.766044, 900, 255, 1, '', 0),
(73510, 1731, 0, 0, 0, 1, 1, -5557.18, 188.45, 416.896, -1.11701, 0, 0, 0.529919, -0.848048, 900, 255, 1, '', 0),
(73511, 1731, 0, 0, 0, 1, 1, -5660.26, 178.374, 427.587, 0.645772, 0, 0, 0.317305, 0.948324, 900, 255, 1, '', 0),
(73512, 1731, 0, 0, 0, 1, 1, -5235.36, 102.745, 392.428, 0.349066, 0, 0, 0.173648, 0.984808, 900, 255, 1, '', 0),
(73513, 1731, 0, 0, 0, 1, 1, -5229.89, 31.2378, 363.083, 0.802851, 0, 0, 0.390731, 0.920505, 900, 255, 1, '', 0),
(73514, 1731, 0, 0, 0, 1, 1, -5120.81, -112.382, 399.669, -0.959931, 0, 0, 0.461749, -0.887011, 900, 255, 1, '', 0),
(73515, 1731, 0, 0, 0, 1, 1, -5085.58, -272.124, 441.336, -3.07178, 0, 0, 0.999391, -0.034899, 900, 255, 1, '', 0),
(73516, 1731, 0, 0, 0, 1, 1, -5515.74, -358.843, 361.206, -2.00713, 0, 0, 0.843391, -0.5373, 900, 255, 1, '', 0),
(73517, 1731, 0, 0, 0, 1, 1, -5452.19, -281.138, 358.577, -2.47837, 0, 0, 0.945519, -0.325568, 900, 255, 1, '', 0),
(73518, 1731, 0, 0, 0, 1, 1, -5499.31, -206.126, 354.253, 0.785398, 0, 0, 0.382683, 0.92388, 900, 255, 1, '', 0),
(73519, 1731, 0, 0, 0, 1, 1, -5770.99, -653.592, 403.583, -0.2618, 0, 0, 0.130526, -0.991445, 900, 255, 1, '', 0),
(73520, 1731, 0, 0, 0, 1, 1, -5334.67, -659.969, 394.837, -0.872665, 0, 0, 0.422618, -0.906308, 900, 255, 1, '', 0),
(73521, 1731, 0, 0, 0, 1, 1, -5600.43, -744.175, 434.065, 1.79769, 0, 0, 0.782608, 0.622515, 900, 255, 1, '', 0),
(73522, 1731, 0, 0, 0, 1, 1, -5789.91, -781.136, 401.291, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73523, 1731, 0, 0, 0, 1, 1, -5723.14, -977.155, 400.596, -0.663225, 0, 0, 0.325568, -0.945519, 900, 255, 1, '', 0),
(73524, 1731, 0, 0, 0, 1, 1, -5255.03, -1049.99, 399.236, 1.48353, 0, 0, 0.67559, 0.737277, 900, 255, 1, '', 0),
(73525, 1731, 0, 0, 0, 1, 1, -5422.98, -1276.98, 447.711, -1.78024, 0, 0, 0.777146, -0.62932, 900, 255, 1, '', 0),
(73526, 1731, 0, 0, 0, 1, 1, -5733.16, -1091.58, 387.806, -1.93731, 0, 0, 0.824126, -0.566406, 900, 255, 1, '', 0),
(73527, 1731, 0, 0, 0, 1, 1, -5717.33, -1426.85, 432.837, 1.11701, 0, 0, 0.529919, 0.848048, 900, 255, 1, '', 0),
(73528, 1731, 0, 0, 0, 1, 1, -5525.45, -1499.6, 409.249, -1.55334, 0, 0, 0.700909, -0.71325, 900, 255, 1, '', 0),
(73529, 1731, 0, 0, 0, 1, 1, -5738.28, -1613.1, 368.367, -1.22173, 0, 0, 0.573576, -0.819152, 900, 255, 1, '', 0),
(73530, 1731, 0, 0, 0, 1, 1, -5757.61, -1588.93, 362.943, 2.94961, 0, 0, 0.995396, 0.095846, 900, 255, 1, '', 0),
(73531, 1731, 0, 0, 0, 1, 1, -5868.58, -1568.41, 368.365, -0.645772, 0, 0, 0.317305, -0.948324, 900, 255, 1, '', 0),
(73532, 1731, 0, 0, 0, 1, 1, -5632, -1752, 357.2, -0.663225, 0, 0, 0.325568, -0.945519, 900, 255, 1, '', 0),
(73533, 1731, 0, 0, 0, 1, 1, -5640.9, -1706.94, 362.449, 2.58309, 0, 0, 0.961262, 0.275637, 900, 255, 1, '', 0),
(73534, 1731, 0, 0, 0, 1, 1, -5546.32, -1687.21, 343.854, 1.32645, 0, 0, 0.615662, 0.788011, 900, 255, 1, '', 0),
(73535, 1731, 0, 0, 0, 1, 1, -5616.14, -1752.76, 413.743, -2.21657, 0, 0, 0.894934, -0.446198, 900, 255, 1, '', 0),
(73536, 1731, 0, 0, 0, 1, 1, -5788.15, -1763.12, 407.646, 2.44346, 0, 0, 0.939693, 0.34202, 900, 255, 1, '', 0),
(73537, 1731, 0, 0, 0, 1, 1, -5950.65, -1827.33, 459.444, -1.93731, 0, 0, 0.824126, -0.566406, 900, 255, 1, '', 0),
(73538, 1731, 0, 0, 0, 1, 1, -5932.28, -1705.11, 425.12, -0.139626, 0, 0, 0.069756, -0.997564, 900, 255, 1, '', 0),
(73539, 1731, 0, 0, 0, 1, 1, -5633, -2081.8, 403.557, -2.21657, 0, 0, 0.894934, -0.446198, 900, 255, 1, '', 0),
(73540, 1731, 0, 0, 0, 1, 1, -5560.81, -2216.06, 436.481, -1.3439, 0, 0, 0.622515, -0.782608, 900, 255, 1, '', 0),
(73541, 1731, 0, 0, 0, 1, 1, -4981.55, -2143.23, 415.25, -1.18682, 0, 0, 0.559193, -0.829037, 900, 255, 1, '', 0),
(73542, 1731, 0, 0, 0, 1, 1, -5135.02, -2059.21, 436.066, 0.05236, 0, 0, 0.026177, 0.999657, 900, 255, 1, '', 0),
(73543, 1731, 0, 0, 0, 1, 1, -6015.35, -577.502, 414.565, 1.6057, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73544, 1731, 0, 0, 0, 1, 1, -5958.88, -661.093, 404.042, 2.07694, 0, 0, 0.861629, 0.507538, 900, 255, 1, '', 0),
(73545, 1731, 0, 0, 0, 1, 1, -5564.15, -1394.78, 409.548, 1.11701, 0, 0, 0.529919, 0.848048, 900, 255, 1, '', 0),
(73546, 1731, 0, 0, 0, 1, 1, -5629.48, -962.107, 406.375, 1.74533, 0, 0, 0.766044, 0.642788, 900, 255, 1, '', 0),
(73547, 1731, 0, 0, 0, 1, 1, -5818.93, -1329.16, 394.533, 3.00197, 0, 0, 0.997564, 0.069757, 900, 255, 1, '', 0),
(73548, 1731, 0, 0, 0, 1, 1, -5649.31, -52.1474, 418.394, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73549, 1731, 0, 0, 0, 1, 1, -5476.12, -111.775, 422.548, 2.67035, 0, 0, 0.97237, 0.233445, 900, 255, 1, '', 0),
(73550, 1731, 0, 0, 0, 1, 1, -5463.95, -181.433, 424.59, 3.00197, 0, 0, 0.997564, 0.069757, 900, 255, 1, '', 0),
(73551, 1731, 0, 0, 0, 1, 1, -5403.73, -119.305, 369.452, -1.22173, 0, 0, 0.573576, -0.819152, 900, 255, 1, '', 0),
(73552, 1731, 0, 0, 0, 1, 1, -5359.79, -196.572, 451.925, 0.575959, 0, 0, 0.284015, 0.95882, 900, 255, 1, '', 0),
(73553, 1731, 0, 0, 0, 1, 1, -5431.58, -308.889, 358.476, -3.07178, 0, 0, 0.999391, -0.034899, 900, 255, 1, '', 0),
(73554, 1731, 0, 0, 0, 1, 1, -5564.47, -307.319, 365.538, -0.994838, 0, 0, 0.477159, -0.878817, 900, 255, 1, '', 0),
(73555, 1731, 0, 0, 0, 1, 1, -5505.15, -1743.88, 336.827, -2.98451, 0, 0, 0.996917, -0.078459, 900, 255, 1, '', 0),
(73556, 1731, 0, 0, 0, 1, 1, -5420.69, -2223.09, 424.955, 1.72788, 0, 0, 0.760406, 0.649448, 900, 255, 1, '', 0),
(73557, 1731, 0, 0, 0, 1, 1, -5168.54, -2256.53, 419.185, -1.15192, 0, 0, 0.544639, -0.838671, 900, 255, 1, '', 0),
(73558, 1731, 0, 0, 0, 1, 1, -5941.51, -676.599, 427.108, -1.22173, 0, 0, 0.573576, -0.819152, 900, 255, 1, '', 0),
(73559, 1731, 0, 0, 0, 1, 1, -5963, -307.491, 455.642, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73560, 1731, 0, 0, 0, 1, 1, -5965.87, 29.5307, 372.507, -0.069813, 0, 0, 0.034899, -0.999391, 900, 255, 1, '', 0),
(73561, 1731, 0, 0, 0, 1, 1, -5647.65, -378.015, 370.657, -0.418879, 0, 0, 0.207912, -0.978148, 900, 255, 1, '', 0),
(73562, 1731, 0, 0, 0, 1, 1, -5286.26, -840.516, 406.846, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73563, 1731, 0, 0, 0, 1, 1, -5613.39, -1589.05, 403.809, 0.261799, 0, 0, 0.130526, 0.991445, 900, 255, 1, '', 0),
(73564, 1731, 0, 0, 0, 1, 1, -5570.23, -1705.6, 371.531, 2.46091, 0, 0, 0.942641, 0.333807, 900, 255, 1, '', 0),
(73565, 1731, 0, 0, 0, 1, 1, -5566.91, -1726.08, 342.898, 0.767945, 0, 0, 0.374607, 0.927184, 900, 255, 1, '', 0),
(73566, 1731, 0, 0, 0, 1, 1, -5456.39, -1757.68, 443.358, 2.09439, 0, 0, 0.866025, 0.5, 900, 255, 1, '', 0),
(73567, 1731, 0, 0, 0, 1, 1, -5594.91, -1894.3, 398.391, -1.91986, 0, 0, 0.819152, -0.573576, 900, 255, 1, '', 0),
(73568, 1731, 0, 0, 0, 1, 1, -5828.94, -67.125, 366.152, 2.58309, 0, 0, 0.961262, 0.275637, 900, 255, 1, '', 0),
(73569, 1731, 0, 0, 0, 1, 1, -5462.9, -321.323, 361.567, 2.79253, 0, 0, 0.984808, 0.173648, 900, 255, 1, '', 0),
(73570, 1731, 0, 0, 0, 1, 1, -5082.65, -153.88, 442.593, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73571, 1731, 0, 0, 0, 1, 1, -5281.5, -78.8783, 402.053, -2.63545, 0, 0, 0.968148, -0.25038, 900, 255, 1, '', 0),
(73572, 1731, 0, 0, 0, 1, 1, -5319.51, -744.61, 392.283, 1.79769, 0, 0, 0.782608, 0.622515, 900, 255, 1, '', 0),
(73573, 1731, 0, 0, 0, 1, 1, -5000.76, -291.451, 445.826, -0.890118, 0, 0, 0.430511, -0.902585, 900, 255, 1, '', 0),
(73574, 1731, 0, 0, 0, 1, 1, -4974.16, -233.916, 415.163, -2.53073, 0, 0, 0.953717, -0.300706, 900, 255, 1, '', 0),
(73575, 1731, 0, 0, 0, 1, 1, -5567.07, -1799.28, 360.231, -0.890117, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73576, 1731, 0, 0, 0, 1, 1, -5419.91, -131.974, 349.558, 1.06465, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73577, 1731, 0, 0, 0, 1, 1, -5684.73, -1663.59, 360.849, 2.47837, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73578, 1731, 0, 0, 0, 1, 1, -5546.48, -1776.41, 345.431, 2.93214, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73579, 1731, 0, 0, 0, 1, 1, -5357.22, -421.696, 397.762, -1.93732, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73580, 1731, 0, 0, 0, 1, 1, -5776.53, -2050.67, 403.284, 2.68781, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73581, 1731, 0, 0, 0, 1, 1, -5879.19, -1522.75, 380.578, -1.53589, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73582, 1731, 0, 0, 0, 1, 1, -5821.6, -1922.89, 413.296, 0.383971, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73583, 1731, 0, 0, 0, 1, 1, -5770.21, -2184.04, 410.181, -2.80997, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73584, 1731, 0, 0, 0, 1, 1, -5605.74, -1713.77, 360.251, 2.75761, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73585, 1731, 0, 0, 0, 1, 1, -5051.87, -585.697, 426.163, 0, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73586, 1731, 0, 0, 0, 1, 1, -5757, -1705.79, 369.181, -1.32645, 0, 0, 0, 1, 900, 255, 1, '', 0),
(73600, 1731, 0, 0, 0, 1, 1, -11045.7, -299.893, 16.2588, -2.53072, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73605, 1731, 0, 0, 0, 1, 1, -10174.9, -700.101, 44.6107, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73610, 1731, 0, 0, 0, 1, 1, -10436, -1012, 48.816, -3, 0, 0, -0.956305, 0.292372, 300, 255, 1, '', 0),
(73615, 1731, 0, 0, 0, 1, 1, -11125.1, -774.882, 59.6363, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73620, 1731, 0, 0, 0, 1, 1, -10974, 73.907, 39.412, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73625, 1731, 0, 0, 0, 1, 1, -11102, -256, 34.241, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73630, 1731, 0, 0, 0, 1, 1, -10519.2, 677.127, 15.6122, 2.58308, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73635, 1731, 0, 0, 0, 1, 1, -10717, -587, 64.392, 2.897, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73640, 1731, 0, 0, 0, 1, 1, -11101, -254, 33.215, 2.566, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73645, 1731, 0, 0, 0, 1, 1, -10449.9, -1020.57, 53.7499, 1.50098, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73650, 1731, 0, 0, 0, 1, 1, -11080, -1109, 44.755, -2, 0, 0, -0.688354, 0.725375, 300, 255, 1, '', 0),
(73655, 1731, 0, 0, 0, 1, 1, -10486.4, -770.31, 62.5892, 1.09956, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73660, 1731, 0, 0, 0, 1, 1, -10415, -1255, 52.485, -3, 0, 0, -0.984808, 0.173648, 300, 255, 1, '', 0),
(73665, 1731, 0, 0, 0, 1, 1, -11010, -990, 68.9247, -1, 0, 0, -0.622514, 0.782608, 300, 255, 1, '', 0),
(73675, 1731, 0, 0, 0, 1, 1, -10233.3, -1358.39, 83.4799, 2.30383, 0, 0, 0.913545, 0.406737, 300, 255, 1, '', 0),
(73680, 1731, 0, 0, 0, 1, 1, -11101.3, -74.5433, 16.4636, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73685, 1731, 0, 0, 0, 1, 1, -11137.5, -904.159, 66.684, 2.1293, 0, 0, 0.87462, 0.48481, 300, 255, 1, '', 0),
(73690, 1731, 0, 0, 0, 1, 1, -11158.4, -925.164, 85.8275, 1.53589, 0, 0, 0.694658, 0.71934, 300, 255, 1, '', 0),
(73695, 1731, 0, 0, 0, 1, 1, -11137.7, -1174.23, 43.9495, 2.47837, 0, 0, 0.945519, 0.325568, 300, 255, 1, '', 0),
(73700, 1731, 0, 0, 0, 1, 1, -11210.8, -846.305, 78.2837, -0.575959, 0, 0, 0.284015, -0.95882, 300, 255, 1, '', 0),
(73705, 1731, 0, 0, 0, 1, 1, -11235.4, -875.842, 85.4821, 2.67035, 0, 0, 0.97237, 0.233445, 300, 255, 1, '', 0),
(73710, 1731, 0, 0, 0, 1, 1, -11087.5, 12.5553, 44.3524, -1.81514, 0, 0, 0.788011, -0.615661, 300, 255, 1, '', 0),
(73715, 1731, 0, 0, 0, 1, 1, -10600.2, -1486.5, 94.3496, -1.29154, 0, 0, 0.601815, -0.798635, 300, 255, 1, '', 0),
(73720, 1731, 0, 0, 0, 1, 1, -10848.7, -1375.08, 63.6493, -2.60054, 0, 0, 0.96363, -0.267238, 300, 255, 1, '', 0),
(73725, 1731, 0, 0, 0, 1, 1, -11044.9, -53.2851, 18.2922, -1.43117, 0, 0, 0.656059, -0.75471, 300, 255, 1, '', 0),
(73730, 1731, 0, 0, 0, 1, 1, -11100, -195.819, 28.6323, -1.41372, 0, 0, 0.649448, -0.760406, 300, 255, 1, '', 0),
(73735, 1731, 0, 0, 0, 1, 1, -10436.5, -780.711, 58.56, -0.244346, 0, 0, 0.121869, -0.992546, 300, 255, 1, '', 0),
(73740, 1731, 0, 0, 0, 1, 1, -10290.9, -137.715, 41.327, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73745, 1731, 0, 0, 0, 1, 1, -10395.6, -748.918, 72.097, -1.09956, 0, 0, 0.522499, -0.85264, 300, 255, 1, '', 0),
(73750, 1731, 0, 0, 0, 1, 1, -10481.8, 41.4525, 42.5627, 1.98968, 0, 0, 0.838671, 0.544639, 300, 255, 1, '', 0),
(73755, 1731, 0, 0, 0, 1, 1, -11129.1, -1153.68, 45.1154, 1.0821, 0, 0, 0.515038, 0.857167, 300, 255, 1, '', 0),
(73760, 1731, 0, 0, 0, 1, 1, -10513.9, -74.5481, 45.2932, 3.07178, 0, 0, 0.999391, 0.034899, 300, 255, 1, '', 0),
(73765, 1731, 0, 0, 0, 1, 1, -10643.7, -985.37, 68.1491, -0.314159, 0, 0, 0.156434, -0.987688, 300, 255, 1, '', 0),
(73770, 1731, 0, 0, 0, 1, 1, -11160.1, 290.516, 41.1961, 2.00713, 0, 0, 0.843391, 0.5373, 300, 255, 1, '', 0),
(73775, 1731, 0, 0, 0, 1, 1, -11125, -253.782, 45.8497, -0.541052, 0, 0, 0.267238, -0.96363, 300, 255, 1, '', 0),
(73780, 1731, 0, 0, 0, 1, 1, -11137.5, -843.614, 76.8614, -0.418879, 0, 0, 0.207912, -0.978148, 300, 255, 1, '', 0),
(73785, 1731, 0, 0, 0, 1, 1, -10638.4, -1390.38, 60.8947, 2.05949, 0, 0, 0.857167, 0.515038, 300, 255, 1, '', 0),
(73790, 1731, 0, 0, 0, 1, 1, -10415.6, -1253.79, 52.5802, -2.79253, 0, 0, 0.984808, -0.173648, 300, 255, 1, '', 0),
(73795, 1731, 0, 0, 0, 1, 1, -11050, -1033.27, 72.4054, 0.244346, 0, 0, 0.121869, 0.992546, 300, 255, 1, '', 0),
(73800, 1731, 0, 0, 0, 1, 1, -10359, -790.396, 61.1401, 2.32129, 0, 0, 0.91706, 0.398749, 300, 255, 1, '', 0),
(73805, 1731, 0, 0, 0, 1, 1, -10483.2, -999.957, 47.3942, 0.331613, 0, 0, 0.165048, 0.986286, 300, 255, 1, '', 0),
(73810, 1731, 0, 0, 0, 1, 1, -11014.4, -1079.73, 51.1815, -2.96706, 0, 0, 0.996195, -0.087156, 300, 255, 1, '', 0),
(73815, 1731, 0, 0, 0, 1, 1, -10341.1, 83.216, 36.8964, -1.53589, 0, 0, 0.694658, -0.71934, 300, 255, 1, '', 0),
(73820, 1731, 0, 0, 0, 1, 1, -10726.6, -965.106, 70.1185, 1.67552, 0, 0, 0.743145, 0.669131, 300, 255, 1, '', 0),
(73825, 1731, 0, 0, 0, 1, 1, -11039.1, -420.595, 36.0661, -0.087267, 0, 0, 0.04362, -0.999048, 300, 255, 1, '', 0),
(73830, 1731, 0, 0, 0, 1, 1, -10467.1, -948.977, 49.8306, -2.54818, 0, 0, 0.956305, -0.292372, 300, 255, 1, '', 0),
(73835, 1731, 0, 0, 0, 1, 1, -11105.8, -1170.61, 42.2928, -0.349066, 0, 0, 0.173648, -0.984808, 300, 255, 1, '', 0),
(73840, 1731, 0, 0, 0, 1, 1, -10854.2, -533.286, 39.3664, -2.46091, 0, 0, 0.942641, -0.333807, 300, 255, 1, '', 0),
(73845, 1731, 0, 0, 0, 1, 1, -10557.2, -760.492, 60.5833, -1.0472, 0, 0, 0.5, -0.866025, 300, 255, 1, '', 0),
(73850, 1731, 0, 0, 0, 1, 1, -10395.8, 113.076, 34.8874, 1.81514, 0, 0, 0.788011, 0.615662, 300, 255, 1, '', 0),
(73855, 1731, 0, 0, 0, 1, 1, -10329.5, 134.72, 35.5812, 2.54818, 0, 0, 0.956305, 0.292372, 300, 255, 1, '', 0),
(73860, 1731, 0, 0, 0, 1, 1, -10800.4, -424.663, 59.9856, -0.383972, 0, 0, 0.190809, -0.981627, 300, 255, 1, '', 0),
(73865, 1731, 0, 0, 0, 1, 1, -10633.3, -1458.44, 91.035, -2.77507, 0, 0, 0.983255, -0.182235, 300, 255, 1, '', 0),
(73870, 1731, 0, 0, 0, 1, 1, -11104.1, -1087.41, 63.1955, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73875, 1731, 0, 0, 0, 1, 1, -11114.4, -1016.67, 80.772, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73880, 1731, 0, 0, 0, 1, 1, -10122.7, -342.507, 51.9179, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73885, 1731, 0, 0, 0, 1, 1, -11155.2, -1167.31, 87.6797, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73890, 1731, 0, 0, 0, 1, 1, -11180.8, -135.495, 80.4793, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73895, 1731, 0, 0, 0, 1, 1, -10662.4, -894.27, 58.8167, 0.994837, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73900, 1731, 0, 0, 0, 1, 1, -10677, -912.576, 63.6344, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73905, 1731, 0, 0, 0, 1, 1, -11221.5, -886.7, 107.681, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73910, 1731, 0, 0, 0, 1, 1, -11181.6, -153.58, 83.9906, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73915, 1731, 0, 0, 0, 1, 1, -11001.5, 8.73486, 30.7184, -0.907571, 0, 0, 0.438371, -0.898794, 300, 255, 1, '', 0),
(73920, 1731, 0, 0, 0, 1, 1, -11234.2, -878.234, 109.259, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73925, 1731, 0, 0, 0, 1, 1, -10121.2, -553.419, 72.1722, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73930, 1731, 0, 0, 0, 1, 1, -11095.2, -72.7349, 14.6408, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73935, 1731, 0, 0, 0, 1, 1, -11093.4, -30.2541, 28.7652, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73940, 1731, 0, 0, 0, 1, 1, -11099.1, -1155.55, 42.4416, 0.331611, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73945, 1731, 0, 0, 0, 1, 1, -11212.6, -174.63, 4.63508, -0.366519, 0, 0, 0.182236, -0.983255, 300, 255, 1, '', 0),
(73950, 1731, 0, 0, 0, 1, 1, -10714.9, -1436.62, 63.2028, -1.90241, 0, 0, 0.814116, -0.580703, 300, 255, 1, '', 0),
(73955, 1731, 0, 0, 0, 1, 1, -11178.4, -153.236, 6.88822, -1.0821, 0, 0, 0.515038, -0.857167, 300, 255, 1, '', 0),
(73960, 1731, 0, 0, 0, 1, 1, -10699.3, -187.865, 39.9624, -2.84488, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73965, 1731, 0, 0, 0, 1, 1, -10856.6, -1283.99, 62.4551, 0.296705, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73970, 1731, 0, 0, 0, 1, 1, -10088, -450.424, 65.3588, 2.28638, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73975, 1731, 0, 0, 0, 1, 1, -10966.5, -183.177, 17.1386, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73980, 1731, 0, 0, 0, 1, 1, -11118.3, -578.917, 46.1832, -0.802851, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73985, 1731, 0, 0, 0, 1, 1, -11072.4, -1385.5, 72.7384, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73990, 1731, 0, 0, 0, 1, 1, -11062.5, -729.195, 59.6658, -1.65806, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73995, 1731, 0, 0, 0, 1, 1, -10160.4, -288.859, 47.7796, -0.349065, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74000, 1731, 0, 0, 0, 1, 1, -11166.5, 154.265, 35.092, -0.575957, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74005, 1731, 0, 0, 0, 1, 1, -10575.1, -1008.32, 55.5346, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74010, 1731, 0, 0, 0, 1, 1, -10766.7, -1437.56, 71.3134, -1.0821, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74015, 1731, 0, 0, 0, 1, 1, -11109.5, 63.564, 41.8177, -2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74020, 1731, 0, 0, 0, 1, 1, -11154.1, 116.377, 39.0482, 2.51327, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74025, 1731, 0, 0, 0, 1, 1, -11029.2, -1266.67, 53.8279, -2.77507, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74035, 1731, 0, 0, 0, 1, 1, -11091.6, -257.724, 32.2351, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74040, 1731, 0, 0, 0, 1, 1, -11099.4, -696.347, 54.7799, 1.58825, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74055, 1731, 0, 0, 0, 1, 1, -10563.4, -1486.63, 95.4168, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74060, 1731, 0, 0, 0, 1, 1, -11183.7, -135.158, 8.11372, 2.09439, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74065, 1731, 0, 0, 0, 1, 1, -10237.8, -1291.58, 46.9721, -1.22173, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74070, 1731, 0, 0, 0, 1, 1, -10678.7, -908.818, 68.4815, 2.65289, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74080, 1731, 0, 0, 0, 1, 1, -11123.5, -1165.56, 44.413, -1.09956, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74085, 1731, 0, 0, 0, 1, 1, -10559.6, -724.451, 76.4627, -0.785397, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74095, 1731, 0, 0, 0, 1, 1, -10301.6, -1467.02, 90.2184, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74100, 1731, 0, 0, 0, 1, 1, -11016.6, -1180.58, 46.4404, 2.93214, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74105, 1731, 0, 0, 0, 1, 1, -11162.2, -182.4, 12.8593, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74110, 1731, 0, 0, 0, 1, 1, -11137.3, -166.128, 11.467, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74115, 1731, 0, 0, 0, 1, 1, -11099.9, -159.898, 13.3211, 2.14675, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74120, 1731, 0, 0, 0, 1, 1, -11096.3, -135.736, 39.7418, 1.46608, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74125, 1731, 0, 0, 0, 1, 1, -11092.8, -1157.22, 55.1905, 1.09956, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74130, 1731, 0, 0, 0, 1, 1, -10997.8, -542.823, 34.96, -0.95993, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74145, 1731, 0, 0, 0, 1, 1, -10716.4, -587.625, 64.6095, 2.89724, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74150, 1731, 0, 0, 0, 1, 1, -11080.6, -1109.6, 45.5113, -1.51844, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74155, 1731, 0, 0, 0, 1, 1, -10446.1, -1312.74, 63.4724, -0.418879, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74160, 1731, 0, 0, 0, 1, 1, -11246, -173.352, 5.11785, 0.383971, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74800, 1731, 0, 0, 0, 1, 1, -9620.11, -46.3336, 47.3641, 2.04204, 0, 0, 0.85264, 0.522499, 900, 255, 1, '', 0),
(74801, 1731, 0, 0, 0, 1, 1, -9605.88, 517.683, 42.3721, -1.6057, 0, 0, 0.71934, -0.694658, 900, 255, 1, '', 0),
(74802, 1731, 0, 0, 0, 1, 1, -9585.05, 211.663, 50.7004, 0.261799, 0, 0, 0.130526, 0.991445, 900, 255, 1, '', 0),
(74803, 1731, 0, 0, 0, 1, 1, -9652.22, -212.85, 50.9247, -0.872665, 0, 0, 0.422618, -0.906308, 900, 255, 1, '', 0),
(74804, 1731, 0, 0, 0, 1, 1, -9685.88, -1195.68, 50.4208, 1.02974, 0, 0, 0.492424, 0.870356, 900, 255, 1, '', 0),
(74805, 1731, 0, 0, 0, 1, 1, -9248.86, 440.654, 88.0219, 3.00197, 0, 0, 0.997564, 0.069757, 900, 255, 1, '', 0),
(74806, 1731, 0, 0, 0, 1, 1, -9448.71, -551.097, 66.5675, -2.68781, 0, 0, 0.97437, -0.224951, 900, 255, 1, '', 0),
(74807, 1731, 0, 0, 0, 1, 1, -9763.46, -528.461, 39.4358, 0.907571, 0, 0, 0.438371, 0.898794, 900, 255, 1, '', 0),
(74808, 1731, 0, 0, 0, 1, 1, -9736.79, 87.7491, 12.8907, -2.63545, 0, 0, 0.968148, -0.25038, 900, 255, 1, '', 0),
(74809, 1731, 0, 0, 0, 1, 1, -9723.19, 93.0938, 48.3514, 2.51327, 0, 0, 0.951057, 0.309017, 900, 255, 1, '', 0),
(74810, 1731, 0, 0, 0, 1, 1, -9758.44, 140.715, 21.5286, 2.63545, 0, 0, 0.968148, 0.25038, 900, 255, 1, '', 0),
(74811, 1731, 0, 0, 0, 1, 1, -9799.79, 107.987, 6.13216, 2.53073, 0, 0, 0.953717, 0.300706, 900, 255, 1, '', 0),
(74812, 1731, 0, 0, 0, 1, 1, -9840.77, 121.837, 6.28642, 2.82743, 0, 0, 0.987688, 0.156434, 900, 255, 1, '', 0),
(74813, 1731, 0, 0, 0, 1, 1, -9787.18, 123.07, 25.8958, 0.069813, 0, 0, 0.034899, 0.999391, 900, 255, 1, '', 0),
(74814, 1731, 0, 0, 0, 1, 1, -9802.83, 128.656, 7.04257, 2.93215, 0, 0, 0.994522, 0.104528, 900, 255, 1, '', 0),
(74815, 1731, 0, 0, 0, 1, 1, -9828.76, 184.747, 13.2738, 2.30383, 0, 0, 0.913545, 0.406737, 900, 255, 1, '', 0),
(74816, 1731, 0, 0, 0, 1, 1, -9628.99, 102.231, 48.9472, 2.32129, 0, 0, 0.91706, 0.398749, 900, 255, 1, '', 0),
(74817, 1731, 0, 0, 0, 1, 1, -9775.94, 145.396, 25.5595, 1.46608, 0, 0, 0.669131, 0.743145, 900, 255, 1, '', 0),
(74818, 1731, 0, 0, 0, 1, 1, -9816, 148.449, 5.88154, -0.610865, 0, 0, 0.300706, -0.953717, 900, 255, 1, '', 0),
(74819, 1731, 0, 0, 0, 1, 1, -9183.71, -614.584, 61.8164, -0.610865, 0, 0, 0.300706, -0.953717, 900, 255, 1, '', 0),
(74820, 1731, 0, 0, 0, 1, 1, -9100.51, -586.156, 58.8091, 0.488692, 0, 0, 0.241922, 0.970296, 900, 255, 1, '', 0),
(74821, 1731, 0, 0, 0, 1, 1, -9030.71, -617.63, 56.8576, -0.820305, 0, 0, 0.398749, -0.91706, 900, 255, 1, '', 0),
(74822, 1731, 0, 0, 0, 1, 1, -9166.6, -572.001, 58.69, 2.33874, 0, 0, 0.920505, 0.390731, 900, 255, 1, '', 0),
(74823, 1731, 0, 0, 0, 1, 1, -9254.93, -452.93, 82.6758, 2.47837, 0, 0, 0.945519, 0.325568, 900, 255, 1, '', 0),
(74824, 1731, 0, 0, 0, 1, 1, -9742.96, 139.8, 20.6754, 1.3439, 0, 0, 0.622515, 0.782608, 900, 255, 1, '', 0),
(74825, 1731, 0, 0, 0, 1, 1, -9817.66, 205.771, 16.6964, -0.872665, 0, 0, 0.422618, -0.906308, 900, 255, 1, '', 0),
(74826, 1731, 0, 0, 0, 1, 1, -9324.58, -1100.02, 66.707, 2.84488, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74827, 1731, 0, 0, 0, 1, 1, -9816.79, 178.992, 23.198, -1.39626, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74828, 1731, 0, 0, 0, 1, 1, -9775.81, -420.04, 49.1723, -2.80997, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74829, 1731, 0, 0, 0, 1, 1, -9733.24, -1031.51, 39.6084, 3.05433, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74830, 1731, 0, 0, 0, 1, 1, -9058.96, -621.586, 54.7671, -2.1293, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74831, 1731, 0, 0, 0, 1, 1, -9792.23, -1253.16, 36.0011, -2.25147, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74832, 1731, 0, 0, 0, 1, 1, -9897.81, -1150, 24.353, -1.93731, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74833, 1731, 0, 0, 0, 1, 1, -9806.22, -1365.82, 54.8668, 0.418879, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74834, 1731, 0, 0, 0, 1, 1, -9958.34, -218.238, 28.9477, -2.74016, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74835, 1731, 0, 0, 0, 1, 1, -9101.61, 76.0012, 93.6697, 1.39626, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74836, 1731, 0, 0, 0, 1, 1, -9532.9, -861.734, 50.7958, 2.37364, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74837, 1731, 0, 0, 0, 1, 1, -9901.75, -202.905, 37.1435, 0, 0, 0, 0, 0, 900, 255, 1, '', 0),
(74838, 1731, 0, 0, 0, 1, 1, -9841.58, -340.996, 46.2621, 2.25147, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74839, 1731, 0, 0, 0, 1, 1, -9945.23, 204.505, 27.2168, -0.942478, 0, 0, 0.453991, -0.891006, 900, 255, 1, '', 0),
(74840, 1731, 0, 0, 0, 1, 1, -9605.38, -741.081, 52.7849, 1.65806, 0, 0, 0.737277, 0.67559, 900, 255, 1, '', 0),
(74841, 1731, 0, 0, 0, 1, 1, -9647.58, -115.74, 48.5227, -2.51327, 0, 0, 0.951057, -0.309017, 900, 255, 1, '', 0),
(74842, 1731, 0, 0, 0, 1, 1, -9415.71, -561.253, 69.0323, 2.05949, 0, 0, 0.857167, 0.515038, 900, 255, 1, '', 0),
(74843, 1731, 0, 0, 0, 1, 1, -9188.92, 488.25, 113.085, -1.32645, 0, 0, 0.615661, -0.788011, 900, 255, 1, '', 0),
(74844, 1731, 0, 0, 0, 1, 1, -9557.9, 573.378, 53.6437, -2.58309, 0, 0, 0.961262, -0.275637, 900, 255, 1, '', 0),
(74845, 1731, 0, 0, 0, 1, 1, -9116.41, 194.16, 112.556, -0.523599, 0, 0, 0.258819, -0.965926, 900, 255, 1, '', 0),
(74846, 1731, 0, 0, 0, 1, 1, -9856.54, 242.269, 24.5645, -0.698132, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74847, 1731, 0, 0, 0, 1, 1, -9921.27, -285.241, 35.7804, 1.53589, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74848, 1731, 0, 0, 0, 1, 1, -9875.08, -251.75, 38.4121, -0.506145, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74849, 1731, 0, 0, 0, 1, 1, -9294.92, -270.276, 81.7051, 0.122173, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74850, 1731, 0, 0, 0, 1, 1, -9802.81, 88.5173, 4.70444, -2.74017, 0, 0, 0.979925, -0.199368, 900, 255, 1, '', 0),
(74851, 1731, 0, 0, 0, 1, 1, -9737.26, 813.852, 33.3772, -0.122173, 0, 0, 0, 1, 900, 255, 1, '', 0),
(74852, 1731, 0, 0, 0, 1, 1, -9862.2, 155.543, 7.98152, -0.593412, 0, 0, 0.292372, -0.956305, 900, 255, 1, '', 0),
(74853, 1731, 0, 0, 0, 1, 1, -9770.37, 150.617, 25.7016, -2.84489, 0, 0, 0.989016, -0.147809, 900, 255, 1, '', 0),
(74854, 1731, 0, 0, 0, 1, 1, -9757.65, 78.5534, 14.1574, -0.837758, 0, 0, 0.406737, -0.913545, 900, 255, 1, '', 0),
(74855, 1731, 0, 0, 0, 1, 1, -9654.41, 17.4469, 49.5562, -0.628319, 0, 0, 0.309017, -0.951056, 900, 255, 1, '', 0),
(74856, 1731, 0, 0, 0, 1, 1, -9808.41, -243.801, 40.8597, -2.1293, 0, 0, 0.87462, -0.48481, 900, 255, 1, '', 0),
(74857, 1731, 0, 0, 0, 1, 1, -9707.75, -267.908, 52.1275, 2.44346, 0, 0, 0.939693, 0.34202, 900, 255, 1, '', 0),
(74858, 1731, 0, 0, 0, 1, 1, -9820.85, -403.445, 46.8509, -1.25664, 0, 0, 0.587785, -0.809017, 900, 255, 1, '', 0),
(74859, 1731, 0, 0, 0, 1, 1, -9729.23, -537.534, 45.7004, -2.87979, 0, 0, 0.991445, -0.130526, 900, 255, 1, '', 0),
(74860, 1731, 0, 0, 0, 1, 1, -9443.36, -981.152, 56.8838, 1.0472, 0, 0, 0.5, 0.866025, 900, 255, 1, '', 0),
(74861, 1731, 0, 0, 0, 1, 1, -9191.63, -598.046, 60.6417, -3.00197, 0, 0, 0.997564, -0.069756, 900, 255, 1, '', 0),
(74862, 1731, 0, 0, 0, 1, 1, -9853.56, 109.835, 34.562, -0.139626, 0, 0, 0.069756, -0.997564, 900, 255, 1, '', 0),
(74863, 1731, 0, 0, 0, 1, 1, -9751.66, 184.723, 55.7324, 3.10669, 0, 0, 0.999848, 0.017452, 900, 255, 1, '', 0),
(74864, 1731, 0, 0, 0, 1, 1, -9978.82, 521.455, 32.8909, 0.453786, 0, 0, 0.224951, 0.97437, 900, 255, 1, '', 0),
(74865, 1731, 0, 0, 0, 1, 1, -9687.82, 915.132, 36.3152, -2.56563, 0, 0, 0.95882, -0.284015, 900, 255, 1, '', 0),
(74866, 1731, 0, 0, 0, 1, 1, -9404.72, -274.812, 62.4757, 1.93731, 0, 0, 0.824126, 0.566406, 900, 255, 1, '', 0),
(74867, 1731, 0, 0, 0, 1, 1, -9115.54, 240.614, 106.374, -0.349066, 0, 0, 0.173648, -0.984808, 900, 255, 1, '', 0),
(74868, 1731, 0, 0, 0, 1, 1, -9074.68, 25.9893, 97.5041, 0.645772, 0, 0, 0.317305, 0.948324, 900, 255, 1, '', 0),
(74869, 1731, 0, 0, 0, 1, 1, -9153.33, 110.741, 76.7171, -0.506145, 0, 0, 0.25038, -0.968148, 900, 255, 1, '', 0),
(74870, 1731, 0, 0, 0, 1, 1, -9401.4, 422.391, 37.7848, 0.122173, 0, 0, 0.061049, 0.998135, 900, 255, 1, '', 0),
(74871, 1731, 0, 0, 0, 1, 1, -9333.82, 494.622, 65.8085, -2.98451, 0, 0, 0.996917, -0.078459, 900, 255, 1, '', 0),
(74872, 1731, 0, 0, 0, 1, 1, -9576.02, 430.352, 40.8878, -1.95477, 0, 0, 0.829038, -0.559193, 900, 255, 1, '', 0),
(74873, 1731, 0, 0, 0, 1, 1, -9617.02, 344.501, 48.3057, 3.05433, 0, 0, 0.999048, 0.043619, 900, 255, 1, '', 0),
(74874, 1731, 0, 0, 0, 1, 1, -9481.64, 649.059, 66.8165, -0.314159, 0, 0, 0.156434, -0.987688, 900, 255, 1, '', 0),
(74875, 1731, 0, 0, 0, 1, 1, -9072.23, -580.832, 63.2114, -1.90241, 0, 0, 0.814116, -0.580703, 900, 255, 1, '', 0),
(74876, 1731, 0, 0, 0, 1, 1, -9035.68, -577.905, 57.1753, -1.23918, 0, 0, 0.580703, -0.814116, 900, 255, 1, '', 0),
(74877, 1731, 0, 0, 0, 1, 1, -9501.33, -246.828, 46.4828, 0.767945, 0, 0, 0.374607, 0.927184, 900, 255, 1, '', 0),
(74878, 1731, 0, 0, 0, 1, 1, -9572.22, -789.044, 46.5476, 2.93215, 0, 0, 0.994522, 0.104528, 900, 255, 1, '', 0),
(74879, 1731, 0, 0, 0, 1, 1, -9090.22, -594.915, 59.1844, -0.191986, 0, 0, 0.095846, -0.995396, 900, 255, 1, '', 0),
(74880, 1731, 0, 0, 0, 1, 1, -9154.89, -982.684, 79.2237, -1.62316, 0, 0, 0.725374, -0.688354, 900, 255, 1, '', 0),
(74881, 1731, 0, 0, 0, 1, 1, -9042.1, -896.944, 57.1838, -0.139626, 0, 0, 0.069756, -0.997564, 900, 255, 1, '', 0),
(74882, 1731, 0, 0, 0, 1, 1, -8926.02, -1270.43, 98.4984, -0.575959, 0, 0, 0.284015, -0.95882, 900, 255, 1, '', 0),
(74883, 1731, 0, 0, 0, 1, 1, -9672.27, 817.424, 32.0737, -0.314159, 0, 0, 0.156434, -0.987688, 900, 255, 1, '', 0),
(74884, 1731, 0, 0, 0, 1, 1, -9892.89, 695.553, 33.7647, -2.07694, 0, 0, 0.861629, -0.507538, 900, 255, 1, '', 0),
(74885, 1731, 0, 0, 0, 1, 1, -9138.78, -591.058, 58.8044, 1.90241, 0, 0, 0.814116, 0.580703, 900, 255, 1, '', 0),
(74886, 1731, 0, 0, 0, 1, 1, -9031.15, -568.044, 55.8196, 2.54818, 0, 0, 0.956305, 0.292372, 900, 255, 1, '', 0),
(74887, 1731, 0, 0, 0, 1, 1, -9090.28, -550.917, 61.9709, 2.02458, 0, 0, 0.848048, 0.529919, 900, 255, 1, '', 0),
(74888, 1731, 0, 0, 0, 1, 1, -9374.96, -1566.89, 88.3435, -0.733038, 0, 0, 0.358368, -0.93358, 900, 255, 1, '', 0),
(74889, 1731, 0, 0, 0, 1, 1, -9332.39, -1480.93, 84.6816, -2.3911, 0, 0, 0.930418, -0.366501, 900, 255, 1, '', 0),
(74890, 1731, 0, 0, 0, 1, 1, -9409.22, -627.169, 70.6956, 0.890118, 0, 0, 0.430511, 0.902585, 900, 255, 1, '', 0),
(74891, 1731, 0, 0, 0, 1, 1, -8954.08, -1005.58, 53.8543, -2.07694, 0, 0, 0.861629, -0.507538, 900, 255, 1, '', 0),
(74892, 1731, 0, 0, 0, 1, 1, -9217.26, -1052.66, 77.0704, 2.49582, 0, 0, 0.948324, 0.317305, 900, 255, 1, '', 0),
(74893, 1731, 0, 0, 0, 1, 1, -9803.75, -1108, 31.2782, -3.01942, 0, 0, 0.998135, -0.061048, 900, 255, 1, '', 0),
(74894, 1731, 0, 0, 0, 1, 1, -9085.04, -1192.78, 56.2631, 1.88496, 0, 0, 0.809017, 0.587785, 900, 255, 1, '', 0),
(74895, 1731, 0, 0, 0, 1, 1, -9136.85, -1119.04, 82.9461, -1.48353, 0, 0, 0.67559, -0.737277, 900, 255, 1, '', 0),
(74896, 1731, 0, 0, 0, 1, 1, -8799.84, -1210.71, 101.44, 1.69297, 0, 0, 0.748956, 0.66262, 900, 255, 1, '', 0),
(74897, 1731, 0, 0, 0, 1, 1, -9923.1, -1080.64, 22.2222, -2.42601, 0, 0, 0.936672, -0.350207, 900, 255, 1, '', 0),
(74898, 1731, 0, 0, 0, 1, 1, -9822.3, -1427.23, 43.4424, -2.68781, 0, 0, 0.97437, -0.224951, 900, 255, 1, '', 0),
(74899, 1731, 0, 0, 0, 1, 1, -9770.87, -1437.77, 54.8067, 1.44862, 0, 0, 0.66262, 0.748956, 900, 255, 1, '', 0),
(74900, 1731, 0, 0, 0, 1, 1, -9723.71, 118.831, 26.5206, -1.50098, 0, 0, 0.681998, -0.731354, 900, 255, 1, '', 0),
(74930, 1731, 530, 0, 0, 1, 1, 8978.16, -7247.94, 111.609, -0.715585, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74931, 1731, 530, 0, 0, 1, 1, 9156.25, -5893.79, 4.91682, -2.25148, 0, 0, 0.902585, -0.430511, 60, 255, 1, '', 0),
(74932, 1731, 530, 0, 0, 1, 1, 8851.35, -6392.48, 32.4433, 3.01942, 0, 0, 0.998135, 0.061049, 60, 255, 1, '', 0),
(74933, 1731, 530, 0, 0, 1, 1, 8957.36, -6504.22, 18.1698, 2.18166, 0, 0, 0.887011, 0.461749, 60, 255, 1, '', 0),
(74934, 1731, 530, 0, 0, 1, 1, 9061.54, -7157.18, 79.3397, -2.33874, 0, 0, 0.920505, -0.390731, 60, 255, 1, '', 0),
(74935, 1731, 530, 0, 0, 1, 1, 9074.31, -6966.63, 18.7819, 1.41372, 0, 0, 0.649448, 0.760406, 60, 255, 1, '', 0),
(74936, 1731, 530, 0, 0, 1, 1, 8772, -6848.48, 61.0593, 1.37881, 0, 0, 0.636078, 0.771625, 60, 255, 1, '', 0),
(74937, 1731, 530, 0, 0, 1, 1, 8620.28, -7021.36, 69.6507, -2.21657, 0, 0, 0.894934, -0.446198, 60, 255, 1, '', 0),
(74938, 1731, 530, 0, 0, 1, 1, 8296.24, -7134.22, 126.994, 0.349066, 0, 0, 0.173648, 0.984808, 60, 255, 1, '', 0),
(74939, 1731, 530, 0, 0, 1, 1, 8304.13, -6966.73, 93.4246, -2.25148, 0, 0, 0.902585, -0.430511, 60, 255, 1, '', 0),
(74940, 1731, 530, 0, 0, 1, 1, 8255.45, -6933.31, 88.8827, -2.11185, 0, 0, 0.870356, -0.492423, 60, 255, 1, '', 0),
(74941, 1731, 530, 0, 0, 1, 1, 8141.18, -7891.01, 201.926, -2.54818, 0, 0, 0.956305, -0.292372, 60, 255, 1, '', 0),
(74942, 1731, 530, 0, 0, 1, 1, 8758.19, -6855.44, 65.6054, -2.82743, 0, 0, 0.987688, -0.156434, 60, 255, 1, '', 0),
(74943, 1731, 530, 0, 0, 1, 1, 9352.82, -7918.88, 15.0909, -0.925024, 0, 0, 0.446198, -0.894934, 60, 255, 1, '', 0),
(74944, 1731, 530, 0, 0, 1, 1, 9153.6, -6066.24, 91.2698, 0.349065, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74945, 1731, 530, 0, 0, 1, 1, 8576.77, -7342.3, 150.207, 1.44862, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74946, 1731, 530, 0, 0, 1, 1, 9113.07, -6119.59, 40.2528, -0.052359, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74947, 1731, 530, 0, 0, 1, 1, 8840.84, -6578.18, 53.5975, 1.53589, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74948, 1731, 530, 0, 0, 1, 1, 9045.76, -6455.03, 2.36801, -1.27409, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74949, 1731, 530, 0, 0, 1, 1, 8113.18, -6211.63, 41.5512, 0.890117, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74950, 1731, 530, 0, 0, 1, 1, 8632.77, -6182.24, 55.188, -2.77507, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74951, 1731, 530, 0, 0, 1, 1, 8671.68, -6909.58, 110.265, -1.93732, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74952, 1731, 530, 0, 0, 1, 1, 8479.97, -6171.22, 77.417, -1.51844, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74953, 1731, 530, 0, 0, 1, 1, 8842.07, -7222.16, 66.3173, -2.26892, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74954, 1731, 530, 0, 0, 1, 1, 8744.72, -7365.18, 95.5962, 0.610864, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74955, 1731, 530, 0, 0, 1, 1, 8707.96, -5766.97, 10.0575, 0.052359, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74956, 1731, 530, 0, 0, 1, 1, 8924, -7719.83, 198.6, -2.00713, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74957, 1731, 530, 0, 0, 1, 1, 8620.07, -5806.49, 37.7047, 2.47837, 0, 0, 0, 1, 60, 255, 1, '', 0),
(74958, 1731, 530, 0, 0, 1, 1, 8133.32, -6785.23, 71.3577, -1.88496, 0, 0, 0.809017, -0.587785, 60, 255, 1, '', 0),
(74959, 1731, 530, 0, 0, 1, 1, 8199.71, -6835.06, 79.741, -0.942478, 0, 0, 0.453991, -0.891006, 60, 255, 1, '', 0),
(74960, 1731, 530, 0, 0, 1, 1, 8852.93, -6967.63, 30.9926, -0.820305, 0, 0, 0.398749, -0.91706, 60, 255, 1, '', 0),
(74961, 1731, 530, 0, 0, 1, 1, 8570.85, -7471.04, 139.781, -2.11185, 0, 0, 0.870356, -0.492423, 60, 255, 1, '', 0),
(74962, 1731, 530, 0, 0, 1, 1, 8522.32, -7738.49, 123.011, -0.15708, 0, 0, 0.078459, -0.996917, 60, 255, 1, '', 0),
(74963, 1731, 530, 0, 0, 1, 1, 8864.79, -7776.76, 172.942, -0.855212, 0, 0, 0.414693, -0.909961, 60, 255, 1, '', 0),
(74964, 1731, 530, 0, 0, 1, 1, 9709.15, -7925.85, 9.3747, -0.139626, 0, 0, 0.069756, -0.997564, 60, 255, 1, '', 0),
(74965, 1731, 530, 0, 0, 1, 1, 9595.45, -7797.68, 41.8183, -1.25664, 0, 0, 0.587785, -0.809017, 60, 255, 1, '', 0),
(74966, 1731, 530, 0, 0, 1, 1, 9258.71, -6442.46, 18.3215, -3.01942, 0, 0, 0.998135, -0.061048, 60, 255, 1, '', 0),
(74967, 1731, 530, 0, 0, 1, 1, 9270.79, -6399.6, 9.563, 0.523599, 0, 0, 0.258819, 0.965926, 60, 255, 1, '', 0),
(74968, 1731, 530, 0, 0, 1, 1, 9227.42, -6113.97, 45.0611, 2.70526, 0, 0, 0.976296, 0.21644, 60, 255, 1, '', 0),
(74969, 1731, 530, 0, 0, 1, 1, 8915.39, -6179.36, 7.73462, 1.64061, 0, 0, 0.731354, 0.681998, 60, 255, 1, '', 0),
(74970, 1731, 530, 0, 0, 1, 1, 9018.65, -5971.72, 12.7394, 2.51327, 0, 0, 0.951057, 0.309017, 60, 255, 1, '', 0),
(74971, 1731, 530, 0, 0, 1, 1, 8999.2, -5967.17, 13.0295, -1.79769, 0, 0, 0.782608, -0.622515, 60, 255, 1, '', 0),
(74972, 1731, 530, 0, 0, 1, 1, 8933.25, -6974.55, 21.7225, 1.23918, 0, 0, 0.580703, 0.814116, 60, 255, 1, '', 0),
(74973, 1731, 530, 0, 0, 1, 1, 8923.4, -6822.55, 54.7797, 1.72788, 0, 0, 0.760406, 0.649448, 60, 255, 1, '', 0),
(74974, 1731, 530, 0, 0, 1, 1, 8796.62, -6693.31, 56.7484, -2.00713, 0, 0, 0.843391, -0.5373, 60, 255, 1, '', 0),
(74975, 1731, 530, 0, 0, 1, 1, 8265.85, -7446.72, 175.822, -2.77507, 0, 0, 0.983255, -0.182235, 60, 255, 1, '', 0),
(74976, 1731, 530, 0, 0, 1, 1, 8338.62, -7384.13, 210.359, 0.122173, 0, 0, 0.061049, 0.998135, 60, 255, 1, '', 0),
(74977, 1731, 530, 0, 0, 1, 1, 8640.4, -7137.19, 86.2068, 2.9147, 0, 0, 0.993572, 0.113203, 60, 255, 1, '', 0),
(74978, 1731, 530, 0, 0, 1, 1, 8757.04, -7205.74, 56.7255, -0.20944, 0, 0, 0.104528, -0.994522, 60, 255, 1, '', 0),
(74979, 1731, 530, 0, 0, 1, 1, 8350.22, -6638.37, 114.945, -1.48353, 0, 0, 0.67559, -0.737277, 60, 255, 1, '', 0),
(74980, 1731, 530, 0, 0, 1, 1, 8391.14, -6303.74, 128.429, 1.50098, 0, 0, 0.681998, 0.731354, 60, 255, 1, '', 0),
(74981, 1731, 530, 0, 0, 1, 1, 8697.75, -6217.64, 35.9229, 1.6057, 0, 0, 0.71934, 0.694658, 60, 255, 1, '', 0),
(74982, 1731, 530, 0, 0, 1, 1, 8616.06, -6041.67, 57.8267, 1.67552, 0, 0, 0.743145, 0.669131, 60, 255, 1, '', 0),
(74983, 1731, 530, 0, 0, 1, 1, 8698.23, -6041.53, 19.0908, 0.855211, 0, 0, 0.414693, 0.909961, 60, 255, 1, '', 0),
(74984, 1731, 530, 0, 0, 1, 1, 8435.37, -5754.15, 26.823, -2.56563, 0, 0, 0.95882, -0.284015, 60, 255, 1, '', 0),
(74985, 1731, 530, 0, 0, 1, 1, 8757.86, -6306.09, 57.1405, -1.0821, 0, 0, 0.515038, -0.857167, 60, 255, 1, '', 0),
(74986, 1731, 530, 0, 0, 1, 1, 8923.64, -6579.72, 40.4971, -0.017453, 0, 0, 0.008727, -0.999962, 60, 255, 1, '', 0),
(74987, 1731, 530, 0, 0, 1, 1, 9157.8, -6194.33, 28.8475, -0.314159, 0, 0, 0.156434, -0.987688, 60, 255, 1, '', 0),
(75000, 1731, 530, 0, 0, 1, 1, 7890.42, -6941.48, 79.6466, -0.575959, 0, 0, 0.284015, -0.95882, 900, 255, 1, '', 0),
(75003, 1731, 530, 0, 0, 1, 1, 6354.8, -6358.58, 71.9724, -0.20944, 0, 0, 0.104528, -0.994522, 900, 255, 1, '', 0),
(75006, 1731, 530, 0, 0, 1, 1, 6948.06, -7521.55, 49.5686, -2.46091, 0, 0, 0.942641, -0.333807, 900, 255, 1, '', 0),
(75009, 1731, 530, 0, 0, 1, 1, 7756.59, -6772.16, 50.1252, -2.67035, 0, 0, 0.97237, -0.233445, 900, 255, 1, '', 0),
(75012, 1731, 530, 0, 0, 1, 1, 7234.2, -6527.35, 12.0761, 2.54818, 0, 0, 0.956305, 0.292372, 900, 255, 1, '', 0),
(75015, 1731, 530, 0, 0, 1, 1, 7605.64, -6331.32, 16.5091, -1.67552, 0, 0, 0.743145, -0.669131, 900, 255, 1, '', 0),
(75018, 1731, 530, 0, 0, 1, 1, 7500.13, -6103.76, 2.77201, 1.98968, 0, 0, 0.838671, 0.544639, 900, 255, 1, '', 0),
(75021, 1731, 530, 0, 0, 1, 1, 6818.22, -5942, 56.7248, 0.698132, 0, 0, 0.34202, 0.939693, 900, 255, 1, '', 0),
(75024, 181248, 530, 0, 0, 1, 1, 7073.06, -6183.51, 21.7027, 2.44346, 0, 0, 0.939693, 0.34202, 900, 255, 1, '', 0),
(75027, 1731, 530, 0, 0, 1, 1, 6861.82, -6071.52, 34.0041, 2.3911, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75030, 1731, 530, 0, 0, 1, 1, 7836.56, -7476.31, 154.909, 1.67551, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75033, 1731, 530, 0, 0, 1, 1, 7532.1, -5748.37, 4.13592, 0.261798, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75036, 1731, 530, 0, 0, 1, 1, 7085.96, -5802.34, 30.6811, -3.12412, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75039, 1731, 530, 0, 0, 1, 1, 8005.6, -7867.93, 192.507, 2.77507, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75042, 1731, 530, 0, 0, 1, 1, 7430.31, -6559.54, 11.2087, -3.07177, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75045, 1731, 530, 0, 0, 1, 1, 7084.87, -7166.42, 51.4525, -1.13446, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75048, 1731, 530, 0, 0, 1, 1, 7213.27, -6609.58, 55.7905, 1.22173, 0, 0, 0.573576, 0.819152, 900, 255, 1, '', 0),
(75051, 181248, 530, 0, 0, 1, 1, 7235.42, -6315.06, 25.745, -1.11701, 0, 0, 0.529919, -0.848048, 900, 255, 1, '', 0),
(75054, 1731, 530, 0, 0, 1, 1, 7787.24, -6298.68, 23.9224, -1.37881, 0, 0, 0.636078, -0.771625, 900, 255, 1, '', 0),
(75057, 1731, 530, 0, 0, 1, 1, 7870.94, -6696.52, 19.3749, 0.785398, 0, 0, 0.382683, 0.92388, 900, 255, 1, '', 0),
(75060, 1731, 530, 0, 0, 1, 1, 7939.39, -6423.14, 59.7752, -1.65806, 0, 0, 0.737277, -0.67559, 900, 255, 1, '', 0),
(75063, 1731, 530, 0, 0, 1, 1, 7517.04, -7870.55, 157.273, -2.09439, 0, 0, 0.866025, -0.5, 900, 255, 1, '', 0),
(75066, 1731, 530, 0, 0, 1, 1, 7970.28, -6264.19, 24.6026, 0.959931, 0, 0, 0.461749, 0.887011, 900, 255, 1, '', 0),
(75069, 1731, 530, 0, 0, 1, 1, 8048.01, -5940.32, 4.73735, 0.959931, 0, 0, 0.461749, 0.887011, 900, 255, 1, '', 0),
(75072, 1731, 530, 0, 0, 1, 1, 7479.82, -7592.04, 124.83, 1.8326, 0, 0, 0.793353, 0.608761, 900, 255, 1, '', 0),
(75075, 1731, 530, 0, 0, 1, 1, 7773.21, -7819.7, 160.805, -0.087267, 0, 0, 0.04362, -0.999048, 900, 255, 1, '', 0),
(75078, 1731, 530, 0, 0, 1, 1, 7866.6, -7938.18, 176.191, -2.60054, 0, 0, 0.96363, -0.267238, 900, 255, 1, '', 0),
(75081, 1731, 530, 0, 0, 1, 1, 7834.53, -5983.14, 4.11614, 0.261799, 0, 0, 0.130526, 0.991445, 900, 255, 1, '', 0),
(75084, 1731, 530, 0, 0, 1, 1, 7209.66, -5844.62, 15.5623, 2.42601, 0, 0, 0.936672, 0.350207, 900, 255, 1, '', 0),
(75087, 1731, 530, 0, 0, 1, 1, 7683.54, -6087.25, 19.2903, -1.3439, 0, 0, 0.622515, -0.782608, 900, 255, 1, '', 0),
(75090, 181248, 530, 0, 0, 1, 1, 7081.05, -6264.08, 19.0276, 2.82743, 0, 0, 0.987688, 0.156434, 900, 255, 1, '', 0),
(75093, 1731, 530, 0, 0, 1, 1, 7795.77, -7253.04, 168.994, 1.55334, 0, 0, 0.700909, 0.71325, 900, 255, 1, '', 0),
(75096, 1731, 530, 0, 0, 1, 1, 6977.29, -6562.71, 11.2095, -0.366518, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75099, 181248, 530, 0, 0, 1, 1, 7142.39, -6211.11, 24.2601, -1.55334, 0, 0, 0.700909, -0.71325, 900, 255, 1, '', 0),
(75102, 1731, 530, 0, 0, 1, 1, 7105.41, -6559.61, 11.4982, 2.23402, 0, 0, 0.898794, 0.438371, 900, 255, 1, '', 0),
(75105, 181248, 530, 0, 0, 1, 1, 7064.71, -6243.39, 18.7586, 1.3439, 0, 0, 0.622515, 0.782608, 900, 255, 1, '', 0),
(75108, 1731, 530, 0, 0, 1, 1, 7241.18, -6447.52, 48.1141, 0.453786, 0, 0, 0.224951, 0.97437, 900, 255, 1, '', 0),
(75111, 1731, 530, 0, 0, 1, 1, 7379.89, -7913.66, 158.573, -0.802851, 0, 0, 0.390731, -0.920505, 900, 255, 1, '', 0),
(75114, 1731, 530, 0, 0, 1, 1, 6830.76, -6491.14, 18.6179, -1.16937, 0, 0, 0.551937, -0.833886, 900, 255, 1, '', 0),
(75117, 1731, 530, 0, 0, 1, 1, 6873.19, -6164.61, 35.753, 1.3439, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75200, 1731, 0, 0, 0, 1, 1, -644.457, -1361.17, 68.5742, 2.23402, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75203, 1731, 0, 0, 0, 1, 1, -893.763, 309.781, 39.3592, -0.122173, 0, 0, 0.061049, -0.998135, 900, 255, 1, '', 0),
(75206, 1731, 0, 0, 0, 1, 1, -315.451, -842.828, 61.7272, -1.78024, 0, 0, 0.777146, -0.62932, 900, 255, 1, '', 0),
(75209, 1731, 0, 0, 0, 1, 1, -801.256, 519.086, 101.712, 1.88496, 0, 0, 0.809017, 0.587785, 900, 255, 1, '', 0),
(75212, 1731, 0, 0, 0, 1, 1, -643.304, 789.86, 128.134, 0.855211, 0, 0, 0.414693, 0.909961, 900, 255, 1, '', 0),
(75215, 1731, 0, 0, 0, 1, 1, -502.591, -828.237, 53.355, 3.03687, 0, 0, 0.99863, 0.052336, 900, 255, 1, '', 0),
(75218, 1731, 0, 0, 0, 1, 1, -445.19, -1529.4, 71.0053, -1.8675, 0, 0, 0.803857, -0.594823, 900, 255, 1, '', 0),
(75221, 1731, 0, 0, 0, 1, 1, -809.145, 21.4447, 36.635, -0.750491, 0, 0, 0.366501, -0.930418, 900, 255, 1, '', 0),
(75224, 1731, 0, 0, 0, 1, 1, -1100.65, -830.58, 17.9532, -0.471239, 0, 0, 0.233445, -0.97237, 900, 255, 1, '', 0),
(75227, 1731, 0, 0, 0, 1, 1, -745.166, 577.436, 103.427, 0.925024, 0, 0, 0.446198, 0.894934, 900, 255, 1, '', 0),
(75230, 1731, 0, 0, 0, 1, 1, -278.271, 228.777, 102.131, -2.23402, 0, 0, 0.898794, -0.438371, 900, 255, 1, '', 0),
(75233, 1731, 0, 0, 0, 1, 1, -386.992, 260.187, 94.6462, 1.93731, 0, 0, 0.824126, 0.566406, 900, 255, 1, '', 0),
(75236, 1731, 0, 0, 0, 1, 1, -743.788, -245.083, 40.2169, 2.49582, 0, 0, 0.948324, 0.317305, 900, 255, 1, '', 0),
(75242, 1731, 0, 0, 0, 1, 1, -620.953, -849.445, 39.3938, -3.05433, 0, 0, 0.999048, -0.043619, 900, 255, 1, '', 0),
(75245, 1731, 0, 0, 0, 1, 1, -497, -824, 49.308, 3.876, 0, 0, 0.93329, -0.359123, 900, 255, 1, '', 0),
(75248, 1731, 0, 0, 0, 1, 1, -665, 489.126, 88.027, 0, 0, 0, 0.069756, -0.997564, 900, 255, 1, '', 0),
(75251, 1731, 0, 0, 0, 1, 1, -1458.58, -1085.96, 7.96406, -1.13446, 0, 0, 0.5373, -0.843391, 900, 255, 1, '', 0),
(75254, 1731, 0, 0, 0, 1, 1, -713.669, 489.348, 82.6661, 0.366518, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75260, 1731, 0, 0, 0, 1, 1, -808, 23.72, 35.22, -1, 0, 0, -0.366501, 0.930418, 900, 255, 1, '', 0),
(75263, 1731, 0, 0, 0, 1, 1, -740, -237, 37.234, 5.908, 0, 0, 0.186536, -0.982448, 900, 255, 1, '', 0),
(75266, 1731, 0, 0, 0, 1, 1, -383.365, -182.706, 61.7857, -1.0472, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75269, 1731, 0, 0, 0, 1, 1, -196.854, -1457.59, 113.475, 1.3439, 0, 0, 0.622515, 0.782608, 900, 255, 1, '', 0),
(75272, 1731, 0, 0, 0, 1, 1, -703.013, -980.705, 46.6122, 1.41372, 0, 0, 0.649448, 0.760406, 900, 255, 1, '', 0),
(75275, 1731, 0, 0, 0, 1, 1, -416.469, -1170.29, 59.894, 1.29154, 0, 0, 0.601815, 0.798636, 900, 255, 1, '', 0),
(75278, 1731, 0, 0, 0, 1, 1, 218.196, -755.703, 109.082, -0.506145, 0, 0, 0.25038, -0.968148, 900, 255, 1, '', 0),
(75281, 1731, 0, 0, 0, 1, 1, -90.0451, -1361.99, 123.778, -2.91469, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75284, 1731, 0, 0, 0, 1, 1, 115.795, -639.137, 97.0621, -0.314158, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75287, 1731, 0, 0, 0, 1, 1, -304.803, -1240.07, 72.5146, 0.209439, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75290, 1731, 0, 0, 0, 1, 1, 143.441, -925.821, 79.4433, -0.575959, 0, 0, 0.284015, -0.95882, 900, 255, 1, '', 0),
(75293, 1731, 0, 0, 0, 1, 1, -817.62, -1210.97, 57.0243, 1.53589, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75296, 1731, 0, 0, 0, 1, 1, -999.126, -1117.26, 53.2347, -2.80997, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75299, 1731, 0, 0, 0, 1, 1, -1000.52, 30.5843, 43.3942, 3.14159, 0, 0, 1, 0, 900, 255, 1, '', 0),
(75302, 1731, 0, 0, 0, 1, 1, 47.3426, -642.852, 94.4918, -0.314159, 0, 0, 0.156434, -0.987688, 900, 255, 1, '', 0),
(75305, 1731, 0, 0, 0, 1, 1, -778.224, -984.915, 36.6733, -0.506145, 0, 0, 0.25038, -0.968148, 900, 255, 1, '', 0),
(75308, 1731, 0, 0, 0, 1, 1, -298.249, -1197.99, 67.9911, 3.14159, 0, 0, 1, 0, 900, 255, 1, '', 0),
(75311, 1731, 0, 0, 0, 1, 1, -1093, -1064, 49.459, 0.314, 0, 0, 0.156434, 0.987688, 900, 255, 1, '', 0),
(75314, 1731, 0, 0, 0, 1, 1, -688.964, 168.78, 18.368, 3.05433, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75317, 1731, 0, 0, 0, 1, 1, -49, 683.216, 78.239, 1.361, 0, 0, 0.62932, 0.777146, 900, 255, 1, '', 0),
(75320, 1731, 0, 0, 0, 1, 1, -150, -996, 60.481, 0, 0, 0, -0.061049, 0.998135, 900, 255, 1, '', 0),
(75323, 1731, 0, 0, 0, 1, 1, -322, -367, 67.164, 1.274, 0, 0, 0.594823, 0.803857, 900, 255, 1, '', 0),
(75326, 1731, 0, 0, 0, 1, 1, 188.658, -1064, 73.208, 1.03, 0, 0, 0.492423, 0.870356, 900, 255, 1, '', 0),
(75329, 1731, 0, 0, 0, 1, 1, 52.3908, -1232.24, 71.2689, 0.59341, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75332, 1731, 0, 0, 0, 1, 1, -106, -690, 73.804, -3, 0, 0, -0.987688, 0.156435, 900, 255, 1, '', 0),
(75335, 1731, 0, 0, 0, 1, 1, -619.142, -1014.93, 61.8551, 0.733038, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75338, 1731, 0, 0, 0, 1, 1, -531, -1178, 60.577, 0.18, 0, 0, 0.089795, 0.99596, 900, 255, 1, '', 0),
(75341, 1731, 0, 0, 0, 1, 1, -845, 86.353, 3.217, -1, 0, 0, -0.642787, 0.766045, 900, 255, 1, '', 0),
(75344, 1731, 0, 0, 0, 1, 1, -922.306, -1453.78, 59.1559, 1.97222, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75347, 1731, 0, 0, 0, 1, 1, -22, -679, 76.608, 2.129, 0, 0, 0.87462, 0.48481, 900, 255, 1, '', 0),
(75350, 1731, 0, 0, 0, 1, 1, -269, -216, 84.475, -3, 0, 0, -0.994522, 0.104529, 900, 255, 1, '', 0),
(75353, 1731, 0, 0, 0, 1, 1, -197, -655, 66.45, 1.239, 0, 0, 0.580703, 0.814115, 900, 255, 1, '', 0),
(75356, 1731, 0, 0, 0, 1, 1, 85.923, -1065, 58.682, -1, 0, 0, -0.309017, 0.951056, 900, 255, 1, '', 0),
(75359, 1731, 0, 0, 0, 1, 1, -567, 813.895, 90.855, 1.833, 0, 0, 0.793353, 0.608761, 900, 255, 1, '', 0),
(75362, 1731, 0, 0, 0, 1, 1, -150, -997, 60.232, 0, 0, 0, -0.061049, 0.998135, 900, 255, 1, '', 0),
(75365, 1731, 0, 0, 0, 1, 1, -961, -166, 51.674, 0.8, 0, 0, 0.389245, 0.921134, 900, 255, 1, '', 0),
(75368, 1731, 0, 0, 0, 1, 1, 73.2704, -1002.17, 59.4981, 0.139626, 0, 0, 0.069756, 0.997564, 900, 255, 1, '', 0),
(75371, 1731, 0, 0, 0, 1, 1, -1289.16, 635.334, 54.1088, 0.174533, 0, 0, 0.087156, 0.996195, 900, 255, 1, '', 0),
(75374, 1731, 0, 0, 0, 1, 1, -770, -1192, 69.22, 2.133, 0, 0, 0, 0, 900, 255, 1, '', 0),
(75377, 1731, 0, 0, 0, 1, 1, -582, -1643, 74.985, 5.733, 0, 0, 0, 0, 900, 255, 1, '', 0),
(75380, 1731, 0, 0, 0, 1, 1, -492, -1665, 81.768, 3.893, 0, 0, 0, 0, 900, 255, 1, '', 0),
(75383, 1731, 0, 0, 0, 1, 1, -1195, -947, 31.334, 5.132, 0, 0, 0, 0, 900, 255, 1, '', 0),
(75386, 1731, 0, 0, 0, 1, 1, -498.583, -1658.01, 84.0551, 2.37364, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75389, 1731, 0, 0, 0, 1, 1, -392, -1775, 113.071, 5.827, 0, 0, 0, 0, 900, 255, 1, '', 0),
(75392, 1731, 0, 0, 0, 1, 1, -164.338, -497.122, 80.3264, -2.51327, 0, 0, 0.951057, -0.309017, 900, 255, 1, '', 0),
(75395, 1731, 0, 0, 0, 1, 1, -152, -134, 116.106, 1.223, 0, 0, 0, 0, 900, 255, 1, '', 0),
(75398, 1731, 0, 0, 0, 1, 1, -1012.89, -1185.04, 65.5292, 1.65806, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75401, 1731, 0, 0, 0, 1, 1, -1171.69, -1305.33, 86.2144, 1.8326, 0, 0, 0.793353, 0.608761, 900, 255, 1, '', 0),
(75404, 1731, 0, 0, 0, 1, 1, -1236.35, -986.903, 16.5418, -1.32645, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75600, 1731, 0, 0, 0, 1, 1, -5860.61, -3754.48, 334.931, -2.93215, 0, 0, 0.994522, -0.104529, 900, 255, 1, '', 0),
(75603, 1731, 0, 0, 0, 1, 1, -4673.84, -3000.53, 320.473, -1.39626, 0, 0, 0.642788, -0.766044, 900, 255, 1, '', 0),
(75606, 1731, 0, 0, 0, 1, 1, -5306.12, -2698.85, 352.962, -2.51327, 0, 0, 0.951057, -0.309017, 900, 255, 1, '', 0),
(75609, 1731, 0, 0, 0, 1, 1, -4926.78, -3765, 322.941, 0.506145, 0, 0, 0.25038, 0.968148, 900, 255, 1, '', 0),
(75612, 1731, 0, 0, 0, 1, 1, -5357.71, -2718.82, 363.565, 0.942478, 0, 0, 0.45399, 0.891007, 900, 255, 1, '', 0),
(75615, 1731, 0, 0, 0, 1, 1, -5534.53, -2711.2, 369.837, 0.261799, 0, 0, 0.130526, 0.991445, 900, 255, 1, '', 0),
(75618, 1731, 0, 0, 0, 1, 1, -4986.73, -2962.82, 316.091, 1.88496, 0, 0, 0.809017, 0.587785, 900, 255, 1, '', 0),
(75621, 1731, 0, 0, 0, 1, 1, -5692.68, -3123.95, 316.189, -2.09439, 0, 0, 0.866025, -0.5, 900, 255, 1, '', 0),
(75624, 1731, 0, 0, 0, 1, 1, -5624.58, -2926.26, 409.998, -3.07178, 0, 0, 0.999391, -0.034899, 900, 255, 1, '', 0),
(75627, 1731, 0, 0, 0, 1, 1, -5744.19, -3532.99, 306.714, -0.541052, 0, 0, 0.267238, -0.96363, 900, 255, 1, '', 0),
(75630, 1731, 0, 0, 0, 1, 1, -5792.43, -3586.6, 337.733, 1.5708, 0, 0, 0.707107, 0.707107, 900, 255, 1, '', 0),
(75633, 1731, 0, 0, 0, 1, 1, -4898.06, -3039.79, 319.946, -2.1293, 0, 0, 0.87462, -0.48481, 900, 255, 1, '', 0),
(75636, 1731, 0, 0, 0, 1, 1, -5366.79, -3137.5, 303.529, -2.40855, 0, 0, 0.93358, -0.358368, 900, 255, 1, '', 0),
(75639, 1731, 0, 0, 0, 1, 1, -5906.61, -3848.87, 350.884, -2.77507, 0, 0, 0.983255, -0.182235, 900, 255, 1, '', 0),
(75642, 1731, 0, 0, 0, 1, 1, -5528.69, -2989.79, 373.278, -0.942478, 0, 0, 0.453991, -0.891006, 900, 255, 1, '', 0),
(75645, 1731, 0, 0, 0, 1, 1, -5960.8, -2781.47, 392.505, 0.122173, 0, 0, 0.061049, 0.998135, 900, 255, 1, '', 0),
(75648, 1731, 0, 0, 0, 1, 1, -5071.19, -2724.19, 320.265, -0.733038, 0, 0, 0.358368, -0.93358, 900, 255, 1, '', 0),
(75651, 1731, 0, 0, 0, 1, 1, -5593.5, -3256.05, 289.35, -1.13446, 0, 0, 0.5373, -0.843391, 900, 255, 1, '', 0),
(75654, 1731, 0, 0, 0, 1, 1, -4965.51, -2996.1, 317.392, -2.6529, 0, 0, 0.970296, -0.241922, 900, 255, 1, '', 0),
(75657, 1731, 0, 0, 0, 1, 1, -4867.67, -2974.29, 318.302, 0.925024, 0, 0, 0.446198, 0.894934, 900, 255, 1, '', 0),
(75660, 1731, 0, 0, 0, 1, 1, -5717.69, -3627.2, 315.512, -2.86234, 0, 0, 0.990268, -0.139173, 900, 255, 1, '', 0),
(75663, 1731, 0, 0, 0, 1, 1, -5376, -4026.73, 345.587, 2.23402, 0, 0, 0.898794, 0.438371, 900, 255, 1, '', 0),
(75666, 1731, 0, 0, 0, 1, 1, -5783.95, -3212.13, 309.844, 0.645772, 0, 0, 0.317305, 0.948324, 900, 255, 1, '', 0),
(75669, 1731, 0, 0, 0, 1, 1, -5189.03, -4137.99, 344.148, -1.48353, 0, 0, 0.67559, -0.737277, 900, 255, 1, '', 0),
(75672, 1731, 0, 0, 0, 1, 1, -4748.16, -3588.43, 308.719, 1.44862, 0, 0, 0.66262, 0.748956, 900, 255, 1, '', 0),
(75675, 1731, 0, 0, 0, 1, 1, -6141, -2972.89, 400.16, 2.80998, 0, 0, 0.986286, 0.165048, 900, 255, 1, '', 0),
(75678, 1731, 0, 0, 0, 1, 1, -6100.43, -2984.39, 419.492, 1.44862, 0, 0, 0.66262, 0.748956, 900, 255, 1, '', 0),
(75681, 1731, 0, 0, 0, 1, 1, -5517.82, -2731.01, 366.158, 0.750492, 0, 0, 0.366501, 0.930418, 900, 255, 1, '', 0),
(75684, 1731, 0, 0, 0, 1, 1, -4971.56, -3146.3, 321.716, 1.27409, 0, 0, 0.594823, 0.803857, 900, 255, 1, '', 0),
(75687, 1731, 0, 0, 0, 1, 1, -4913.92, -3381, 302.121, 0.541052, 0, 0, 0.267238, 0.96363, 900, 255, 1, '', 0),
(75690, 1731, 0, 0, 0, 1, 1, -5642.75, -3256.99, 312.215, -0.837758, 0, 0, 0.406737, -0.913545, 900, 255, 1, '', 0),
(75693, 1731, 0, 0, 0, 1, 1, -5142.94, -3360.46, 284.629, -1.29154, 0, 0, 0.601815, -0.798635, 900, 255, 1, '', 0),
(75696, 1731, 0, 0, 0, 1, 1, -5498.42, -3316.85, 286.99, -2.1293, 0, 0, 0.87462, -0.48481, 900, 255, 1, '', 0),
(75699, 1731, 0, 0, 0, 1, 1, -5423.67, -2709.96, 368.752, 2.51327, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75702, 1731, 0, 0, 0, 1, 1, -5582.72, -3545.79, 290.129, -1.23918, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75705, 1731, 0, 0, 0, 1, 1, -6054.89, -2957.95, 401.839, -1.18682, 0, 0, 0.559193, -0.829037, 900, 255, 1, '', 0),
(75708, 1731, 0, 0, 0, 1, 1, -6001.23, -2859, 394.728, 3.05433, 0, 0, 0.999048, 0.043619, 900, 255, 1, '', 0),
(75711, 1731, 0, 0, 0, 1, 1, -4728.93, -3142.43, 302.941, 3.10669, 0, 0, 0.999848, 0.017452, 900, 255, 1, '', 0),
(75714, 1731, 0, 0, 0, 1, 1, -4651.34, -3121.1, 301.035, -0.401426, 0, 0, 0.199368, -0.979925, 900, 255, 1, '', 0),
(75717, 1731, 0, 0, 0, 1, 1, -4834.88, -2986.44, 323.039, 2.33874, 0, 0, 0.920505, 0.390731, 900, 255, 1, '', 0),
(75720, 1731, 0, 0, 0, 1, 1, -4923.44, -2971.1, 317.558, -2.07694, 0, 0, 0.861629, -0.507538, 900, 255, 1, '', 0),
(75723, 1731, 0, 0, 0, 1, 1, -4968.39, -2911.64, 338.077, 1.53589, 0, 0, 0.694658, 0.71934, 900, 255, 1, '', 0),
(75726, 1731, 0, 0, 0, 1, 1, -5362.56, -3552.67, 280.771, -1.64061, 0, 0, 0.731354, -0.681998, 900, 255, 1, '', 0),
(75729, 1731, 0, 0, 0, 1, 1, -5306.52, -3564.57, 287.213, -2.37365, 0, 0, 0.927184, -0.374607, 900, 255, 1, '', 0),
(75732, 1731, 0, 0, 0, 1, 1, -5748.61, -3936.8, 331.427, 2.87979, 0, 0, 0.991445, 0.130526, 900, 255, 1, '', 0),
(75735, 1731, 0, 0, 0, 1, 1, -5538.97, -4016.46, 382.358, -1.06465, 0, 0, 0.507538, -0.861629, 900, 255, 1, '', 0),
(75738, 1731, 0, 0, 0, 1, 1, -5791.16, -2947.83, 376.026, -2.98451, 0, 0, 0.996917, -0.078459, 900, 255, 1, '', 0),
(75741, 1731, 0, 0, 0, 1, 1, -5541.88, -4316.9, 399.572, 2.23402, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75744, 1731, 0, 0, 0, 1, 1, -5869.6, -4071.64, 398.154, -1.16937, 0, 0, 0.551937, -0.833886, 900, 255, 1, '', 0),
(75747, 1731, 0, 0, 0, 1, 1, -5593, -3257, 289.07, -1, 0, 0, -0.5373, 0.843391, 900, 255, 1, '', 0),
(75750, 1731, 0, 0, 0, 1, 1, -4928, -3765, 323.991, 0.506, 0, 0, 0.25038, 0.968148, 900, 255, 1, '', 0),
(75753, 1731, 0, 0, 0, 1, 1, -5750, -3940, 328.844, 4.389, 0, 0, 0.811768, -0.58398, 900, 255, 1, '', 0),
(75756, 1731, 0, 0, 0, 1, 1, -5846, -3040, 330.966, 1.399, 0, 0, 0.643664, 0.765308, 900, 255, 1, '', 0),
(75759, 1731, 0, 0, 0, 1, 1, -4943, -2974, 321.613, 0, 0, 0, -0.069756, 0.997564, 900, 255, 1, '', 0),
(75765, 1731, 0, 0, 0, 1, 1, -5896, -2848, 383.134, 1.871, 0, 0, 0.804971, 0.593315, 900, 255, 1, '', 0),
(75768, 1731, 0, 0, 0, 1, 1, -5359, -3894, 341.694, 0.507, 0, 0, 0.250971, 0.967995, 900, 255, 1, '', 0),
(75771, 1731, 0, 0, 0, 1, 1, -5912, -3855, 353.174, 4.247, 0, 0, 0.851183, -0.524869, 900, 255, 1, '', 0),
(75774, 1731, 0, 0, 0, 1, 1, -5091.8, -3324.24, 280.257, -2.49582, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75777, 1731, 0, 0, 0, 1, 1, -4838.35, -3140.2, 318.547, -1.36136, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75780, 1731, 0, 0, 0, 1, 1, -4985, -3873, 317.697, 2.653, 0, 0, 0.970296, 0.241922, 900, 255, 1, '', 0),
(75783, 1731, 0, 0, 0, 1, 1, -4829, -2988, 321.079, 2.339, 0, 0, 0, 0, 900, 255, 1, '', 0),
(75786, 1731, 0, 0, 0, 1, 1, -5804, -3134, 342.026, 3.506, 0, 0, 0.983404, -0.181432, 900, 255, 1, '', 0),
(75789, 1731, 0, 0, 0, 1, 1, -5849, -3600, 351.124, 5.819, 0, 0, 0.230151, -0.973155, 900, 255, 1, '', 0),
(75792, 1731, 0, 0, 0, 1, 1, -5538, -3116, 342.941, 3.977, 0, 0, 0.914032, -0.405643, 900, 255, 1, '', 0),
(75795, 1731, 0, 0, 0, 1, 1, -5900, -2973, 371.701, -2, 0, 0, -0.75471, 0.656059, 900, 255, 1, '', 0),
(75798, 1731, 0, 0, 0, 1, 1, -5464, -3190, 335.219, 1.222, 0, 0, 0.573576, 0.819152, 900, 255, 1, '', 0),
(75801, 1731, 0, 0, 0, 1, 1, -5109, -3122, 313.269, -2, 0, 0, -0.939693, 0.34202, 900, 255, 1, '', 0),
(75804, 1731, 0, 0, 0, 1, 1, -6119.89, -2964.38, 398.496, -2.82743, 0, 0, 0.987688, -0.156434, 900, 255, 1, '', 0),
(75807, 1731, 0, 0, 0, 1, 1, -5090.62, -4145.23, 336.802, 0.959931, 0, 0, 0.461749, 0.887011, 900, 255, 1, '', 0),
(75810, 1731, 0, 0, 0, 1, 1, -4895.64, -4037.91, 325.167, 1.43117, 0, 0, 0.656059, 0.75471, 900, 255, 1, '', 0),
(75813, 1731, 0, 0, 0, 1, 1, -5711, -3176, 321.194, -1, 0, 0, -0.25038, 0.968148, 900, 255, 1, '', 0),
(75816, 1731, 0, 0, 0, 1, 1, -5477.28, -4141.59, 392.58, 1.85005, 0, 0, 0.798636, 0.601815, 900, 255, 1, '', 0),
(75819, 1731, 0, 0, 0, 1, 1, -4826.3, -3957.94, 336.999, 0.575959, 0, 0, 0.284015, 0.95882, 900, 255, 1, '', 0),
(75822, 1731, 0, 0, 0, 1, 1, -5358, -2720, 363.231, 0.942, 0, 0, 0.453991, 0.891006, 900, 255, 1, '', 0),
(75825, 1731, 0, 0, 0, 1, 1, -5522.71, -4161.74, 383.012, -0.837758, 0, 0, 0.406737, -0.913545, 900, 255, 1, '', 0),
(75828, 1731, 0, 0, 0, 1, 1, -5407.56, -3421.51, 285.106, 1.88496, 0, 0, 0.809017, 0.587785, 900, 255, 1, '', 0),
(75831, 1731, 0, 0, 0, 1, 1, -5547.15, -2933.8, 374.021, 3.10665, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75834, 1731, 0, 0, 0, 1, 1, -6120.49, -3021.54, 396.495, -1.25664, 0, 0, 0.587785, -0.809017, 900, 255, 1, '', 0),
(75837, 1731, 0, 0, 0, 1, 1, -5361.94, -3235.94, 286.772, 0.942478, 0, 0, 0.45399, 0.891007, 900, 255, 1, '', 0),
(75840, 1731, 0, 0, 0, 1, 1, -5721.44, -3713.94, 312.301, -2.53072, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75843, 1731, 0, 0, 0, 1, 1, -4946, -2901, 352.869, 4.391, 0, 0, 0.811015, -0.585025, 900, 255, 1, '', 0),
(75846, 1731, 0, 0, 0, 1, 1, -5606, -4105, 390.494, 3.054, 0, 0, 0.999048, 0.04362, 900, 255, 1, '', 0),
(75849, 1731, 0, 0, 0, 1, 1, -5626, -3152, 323.433, 1.396, 0, 0, 0.642787, 0.766045, 900, 255, 1, '', 0),
(75852, 1731, 0, 0, 0, 1, 1, -5284.12, -3306.02, 254.845, 1.13446, 0, 0, 0.5373, 0.843391, 900, 255, 1, '', 0),
(75855, 1731, 0, 0, 0, 1, 1, -5773, -2917, 365.51, 0.731, 0, 0, 0.35732, 0.933982, 900, 255, 1, '', 0),
(75858, 1731, 0, 0, 0, 1, 1, -5179.53, -3290.78, 276.421, -2.89725, 0, 0, 0.992546, -0.121869, 900, 255, 1, '', 0),
(75861, 1731, 0, 0, 0, 1, 1, -4939, -2632, 331.85, 0.873, 0, 0, 0.422618, 0.906308, 900, 255, 1, '', 0),
(75864, 1731, 0, 0, 0, 1, 1, -5349.29, -4168.89, 336.427, -2.58309, 0, 0, 0.961262, -0.275637, 900, 255, 1, '', 0),
(75867, 1731, 0, 0, 0, 1, 1, -5575.64, -2751.37, 370.087, -0.977384, 0, 0, 0.469472, -0.882948, 900, 255, 1, '', 0),
(75870, 1731, 0, 0, 0, 1, 1, -6194.21, -2996.44, 387.533, -0.05236, 0, 0, 0.026177, -0.999657, 900, 255, 1, '', 0),
(75873, 1731, 0, 0, 0, 1, 1, -6225.2, -2998.35, 386.989, 2.60054, 0, 0, 0.96363, 0.267238, 900, 255, 1, '', 0),
(75876, 1731, 0, 0, 0, 1, 1, -5691.15, -4099.24, 397.565, -1.309, 0, 0, 0, 1, 900, 255, 1, '', 0),
(75879, 1731, 0, 0, 0, 1, 1, -6097.9, -2814.65, 420.024, 2.47837, 0, 0, 0.945519, 0.325568, 900, 255, 1, '', 0),
(75882, 1731, 0, 0, 0, 1, 1, -5802.45, -3740.06, 325.453, -0.069813, 0, 0, 0.034899, -0.999391, 900, 255, 1, '', 0),
(75885, 1731, 0, 0, 0, 1, 1, -5067.35, -2619.15, 373.198, 0.017453, 0, 0, 0.008727, 0.999962, 900, 255, 1, '', 0),
(75888, 1731, 0, 0, 0, 1, 1, -5344.7, -3897.95, 334.121, 1.41372, 0, 0, 0.649448, 0.760406, 900, 255, 1, '', 0),
(75891, 1731, 0, 0, 0, 1, 1, -4827.34, -3870.07, 308.506, 1.06465, 0, 0, 0.507538, 0.861629, 900, 255, 1, '', 0),
(75894, 1731, 0, 0, 0, 1, 1, -4991.61, -4149.51, 312.482, 0.383972, 0, 0, 0.190809, 0.981627, 900, 255, 1, '', 0),
(75897, 1731, 0, 0, 0, 1, 1, -4787.7, -4173.91, 305.719, -0.575959, 0, 0, 0.284015, -0.95882, 900, 255, 1, '', 0),
(75900, 1731, 0, 0, 0, 1, 1, -5003.64, -3982.49, 302.199, 0.715585, 0, 0, 0.350207, 0.936672, 900, 255, 1, '', 0),
(75903, 1731, 0, 0, 0, 1, 1, -6062.61, -2740.13, 422.309, -0.20944, 0, 0, 0.104528, -0.994522, 900, 255, 1, '', 0),
(75906, 1731, 0, 0, 0, 1, 1, -5821, -2801, 381.365, 1.833, 0, 0, 0.793353, 0.608761, 900, 255, 1, '', 0),
(75909, 1731, 0, 0, 0, 1, 1, -5879, -3948, 357.137, 5.669, 0, 0, 0.302264, -0.953224, 900, 255, 1, '', 0),
(75912, 1731, 0, 0, 0, 1, 1, -4843, -3956, 319.805, 1.765, 0, 0, 0.7722, 0.635379, 900, 255, 1, '', 0),
(75915, 1731, 0, 0, 0, 1, 1, -4739.3, -4114.37, 311.837, -1.25664, 0, 0, 0.587785, -0.809017, 900, 255, 1, '', 0),
(75918, 1731, 0, 0, 0, 1, 1, -4779.18, -4055.91, 311.679, 0.15708, 0, 0, 0.078459, 0.996917, 900, 255, 1, '', 0),
(75921, 1731, 0, 0, 0, 1, 1, -4939.38, -4077.81, 302.542, -0.122173, 0, 0, 0.061049, -0.998135, 900, 255, 1, '', 0),
(75924, 1731, 0, 0, 0, 1, 1, -5639.08, -3903.59, 325.725, 1.3439, 0, 0, 0.622515, 0.782608, 900, 255, 1, '', 0),
(75927, 1731, 0, 0, 0, 1, 1, -5736, -3879, 334.654, 1.838, 0, 0, 0.794947, 0.606679, 900, 255, 1, '', 0),
(75930, 1731, 0, 0, 0, 1, 1, -5653.57, -3924.1, 328.478, 0.20944, 0, 0, 0.104528, 0.994522, 900, 255, 1, '', 0),
(75936, 1731, 0, 0, 0, 1, 1, -5842.29, -4205.48, 417.789, -2.44346, 0, 0, 0.939693, -0.34202, 900, 255, 1, '', 0),
(120287, 3763, 1, 0, 0, 1, 1, -66.0833, -3381.52, 93.0802, -2.32129, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120290, 1731, 1, 0, 0, 1, 1, 1041.65, -4731.42, 17.7684, -1.01229, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120291, 1731, 530, 0, 0, 1, 1, -3584.34, -11828.8, 11.2678, 2.30383, 0, 0, 0, 1, 60, 255, 1, '', 0),
(120292, 1731, 1, 0, 0, 1, 1, 850.867, -4766.13, 38.2993, -1.0472, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120293, 1731, 1, 0, 0, 1, 1, 931.159, -4703.13, 23.2867, 0.733038, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120294, 1731, 1, 0, 0, 1, 1, -150.154, -5160.85, 25.5989, 0.279252, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120295, 1731, 1, 0, 0, 1, 1, 11.737, -4641.79, 49.2284, 3.05433, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120296, 1731, 1, 0, 0, 1, 1, -5.93815, -4229.33, 97.8519, 2.25147, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120297, 1731, 1, 0, 0, 1, 1, 842.223, -4749.95, 12.749, -1.91986, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120298, 1731, 1, 0, 0, 1, 1, 72.8817, -4528.48, 61.0341, -3.00195, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120299, 1731, 1, 0, 0, 1, 1, 535.034, -4936.4, 37.0527, -0.820303, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120300, 1731, 1, 0, 0, 1, 1, 847.784, -4206.12, -10.5332, 0.95993, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120301, 1731, 1, 0, 0, 1, 1, 149.417, -5075.54, 16.1375, 0.034906, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120302, 1731, 1, 0, 0, 1, 1, 598.039, -4543.84, 17.8262, 0.733038, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120303, 1731, 1, 0, 0, 1, 1, 35.3273, -4101.95, 63.2919, -2.26892, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120304, 1731, 1, 0, 0, 1, 1, -717.853, -4696.23, 37.8685, 2.42601, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120305, 1731, 1, 0, 0, 1, 1, -982.906, -4436.19, 34.2814, 0.418879, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120306, 1731, 0, 0, 0, 1, 1, -9403.04, -1049.86, 61.608, 1.51844, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120307, 1731, 1, 0, 0, 1, 1, 832.711, -4756.26, 38.3843, 2.42601, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120308, 1731, 1, 0, 0, 1, 1, 873.399, -4743.74, 30.9023, 0.942477, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120309, 1731, 0, 0, 0, 1, 1, -9296.18, -1008.83, 55.8491, -0.122173, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120310, 1731, 1, 0, 0, 1, 1, 908.307, -4224.11, 26.1928, -3.03684, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120311, 1731, 1, 0, 0, 1, 1, 73.9236, -4487.36, 50.8945, 0.802851, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120312, 1731, 1, 0, 0, 1, 1, 1114.85, -4224.99, 28.0452, 0.541051, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120313, 1731, 1, 0, 0, 1, 1, 894.402, -4080.42, 26.5511, 0.366518, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120314, 1731, 1, 0, 0, 1, 1, 901.996, -4036.53, -11.3043, 0.645772, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120315, 1731, 1, 0, 0, 1, 1, 947.336, -4220.51, -6.28864, -1.71042, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120316, 1731, 0, 0, 0, 1, 1, -9758.25, -460.59, 43.6968, -0.575957, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120317, 1731, 1, 0, 0, 1, 1, 798.026, -4046.3, -1.21654, 3.03684, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120318, 1731, 0, 0, 0, 1, 1, -9471.69, 560.812, 58.0184, 0.383971, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120319, 1731, 1, 0, 0, 1, 1, 7342.31, -1098.23, 47.9114, 0.558504, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120320, 1731, 1, 0, 0, 1, 1, -2266.06, 324.555, 114.628, 2.44346, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120321, 1731, 1, 0, 0, 1, 1, -2316.38, 421.633, 49.3617, -3.12412, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120322, 1731, 1, 0, 0, 1, 1, -5291.4, -1551.58, -47.6369, -3.00195, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120323, 1731, 1, 0, 0, 1, 1, -5140.26, -1305.46, -45.5541, 1.85005, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120325, 1731, 530, 0, 0, 1, 1, -2620.13, -11774.8, 10.9012, 3.18007, 0, 0, -0.999815, 0.01924, 300, 0, 1, '', 0),
(120326, 1731, 1, 0, 0, 1, 1, 7686.48, -446.749, -21.1478, 0.645772, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120327, 1731, 1, 0, 0, 1, 1, 1071.53, -3948.96, 24.9357, -2.75761, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120328, 1731, 1, 0, 0, 1, 1, 1213.05, -4591.97, 23.571, 2.05949, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120329, 1731, 1, 0, 0, 1, 1, 1200.46, -4646.21, 23.5411, 3.01941, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120330, 1731, 1, 0, 0, 1, 1, 966.096, -4906.56, 28.7647, -2.00713, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120331, 1731, 1, 0, 0, 1, 1, -520.767, -3323.54, 94.2695, -1.76278, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120332, 1731, 1, 0, 0, 1, 1, 955.784, -4045.23, -11.4768, -2.35619, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120333, 1731, 1, 0, 0, 1, 1, -370.268, -3674.13, 46.1044, -2.05949, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120334, 1731, 1, 0, 0, 1, 1, -4678.42, -1576.62, -28.2, -0.506145, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120335, 1731, 1, 0, 0, 1, 1, -4571.58, -1204.01, -45.4149, 2.86233, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120336, 103713, 1, 0, 0, 1, 1, -4146.65, -2297.34, 105.493, 0.296705, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120337, 1731, 1, 0, 0, 1, 1, 7513.76, -580.116, 0.446005, 0.401425, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120338, 1731, 0, 0, 0, 1, 1, -3220.77, -1333.22, 1.52318, 3.12412, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120339, 1731, 1, 0, 0, 1, 1, -511.093, -3651.16, 101.68, -0.471238, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120340, 1731, 530, 0, 0, 1, 1, -2997.91, -12274.1, 17.2133, 0.191985, 0, 0, 0, 1, 60, 255, 1, '', 0),
(120341, 1731, 1, 0, 0, 1, 1, -3576.84, -2404.6, 99.1772, -1.3439, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120342, 1731, 0, 0, 0, 1, 1, 1086.69, 1964.56, 8.29545, -1.62316, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120343, 1731, 0, 0, 0, 1, 1, 360.693, 1067.62, 105.336, 0.872664, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120344, 1731, 0, 0, 0, 1, 1, -745.488, -98.0213, 57.8578, 1.81514, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120345, 1731, 0, 0, 0, 1, 1, 1292.67, 1379.22, 53.7565, 2.51327, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120346, 1731, 0, 0, 0, 1, 1, 1193.68, 1263.56, 49.0877, 1.74533, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120347, 1731, 1, 0, 0, 1, 1, -1084.01, -1189.03, 74.0368, -2.02458, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120348, 1731, 1, 0, 0, 1, 1, -759.502, -750.369, 19.4819, 2.51327, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120349, 1731, 0, 0, 0, 1, 1, 2196.57, -500.417, 86.2787, -0.488691, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120350, 1731, 1, 0, 0, 1, 1, -2745.81, -1099.56, 34.6196, -2.68781, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120351, 1731, 1, 0, 0, 1, 1, -845.568, -1614.51, 92.8366, -2.44346, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120352, 1731, 1, 0, 0, 1, 1, 1242.78, -4948.08, 16.0424, -2.67035, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120353, 1731, 1, 0, 0, 1, 1, 2985.02, 467.96, 21.9406, 1.13446, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120354, 1731, 0, 0, 0, 1, 1, 2673.29, -772.678, 85.4531, -0.418879, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120355, 1731, 1, 0, 0, 1, 1, 2445.04, 1097.22, 338.232, 0.698132, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120356, 1731, 1, 0, 0, 1, 1, 5153.76, 458.863, 26.0669, 1.43117, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120357, 1731, 1, 0, 0, 1, 1, -1488.98, -3509.48, 179.095, -1.22173, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120358, 1731, 1, 0, 0, 1, 1, -1459.06, -3604.05, 93.9616, 2.96704, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120359, 1731, 1, 0, 0, 1, 1, -577.007, -3096.23, 93.9835, 2.14675, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120360, 1731, 1, 0, 0, 1, 1, -1706.42, -3703.33, 33.6647, 2.35619, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120361, 1731, 1, 0, 0, 1, 1, 7517.69, -725.736, 3.49451, 2.00713, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120362, 1731, 530, 0, 0, 1, 1, -3208.36, -12254.1, 57.0038, -0.680679, 0, 0, 0, 1, 60, 255, 1, '', 0),
(120324, 1731, 530, 0, 0, 1, 1, -1488.09, -10655.4, 135.281, 4.83145, 0, 0, -0.663785, 0.747923, 300, 0, 1, '', 0),
(120587, 3763, 1, 0, 0, 1, 1, -2241.22, -2552.48, 92.1614, -2.18166, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120588, 3763, 1, 0, 0, 1, 1, -3956.45, -1863.94, 97.3946, -1.81514, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120589, 3763, 1, 0, 0, 1, 1, -3885.71, -1617.31, 91.6667, 2.91469, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120594, 1731, 0, 0, 0, 1, 1, -4950.32, -2971.38, 322.214, -0.139624, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120595, 1731, 0, 0, 0, 1, 1, -9892.58, 1420.49, 40.9331, 1.36136, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120596, 1731, 0, 0, 0, 1, 1, -4218.17, -2474.73, 282.472, 2.93214, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120597, 1731, 0, 0, 0, 1, 1, -9211.76, -1331.8, 101.91, 1.15192, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120598, 1731, 0, 0, 0, 1, 1, -9833.92, 1408.46, 38.2337, -2.82743, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120599, 1731, 0, 0, 0, 1, 1, 2202.98, -65.3898, 30.8705, -2.75761, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120600, 1731, 0, 0, 0, 1, 1, -5127.31, -3926.78, 325.466, -3.08918, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120601, 1731, 0, 0, 0, 1, 1, 2646.08, 1350.02, 9.43725, -1.67551, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120602, 1731, 0, 0, 0, 1, 1, 1727.84, 808.579, 68.0469, -1.55334, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120603, 1731, 0, 0, 0, 1, 1, -4733.4, -3428.12, 287.19, 0.90757, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120604, 1731, 0, 0, 0, 1, 1, -4855.06, -2882.53, 336.653, 1.97222, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120605, 1731, 0, 0, 0, 1, 1, -1004.35, -339.056, 13.7319, 0.226892, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120606, 1731, 1, 0, 0, 1, 1, 3457.16, -345.804, 136.905, -1.15192, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120607, 1731, 1, 0, 0, 1, 1, -864.57, -1157.19, 118.865, -1.37881, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120608, 1731, 1, 0, 0, 1, 1, -5183.99, -2413.04, -41.9981, -2.58308, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120609, 1731, 1, 0, 0, 1, 1, -4651.72, -1381.44, -42.4803, -3.10665, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120610, 1731, 1, 0, 0, 1, 1, 1794.04, 1073.96, 175.753, 2.63544, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120611, 1731, 1, 0, 0, 1, 1, 3049.16, -221.964, 124.717, 1.22173, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120612, 1731, 1, 0, 0, 1, 1, 1459.55, -4745.43, -0.996877, -2.00713, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120613, 1731, 1, 0, 0, 1, 1, 1562.55, -4769.49, 15.478, -0.279252, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120614, 1731, 1, 0, 0, 1, 1, 1464.22, -4891.54, 14.0545, 0.296705, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120615, 1731, 1, 0, 0, 1, 1, 1488.58, -4816.56, 9.27292, 1.0821, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120616, 1731, 1, 0, 0, 1, 1, -1731.5, 446.03, 104.992, 1.50098, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120617, 1731, 1, 0, 0, 1, 1, 388.236, -2245.49, 196.681, 0.087266, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120618, 1731, 530, 0, 0, 1, 1, 8764.94, -7016, 40.6887, 0.890117, 0, 0, 0, 1, 60, 255, 1, '', 0),
(120672, 1731, 0, 0, 0, 1, 1, -6113.85, -155.679, 438.348, 0.453785, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120699, 1731, 0, 0, 0, 1, 1, -476.366, 1585.88, 17.7124, -0.157079, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120700, 1731, 0, 0, 0, 1, 1, -472.812, 368.856, 106.055, 2.67035, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120785, 1731, 0, 0, 0, 1, 1, -11557.6, 1578.82, -14.9868, -0.017452, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120786, 1731, 0, 0, 0, 1, 1, -11446.9, 1718.49, 13.7341, 2.30383, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120787, 1731, 0, 0, 0, 1, 1, -11217.4, 1538.02, 35.4237, -0.139624, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120788, 1731, 0, 0, 0, 1, 1, -5727.6, -1662.49, 363.751, 1.48353, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120789, 1731, 1, 0, 0, 1, 1, -4913.51, -1285.78, -32.4988, 0.628317, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120790, 1731, 1, 0, 0, 1, 1, -4620.59, -1715.87, -22.4819, -0.523598, 0, 0, 0, 1, 3600, 255, 1, '', 0),
(120791, 1731, 530, 0, 0, 1, 1, -4095.92, -11588.5, 19.9823, -1.78023, 0, 0, 0, 1, 60, 255, 1, '', 0),
(150129, 1731, 0, 0, 0, 1, 1, -8801.88, -883.678, 82.6097, -2.93214, 0, 0, 0, 0, 120, 0, 1, '', 0),
(150130, 1731, 0, 0, 0, 1, 1, -9025.47, -595.008, 56.6837, 0.767944, 0, 0, 0, 0, 120, 0, 1, '', 0),
(150138, 1731, 0, 0, 0, 1, 1, -9505.85, -1566.84, 71.6791, -2.19911, 0, 0, 0, 0, 120, 0, 1, '', 0),
(150139, 1731, 0, 0, 0, 1, 1, -9541.09, 652.099, 49.707, 0.226892, 0, 0, 0, 0, 120, 0, 1, '', 0),
(2134521, 1731, 530, 0, 0, 1, 1, -2141.63, -12362, 28.6973, 6.15893, 0, 0, -0.0620857, 0.998071, 300, 0, 1, '', 0),
(2134623, 1731, 530, 0, 0, 1, 1, -2474.41, -12316.3, 15.1395, 1.79086, 0, 0, -0.780479, -0.625182, 300, 0, 1, '', 0),
(2134624, 1731, 530, 0, 0, 1, 1, -2424.38, -12134.5, 35.782, 2.10502, 0, 0, -0.86867, -0.495392, 300, 0, 1, '', 0),
(2134625, 1731, 530, 0, 0, 1, 1, -1921.96, -11650.8, 38.4271, 5.58182, 0, 0, -0.343537, 0.939139, 300, 0, 1, '', 0),
(2134626, 1731, 530, 0, 0, 1, 1, -1341.23, -11605.1, 8.95622, 5.65439, 0, 0, -0.309242, 0.950984, 300, 0, 1, '', 0),
(2134627, 1731, 530, 0, 0, 1, 1, -1137.25, -11491.4, -3.61492, 4.48289, 0, 0, -0.783418, 0.621496, 300, 0, 1, '', 0),
(2134631, 1731, 530, 0, 0, 1, 1, -2109.01, -10874.3, 74.3795, 4.78009, 0, 0, -0.682771, 0.730633, 300, 0, 1, '', 0),
(2134632, 1731, 530, 0, 0, 1, 1, -1663.13, -11742.1, 36.28, 1.60708, 0, 0, -0.719818, -0.694162, 300, 0, 1, '', 0),
(2134633, 1731, 530, 0, 0, 1, 1, -1500.01, -11706.2, 35.8202, 3.27684, 0, 0, -0.997715, 0.0675699, 300, 0, 1, '', 0),
(2134634, 1731, 530, 0, 0, 1, 1, -2041.58, -12329.4, 12.4268, 4.08894, 0, 0, -0.889899, 0.456158, 300, 0, 1, '', 0),
(2134635, 1731, 530, 0, 0, 1, 1, -1967.73, -11628.9, 48.4035, 3.25264, 0, 0, -0.998459, 0.0554972, 300, 0, 1, '', 0),
(2134636, 1731, 530, 0, 0, 1, 1, -1878.46, -11569.2, 45.1684, 1.02997, 0, 0, -0.492522, -0.8703, 300, 0, 1, '', 0),
(2134637, 1731, 530, 0, 0, 1, 1, -2372.18, -11507.2, 25.9138, 1.38591, 0, 0, -0.638815, -0.769361, 300, 0, 1, '', 0),
(2134638, 1731, 530, 0, 0, 1, 1, -2291.71, -11507.2, 26.5163, 2.19518, 0, 0, -0.890113, -0.45574, 300, 0, 1, '', 0),
(2134639, 1731, 530, 0, 0, 1, 1, -2204.71, -12381.6, 42.0177, 2.9831, 0, 0, -0.996862, -0.0791652, 300, 0, 1, '', 0),
(2134640, 1731, 530, 0, 0, 1, 1, -2106.83, -11490.9, 64.849, 1.25805, 0, 0, -0.588357, -0.808602, 300, 0, 1, '', 0),
(2134643, 1731, 530, 0, 0, 1, 1, -2675.37, -11420.7, 27.6011, 0.789321, 0, 0, -0.384495, -0.923127, 300, 0, 1, '', 0),
(2134645, 1731, 530, 0, 0, 1, 1, -2109.01, -11164.7, 72.2675, 2.94508, 0, 0, -0.995177, -0.0980966, 300, 0, 1, '', 0),
(2134647, 1731, 530, 0, 0, 1, 1, -2591.03, -11575.8, 27.5438, 2.61365, 0, 0, -0.965361, -0.260918, 300, 0, 1, '', 0),
(2134648, 1731, 530, 0, 0, 1, 1, -2353.22, -11458.8, 25.0549, 1.80359, 0, 0, -0.784441, -0.620204, 300, 0, 1, '', 0),
(2134649, 1731, 530, 0, 0, 1, 1, -1634.86, -11083.1, 70.4121, 3.39433, 0, 0, -0.992026, 0.126033, 300, 0, 1, '', 0),
(2134651, 1731, 530, 0, 0, 1, 1, -1895.86, -11406.1, 56.617, 6.21768, 0, 0, -0.0327457, 0.999464, 300, 0, 1, '', 0),
(2134652, 1731, 530, 0, 0, 1, 1, -2050.28, -11057, 60.3768, 0.466683, 0, 0, -0.23123, -0.972899, 300, 0, 1, '', 0),
(2134653, 1731, 530, 0, 0, 1, 1, -1541.33, -11471.3, 61.391, 0.580722, 0, 0, -0.286298, -0.958141, 300, 0, 1, '', 0),
(2134654, 1731, 530, 0, 0, 1, 1, -1225.55, -11112.2, -29.8231, 0.349187, 0, 0, -0.173708, -0.984797, 300, 0, 1, '', 0),
(2134656, 1731, 530, 0, 0, 1, 1, -2286.65, -11245, 38.6148, 1.15438, 0, 0, -0.54567, -0.838, 300, 0, 1, '', 0),
(2134658, 1731, 530, 0, 0, 1, 1, -1965.31, -11014.4, 61.4692, 1.775, 0, 0, -0.775495, -0.631354, 300, 0, 1, '', 0),
(2134659, 1731, 530, 0, 0, 1, 1, -1164.71, -11177.3, -53.5757, 4.50788, 0, 0, -0.775591, 0.631235, 300, 0, 1, '', 0),
(2134661, 1731, 530, 0, 0, 1, 1, -1984.3, -12204.3, 20.6867, 5.03581, 0, 0, -0.584032, 0.811731, 300, 0, 1, '', 0),
(2134662, 1731, 530, 0, 0, 1, 1, -1695.76, -10809.1, 64.1206, 3.56712, 0, 0, -0.977451, 0.211162, 300, 0, 1, '', 0),
(2134663, 1731, 530, 0, 0, 1, 1, -1565.26, -11190.8, 67.9502, 1.5587, 0, 0, -0.702817, -0.71137, 300, 0, 1, '', 0),
(2134664, 1731, 530, 0, 0, 1, 1, -1811.05, -12126.4, 36.0542, 4.79862, 0, 0, -0.675971, 0.736928, 300, 0, 1, '', 0),
(2134665, 1731, 530, 0, 0, 1, 1, -2678.86, -11474.6, 27.1757, 1.65201, 0, 0, -0.735228, -0.67782, 300, 0, 1, '', 0),
(2134667, 1731, 530, 0, 0, 1, 1, -1841.48, -11057, 67.1545, 4.26173, 0, 0, -0.84722, 0.531242, 300, 0, 1, '', 0),
(2134669, 1731, 530, 0, 0, 1, 1, -1702.28, -12052.1, 14.2249, 1.61745, 0, 0, -0.723407, -0.690422, 300, 0, 1, '', 0),
(2134670, 1731, 530, 0, 0, 1, 1, -2490.87, -11650.9, 23.0702, 0.565012, 0, 0, -0.278763, -0.96036, 300, 0, 1, '', 0),
(2134673, 1731, 530, 0, 0, 1, 1, -1282.51, -12505.6, 56.1227, 4.43451, 0, 0, -0.798221, 0.602365, 300, 0, 1, '', 0),
(2134674, 1731, 530, 0, 0, 1, 1, -2176.43, -12287, 53.9191, 2.47164, 0, 0, -0.944419, -0.328745, 300, 0, 1, '', 0),
(2134675, 1731, 530, 0, 0, 1, 1, -1458.68, -11549.6, 34.0723, 0.33882, 0, 0, -0.168601, -0.985685, 300, 0, 1, '', 0),
(2134676, 1731, 530, 0, 0, 1, 1, -1878.46, -11474.6, 50.4829, 4.37577, 0, 0, -0.815568, 0.578661, 300, 0, 1, '', 0),
(2134678, 1731, 530, 0, 0, 1, 1, -2207.48, -11116.8, 53.2854, 6.02824, 0, 0, -0.127126, 0.991887, 300, 0, 1, '', 0),
(2134680, 1731, 530, 0, 0, 1, 1, -2572.28, -11213.6, 21.2003, 0.231691, 0, 0, -0.115587, -0.993297, 300, 0, 1, '', 0),
(2134681, 1731, 530, 0, 0, 1, 1, -1174.23, -12542.7, 66.8175, 0.720042, 0, 0, -0.352294, -0.935889, 300, 0, 1, '', 0),
(2134682, 1731, 530, 0, 0, 1, 1, -2287.36, -12322.9, 51.7417, 1.66583, 0, 0, -0.739895, -0.672722, 300, 0, 1, '', 0),
(2134683, 1731, 530, 0, 0, 1, 1, -2615.78, -12189.1, 28.3355, 0.356099, 0, 0, -0.17711, -0.984191, 300, 0, 1, '', 0),
(2134684, 1731, 530, 0, 0, 1, 1, -2539.66, -11915, 21.7351, 5.82373, 0, 0, -0.227714, 0.973728, 300, 0, 1, '', 0),
(2134685, 1731, 530, 0, 0, 1, 1, -2150.33, -11647.5, 50.494, 3.29066, 0, 0, -0.997224, 0.0744643, 300, 0, 1, '', 0),
(2134689, 1731, 530, 0, 0, 1, 1, -2291.67, -11118.2, 11.4338, 3.49643, 0, 0, -0.984302, 0.176491, 300, 0, 1, '', 0),
(2134690, 1731, 530, 0, 0, 1, 1, -2030.71, -10760.1, 93.8289, 4.37577, 0, 0, -0.815568, 0.578661, 300, 0, 1, '', 0),
(2134691, 1731, 530, 0, 0, 1, 1, -1413.01, -10724.2, 80.2255, 2.41981, 0, 0, -0.935582, -0.353109, 300, 0, 1, '', 0),
(2134693, 1731, 530, 0, 0, 1, 1, -1517.41, -11239.7, 68.91, 0.580722, 0, 0, -0.286298, -0.958141, 300, 0, 1, '', 0),
(2134695, 1731, 530, 0, 0, 1, 1, -1882.81, -12094.5, 28.5107, 5.35374, 0, 0, -0.448173, 0.893947, 300, 0, 1, '', 0),
(2134696, 1731, 530, 0, 0, 1, 1, -1876.28, -11383.3, 56.5906, 6.1693, 0, 0, -0.0569113, 0.998379, 300, 0, 1, '', 0),
(2134700, 1731, 530, 0, 0, 1, 1, -1995.28, -10558.2, 180.937, 3.5013, 0, 0, -0.98387, 0.178888, 300, 0, 1, '', 0),
(2134702, 1731, 530, 0, 0, 1, 1, -2543.74, -11213.9, 22.2677, 3.71069, 0, 0, -0.959788, 0.280726, 300, 0, 1, '', 0),
(2134704, 1731, 530, 0, 0, 1, 1, -1752.31, -11455, 47.9052, 0.570355, 0, 0, -0.281328, -0.959612, 300, 0, 1, '', 0),
(2134705, 1731, 530, 0, 0, 1, 1, -2206.88, -11109.2, 46.7431, 6.11401, 0, 0, -0.0844871, 0.996425, 300, 0, 1, '', 0),
(2134706, 1731, 530, 0, 0, 1, 1, -2334.65, -11213.8, 23.1339, 5.00015, 0, 0, -0.598412, 0.801189, 300, 0, 1, '', 0),
(2134707, 1731, 530, 0, 0, 1, 1, -1721.86, -11709.5, 42.2004, 6.04489, 0, 0, -0.118864, 0.992911, 300, 0, 1, '', 0),
(2134708, 1731, 530, 0, 0, 1, 1, -2633.48, -10812.1, -17.7529, 0.468565, 0, 0, -0.232145, -0.972681, 300, 0, 1, '', 0),
(2134711, 1731, 530, 0, 0, 1, 1, -2254.73, -10861.3, 8.6671, 4.66259, 0, 0, -0.724491, 0.689284, 300, 0, 1, '', 0),
(2134712, 1731, 530, 0, 0, 1, 1, -2111.18, -10906.9, 69.0148, 3.45308, 0, 0, -0.987897, 0.155114, 300, 0, 1, '', 0),
(2134713, 1731, 530, 0, 0, 1, 1, -1900.21, -11667.1, 42.0804, 5.76843, 0, 0, -0.254544, 0.967061, 300, 0, 1, '', 0),
(2134714, 1731, 530, 0, 0, 1, 1, -2074.21, -11037.4, 62.5974, 5.24661, 0, 0, -0.495391, 0.86867, 300, 0, 1, '', 0),
(2134715, 1731, 530, 0, 0, 1, 1, -2287.36, -12052.1, 27.5849, 0.801891, 0, 0, -0.390289, -0.920693, 300, 0, 1, '', 0),
(2134716, 1731, 530, 0, 0, 1, 1, -2263.43, -11778, 23.1581, 0.349187, 0, 0, -0.173708, -0.984797, 300, 0, 1, '', 0),
(2134720, 1731, 530, 0, 0, 1, 1, -1911.08, -11291.9, 66.1291, 2.19518, 0, 0, -0.890113, -0.45574, 300, 0, 1, '', 0),
(2134721, 1731, 530, 0, 0, 1, 1, -2087.26, -11422.4, 65.3849, 4.73171, 0, 0, -0.700244, 0.713904, 300, 0, 1, '', 0),
(2134722, 1731, 530, 0, 0, 1, 1, -1282.51, -11422.4, 10.0034, 1.89453, 0, 0, -0.811823, -0.583904, 300, 0, 1, '', 0),
(2134723, 1731, 530, 0, 0, 1, 1, -2642.91, -11894.6, 10.7262, 1.82715, 0, 0, -0.791692, -0.610921, 300, 0, 1, '', 0),
(2134724, 1731, 530, 0, 0, 1, 1, -1513.06, -11621.4, 23.5941, 3.63623, 0, 0, -0.969572, 0.244807, 300, 0, 1, '', 0),
(2134725, 1731, 530, 0, 0, 1, 1, -2045.93, -11239.7, 80.7004, 5.13258, 0, 0, -0.544091, 0.839026, 300, 0, 1, '', 0),
(2134726, 1731, 530, 0, 0, 1, 1, -1519.58, -11125.5, 79.8109, 1.44121, 0, 0, -0.659838, -0.751408, 300, 0, 1, '', 0),
(2134728, 1731, 530, 0, 0, 1, 1, -2596.21, -11282.1, 35.5212, 2.87597, 0, 0, -0.991194, -0.132422, 300, 0, 1, '', 0),
(2134729, 1731, 530, 0, 0, 1, 1, -2021.07, -10685.3, 125.446, 3.33323, 0, 0, -0.995413, 0.0956708, 300, 0, 1, '', 0),
(2134731, 1731, 530, 0, 0, 1, 1, -1774.06, -11510.5, 48.2173, 3.50837, 0, 0, -0.983231, 0.182363, 300, 0, 1, '', 0),
(2134732, 1731, 530, 0, 0, 1, 1, -1839.31, -10655.7, 146.297, 2.63752, 0, 0, -0.968407, -0.249376, 300, 0, 1, '', 0),
(2134733, 1731, 530, 0, 0, 1, 1, -2544.01, -11419.1, 41.3461, 2.76884, 0, 0, -0.982682, -0.185299, 300, 0, 1, '', 0),
(2134734, 1731, 530, 0, 0, 1, 1, -1193.33, -12084.7, 5.45135, 5.12566, 0, 0, -0.546987, 0.837141, 300, 0, 1, '', 0),
(2134735, 1731, 530, 0, 0, 1, 1, -1908.91, -11216.9, 58.905, 1.27878, 0, 0, -0.596708, -0.802459, 300, 0, 1, '', 0),
(2134736, 1731, 530, 0, 0, 1, 1, -2413.51, -11970.5, 18.3405, 3.39433, 0, 0, -0.992026, 0.126033, 300, 0, 1, '', 0),
(2134737, 1731, 530, 0, 0, 1, 1, -2106.45, -11498.1, 59.5798, 5.5369, 0, 0, -0.364544, 0.931186, 300, 0, 1, '', 0);

-- Duskwood Gameobjects
INSERT INTO `gameobject` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`, `ScriptName`, `VerifiedBuild`) VALUES 
(73601, 1732, 0, 0, 0, 1, 1, -11045.7, -299.893, 16.2588, -2.53072, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73602, 1735, 0, 0, 0, 1, 1, -11045.7, -299.893, 16.2588, -2.53072, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73603, 1733, 0, 0, 0, 1, 1, -11045.7, -299.893, 16.2588, -2.53072, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73604, 1734, 0, 0, 0, 1, 1, -11045.7, -299.893, 16.2588, -2.53072, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73606, 1732, 0, 0, 0, 1, 1, -10174.9, -700.101, 44.6107, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73607, 1735, 0, 0, 0, 1, 1, -10174.9, -700.101, 44.6107, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73608, 1733, 0, 0, 0, 1, 1, -10174.9, -700.101, 44.6107, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73609, 1734, 0, 0, 0, 1, 1, -10174.9, -700.101, 44.6107, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73611, 1732, 0, 0, 0, 1, 1, -10436, -1012, 48.816, -3, 0, 0, -0.956305, 0.292372, 300, 255, 1, '', 0),
(73612, 1735, 0, 0, 0, 1, 1, -10436, -1012, 48.816, -3, 0, 0, -0.956305, 0.292372, 300, 255, 1, '', 0),
(73613, 1733, 0, 0, 0, 1, 1, -10436, -1012, 48.816, -3, 0, 0, -0.956305, 0.292372, 300, 255, 1, '', 0),
(73614, 1734, 0, 0, 0, 1, 1, -10436, -1012, 48.816, -3, 0, 0, -0.956305, 0.292372, 300, 255, 1, '', 0),
(73616, 1732, 0, 0, 0, 1, 1, -11125.1, -774.882, 59.6363, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73617, 1735, 0, 0, 0, 1, 1, -11125.1, -774.882, 59.6363, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73618, 1733, 0, 0, 0, 1, 1, -11125.1, -774.882, 59.6363, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73619, 1734, 0, 0, 0, 1, 1, -11125.1, -774.882, 59.6363, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73621, 1732, 0, 0, 0, 1, 1, -10974, 73.907, 39.412, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73622, 1735, 0, 0, 0, 1, 1, -10974, 73.907, 39.412, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73623, 1733, 0, 0, 0, 1, 1, -10974, 73.907, 39.412, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73624, 1734, 0, 0, 0, 1, 1, -10974, 73.907, 39.412, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73626, 1732, 0, 0, 0, 1, 1, -11102, -256, 34.241, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73627, 1735, 0, 0, 0, 1, 1, -11102, -256, 34.241, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73628, 1733, 0, 0, 0, 1, 1, -11102, -256, 34.241, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73629, 1734, 0, 0, 0, 1, 1, -11102, -256, 34.241, -1, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73631, 1732, 0, 0, 0, 1, 1, -10519.2, 677.127, 15.6122, 2.58308, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73632, 1735, 0, 0, 0, 1, 1, -10519.2, 677.127, 15.6122, 2.58308, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73633, 1733, 0, 0, 0, 1, 1, -10519.2, 677.127, 15.6122, 2.58308, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73634, 1734, 0, 0, 0, 1, 1, -10519.2, 677.127, 15.6122, 2.58308, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73636, 1732, 0, 0, 0, 1, 1, -10717, -587, 64.392, 2.897, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73637, 1735, 0, 0, 0, 1, 1, -10717, -587, 64.392, 2.897, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73638, 1733, 0, 0, 0, 1, 1, -10717, -587, 64.392, 2.897, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73639, 1734, 0, 0, 0, 1, 1, -10717, -587, 64.392, 2.897, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73641, 1732, 0, 0, 0, 1, 1, -11101, -254, 33.215, 2.566, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73642, 1735, 0, 0, 0, 1, 1, -11101, -254, 33.215, 2.566, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73643, 1733, 0, 0, 0, 1, 1, -11101, -254, 33.215, 2.566, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73644, 1734, 0, 0, 0, 1, 1, -11101, -254, 33.215, 2.566, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73646, 1732, 0, 0, 0, 1, 1, -10449.9, -1020.57, 53.7499, 1.50098, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73647, 1735, 0, 0, 0, 1, 1, -10449.9, -1020.57, 53.7499, 1.50098, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73648, 1733, 0, 0, 0, 1, 1, -10449.9, -1020.57, 53.7499, 1.50098, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73649, 1734, 0, 0, 0, 1, 1, -10449.9, -1020.57, 53.7499, 1.50098, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73656, 1732, 0, 0, 0, 1, 1, -10486.4, -770.31, 62.5892, 1.09956, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73657, 1735, 0, 0, 0, 1, 1, -10486.4, -770.31, 62.5892, 1.09956, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73658, 1733, 0, 0, 0, 1, 1, -10486.4, -770.31, 62.5892, 1.09956, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73659, 1734, 0, 0, 0, 1, 1, -10486.4, -770.31, 62.5892, 1.09956, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73661, 1732, 0, 0, 0, 1, 1, -10415, -1255, 52.485, -3, 0, 0, -0.984808, 0.173648, 300, 255, 1, '', 0),
(73662, 1735, 0, 0, 0, 1, 1, -10415, -1255, 52.485, -3, 0, 0, -0.984808, 0.173648, 300, 255, 1, '', 0),
(73663, 1733, 0, 0, 0, 1, 1, -10415, -1255, 52.485, -3, 0, 0, -0.984808, 0.173648, 300, 255, 1, '', 0),
(73664, 1734, 0, 0, 0, 1, 1, -10415, -1255, 52.485, -3, 0, 0, -0.984808, 0.173648, 300, 255, 1, '', 0),
(73666, 1732, 0, 0, 0, 1, 1, -11010, -990, 68.9247, -1, 0, 0, -0.622514, 0.782608, 300, 255, 1, '', 0),
(73667, 1735, 0, 0, 0, 1, 1, -11010, -990, 68.9247, -1, 0, 0, -0.622514, 0.782608, 300, 255, 1, '', 0),
(73668, 1733, 0, 0, 0, 1, 1, -11010, -990, 68.9247, -1, 0, 0, -0.622514, 0.782608, 300, 255, 1, '', 0),
(73669, 1734, 0, 0, 0, 1, 1, -11010, -990, 68.9247, -1, 0, 0, -0.622514, 0.782608, 300, 255, 1, '', 0),
(73676, 1732, 0, 0, 0, 1, 1, -10233.3, -1358.39, 83.4799, 2.30383, 0, 0, 0.913545, 0.406737, 300, 255, 1, '', 0),
(73677, 1735, 0, 0, 0, 1, 1, -10233.3, -1358.39, 83.4799, 2.30383, 0, 0, 0.913545, 0.406737, 300, 255, 1, '', 0),
(73678, 1733, 0, 0, 0, 1, 1, -10233.3, -1358.39, 83.4799, 2.30383, 0, 0, 0.913545, 0.406737, 300, 255, 1, '', 0),
(73679, 1734, 0, 0, 0, 1, 1, -10233.3, -1358.39, 83.4799, 2.30383, 0, 0, 0.913545, 0.406737, 300, 255, 1, '', 0),
(73681, 1732, 0, 0, 0, 1, 1, -11101.3, -74.5433, 16.4636, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73682, 1735, 0, 0, 0, 1, 1, -11101.3, -74.5433, 16.4636, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73683, 1733, 0, 0, 0, 1, 1, -11101.3, -74.5433, 16.4636, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73684, 1734, 0, 0, 0, 1, 1, -11101.3, -74.5433, 16.4636, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73686, 1732, 0, 0, 0, 1, 1, -11137.5, -904.159, 66.684, 2.1293, 0, 0, 0.87462, 0.48481, 300, 255, 1, '', 0),
(73687, 1735, 0, 0, 0, 1, 1, -11137.5, -904.159, 66.684, 2.1293, 0, 0, 0.87462, 0.48481, 300, 255, 1, '', 0),
(73688, 1733, 0, 0, 0, 1, 1, -11137.5, -904.159, 66.684, 2.1293, 0, 0, 0.87462, 0.48481, 300, 255, 1, '', 0),
(73689, 1734, 0, 0, 0, 1, 1, -11137.5, -904.159, 66.684, 2.1293, 0, 0, 0.87462, 0.48481, 300, 255, 1, '', 0),
(73691, 1732, 0, 0, 0, 1, 1, -11158.4, -925.164, 85.8275, 1.53589, 0, 0, 0.694658, 0.71934, 300, 255, 1, '', 0),
(73692, 1735, 0, 0, 0, 1, 1, -11158.4, -925.164, 85.8275, 1.53589, 0, 0, 0.694658, 0.71934, 300, 255, 1, '', 0),
(73693, 1733, 0, 0, 0, 1, 1, -11158.4, -925.164, 85.8275, 1.53589, 0, 0, 0.694658, 0.71934, 300, 255, 1, '', 0),
(73694, 1734, 0, 0, 0, 1, 1, -11158.4, -925.164, 85.8275, 1.53589, 0, 0, 0.694658, 0.71934, 300, 255, 1, '', 0),
(73696, 1732, 0, 0, 0, 1, 1, -11137.7, -1174.23, 43.9495, 2.47837, 0, 0, 0.945519, 0.325568, 300, 255, 1, '', 0),
(73697, 1735, 0, 0, 0, 1, 1, -11137.7, -1174.23, 43.9495, 2.47837, 0, 0, 0.945519, 0.325568, 300, 255, 1, '', 0),
(73698, 1733, 0, 0, 0, 1, 1, -11137.7, -1174.23, 43.9495, 2.47837, 0, 0, 0.945519, 0.325568, 300, 255, 1, '', 0),
(73699, 1734, 0, 0, 0, 1, 1, -11137.7, -1174.23, 43.9495, 2.47837, 0, 0, 0.945519, 0.325568, 300, 255, 1, '', 0),
(73701, 1732, 0, 0, 0, 1, 1, -11210.8, -846.305, 78.2837, -0.575959, 0, 0, 0.284015, -0.95882, 300, 255, 1, '', 0),
(73702, 1735, 0, 0, 0, 1, 1, -11210.8, -846.305, 78.2837, -0.575959, 0, 0, 0.284015, -0.95882, 300, 255, 1, '', 0),
(73703, 1733, 0, 0, 0, 1, 1, -11210.8, -846.305, 78.2837, -0.575959, 0, 0, 0.284015, -0.95882, 300, 255, 1, '', 0),
(73704, 1734, 0, 0, 0, 1, 1, -11210.8, -846.305, 78.2837, -0.575959, 0, 0, 0.284015, -0.95882, 300, 255, 1, '', 0),
(73706, 1732, 0, 0, 0, 1, 1, -11235.4, -875.842, 85.4821, 2.67035, 0, 0, 0.97237, 0.233445, 300, 255, 1, '', 0),
(73707, 1735, 0, 0, 0, 1, 1, -11235.4, -875.842, 85.4821, 2.67035, 0, 0, 0.97237, 0.233445, 300, 255, 1, '', 0),
(73708, 1733, 0, 0, 0, 1, 1, -11235.4, -875.842, 85.4821, 2.67035, 0, 0, 0.97237, 0.233445, 300, 255, 1, '', 0),
(73709, 1734, 0, 0, 0, 1, 1, -11235.4, -875.842, 85.4821, 2.67035, 0, 0, 0.97237, 0.233445, 300, 255, 1, '', 0),
(73711, 1732, 0, 0, 0, 1, 1, -11087.5, 12.5553, 44.3524, -1.81514, 0, 0, 0.788011, -0.615661, 300, 255, 1, '', 0),
(73712, 1735, 0, 0, 0, 1, 1, -11087.5, 12.5553, 44.3524, -1.81514, 0, 0, 0.788011, -0.615661, 300, 255, 1, '', 0),
(73713, 1733, 0, 0, 0, 1, 1, -11087.5, 12.5553, 44.3524, -1.81514, 0, 0, 0.788011, -0.615661, 300, 255, 1, '', 0),
(73714, 1734, 0, 0, 0, 1, 1, -11087.5, 12.5553, 44.3524, -1.81514, 0, 0, 0.788011, -0.615661, 300, 255, 1, '', 0),
(73716, 1732, 0, 0, 0, 1, 1, -10600.2, -1486.5, 94.3496, -1.29154, 0, 0, 0.601815, -0.798635, 300, 255, 1, '', 0),
(73717, 1735, 0, 0, 0, 1, 1, -10600.2, -1486.5, 94.3496, -1.29154, 0, 0, 0.601815, -0.798635, 300, 255, 1, '', 0),
(73718, 1733, 0, 0, 0, 1, 1, -10600.2, -1486.5, 94.3496, -1.29154, 0, 0, 0.601815, -0.798635, 300, 255, 1, '', 0),
(73719, 1734, 0, 0, 0, 1, 1, -10600.2, -1486.5, 94.3496, -1.29154, 0, 0, 0.601815, -0.798635, 300, 255, 1, '', 0),
(73721, 1732, 0, 0, 0, 1, 1, -10848.7, -1375.08, 63.6493, -2.60054, 0, 0, 0.96363, -0.267238, 300, 255, 1, '', 0),
(73722, 1735, 0, 0, 0, 1, 1, -10848.7, -1375.08, 63.6493, -2.60054, 0, 0, 0.96363, -0.267238, 300, 255, 1, '', 0),
(73723, 1733, 0, 0, 0, 1, 1, -10848.7, -1375.08, 63.6493, -2.60054, 0, 0, 0.96363, -0.267238, 300, 255, 1, '', 0),
(73724, 1734, 0, 0, 0, 1, 1, -10848.7, -1375.08, 63.6493, -2.60054, 0, 0, 0.96363, -0.267238, 300, 255, 1, '', 0),
(73726, 1732, 0, 0, 0, 1, 1, -11044.9, -53.2851, 18.2922, -1.43117, 0, 0, 0.656059, -0.75471, 300, 255, 1, '', 0),
(73727, 1735, 0, 0, 0, 1, 1, -11044.9, -53.2851, 18.2922, -1.43117, 0, 0, 0.656059, -0.75471, 300, 255, 1, '', 0),
(73728, 1733, 0, 0, 0, 1, 1, -11044.9, -53.2851, 18.2922, -1.43117, 0, 0, 0.656059, -0.75471, 300, 255, 1, '', 0),
(73729, 1734, 0, 0, 0, 1, 1, -11044.9, -53.2851, 18.2922, -1.43117, 0, 0, 0.656059, -0.75471, 300, 255, 1, '', 0),
(73731, 1732, 0, 0, 0, 1, 1, -11100, -195.819, 28.6323, -1.41372, 0, 0, 0.649448, -0.760406, 300, 255, 1, '', 0),
(73732, 1735, 0, 0, 0, 1, 1, -11100, -195.819, 28.6323, -1.41372, 0, 0, 0.649448, -0.760406, 300, 255, 1, '', 0),
(73733, 1733, 0, 0, 0, 1, 1, -11100, -195.819, 28.6323, -1.41372, 0, 0, 0.649448, -0.760406, 300, 255, 1, '', 0),
(73734, 1734, 0, 0, 0, 1, 1, -11100, -195.819, 28.6323, -1.41372, 0, 0, 0.649448, -0.760406, 300, 255, 1, '', 0),
(73736, 1732, 0, 0, 0, 1, 1, -10436.5, -780.711, 58.56, -0.244346, 0, 0, 0.121869, -0.992546, 300, 255, 1, '', 0),
(73737, 1735, 0, 0, 0, 1, 1, -10436.5, -780.711, 58.56, -0.244346, 0, 0, 0.121869, -0.992546, 300, 255, 1, '', 0),
(73738, 1733, 0, 0, 0, 1, 1, -10436.5, -780.711, 58.56, -0.244346, 0, 0, 0.121869, -0.992546, 300, 255, 1, '', 0),
(73739, 1734, 0, 0, 0, 1, 1, -10436.5, -780.711, 58.56, -0.244346, 0, 0, 0.121869, -0.992546, 300, 255, 1, '', 0),
(73741, 1732, 0, 0, 0, 1, 1, -10290.9, -137.715, 41.327, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73742, 1735, 0, 0, 0, 1, 1, -10290.9, -137.715, 41.327, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73743, 1733, 0, 0, 0, 1, 1, -10290.9, -137.715, 41.327, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73744, 1734, 0, 0, 0, 1, 1, -10290.9, -137.715, 41.327, -2.11185, 0, 0, 0.870356, -0.492423, 300, 255, 1, '', 0),
(73746, 1732, 0, 0, 0, 1, 1, -10395.6, -748.918, 72.097, -1.09956, 0, 0, 0.522499, -0.85264, 300, 255, 1, '', 0),
(73747, 1735, 0, 0, 0, 1, 1, -10395.6, -748.918, 72.097, -1.09956, 0, 0, 0.522499, -0.85264, 300, 255, 1, '', 0),
(73748, 1733, 0, 0, 0, 1, 1, -10395.6, -748.918, 72.097, -1.09956, 0, 0, 0.522499, -0.85264, 300, 255, 1, '', 0),
(73749, 1734, 0, 0, 0, 1, 1, -10395.6, -748.918, 72.097, -1.09956, 0, 0, 0.522499, -0.85264, 300, 255, 1, '', 0),
(73751, 1732, 0, 0, 0, 1, 1, -10481.8, 41.4525, 42.5627, 1.98968, 0, 0, 0.838671, 0.544639, 300, 255, 1, '', 0),
(73752, 1735, 0, 0, 0, 1, 1, -10481.8, 41.4525, 42.5627, 1.98968, 0, 0, 0.838671, 0.544639, 300, 255, 1, '', 0),
(73753, 1733, 0, 0, 0, 1, 1, -10481.8, 41.4525, 42.5627, 1.98968, 0, 0, 0.838671, 0.544639, 300, 255, 1, '', 0),
(73754, 1734, 0, 0, 0, 1, 1, -10481.8, 41.4525, 42.5627, 1.98968, 0, 0, 0.838671, 0.544639, 300, 255, 1, '', 0),
(73761, 1732, 0, 0, 0, 1, 1, -10513.9, -74.5481, 45.2932, 3.07178, 0, 0, 0.999391, 0.034899, 300, 255, 1, '', 0),
(73762, 1735, 0, 0, 0, 1, 1, -10513.9, -74.5481, 45.2932, 3.07178, 0, 0, 0.999391, 0.034899, 300, 255, 1, '', 0),
(73763, 1733, 0, 0, 0, 1, 1, -10513.9, -74.5481, 45.2932, 3.07178, 0, 0, 0.999391, 0.034899, 300, 255, 1, '', 0),
(73764, 1734, 0, 0, 0, 1, 1, -10513.9, -74.5481, 45.2932, 3.07178, 0, 0, 0.999391, 0.034899, 300, 255, 1, '', 0),
(73766, 1732, 0, 0, 0, 1, 1, -10643.7, -985.37, 68.1491, -0.314159, 0, 0, 0.156434, -0.987688, 300, 255, 1, '', 0),
(73767, 1735, 0, 0, 0, 1, 1, -10643.7, -985.37, 68.1491, -0.314159, 0, 0, 0.156434, -0.987688, 300, 255, 1, '', 0),
(73768, 1733, 0, 0, 0, 1, 1, -10643.7, -985.37, 68.1491, -0.314159, 0, 0, 0.156434, -0.987688, 300, 255, 1, '', 0),
(73769, 1734, 0, 0, 0, 1, 1, -10643.7, -985.37, 68.1491, -0.314159, 0, 0, 0.156434, -0.987688, 300, 255, 1, '', 0),
(73771, 1732, 0, 0, 0, 1, 1, -11160.1, 290.516, 41.1961, 2.00713, 0, 0, 0.843391, 0.5373, 300, 255, 1, '', 0),
(73772, 1735, 0, 0, 0, 1, 1, -11160.1, 290.516, 41.1961, 2.00713, 0, 0, 0.843391, 0.5373, 300, 255, 1, '', 0),
(73773, 1733, 0, 0, 0, 1, 1, -11160.1, 290.516, 41.1961, 2.00713, 0, 0, 0.843391, 0.5373, 300, 255, 1, '', 0),
(73774, 1734, 0, 0, 0, 1, 1, -11160.1, 290.516, 41.1961, 2.00713, 0, 0, 0.843391, 0.5373, 300, 255, 1, '', 0),
(73776, 1732, 0, 0, 0, 1, 1, -11125, -253.782, 45.8497, -0.541052, 0, 0, 0.267238, -0.96363, 300, 255, 1, '', 0),
(73777, 1735, 0, 0, 0, 1, 1, -11125, -253.782, 45.8497, -0.541052, 0, 0, 0.267238, -0.96363, 300, 255, 1, '', 0),
(73778, 1733, 0, 0, 0, 1, 1, -11125, -253.782, 45.8497, -0.541052, 0, 0, 0.267238, -0.96363, 300, 255, 1, '', 0),
(73779, 1734, 0, 0, 0, 1, 1, -11125, -253.782, 45.8497, -0.541052, 0, 0, 0.267238, -0.96363, 300, 255, 1, '', 0),
(73781, 1732, 0, 0, 0, 1, 1, -11137.5, -843.614, 76.8614, -0.418879, 0, 0, 0.207912, -0.978148, 300, 255, 1, '', 0),
(73782, 1735, 0, 0, 0, 1, 1, -11137.5, -843.614, 76.8614, -0.418879, 0, 0, 0.207912, -0.978148, 300, 255, 1, '', 0),
(73783, 1733, 0, 0, 0, 1, 1, -11137.5, -843.614, 76.8614, -0.418879, 0, 0, 0.207912, -0.978148, 300, 255, 1, '', 0),
(73784, 1734, 0, 0, 0, 1, 1, -11137.5, -843.614, 76.8614, -0.418879, 0, 0, 0.207912, -0.978148, 300, 255, 1, '', 0),
(73786, 1732, 0, 0, 0, 1, 1, -10638.4, -1390.38, 60.8947, 2.05949, 0, 0, 0.857167, 0.515038, 300, 255, 1, '', 0),
(73787, 1735, 0, 0, 0, 1, 1, -10638.4, -1390.38, 60.8947, 2.05949, 0, 0, 0.857167, 0.515038, 300, 255, 1, '', 0),
(73788, 1733, 0, 0, 0, 1, 1, -10638.4, -1390.38, 60.8947, 2.05949, 0, 0, 0.857167, 0.515038, 300, 255, 1, '', 0),
(73789, 1734, 0, 0, 0, 1, 1, -10638.4, -1390.38, 60.8947, 2.05949, 0, 0, 0.857167, 0.515038, 300, 255, 1, '', 0),
(73791, 1732, 0, 0, 0, 1, 1, -10415.6, -1253.79, 52.5802, -2.79253, 0, 0, 0.984808, -0.173648, 300, 255, 1, '', 0),
(73792, 1735, 0, 0, 0, 1, 1, -10415.6, -1253.79, 52.5802, -2.79253, 0, 0, 0.984808, -0.173648, 300, 255, 1, '', 0),
(73793, 1733, 0, 0, 0, 1, 1, -10415.6, -1253.79, 52.5802, -2.79253, 0, 0, 0.984808, -0.173648, 300, 255, 1, '', 0),
(73794, 1734, 0, 0, 0, 1, 1, -10415.6, -1253.79, 52.5802, -2.79253, 0, 0, 0.984808, -0.173648, 300, 255, 1, '', 0),
(73796, 1732, 0, 0, 0, 1, 1, -11050, -1033.27, 72.4054, 0.244346, 0, 0, 0.121869, 0.992546, 300, 255, 1, '', 0),
(73797, 1735, 0, 0, 0, 1, 1, -11050, -1033.27, 72.4054, 0.244346, 0, 0, 0.121869, 0.992546, 300, 255, 1, '', 0),
(73798, 1733, 0, 0, 0, 1, 1, -11050, -1033.27, 72.4054, 0.244346, 0, 0, 0.121869, 0.992546, 300, 255, 1, '', 0),
(73799, 1734, 0, 0, 0, 1, 1, -11050, -1033.27, 72.4054, 0.244346, 0, 0, 0.121869, 0.992546, 300, 255, 1, '', 0),
(73801, 1732, 0, 0, 0, 1, 1, -10359, -790.396, 61.1401, 2.32129, 0, 0, 0.91706, 0.398749, 300, 255, 1, '', 0),
(73802, 1735, 0, 0, 0, 1, 1, -10359, -790.396, 61.1401, 2.32129, 0, 0, 0.91706, 0.398749, 300, 255, 1, '', 0),
(73803, 1733, 0, 0, 0, 1, 1, -10359, -790.396, 61.1401, 2.32129, 0, 0, 0.91706, 0.398749, 300, 255, 1, '', 0),
(73804, 1734, 0, 0, 0, 1, 1, -10359, -790.396, 61.1401, 2.32129, 0, 0, 0.91706, 0.398749, 300, 255, 1, '', 0),
(73806, 1732, 0, 0, 0, 1, 1, -10483.2, -999.957, 47.3942, 0.331613, 0, 0, 0.165048, 0.986286, 300, 255, 1, '', 0),
(73807, 1735, 0, 0, 0, 1, 1, -10483.2, -999.957, 47.3942, 0.331613, 0, 0, 0.165048, 0.986286, 300, 255, 1, '', 0),
(73808, 1733, 0, 0, 0, 1, 1, -10483.2, -999.957, 47.3942, 0.331613, 0, 0, 0.165048, 0.986286, 300, 255, 1, '', 0),
(73809, 1734, 0, 0, 0, 1, 1, -10483.2, -999.957, 47.3942, 0.331613, 0, 0, 0.165048, 0.986286, 300, 255, 1, '', 0),
(73811, 1732, 0, 0, 0, 1, 1, -11014.4, -1079.73, 51.1815, -2.96706, 0, 0, 0.996195, -0.087156, 300, 255, 1, '', 0),
(73812, 1735, 0, 0, 0, 1, 1, -11014.4, -1079.73, 51.1815, -2.96706, 0, 0, 0.996195, -0.087156, 300, 255, 1, '', 0),
(73813, 1733, 0, 0, 0, 1, 1, -11014.4, -1079.73, 51.1815, -2.96706, 0, 0, 0.996195, -0.087156, 300, 255, 1, '', 0),
(73814, 1734, 0, 0, 0, 1, 1, -11014.4, -1079.73, 51.1815, -2.96706, 0, 0, 0.996195, -0.087156, 300, 255, 1, '', 0),
(73816, 1732, 0, 0, 0, 1, 1, -10341.1, 83.216, 36.8964, -1.53589, 0, 0, 0.694658, -0.71934, 300, 255, 1, '', 0),
(73817, 1735, 0, 0, 0, 1, 1, -10341.1, 83.216, 36.8964, -1.53589, 0, 0, 0.694658, -0.71934, 300, 255, 1, '', 0),
(73818, 1733, 0, 0, 0, 1, 1, -10341.1, 83.216, 36.8964, -1.53589, 0, 0, 0.694658, -0.71934, 300, 255, 1, '', 0),
(73819, 1734, 0, 0, 0, 1, 1, -10341.1, 83.216, 36.8964, -1.53589, 0, 0, 0.694658, -0.71934, 300, 255, 1, '', 0),
(73821, 1732, 0, 0, 0, 1, 1, -10726.6, -965.106, 70.1185, 1.67552, 0, 0, 0.743145, 0.669131, 300, 255, 1, '', 0),
(73822, 1735, 0, 0, 0, 1, 1, -10726.6, -965.106, 70.1185, 1.67552, 0, 0, 0.743145, 0.669131, 300, 255, 1, '', 0),
(73823, 1733, 0, 0, 0, 1, 1, -10726.6, -965.106, 70.1185, 1.67552, 0, 0, 0.743145, 0.669131, 300, 255, 1, '', 0),
(73824, 1734, 0, 0, 0, 1, 1, -10726.6, -965.106, 70.1185, 1.67552, 0, 0, 0.743145, 0.669131, 300, 255, 1, '', 0),
(73826, 1732, 0, 0, 0, 1, 1, -11039.1, -420.595, 36.0661, -0.087267, 0, 0, 0.04362, -0.999048, 300, 255, 1, '', 0),
(73827, 1735, 0, 0, 0, 1, 1, -11039.1, -420.595, 36.0661, -0.087267, 0, 0, 0.04362, -0.999048, 300, 255, 1, '', 0),
(73828, 1733, 0, 0, 0, 1, 1, -11039.1, -420.595, 36.0661, -0.087267, 0, 0, 0.04362, -0.999048, 300, 255, 1, '', 0),
(73829, 1734, 0, 0, 0, 1, 1, -11039.1, -420.595, 36.0661, -0.087267, 0, 0, 0.04362, -0.999048, 300, 255, 1, '', 0),
(73831, 1732, 0, 0, 0, 1, 1, -10467.1, -948.977, 49.8306, -2.54818, 0, 0, 0.956305, -0.292372, 300, 255, 1, '', 0),
(73832, 1735, 0, 0, 0, 1, 1, -10467.1, -948.977, 49.8306, -2.54818, 0, 0, 0.956305, -0.292372, 300, 255, 1, '', 0),
(73833, 1733, 0, 0, 0, 1, 1, -10467.1, -948.977, 49.8306, -2.54818, 0, 0, 0.956305, -0.292372, 300, 255, 1, '', 0),
(73834, 1734, 0, 0, 0, 1, 1, -10467.1, -948.977, 49.8306, -2.54818, 0, 0, 0.956305, -0.292372, 300, 255, 1, '', 0),
(73836, 1732, 0, 0, 0, 1, 1, -11105.8, -1170.61, 42.2928, -0.349066, 0, 0, 0.173648, -0.984808, 300, 255, 1, '', 0),
(73837, 1735, 0, 0, 0, 1, 1, -11105.8, -1170.61, 42.2928, -0.349066, 0, 0, 0.173648, -0.984808, 300, 255, 1, '', 0),
(73838, 1733, 0, 0, 0, 1, 1, -11105.8, -1170.61, 42.2928, -0.349066, 0, 0, 0.173648, -0.984808, 300, 255, 1, '', 0),
(73839, 1734, 0, 0, 0, 1, 1, -11105.8, -1170.61, 42.2928, -0.349066, 0, 0, 0.173648, -0.984808, 300, 255, 1, '', 0),
(73841, 1732, 0, 0, 0, 1, 1, -10854.2, -533.286, 39.3664, -2.46091, 0, 0, 0.942641, -0.333807, 300, 255, 1, '', 0),
(73842, 1735, 0, 0, 0, 1, 1, -10854.2, -533.286, 39.3664, -2.46091, 0, 0, 0.942641, -0.333807, 300, 255, 1, '', 0),
(73843, 1733, 0, 0, 0, 1, 1, -10854.2, -533.286, 39.3664, -2.46091, 0, 0, 0.942641, -0.333807, 300, 255, 1, '', 0),
(73844, 1734, 0, 0, 0, 1, 1, -10854.2, -533.286, 39.3664, -2.46091, 0, 0, 0.942641, -0.333807, 300, 255, 1, '', 0),
(73846, 1732, 0, 0, 0, 1, 1, -10557.2, -760.492, 60.5833, -1.0472, 0, 0, 0.5, -0.866025, 300, 255, 1, '', 0),
(73847, 1735, 0, 0, 0, 1, 1, -10557.2, -760.492, 60.5833, -1.0472, 0, 0, 0.5, -0.866025, 300, 255, 1, '', 0),
(73848, 1733, 0, 0, 0, 1, 1, -10557.2, -760.492, 60.5833, -1.0472, 0, 0, 0.5, -0.866025, 300, 255, 1, '', 0),
(73849, 1734, 0, 0, 0, 1, 1, -10557.2, -760.492, 60.5833, -1.0472, 0, 0, 0.5, -0.866025, 300, 255, 1, '', 0),
(73851, 1732, 0, 0, 0, 1, 1, -10395.8, 113.076, 34.8874, 1.81514, 0, 0, 0.788011, 0.615662, 300, 255, 1, '', 0),
(73852, 1735, 0, 0, 0, 1, 1, -10395.8, 113.076, 34.8874, 1.81514, 0, 0, 0.788011, 0.615662, 300, 255, 1, '', 0),
(73853, 1733, 0, 0, 0, 1, 1, -10395.8, 113.076, 34.8874, 1.81514, 0, 0, 0.788011, 0.615662, 300, 255, 1, '', 0),
(73854, 1734, 0, 0, 0, 1, 1, -10395.8, 113.076, 34.8874, 1.81514, 0, 0, 0.788011, 0.615662, 300, 255, 1, '', 0),
(73856, 1732, 0, 0, 0, 1, 1, -10329.5, 134.72, 35.5812, 2.54818, 0, 0, 0.956305, 0.292372, 300, 255, 1, '', 0),
(73857, 1735, 0, 0, 0, 1, 1, -10329.5, 134.72, 35.5812, 2.54818, 0, 0, 0.956305, 0.292372, 300, 255, 1, '', 0),
(73858, 1733, 0, 0, 0, 1, 1, -10329.5, 134.72, 35.5812, 2.54818, 0, 0, 0.956305, 0.292372, 300, 255, 1, '', 0),
(73859, 1734, 0, 0, 0, 1, 1, -10329.5, 134.72, 35.5812, 2.54818, 0, 0, 0.956305, 0.292372, 300, 255, 1, '', 0),
(73861, 1732, 0, 0, 0, 1, 1, -10800.4, -424.663, 59.9856, -0.383972, 0, 0, 0.190809, -0.981627, 300, 255, 1, '', 0),
(73862, 1735, 0, 0, 0, 1, 1, -10800.4, -424.663, 59.9856, -0.383972, 0, 0, 0.190809, -0.981627, 300, 255, 1, '', 0),
(73863, 1733, 0, 0, 0, 1, 1, -10800.4, -424.663, 59.9856, -0.383972, 0, 0, 0.190809, -0.981627, 300, 255, 1, '', 0),
(73864, 1734, 0, 0, 0, 1, 1, -10800.4, -424.663, 59.9856, -0.383972, 0, 0, 0.190809, -0.981627, 300, 255, 1, '', 0),
(73866, 1732, 0, 0, 0, 1, 1, -10633.3, -1458.44, 91.035, -2.77507, 0, 0, 0.983255, -0.182235, 300, 255, 1, '', 0),
(73867, 1735, 0, 0, 0, 1, 1, -10633.3, -1458.44, 91.035, -2.77507, 0, 0, 0.983255, -0.182235, 300, 255, 1, '', 0),
(73868, 1733, 0, 0, 0, 1, 1, -10633.3, -1458.44, 91.035, -2.77507, 0, 0, 0.983255, -0.182235, 300, 255, 1, '', 0),
(73869, 1734, 0, 0, 0, 1, 1, -10633.3, -1458.44, 91.035, -2.77507, 0, 0, 0.983255, -0.182235, 300, 255, 1, '', 0),
(73876, 1732, 0, 0, 0, 1, 1, -11114.4, -1016.67, 80.772, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73877, 1735, 0, 0, 0, 1, 1, -11114.4, -1016.67, 80.772, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73878, 1733, 0, 0, 0, 1, 1, -11114.4, -1016.67, 80.772, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73879, 1734, 0, 0, 0, 1, 1, -11114.4, -1016.67, 80.772, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73881, 1732, 0, 0, 0, 1, 1, -10122.7, -342.507, 51.9179, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73882, 1735, 0, 0, 0, 1, 1, -10122.7, -342.507, 51.9179, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73883, 1733, 0, 0, 0, 1, 1, -10122.7, -342.507, 51.9179, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73884, 1734, 0, 0, 0, 1, 1, -10122.7, -342.507, 51.9179, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73886, 1732, 0, 0, 0, 1, 1, -11155.2, -1167.31, 87.6797, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73887, 1735, 0, 0, 0, 1, 1, -11155.2, -1167.31, 87.6797, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73888, 1733, 0, 0, 0, 1, 1, -11155.2, -1167.31, 87.6797, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73889, 1734, 0, 0, 0, 1, 1, -11155.2, -1167.31, 87.6797, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73891, 1732, 0, 0, 0, 1, 1, -11180.8, -135.495, 80.4793, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73892, 1735, 0, 0, 0, 1, 1, -11180.8, -135.495, 80.4793, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73893, 1733, 0, 0, 0, 1, 1, -11180.8, -135.495, 80.4793, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73894, 1734, 0, 0, 0, 1, 1, -11180.8, -135.495, 80.4793, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73896, 1732, 0, 0, 0, 1, 1, -10662.4, -894.27, 58.8167, 0.994837, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73897, 1735, 0, 0, 0, 1, 1, -10662.4, -894.27, 58.8167, 0.994837, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73898, 1733, 0, 0, 0, 1, 1, -10662.4, -894.27, 58.8167, 0.994837, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73899, 1734, 0, 0, 0, 1, 1, -10662.4, -894.27, 58.8167, 0.994837, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73901, 1732, 0, 0, 0, 1, 1, -10677, -912.576, 63.6344, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73902, 1735, 0, 0, 0, 1, 1, -10677, -912.576, 63.6344, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73903, 1733, 0, 0, 0, 1, 1, -10677, -912.576, 63.6344, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73904, 1734, 0, 0, 0, 1, 1, -10677, -912.576, 63.6344, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73906, 1732, 0, 0, 0, 1, 1, -11221.5, -886.7, 107.681, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73907, 1735, 0, 0, 0, 1, 1, -11221.5, -886.7, 107.681, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73908, 1733, 0, 0, 0, 1, 1, -11221.5, -886.7, 107.681, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73909, 1734, 0, 0, 0, 1, 1, -11221.5, -886.7, 107.681, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73911, 1732, 0, 0, 0, 1, 1, -11181.6, -153.58, 83.9906, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73912, 1735, 0, 0, 0, 1, 1, -11181.6, -153.58, 83.9906, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73913, 1733, 0, 0, 0, 1, 1, -11181.6, -153.58, 83.9906, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73914, 1734, 0, 0, 0, 1, 1, -11181.6, -153.58, 83.9906, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73916, 1732, 0, 0, 0, 1, 1, -11001.5, 8.73486, 30.7184, -0.907571, 0, 0, 0.438371, -0.898794, 300, 255, 1, '', 0),
(73917, 1735, 0, 0, 0, 1, 1, -11001.5, 8.73486, 30.7184, -0.907571, 0, 0, 0.438371, -0.898794, 300, 255, 1, '', 0),
(73918, 1733, 0, 0, 0, 1, 1, -11001.5, 8.73486, 30.7184, -0.907571, 0, 0, 0.438371, -0.898794, 300, 255, 1, '', 0),
(73919, 1734, 0, 0, 0, 1, 1, -11001.5, 8.73486, 30.7184, -0.907571, 0, 0, 0.438371, -0.898794, 300, 255, 1, '', 0),
(73921, 1732, 0, 0, 0, 1, 1, -11234.2, -878.234, 109.259, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73922, 1735, 0, 0, 0, 1, 1, -11234.2, -878.234, 109.259, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73923, 1733, 0, 0, 0, 1, 1, -11234.2, -878.234, 109.259, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73924, 1734, 0, 0, 0, 1, 1, -11234.2, -878.234, 109.259, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73926, 1732, 0, 0, 0, 1, 1, -10121.2, -553.419, 72.1722, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73927, 1735, 0, 0, 0, 1, 1, -10121.2, -553.419, 72.1722, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73928, 1733, 0, 0, 0, 1, 1, -10121.2, -553.419, 72.1722, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73929, 1734, 0, 0, 0, 1, 1, -10121.2, -553.419, 72.1722, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73931, 1732, 0, 0, 0, 1, 1, -11095.2, -72.7349, 14.6408, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73932, 1735, 0, 0, 0, 1, 1, -11095.2, -72.7349, 14.6408, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73933, 1733, 0, 0, 0, 1, 1, -11095.2, -72.7349, 14.6408, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73934, 1734, 0, 0, 0, 1, 1, -11095.2, -72.7349, 14.6408, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73936, 1732, 0, 0, 0, 1, 1, -11093.4, -30.2541, 28.7652, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73937, 1735, 0, 0, 0, 1, 1, -11093.4, -30.2541, 28.7652, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73938, 1733, 0, 0, 0, 1, 1, -11093.4, -30.2541, 28.7652, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73939, 1734, 0, 0, 0, 1, 1, -11093.4, -30.2541, 28.7652, 0, 0, 0, 0, 0, 300, 255, 1, '', 0),
(73946, 1732, 0, 0, 0, 1, 1, -11212.6, -174.63, 4.63508, -0.366519, 0, 0, 0.182236, -0.983255, 300, 255, 1, '', 0),
(73947, 1735, 0, 0, 0, 1, 1, -11212.6, -174.63, 4.63508, -0.366519, 0, 0, 0.182236, -0.983255, 300, 255, 1, '', 0),
(73948, 1733, 0, 0, 0, 1, 1, -11212.6, -174.63, 4.63508, -0.366519, 0, 0, 0.182236, -0.983255, 300, 255, 1, '', 0),
(73949, 1734, 0, 0, 0, 1, 1, -11212.6, -174.63, 4.63508, -0.366519, 0, 0, 0.182236, -0.983255, 300, 255, 1, '', 0),
(73951, 1732, 0, 0, 0, 1, 1, -10714.9, -1436.62, 63.2028, -1.90241, 0, 0, 0.814116, -0.580703, 300, 255, 1, '', 0),
(73952, 1735, 0, 0, 0, 1, 1, -10714.9, -1436.62, 63.2028, -1.90241, 0, 0, 0.814116, -0.580703, 300, 255, 1, '', 0),
(73953, 1733, 0, 0, 0, 1, 1, -10714.9, -1436.62, 63.2028, -1.90241, 0, 0, 0.814116, -0.580703, 300, 255, 1, '', 0),
(73954, 1734, 0, 0, 0, 1, 1, -10714.9, -1436.62, 63.2028, -1.90241, 0, 0, 0.814116, -0.580703, 300, 255, 1, '', 0),
(73956, 1732, 0, 0, 0, 1, 1, -11178.4, -153.236, 6.88822, -1.0821, 0, 0, 0.515038, -0.857167, 300, 255, 1, '', 0),
(73957, 1735, 0, 0, 0, 1, 1, -11178.4, -153.236, 6.88822, -1.0821, 0, 0, 0.515038, -0.857167, 300, 255, 1, '', 0),
(73958, 1733, 0, 0, 0, 1, 1, -11178.4, -153.236, 6.88822, -1.0821, 0, 0, 0.515038, -0.857167, 300, 255, 1, '', 0),
(73959, 1734, 0, 0, 0, 1, 1, -11178.4, -153.236, 6.88822, -1.0821, 0, 0, 0.515038, -0.857167, 300, 255, 1, '', 0),
(73961, 1732, 0, 0, 0, 1, 1, -10699.3, -187.865, 39.9624, -2.84488, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73962, 1735, 0, 0, 0, 1, 1, -10699.3, -187.865, 39.9624, -2.84488, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73963, 1733, 0, 0, 0, 1, 1, -10699.3, -187.865, 39.9624, -2.84488, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73964, 1734, 0, 0, 0, 1, 1, -10699.3, -187.865, 39.9624, -2.84488, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73966, 1732, 0, 0, 0, 1, 1, -10856.6, -1283.99, 62.4551, 0.296705, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73967, 1735, 0, 0, 0, 1, 1, -10856.6, -1283.99, 62.4551, 0.296705, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73968, 1733, 0, 0, 0, 1, 1, -10856.6, -1283.99, 62.4551, 0.296705, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73969, 1734, 0, 0, 0, 1, 1, -10856.6, -1283.99, 62.4551, 0.296705, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73971, 1732, 0, 0, 0, 1, 1, -10088, -450.424, 65.3588, 2.28638, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73972, 1735, 0, 0, 0, 1, 1, -10088, -450.424, 65.3588, 2.28638, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73973, 1733, 0, 0, 0, 1, 1, -10088, -450.424, 65.3588, 2.28638, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73974, 1734, 0, 0, 0, 1, 1, -10088, -450.424, 65.3588, 2.28638, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73976, 1732, 0, 0, 0, 1, 1, -10966.5, -183.177, 17.1386, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73977, 1735, 0, 0, 0, 1, 1, -10966.5, -183.177, 17.1386, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73978, 1733, 0, 0, 0, 1, 1, -10966.5, -183.177, 17.1386, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73979, 1734, 0, 0, 0, 1, 1, -10966.5, -183.177, 17.1386, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73981, 1732, 0, 0, 0, 1, 1, -11118.3, -578.917, 46.1832, -0.802851, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73982, 1735, 0, 0, 0, 1, 1, -11118.3, -578.917, 46.1832, -0.802851, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73983, 1733, 0, 0, 0, 1, 1, -11118.3, -578.917, 46.1832, -0.802851, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73984, 1734, 0, 0, 0, 1, 1, -11118.3, -578.917, 46.1832, -0.802851, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73986, 1732, 0, 0, 0, 1, 1, -11072.4, -1385.5, 72.7384, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73987, 1735, 0, 0, 0, 1, 1, -11072.4, -1385.5, 72.7384, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73988, 1733, 0, 0, 0, 1, 1, -11072.4, -1385.5, 72.7384, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73989, 1734, 0, 0, 0, 1, 1, -11072.4, -1385.5, 72.7384, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73991, 1732, 0, 0, 0, 1, 1, -11062.5, -729.195, 59.6658, -1.65806, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73992, 1735, 0, 0, 0, 1, 1, -11062.5, -729.195, 59.6658, -1.65806, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73993, 1733, 0, 0, 0, 1, 1, -11062.5, -729.195, 59.6658, -1.65806, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73994, 1734, 0, 0, 0, 1, 1, -11062.5, -729.195, 59.6658, -1.65806, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73996, 1732, 0, 0, 0, 1, 1, -10160.4, -288.859, 47.7796, -0.349065, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73997, 1735, 0, 0, 0, 1, 1, -10160.4, -288.859, 47.7796, -0.349065, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73998, 1733, 0, 0, 0, 1, 1, -10160.4, -288.859, 47.7796, -0.349065, 0, 0, 0, 1, 300, 255, 1, '', 0),
(73999, 1734, 0, 0, 0, 1, 1, -10160.4, -288.859, 47.7796, -0.349065, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74001, 1732, 0, 0, 0, 1, 1, -11166.5, 154.265, 35.092, -0.575957, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74002, 1735, 0, 0, 0, 1, 1, -11166.5, 154.265, 35.092, -0.575957, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74003, 1733, 0, 0, 0, 1, 1, -11166.5, 154.265, 35.092, -0.575957, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74004, 1734, 0, 0, 0, 1, 1, -11166.5, 154.265, 35.092, -0.575957, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74006, 1732, 0, 0, 0, 1, 1, -10575.1, -1008.32, 55.5346, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74007, 1735, 0, 0, 0, 1, 1, -10575.1, -1008.32, 55.5346, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74008, 1733, 0, 0, 0, 1, 1, -10575.1, -1008.32, 55.5346, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74009, 1734, 0, 0, 0, 1, 1, -10575.1, -1008.32, 55.5346, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74011, 1732, 0, 0, 0, 1, 1, -10766.7, -1437.56, 71.3134, -1.0821, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74012, 1735, 0, 0, 0, 1, 1, -10766.7, -1437.56, 71.3134, -1.0821, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74013, 1733, 0, 0, 0, 1, 1, -10766.7, -1437.56, 71.3134, -1.0821, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74014, 1734, 0, 0, 0, 1, 1, -10766.7, -1437.56, 71.3134, -1.0821, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74016, 1732, 0, 0, 0, 1, 1, -11109.5, 63.564, 41.8177, -2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74017, 1735, 0, 0, 0, 1, 1, -11109.5, 63.564, 41.8177, -2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74018, 1733, 0, 0, 0, 1, 1, -11109.5, 63.564, 41.8177, -2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74019, 1734, 0, 0, 0, 1, 1, -11109.5, 63.564, 41.8177, -2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74021, 1732, 0, 0, 0, 1, 1, -11154.1, 116.377, 39.0482, 2.51327, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74022, 1735, 0, 0, 0, 1, 1, -11154.1, 116.377, 39.0482, 2.51327, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74023, 1733, 0, 0, 0, 1, 1, -11154.1, 116.377, 39.0482, 2.51327, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74024, 1734, 0, 0, 0, 1, 1, -11154.1, 116.377, 39.0482, 2.51327, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74026, 1732, 0, 0, 0, 1, 1, -11029.2, -1266.67, 53.8279, -2.77507, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74027, 1735, 0, 0, 0, 1, 1, -11029.2, -1266.67, 53.8279, -2.77507, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74028, 1733, 0, 0, 0, 1, 1, -11029.2, -1266.67, 53.8279, -2.77507, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74029, 1734, 0, 0, 0, 1, 1, -11029.2, -1266.67, 53.8279, -2.77507, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74036, 1732, 0, 0, 0, 1, 1, -11091.6, -257.724, 32.2351, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74037, 1735, 0, 0, 0, 1, 1, -11091.6, -257.724, 32.2351, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74038, 1733, 0, 0, 0, 1, 1, -11091.6, -257.724, 32.2351, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74039, 1734, 0, 0, 0, 1, 1, -11091.6, -257.724, 32.2351, 2.56563, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74041, 1732, 0, 0, 0, 1, 1, -11099.4, -696.347, 54.7799, 1.58825, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74042, 1735, 0, 0, 0, 1, 1, -11099.4, -696.347, 54.7799, 1.58825, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74043, 1733, 0, 0, 0, 1, 1, -11099.4, -696.347, 54.7799, 1.58825, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74044, 1734, 0, 0, 0, 1, 1, -11099.4, -696.347, 54.7799, 1.58825, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74056, 1732, 0, 0, 0, 1, 1, -10563.4, -1486.63, 95.4168, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74057, 1735, 0, 0, 0, 1, 1, -10563.4, -1486.63, 95.4168, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74058, 1733, 0, 0, 0, 1, 1, -10563.4, -1486.63, 95.4168, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74059, 1734, 0, 0, 0, 1, 1, -10563.4, -1486.63, 95.4168, 0.034906, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74061, 1732, 0, 0, 0, 1, 1, -11183.7, -135.158, 8.11372, 2.09439, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74062, 1735, 0, 0, 0, 1, 1, -11183.7, -135.158, 8.11372, 2.09439, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74063, 1733, 0, 0, 0, 1, 1, -11183.7, -135.158, 8.11372, 2.09439, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74064, 1734, 0, 0, 0, 1, 1, -11183.7, -135.158, 8.11372, 2.09439, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74066, 1732, 0, 0, 0, 1, 1, -10237.8, -1291.58, 46.9721, -1.22173, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74067, 1735, 0, 0, 0, 1, 1, -10237.8, -1291.58, 46.9721, -1.22173, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74068, 1733, 0, 0, 0, 1, 1, -10237.8, -1291.58, 46.9721, -1.22173, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74069, 1734, 0, 0, 0, 1, 1, -10237.8, -1291.58, 46.9721, -1.22173, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74071, 1732, 0, 0, 0, 1, 1, -10678.7, -908.818, 68.4815, 2.65289, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74072, 1735, 0, 0, 0, 1, 1, -10678.7, -908.818, 68.4815, 2.65289, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74073, 1733, 0, 0, 0, 1, 1, -10678.7, -908.818, 68.4815, 2.65289, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74074, 1734, 0, 0, 0, 1, 1, -10678.7, -908.818, 68.4815, 2.65289, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74086, 1732, 0, 0, 0, 1, 1, -10559.6, -724.451, 76.4627, -0.785397, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74087, 1735, 0, 0, 0, 1, 1, -10559.6, -724.451, 76.4627, -0.785397, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74088, 1733, 0, 0, 0, 1, 1, -10559.6, -724.451, 76.4627, -0.785397, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74089, 1734, 0, 0, 0, 1, 1, -10559.6, -724.451, 76.4627, -0.785397, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74096, 1732, 0, 0, 0, 1, 1, -10301.6, -1467.02, 90.2184, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74097, 1735, 0, 0, 0, 1, 1, -10301.6, -1467.02, 90.2184, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74098, 1733, 0, 0, 0, 1, 1, -10301.6, -1467.02, 90.2184, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74099, 1734, 0, 0, 0, 1, 1, -10301.6, -1467.02, 90.2184, -0.366518, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74101, 1732, 0, 0, 0, 1, 1, -11016.6, -1180.58, 46.4404, 2.93214, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74102, 1735, 0, 0, 0, 1, 1, -11016.6, -1180.58, 46.4404, 2.93214, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74103, 1733, 0, 0, 0, 1, 1, -11016.6, -1180.58, 46.4404, 2.93214, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74104, 1734, 0, 0, 0, 1, 1, -11016.6, -1180.58, 46.4404, 2.93214, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74106, 1732, 0, 0, 0, 1, 1, -11162.2, -182.4, 12.8593, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74107, 1735, 0, 0, 0, 1, 1, -11162.2, -182.4, 12.8593, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74108, 1733, 0, 0, 0, 1, 1, -11162.2, -182.4, 12.8593, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74109, 1734, 0, 0, 0, 1, 1, -11162.2, -182.4, 12.8593, 1.48353, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74111, 1732, 0, 0, 0, 1, 1, -11137.3, -166.128, 11.467, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74112, 1735, 0, 0, 0, 1, 1, -11137.3, -166.128, 11.467, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74113, 1733, 0, 0, 0, 1, 1, -11137.3, -166.128, 11.467, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74114, 1734, 0, 0, 0, 1, 1, -11137.3, -166.128, 11.467, 2.96704, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74116, 1732, 0, 0, 0, 1, 1, -11099.9, -159.898, 13.3211, 2.14675, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74117, 1735, 0, 0, 0, 1, 1, -11099.9, -159.898, 13.3211, 2.14675, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74118, 1733, 0, 0, 0, 1, 1, -11099.9, -159.898, 13.3211, 2.14675, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74119, 1734, 0, 0, 0, 1, 1, -11099.9, -159.898, 13.3211, 2.14675, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74121, 1732, 0, 0, 0, 1, 1, -11096.3, -135.736, 39.7418, 1.46608, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74122, 1735, 0, 0, 0, 1, 1, -11096.3, -135.736, 39.7418, 1.46608, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74123, 1733, 0, 0, 0, 1, 1, -11096.3, -135.736, 39.7418, 1.46608, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74124, 1734, 0, 0, 0, 1, 1, -11096.3, -135.736, 39.7418, 1.46608, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74131, 1732, 0, 0, 0, 1, 1, -10997.8, -542.823, 34.96, -0.95993, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74132, 1735, 0, 0, 0, 1, 1, -10997.8, -542.823, 34.96, -0.95993, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74133, 1733, 0, 0, 0, 1, 1, -10997.8, -542.823, 34.96, -0.95993, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74134, 1734, 0, 0, 0, 1, 1, -10997.8, -542.823, 34.96, -0.95993, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74146, 1732, 0, 0, 0, 1, 1, -10716.4, -587.625, 64.6095, 2.89724, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74147, 1735, 0, 0, 0, 1, 1, -10716.4, -587.625, 64.6095, 2.89724, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74148, 1733, 0, 0, 0, 1, 1, -10716.4, -587.625, 64.6095, 2.89724, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74149, 1734, 0, 0, 0, 1, 1, -10716.4, -587.625, 64.6095, 2.89724, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74156, 1732, 0, 0, 0, 1, 1, -10446.1, -1312.74, 63.4724, -0.418879, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74157, 1735, 0, 0, 0, 1, 1, -10446.1, -1312.74, 63.4724, -0.418879, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74158, 1733, 0, 0, 0, 1, 1, -10446.1, -1312.74, 63.4724, -0.418879, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74159, 1734, 0, 0, 0, 1, 1, -10446.1, -1312.74, 63.4724, -0.418879, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74161, 1732, 0, 0, 0, 1, 1, -11246, -173.352, 5.11785, 0.383971, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74162, 1735, 0, 0, 0, 1, 1, -11246, -173.352, 5.11785, 0.383971, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74163, 1733, 0, 0, 0, 1, 1, -11246, -173.352, 5.11785, 0.383971, 0, 0, 0, 1, 300, 255, 1, '', 0),
(74164, 1734, 0, 0, 0, 1, 1, -11246, -173.352, 5.11785, 0.383971, 0, 0, 0, 1, 300, 255, 1, '', 0);

INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES 
(73601, 4400, 0, 'Spawn Point 1 - Tin'),
(73602, 4400, 0, 'Spawn Point 1 - Iron'),
(73603, 4400, 10, 'Spawn Point 1 - Silver'),
(73604, 4400, 10, 'Spawn Point 1 - Gold'),
(73606, 4401, 0, 'Spawn Point 2 - Tin'),
(73607, 4401, 0, 'Spawn Point 2 - Iron'),
(73608, 4401, 10, 'Spawn Point 2 - Silver'),
(73609, 4401, 10, 'Spawn Point 2 - Gold'),
(73611, 4402, 0, 'Spawn Point 3 - Tin'),
(73612, 4402, 0, 'Spawn Point 3 - Iron'),
(73613, 4402, 10, 'Spawn Point 3 - Silver'),
(73614, 4402, 10, 'Spawn Point 3 - Gold'),
(73616, 4403, 0, 'Spawn Point 4 - Tin'),
(73617, 4403, 0, 'Spawn Point 4 - Iron'),
(73618, 4403, 10, 'Spawn Point 4 - Silver'),
(73619, 4403, 10, 'Spawn Point 4 - Gold'),
(73621, 4404, 0, 'Spawn Point 5 - Tin'),
(73622, 4404, 0, 'Spawn Point 5 - Iron'),
(73623, 4404, 10, 'Spawn Point 5 - Silver'),
(73624, 4404, 10, 'Spawn Point 5 - Gold'),
(73626, 4405, 0, 'Spawn Point 6 - Tin'),
(73627, 4405, 0, 'Spawn Point 6 - Iron'),
(73628, 4405, 10, 'Spawn Point 6 - Silver'),
(73629, 4405, 10, 'Spawn Point 6 - Gold'),
(73631, 4406, 0, 'Spawn Point 7 - Tin'),
(73632, 4406, 0, 'Spawn Point 7 - Iron'),
(73633, 4406, 10, 'Spawn Point 7 - Silver'),
(73634, 4406, 10, 'Spawn Point 7 - Gold'),
(73636, 4407, 0, 'Spawn Point 8 - Tin'),
(73637, 4407, 0, 'Spawn Point 8 - Iron'),
(73638, 4407, 10, 'Spawn Point 8 - Silver'),
(73639, 4407, 10, 'Spawn Point 8 - Gold'),
(73641, 4408, 0, 'Spawn Point 9 - Tin'),
(73642, 4408, 0, 'Spawn Point 9 - Iron'),
(73643, 4408, 10, 'Spawn Point 9 - Silver'),
(73644, 4408, 10, 'Spawn Point 9 - Gold'),
(73646, 4409, 0, 'Spawn Point 10 - Tin'),
(73647, 4409, 0, 'Spawn Point 10 - Iron'),
(73648, 4409, 10, 'Spawn Point 10 - Silver'),
(73649, 4409, 10, 'Spawn Point 10 - Gold'),
(73656, 4411, 0, 'Spawn Point 12 - Tin'),
(73657, 4411, 0, 'Spawn Point 12 - Iron'),
(73658, 4411, 10, 'Spawn Point 12 - Silver'),
(73659, 4411, 10, 'Spawn Point 12 - Gold'),
(73661, 4412, 0, 'Spawn Point 13 - Tin'),
(73662, 4412, 0, 'Spawn Point 13 - Iron'),
(73663, 4412, 10, 'Spawn Point 13 - Silver'),
(73664, 4412, 10, 'Spawn Point 13 - Gold'),
(73666, 4413, 0, 'Spawn Point 14 - Tin'),
(73667, 4413, 0, 'Spawn Point 14 - Iron'),
(73668, 4413, 10, 'Spawn Point 14 - Silver'),
(73669, 4413, 10, 'Spawn Point 14 - Gold'),
(73676, 4415, 0, 'Spawn Point 16 - Tin'),
(73677, 4415, 0, 'Spawn Point 16 - Iron'),
(73678, 4415, 10, 'Spawn Point 16 - Silver'),
(73679, 4415, 10, 'Spawn Point 16 - Gold'),
(73681, 4416, 0, 'Spawn Point 17 - Tin'),
(73682, 4416, 0, 'Spawn Point 17 - Iron'),
(73683, 4416, 10, 'Spawn Point 17 - Silver'),
(73684, 4416, 10, 'Spawn Point 17 - Gold'),
(73686, 4417, 0, 'Spawn Point 18 - Tin'),
(73687, 4417, 0, 'Spawn Point 18 - Iron'),
(73688, 4417, 10, 'Spawn Point 18 - Silver'),
(73689, 4417, 10, 'Spawn Point 18 - Gold'),
(73691, 4418, 0, 'Spawn Point 19 - Tin'),
(73692, 4418, 0, 'Spawn Point 19 - Iron'),
(73693, 4418, 10, 'Spawn Point 19 - Silver'),
(73694, 4418, 10, 'Spawn Point 19 - Gold'),
(73696, 4419, 0, 'Spawn Point 20 - Tin'),
(73697, 4419, 0, 'Spawn Point 20 - Iron'),
(73698, 4419, 10, 'Spawn Point 20 - Silver'),
(73699, 4419, 10, 'Spawn Point 20 - Gold'),
(73701, 4420, 0, 'Spawn Point 21 - Tin'),
(73702, 4420, 0, 'Spawn Point 21 - Iron'),
(73703, 4420, 10, 'Spawn Point 21 - Silver'),
(73704, 4420, 10, 'Spawn Point 21 - Gold'),
(73706, 4421, 0, 'Spawn Point 22 - Tin'),
(73707, 4421, 0, 'Spawn Point 22 - Iron'),
(73708, 4421, 10, 'Spawn Point 22 - Silver'),
(73709, 4421, 10, 'Spawn Point 22 - Gold'),
(73711, 4422, 0, 'Spawn Point 23 - Tin'),
(73712, 4422, 0, 'Spawn Point 23 - Iron'),
(73713, 4422, 10, 'Spawn Point 23 - Silver'),
(73714, 4422, 10, 'Spawn Point 23 - Gold'),
(73716, 4423, 0, 'Spawn Point 24 - Tin'),
(73717, 4423, 0, 'Spawn Point 24 - Iron'),
(73718, 4423, 10, 'Spawn Point 24 - Silver'),
(73719, 4423, 10, 'Spawn Point 24 - Gold'),
(73721, 4424, 0, 'Spawn Point 25 - Tin'),
(73722, 4424, 0, 'Spawn Point 25 - Iron'),
(73723, 4424, 10, 'Spawn Point 25 - Silver'),
(73724, 4424, 10, 'Spawn Point 25 - Gold'),
(73726, 4425, 0, 'Spawn Point 26 - Tin'),
(73727, 4425, 0, 'Spawn Point 26 - Iron'),
(73728, 4425, 10, 'Spawn Point 26 - Silver'),
(73729, 4425, 10, 'Spawn Point 26 - Gold'),
(73731, 4426, 0, 'Spawn Point 27 - Tin'),
(73732, 4426, 0, 'Spawn Point 27 - Iron'),
(73733, 4426, 10, 'Spawn Point 27 - Silver'),
(73734, 4426, 10, 'Spawn Point 27 - Gold'),
(73736, 4427, 0, 'Spawn Point 28 - Tin'),
(73737, 4427, 0, 'Spawn Point 28 - Iron'),
(73738, 4427, 10, 'Spawn Point 28 - Silver'),
(73739, 4427, 10, 'Spawn Point 28 - Gold'),
(73741, 4428, 0, 'Spawn Point 29 - Tin'),
(73742, 4428, 0, 'Spawn Point 29 - Iron'),
(73743, 4428, 10, 'Spawn Point 29 - Silver'),
(73744, 4428, 10, 'Spawn Point 29 - Gold'),
(73746, 4429, 0, 'Spawn Point 30 - Tin'),
(73747, 4429, 0, 'Spawn Point 30 - Iron'),
(73748, 4429, 10, 'Spawn Point 30 - Silver'),
(73749, 4429, 10, 'Spawn Point 30 - Gold'),
(73751, 4430, 0, 'Spawn Point 31 - Tin'),
(73752, 4430, 0, 'Spawn Point 31 - Iron'),
(73753, 4430, 10, 'Spawn Point 31 - Silver'),
(73754, 4430, 10, 'Spawn Point 31 - Gold'),
(73761, 4432, 0, 'Spawn Point 33 - Tin'),
(73762, 4432, 0, 'Spawn Point 33 - Iron'),
(73763, 4432, 10, 'Spawn Point 33 - Silver'),
(73764, 4432, 10, 'Spawn Point 33 - Gold'),
(73766, 4433, 0, 'Spawn Point 34 - Tin'),
(73767, 4433, 0, 'Spawn Point 34 - Iron'),
(73768, 4433, 10, 'Spawn Point 34 - Silver'),
(73769, 4433, 10, 'Spawn Point 34 - Gold'),
(73771, 4434, 0, 'Spawn Point 35 - Tin'),
(73772, 4434, 0, 'Spawn Point 35 - Iron'),
(73773, 4434, 10, 'Spawn Point 35 - Silver'),
(73774, 4434, 10, 'Spawn Point 35 - Gold'),
(73776, 4435, 0, 'Spawn Point 36 - Tin'),
(73777, 4435, 0, 'Spawn Point 36 - Iron'),
(73778, 4435, 10, 'Spawn Point 36 - Silver'),
(73779, 4435, 10, 'Spawn Point 36 - Gold'),
(73781, 4436, 0, 'Spawn Point 37 - Tin'),
(73782, 4436, 0, 'Spawn Point 37 - Iron'),
(73783, 4436, 10, 'Spawn Point 37 - Silver'),
(73784, 4436, 10, 'Spawn Point 37 - Gold'),
(73786, 4437, 0, 'Spawn Point 38 - Tin'),
(73787, 4437, 0, 'Spawn Point 38 - Iron'),
(73788, 4437, 10, 'Spawn Point 38 - Silver'),
(73789, 4437, 10, 'Spawn Point 38 - Gold'),
(73791, 4438, 0, 'Spawn Point 39 - Tin'),
(73792, 4438, 0, 'Spawn Point 39 - Iron'),
(73793, 4438, 10, 'Spawn Point 39 - Silver'),
(73794, 4438, 10, 'Spawn Point 39 - Gold'),
(73796, 4439, 0, 'Spawn Point 40 - Tin'),
(73797, 4439, 0, 'Spawn Point 40 - Iron'),
(73798, 4439, 10, 'Spawn Point 40 - Silver'),
(73799, 4439, 10, 'Spawn Point 40 - Gold'),
(73801, 4440, 0, 'Spawn Point 41 - Tin'),
(73802, 4440, 0, 'Spawn Point 41 - Iron'),
(73803, 4440, 10, 'Spawn Point 41 - Silver'),
(73804, 4440, 10, 'Spawn Point 41 - Gold'),
(73806, 4441, 0, 'Spawn Point 42 - Tin'),
(73807, 4441, 0, 'Spawn Point 42 - Iron'),
(73808, 4441, 10, 'Spawn Point 42 - Silver'),
(73809, 4441, 10, 'Spawn Point 42 - Gold'),
(73811, 4442, 0, 'Spawn Point 43 - Tin'),
(73812, 4442, 0, 'Spawn Point 43 - Iron'),
(73813, 4442, 10, 'Spawn Point 43 - Silver'),
(73814, 4442, 10, 'Spawn Point 43 - Gold'),
(73816, 4443, 0, 'Spawn Point 44 - Tin'),
(73817, 4443, 0, 'Spawn Point 44 - Iron'),
(73818, 4443, 10, 'Spawn Point 44 - Silver'),
(73819, 4443, 10, 'Spawn Point 44 - Gold'),
(73821, 4444, 0, 'Spawn Point 45 - Tin'),
(73822, 4444, 0, 'Spawn Point 45 - Iron'),
(73823, 4444, 10, 'Spawn Point 45 - Silver'),
(73824, 4444, 10, 'Spawn Point 45 - Gold'),
(73826, 4445, 0, 'Spawn Point 46 - Tin'),
(73827, 4445, 0, 'Spawn Point 46 - Iron'),
(73828, 4445, 10, 'Spawn Point 46 - Silver'),
(73829, 4445, 10, 'Spawn Point 46 - Gold'),
(73831, 4446, 0, 'Spawn Point 47 - Tin'),
(73832, 4446, 0, 'Spawn Point 47 - Iron'),
(73833, 4446, 10, 'Spawn Point 47 - Silver'),
(73834, 4446, 10, 'Spawn Point 47 - Gold'),
(73836, 4447, 0, 'Spawn Point 48 - Tin'),
(73837, 4447, 0, 'Spawn Point 48 - Iron'),
(73838, 4447, 10, 'Spawn Point 48 - Silver'),
(73839, 4447, 10, 'Spawn Point 48 - Gold'),
(73841, 4448, 0, 'Spawn Point 49 - Tin'),
(73842, 4448, 0, 'Spawn Point 49 - Iron'),
(73843, 4448, 10, 'Spawn Point 49 - Silver'),
(73844, 4448, 10, 'Spawn Point 49 - Gold'),
(73846, 4449, 0, 'Spawn Point 50 - Tin'),
(73847, 4449, 0, 'Spawn Point 50 - Iron'),
(73848, 4449, 10, 'Spawn Point 50 - Silver'),
(73849, 4449, 10, 'Spawn Point 50 - Gold'),
(73851, 4450, 0, 'Spawn Point 51 - Tin'),
(73852, 4450, 0, 'Spawn Point 51 - Iron'),
(73853, 4450, 10, 'Spawn Point 51 - Silver'),
(73854, 4450, 10, 'Spawn Point 51 - Gold'),
(73856, 4451, 0, 'Spawn Point 52 - Tin'),
(73857, 4451, 0, 'Spawn Point 52 - Iron'),
(73858, 4451, 10, 'Spawn Point 52 - Silver'),
(73859, 4451, 10, 'Spawn Point 52 - Gold'),
(73861, 4452, 0, 'Spawn Point 53 - Tin'),
(73862, 4452, 0, 'Spawn Point 53 - Iron'),
(73863, 4452, 10, 'Spawn Point 53 - Silver'),
(73864, 4452, 10, 'Spawn Point 53 - Gold'),
(73866, 4453, 0, 'Spawn Point 54 - Tin'),
(73867, 4453, 0, 'Spawn Point 54 - Iron'),
(73868, 4453, 10, 'Spawn Point 54 - Silver'),
(73869, 4453, 10, 'Spawn Point 54 - Gold'),
(73876, 4455, 0, 'Spawn Point 56 - Tin'),
(73877, 4455, 0, 'Spawn Point 56 - Iron'),
(73878, 4455, 10, 'Spawn Point 56 - Silver'),
(73879, 4455, 10, 'Spawn Point 56 - Gold'),
(73881, 4456, 0, 'Spawn Point 57 - Tin'),
(73882, 4456, 0, 'Spawn Point 57 - Iron'),
(73883, 4456, 10, 'Spawn Point 57 - Silver'),
(73884, 4456, 10, 'Spawn Point 57 - Gold'),
(73886, 4457, 0, 'Spawn Point 58 - Tin'),
(73887, 4457, 0, 'Spawn Point 58 - Iron'),
(73888, 4457, 10, 'Spawn Point 58 - Silver'),
(73889, 4457, 10, 'Spawn Point 58 - Gold'),
(73891, 4458, 0, 'Spawn Point 59 - Tin'),
(73892, 4458, 0, 'Spawn Point 59 - Iron'),
(73893, 4458, 10, 'Spawn Point 59 - Silver'),
(73894, 4458, 10, 'Spawn Point 59 - Gold'),
(73896, 4459, 0, 'Spawn Point 60 - Tin'),
(73897, 4459, 0, 'Spawn Point 60 - Iron'),
(73898, 4459, 10, 'Spawn Point 60 - Silver'),
(73899, 4459, 10, 'Spawn Point 60 - Gold'),
(73901, 4460, 0, 'Spawn Point 61 - Tin'),
(73902, 4460, 0, 'Spawn Point 61 - Iron'),
(73903, 4460, 10, 'Spawn Point 61 - Silver'),
(73904, 4460, 10, 'Spawn Point 61 - Gold'),
(73906, 4461, 0, 'Spawn Point 62 - Tin'),
(73907, 4461, 0, 'Spawn Point 62 - Iron'),
(73908, 4461, 10, 'Spawn Point 62 - Silver'),
(73909, 4461, 10, 'Spawn Point 62 - Gold'),
(73911, 4462, 0, 'Spawn Point 63 - Tin'),
(73912, 4462, 0, 'Spawn Point 63 - Iron'),
(73913, 4462, 10, 'Spawn Point 63 - Silver'),
(73914, 4462, 10, 'Spawn Point 63 - Gold'),
(73916, 4463, 0, 'Spawn Point 64 - Tin'),
(73917, 4463, 0, 'Spawn Point 64 - Iron'),
(73918, 4463, 10, 'Spawn Point 64 - Silver'),
(73919, 4463, 10, 'Spawn Point 64 - Gold'),
(73921, 4464, 0, 'Spawn Point 65 - Tin'),
(73922, 4464, 0, 'Spawn Point 65 - Iron'),
(73923, 4464, 10, 'Spawn Point 65 - Silver'),
(73924, 4464, 10, 'Spawn Point 65 - Gold'),
(73926, 4465, 0, 'Spawn Point 66 - Tin'),
(73927, 4465, 0, 'Spawn Point 66 - Iron'),
(73928, 4465, 10, 'Spawn Point 66 - Silver'),
(73929, 4465, 10, 'Spawn Point 66 - Gold'),
(73931, 4466, 0, 'Spawn Point 67 - Tin'),
(73932, 4466, 0, 'Spawn Point 67 - Iron'),
(73933, 4466, 10, 'Spawn Point 67 - Silver'),
(73934, 4466, 10, 'Spawn Point 67 - Gold'),
(73936, 4467, 0, 'Spawn Point 68 - Tin'),
(73937, 4467, 0, 'Spawn Point 68 - Iron'),
(73938, 4467, 10, 'Spawn Point 68 - Silver'),
(73939, 4467, 10, 'Spawn Point 68 - Gold'),
(73946, 4469, 0, 'Spawn Point 70 - Tin'),
(73947, 4469, 0, 'Spawn Point 70 - Iron'),
(73948, 4469, 10, 'Spawn Point 70 - Silver'),
(73949, 4469, 10, 'Spawn Point 70 - Gold'),
(73951, 4470, 0, 'Spawn Point 71 - Tin'),
(73952, 4470, 0, 'Spawn Point 71 - Iron'),
(73953, 4470, 10, 'Spawn Point 71 - Silver'),
(73954, 4470, 10, 'Spawn Point 71 - Gold'),
(73956, 4471, 0, 'Spawn Point 72 - Tin'),
(73957, 4471, 0, 'Spawn Point 72 - Iron'),
(73958, 4471, 10, 'Spawn Point 72 - Silver'),
(73959, 4471, 10, 'Spawn Point 72 - Gold'),
(73961, 4472, 0, 'Spawn Point 73 - Tin'),
(73962, 4472, 0, 'Spawn Point 73 - Iron'),
(73963, 4472, 10, 'Spawn Point 73 - Silver'),
(73964, 4472, 10, 'Spawn Point 73 - Gold'),
(73966, 4473, 0, 'Spawn Point 74 - Tin'),
(73967, 4473, 0, 'Spawn Point 74 - Iron'),
(73968, 4473, 10, 'Spawn Point 74 - Silver'),
(73969, 4473, 10, 'Spawn Point 74 - Gold'),
(73971, 4474, 0, 'Spawn Point 75 - Tin'),
(73972, 4474, 0, 'Spawn Point 75 - Iron'),
(73973, 4474, 10, 'Spawn Point 75 - Silver'),
(73974, 4474, 10, 'Spawn Point 75 - Gold'),
(73976, 4475, 0, 'Spawn Point 76 - Tin'),
(73977, 4475, 0, 'Spawn Point 76 - Iron'),
(73978, 4475, 10, 'Spawn Point 76 - Silver'),
(73979, 4475, 10, 'Spawn Point 76 - Gold'),
(73981, 4476, 0, 'Spawn Point 77 - Tin'),
(73982, 4476, 0, 'Spawn Point 77 - Iron'),
(73983, 4476, 10, 'Spawn Point 77 - Silver'),
(73984, 4476, 10, 'Spawn Point 77 - Gold'),
(73986, 4477, 0, 'Spawn Point 78 - Tin'),
(73987, 4477, 0, 'Spawn Point 78 - Iron'),
(73988, 4477, 10, 'Spawn Point 78 - Silver'),
(73989, 4477, 10, 'Spawn Point 78 - Gold'),
(73991, 4478, 0, 'Spawn Point 79 - Tin'),
(73992, 4478, 0, 'Spawn Point 79 - Iron'),
(73993, 4478, 10, 'Spawn Point 79 - Silver'),
(73994, 4478, 10, 'Spawn Point 79 - Gold'),
(73996, 4479, 0, 'Spawn Point 80 - Tin'),
(73997, 4479, 0, 'Spawn Point 80 - Iron'),
(73998, 4479, 10, 'Spawn Point 80 - Silver'),
(73999, 4479, 10, 'Spawn Point 80 - Gold'),
(74001, 4480, 0, 'Spawn Point 81 - Tin'),
(74002, 4480, 0, 'Spawn Point 81 - Iron'),
(74003, 4480, 10, 'Spawn Point 81 - Silver'),
(74004, 4480, 10, 'Spawn Point 81 - Gold'),
(74006, 4481, 0, 'Spawn Point 82 - Tin'),
(74007, 4481, 0, 'Spawn Point 82 - Iron'),
(74008, 4481, 10, 'Spawn Point 82 - Silver'),
(74009, 4481, 10, 'Spawn Point 82 - Gold'),
(74011, 4482, 0, 'Spawn Point 83 - Tin'),
(74012, 4482, 0, 'Spawn Point 83 - Iron'),
(74013, 4482, 10, 'Spawn Point 83 - Silver'),
(74014, 4482, 10, 'Spawn Point 83 - Gold'),
(74016, 4483, 0, 'Spawn Point 84 - Tin'),
(74017, 4483, 0, 'Spawn Point 84 - Iron'),
(74018, 4483, 10, 'Spawn Point 84 - Silver'),
(74019, 4483, 10, 'Spawn Point 84 - Gold'),
(74021, 4484, 0, 'Spawn Point 85 - Tin'),
(74022, 4484, 0, 'Spawn Point 85 - Iron'),
(74023, 4484, 10, 'Spawn Point 85 - Silver'),
(74024, 4484, 10, 'Spawn Point 85 - Gold'),
(74026, 4485, 0, 'Spawn Point 86 - Tin'),
(74027, 4485, 0, 'Spawn Point 86 - Iron'),
(74028, 4485, 10, 'Spawn Point 86 - Silver'),
(74029, 4485, 10, 'Spawn Point 86 - Gold'),
(74036, 4487, 0, 'Spawn Point 88 - Tin'),
(74037, 4487, 0, 'Spawn Point 88 - Iron'),
(74038, 4487, 10, 'Spawn Point 88 - Silver'),
(74039, 4487, 10, 'Spawn Point 88 - Gold'),
(74041, 4488, 0, 'Spawn Point 89 - Tin'),
(74042, 4488, 0, 'Spawn Point 89 - Iron'),
(74043, 4488, 10, 'Spawn Point 89 - Silver'),
(74044, 4488, 10, 'Spawn Point 89 - Gold'),
(74056, 4491, 0, 'Spawn Point 92 - Tin'),
(74057, 4491, 0, 'Spawn Point 92 - Iron'),
(74058, 4491, 10, 'Spawn Point 92 - Silver'),
(74059, 4491, 10, 'Spawn Point 92 - Gold'),
(74061, 4492, 0, 'Spawn Point 93 - Tin'),
(74062, 4492, 0, 'Spawn Point 93 - Iron'),
(74063, 4492, 10, 'Spawn Point 93 - Silver'),
(74064, 4492, 10, 'Spawn Point 93 - Gold'),
(74066, 4493, 0, 'Spawn Point 94 - Tin'),
(74067, 4493, 0, 'Spawn Point 94 - Iron'),
(74068, 4493, 10, 'Spawn Point 94 - Silver'),
(74069, 4493, 10, 'Spawn Point 94 - Gold'),
(74071, 4494, 0, 'Spawn Point 95 - Tin'),
(74072, 4494, 0, 'Spawn Point 95 - Iron'),
(74073, 4494, 10, 'Spawn Point 95 - Silver'),
(74074, 4494, 10, 'Spawn Point 95 - Gold'),
(74086, 4497, 0, 'Spawn Point 98 - Tin'),
(74087, 4497, 0, 'Spawn Point 98 - Iron'),
(74088, 4497, 10, 'Spawn Point 98 - Silver'),
(74089, 4497, 10, 'Spawn Point 98 - Gold'),
(74096, 4499, 0, 'Spawn Point 100 - Tin'),
(74097, 4499, 0, 'Spawn Point 100 - Iron'),
(74098, 4499, 10, 'Spawn Point 100 - Silver'),
(74099, 4499, 10, 'Spawn Point 100 - Gold'),
(74101, 4500, 0, 'Spawn Point 101 - Tin'),
(74102, 4500, 0, 'Spawn Point 101 - Iron'),
(74103, 4500, 10, 'Spawn Point 101 - Silver'),
(74104, 4500, 10, 'Spawn Point 101 - Gold'),
(74106, 4501, 0, 'Spawn Point 102 - Tin'),
(74107, 4501, 0, 'Spawn Point 102 - Iron'),
(74108, 4501, 10, 'Spawn Point 102 - Silver'),
(74109, 4501, 10, 'Spawn Point 102 - Gold'),
(74111, 4502, 0, 'Spawn Point 103 - Tin'),
(74112, 4502, 0, 'Spawn Point 103 - Iron'),
(74113, 4502, 10, 'Spawn Point 103 - Silver'),
(74114, 4502, 10, 'Spawn Point 103 - Gold'),
(74116, 4503, 0, 'Spawn Point 104 - Tin'),
(74117, 4503, 0, 'Spawn Point 104 - Iron'),
(74118, 4503, 10, 'Spawn Point 104 - Silver'),
(74119, 4503, 10, 'Spawn Point 104 - Gold'),
(74121, 4504, 0, 'Spawn Point 105 - Tin'),
(74122, 4504, 0, 'Spawn Point 105 - Iron'),
(74123, 4504, 10, 'Spawn Point 105 - Silver'),
(74124, 4504, 10, 'Spawn Point 105 - Gold'),
(74131, 4506, 0, 'Spawn Point 107 - Tin'),
(74132, 4506, 0, 'Spawn Point 107 - Iron'),
(74133, 4506, 10, 'Spawn Point 107 - Silver'),
(74134, 4506, 10, 'Spawn Point 107 - Gold'),
(74146, 4509, 0, 'Spawn Point 110 - Tin'),
(74147, 4509, 0, 'Spawn Point 110 - Iron'),
(74148, 4509, 10, 'Spawn Point 110 - Silver'),
(74149, 4509, 10, 'Spawn Point 110 - Gold'),
(74156, 4511, 0, 'Spawn Point 112 - Tin'),
(74157, 4511, 0, 'Spawn Point 112 - Iron'),
(74158, 4511, 10, 'Spawn Point 112 - Silver'),
(74159, 4511, 10, 'Spawn Point 112 - Gold'),
(74161, 4512, 0, 'Spawn Point 113 - Tin'),
(74162, 4512, 0, 'Spawn Point 113 - Iron'),
(74163, 4512, 10, 'Spawn Point 113 - Silver'),
(74164, 4512, 10, 'Spawn Point 113 - Gold');

INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES 
(2009, 30, 'Master Mineral Pool - Duskwood'),
(4400, 1, 'Spawn Point 1 - Duskwood'),
(4401, 1, 'Spawn Point 2 - Duskwood'),
(4402, 1, 'Spawn Point 3 - Duskwood'),
(4403, 1, 'Spawn Point 4 - Duskwood'),
(4404, 1, 'Spawn Point 5 - Duskwood'),
(4405, 1, 'Spawn Point 6 - Duskwood'),
(4406, 1, 'Spawn Point 7 - Duskwood'),
(4407, 1, 'Spawn Point 8 - Duskwood'),
(4408, 1, 'Spawn Point 9 - Duskwood'),
(4409, 1, 'Spawn Point 10 - Duskwood'),
(4410, 1, 'Spawn Point 11 - Duskwood'),
(4411, 1, 'Spawn Point 12 - Duskwood'),
(4412, 1, 'Spawn Point 13 - Duskwood'),
(4413, 1, 'Spawn Point 14 - Duskwood'),
(4415, 1, 'Spawn Point 16 - Duskwood'),
(4416, 1, 'Spawn Point 17 - Duskwood'),
(4417, 1, 'Spawn Point 18 - Duskwood'),
(4418, 1, 'Spawn Point 19 - Duskwood'),
(4419, 1, 'Spawn Point 20 - Duskwood'),
(4420, 1, 'Spawn Point 21 - Duskwood'),
(4421, 1, 'Spawn Point 22 - Duskwood'),
(4422, 1, 'Spawn Point 23 - Duskwood'),
(4423, 1, 'Spawn Point 24 - Duskwood'),
(4424, 1, 'Spawn Point 25 - Duskwood'),
(4425, 1, 'Spawn Point 26 - Duskwood'),
(4426, 1, 'Spawn Point 27 - Duskwood'),
(4427, 1, 'Spawn Point 28 - Duskwood'),
(4428, 1, 'Spawn Point 29 - Duskwood'),
(4429, 1, 'Spawn Point 30 - Duskwood'),
(4430, 1, 'Spawn Point 31 - Duskwood'),
(4431, 1, 'Spawn Point 32 - Duskwood'),
(4432, 1, 'Spawn Point 33 - Duskwood'),
(4433, 1, 'Spawn Point 34 - Duskwood'),
(4434, 1, 'Spawn Point 35 - Duskwood'),
(4435, 1, 'Spawn Point 36 - Duskwood'),
(4436, 1, 'Spawn Point 37 - Duskwood'),
(4437, 1, 'Spawn Point 38 - Duskwood'),
(4438, 1, 'Spawn Point 39 - Duskwood'),
(4439, 1, 'Spawn Point 40 - Duskwood'),
(4440, 1, 'Spawn Point 41 - Duskwood'),
(4441, 1, 'Spawn Point 42 - Duskwood'),
(4442, 1, 'Spawn Point 43 - Duskwood'),
(4443, 1, 'Spawn Point 44 - Duskwood'),
(4444, 1, 'Spawn Point 45 - Duskwood'),
(4445, 1, 'Spawn Point 46 - Duskwood'),
(4446, 1, 'Spawn Point 47 - Duskwood'),
(4447, 1, 'Spawn Point 48 - Duskwood'),
(4448, 1, 'Spawn Point 49 - Duskwood'),
(4449, 1, 'Spawn Point 50 - Duskwood'),
(4450, 1, 'Spawn Point 51 - Duskwood'),
(4451, 1, 'Spawn Point 52 - Duskwood'),
(4452, 1, 'Spawn Point 53 - Duskwood'),
(4453, 1, 'Spawn Point 54 - Duskwood'),
(4454, 1, 'Spawn Point 55 - Duskwood'),
(4455, 1, 'Spawn Point 56 - Duskwood'),
(4456, 1, 'Spawn Point 57 - Duskwood'),
(4457, 1, 'Spawn Point 58 - Duskwood'),
(4458, 1, 'Spawn Point 59 - Duskwood'),
(4459, 1, 'Spawn Point 60 - Duskwood'),
(4460, 1, 'Spawn Point 61 - Duskwood'),
(4461, 1, 'Spawn Point 62 - Duskwood'),
(4462, 1, 'Spawn Point 63 - Duskwood'),
(4463, 1, 'Spawn Point 64 - Duskwood'),
(4464, 1, 'Spawn Point 65 - Duskwood'),
(4465, 1, 'Spawn Point 66 - Duskwood'),
(4466, 1, 'Spawn Point 67 - Duskwood'),
(4467, 1, 'Spawn Point 68 - Duskwood'),
(4468, 1, 'Spawn Point 69 - Duskwood'),
(4469, 1, 'Spawn Point 70 - Duskwood'),
(4470, 1, 'Spawn Point 71 - Duskwood'),
(4471, 1, 'Spawn Point 72 - Duskwood'),
(4472, 1, 'Spawn Point 73 - Duskwood'),
(4473, 1, 'Spawn Point 74 - Duskwood'),
(4474, 1, 'Spawn Point 75 - Duskwood'),
(4475, 1, 'Spawn Point 76 - Duskwood'),
(4476, 1, 'Spawn Point 77 - Duskwood'),
(4477, 1, 'Spawn Point 78 - Duskwood'),
(4478, 1, 'Spawn Point 79 - Duskwood'),
(4479, 1, 'Spawn Point 80 - Duskwood'),
(4480, 1, 'Spawn Point 81 - Duskwood'),
(4481, 1, 'Spawn Point 82 - Duskwood'),
(4482, 1, 'Spawn Point 83 - Duskwood'),
(4483, 1, 'Spawn Point 84 - Duskwood'),
(4484, 1, 'Spawn Point 85 - Duskwood'),
(4485, 1, 'Spawn Point 86 - Duskwood'),
(4486, 1, 'Spawn Point 87 - Duskwood'),
(4487, 1, 'Spawn Point 88 - Duskwood'),
(4488, 1, 'Spawn Point 89 - Duskwood'),
(4489, 1, 'Spawn Point 90 - Duskwood'),
(4490, 1, 'Spawn Point 91 - Duskwood'),
(4491, 1, 'Spawn Point 92 - Duskwood'),
(4492, 1, 'Spawn Point 93 - Duskwood'),
(4493, 1, 'Spawn Point 94 - Duskwood'),
(4494, 1, 'Spawn Point 95 - Duskwood'),
(4495, 1, 'Spawn Point 96 - Duskwood'),
(4496, 1, 'Spawn Point 97 - Duskwood'),
(4497, 1, 'Spawn Point 98 - Duskwood'),
(4498, 1, 'Spawn Point 99 - Duskwood'),
(4499, 1, 'Spawn Point 100 - Duskwood'),
(4500, 1, 'Spawn Point 101 - Duskwood'),
(4501, 1, 'Spawn Point 102 - Duskwood'),
(4502, 1, 'Spawn Point 103 - Duskwood'),
(4503, 1, 'Spawn Point 104 - Duskwood'),
(4504, 1, 'Spawn Point 105 - Duskwood'),
(4505, 1, 'Spawn Point 106 - Duskwood'),
(4506, 1, 'Spawn Point 107 - Duskwood'),
(4507, 1, 'Spawn Point 108 - Duskwood'),
(4508, 1, 'Spawn Point 109 - Duskwood'),
(4509, 1, 'Spawn Point 110 - Duskwood'),
(4510, 1, 'Spawn Point 111 - Duskwood'),
(4511, 1, 'Spawn Point 112 - Duskwood'),
(4512, 1, 'Spawn Point 113 - Duskwood');

INSERT INTO `pool_pool` (`pool_id`, `mother_pool`, `chance`, `description`) VALUES 
(4400, 2009, 0, 'Spawn Point 1 - Duskwood'),
(4401, 2009, 0, 'Spawn Point 2 - Duskwood'),
(4402, 2009, 0, 'Spawn Point 3 - Duskwood'),
(4403, 2009, 0, 'Spawn Point 4 - Duskwood'),
(4404, 2009, 0, 'Spawn Point 5 - Duskwood'),
(4405, 2009, 0, 'Spawn Point 6 - Duskwood'),
(4406, 2009, 0, 'Spawn Point 7 - Duskwood'),
(4407, 2009, 0, 'Spawn Point 8 - Duskwood'),
(4408, 2009, 0, 'Spawn Point 9 - Duskwood'),
(4409, 2009, 0, 'Spawn Point 10 - Duskwood'),
(4411, 2009, 0, 'Spawn Point 12 - Duskwood'),
(4412, 2009, 0, 'Spawn Point 13 - Duskwood'),
(4413, 2009, 0, 'Spawn Point 14 - Duskwood'),
(4415, 2009, 0, 'Spawn Point 16 - Duskwood'),
(4416, 2009, 0, 'Spawn Point 17 - Duskwood'),
(4417, 2009, 0, 'Spawn Point 18 - Duskwood'),
(4418, 2009, 0, 'Spawn Point 19 - Duskwood'),
(4419, 2009, 0, 'Spawn Point 20 - Duskwood'),
(4420, 2009, 0, 'Spawn Point 21 - Duskwood'),
(4421, 2009, 0, 'Spawn Point 22 - Duskwood'),
(4422, 2009, 0, 'Spawn Point 23 - Duskwood'),
(4423, 2009, 0, 'Spawn Point 24 - Duskwood'),
(4424, 2009, 0, 'Spawn Point 25 - Duskwood'),
(4425, 2009, 0, 'Spawn Point 26 - Duskwood'),
(4426, 2009, 0, 'Spawn Point 27 - Duskwood'),
(4427, 2009, 0, 'Spawn Point 28 - Duskwood'),
(4428, 2009, 0, 'Spawn Point 29 - Duskwood'),
(4429, 2009, 0, 'Spawn Point 30 - Duskwood'),
(4430, 2009, 0, 'Spawn Point 31 - Duskwood'),
(4468, 11650, 0, 'Minerals - Rolands Doom - Duskwood'),
(4432, 2009, 0, 'Spawn Point 33 - Duskwood'),
(4433, 2009, 0, 'Spawn Point 34 - Duskwood'),
(4434, 2009, 0, 'Spawn Point 35 - Duskwood'),
(4435, 2009, 0, 'Spawn Point 36 - Duskwood'),
(4436, 2009, 0, 'Spawn Point 37 - Duskwood'),
(4437, 2009, 0, 'Spawn Point 38 - Duskwood'),
(4438, 2009, 0, 'Spawn Point 39 - Duskwood'),
(4439, 2009, 0, 'Spawn Point 40 - Duskwood'),
(4440, 2009, 0, 'Spawn Point 41 - Duskwood'),
(4441, 2009, 0, 'Spawn Point 42 - Duskwood'),
(4442, 2009, 0, 'Spawn Point 43 - Duskwood'),
(4443, 2009, 0, 'Spawn Point 44 - Duskwood'),
(4444, 2009, 0, 'Spawn Point 45 - Duskwood'),
(4445, 2009, 0, 'Spawn Point 46 - Duskwood'),
(4446, 2009, 0, 'Spawn Point 47 - Duskwood'),
(4447, 2009, 0, 'Spawn Point 48 - Duskwood'),
(4448, 2009, 0, 'Spawn Point 49 - Duskwood'),
(4449, 2009, 0, 'Spawn Point 50 - Duskwood'),
(4450, 2009, 0, 'Spawn Point 51 - Duskwood'),
(4451, 2009, 0, 'Spawn Point 52 - Duskwood'),
(4452, 2009, 0, 'Spawn Point 53 - Duskwood'),
(4453, 2009, 0, 'Spawn Point 54 - Duskwood'),
(4455, 2009, 0, 'Spawn Point 56 - Duskwood'),
(4456, 2009, 0, 'Spawn Point 57 - Duskwood'),
(4457, 2009, 0, 'Spawn Point 58 - Duskwood'),
(4458, 2009, 0, 'Spawn Point 59 - Duskwood'),
(4459, 2009, 0, 'Spawn Point 60 - Duskwood'),
(4460, 2009, 0, 'Spawn Point 61 - Duskwood'),
(4461, 2009, 0, 'Spawn Point 62 - Duskwood'),
(4462, 2009, 0, 'Spawn Point 63 - Duskwood'),
(4463, 2009, 0, 'Spawn Point 64 - Duskwood'),
(4464, 2009, 0, 'Spawn Point 65 - Duskwood'),
(4465, 2009, 0, 'Spawn Point 66 - Duskwood'),
(4466, 2009, 0, 'Spawn Point 67 - Duskwood'),
(4467, 2009, 0, 'Spawn Point 68 - Duskwood'),
(4454, 11650, 0, 'Minerals - Rolands Doom - Duskwood'),
(4469, 2009, 0, 'Spawn Point 70 - Duskwood'),
(4470, 2009, 0, 'Spawn Point 71 - Duskwood'),
(4471, 2009, 0, 'Spawn Point 72 - Duskwood'),
(4472, 2009, 0, 'Spawn Point 73 - Duskwood'),
(4473, 2009, 0, 'Spawn Point 74 - Duskwood'),
(4474, 2009, 0, 'Spawn Point 75 - Duskwood'),
(4475, 2009, 0, 'Spawn Point 76 - Duskwood'),
(4476, 2009, 0, 'Spawn Point 77 - Duskwood'),
(4477, 2009, 0, 'Spawn Point 78 - Duskwood'),
(4478, 2009, 0, 'Spawn Point 79 - Duskwood'),
(4479, 2009, 0, 'Spawn Point 80 - Duskwood'),
(4480, 2009, 0, 'Spawn Point 81 - Duskwood'),
(4481, 2009, 0, 'Spawn Point 82 - Duskwood'),
(4482, 2009, 0, 'Spawn Point 83 - Duskwood'),
(4483, 2009, 0, 'Spawn Point 84 - Duskwood'),
(4484, 2009, 0, 'Spawn Point 85 - Duskwood'),
(4485, 2009, 0, 'Spawn Point 86 - Duskwood'),
(4486, 2009, 0, 'Spawn Point 87 - Duskwood'),
(4487, 2009, 0, 'Spawn Point 88 - Duskwood'),
(4488, 2009, 0, 'Spawn Point 89 - Duskwood'),
(4489, 2009, 0, 'Spawn Point 90 - Duskwood'),
(4490, 2009, 0, 'Spawn Point 91 - Duskwood'),
(4491, 2009, 0, 'Spawn Point 92 - Duskwood'),
(4492, 2009, 0, 'Spawn Point 93 - Duskwood'),
(4493, 2009, 0, 'Spawn Point 94 - Duskwood'),
(4494, 2009, 0, 'Spawn Point 95 - Duskwood'),
(4495, 2009, 0, 'Spawn Point 96 - Duskwood'),
(4497, 2009, 0, 'Spawn Point 98 - Duskwood'),
(4498, 2009, 0, 'Spawn Point 99 - Duskwood'),
(4499, 2009, 0, 'Spawn Point 100 - Duskwood'),
(4500, 2009, 0, 'Spawn Point 101 - Duskwood'),
(4501, 2009, 0, 'Spawn Point 102 - Duskwood'),
(4502, 2009, 0, 'Spawn Point 103 - Duskwood'),
(4503, 2009, 0, 'Spawn Point 104 - Duskwood'),
(4504, 2009, 0, 'Spawn Point 105 - Duskwood'),
(4431, 11650, 0, 'Minerals - Rolands Doom - Duskwood'),
(4506, 2009, 0, 'Spawn Point 107 - Duskwood'),
(4507, 2009, 0, 'Spawn Point 108 - Duskwood'),
(4509, 2009, 0, 'Spawn Point 110 - Duskwood'),
(4410, 11650, 0, 'Minerals - Rolands Doom - Duskwood'),
(4511, 2009, 0, 'Spawn Point 112 - Duskwood'),
(4512, 2009, 0, 'Spawn Point 113 - Duskwood'),
(4496, 11650, 0, 'Minerals - Rolands Doom - Duskwood'),
(4505, 11650, 0, 'Minerals - Rolands Doom - Duskwood'),
(4508, 11650, 0, 'Minerals - Rolands Doom - Duskwood'),
(4510, 11650, 0, 'Minerals - Rolands Doom - Duskwood');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_06_03' WHERE sql_rev = '1638546663868479700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
