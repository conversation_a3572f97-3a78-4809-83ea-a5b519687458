-- DB update 2020_09_12_00 -> 2020_09_12_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_09_12_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_09_12_00 2020_09_12_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1598879308422919200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1598879308422919200');
/*
 * General: Build Update
 * Update by Knindza | <www.azerothcore.org>
 * Copyright (C) <www.shadowburn.net> & <www.lichbane.com>
*/

/* Content 3.2.0 */ 
SET @Build := 10314;

UPDATE `item_template` SET `VerifiedBuild` = @Build WHERE `entry` IN (47551, 47552, 46964, 46965, 46966, 46967, 46968, 46969, 46971, 46973, 46975, 46977, 46980, 46986, 46989, 46991, 46993, 46995, 47001, 47002, 47003, 47004, 47059, 47060, 47061, 47062, 47063, 47064, 47066, 47067, 47068, 47074, 47075, 47076, 47077, 47078, 47084, 47085, 47086, 47087, 47088, 47095, 47096, 47097, 47098, 47099, 47109, 47110, 47111, 47112, 47113, 47129, 47130, 47131, 47132, 47133, 47143, 47144, 47145, 47146, 47147, 47153, 47155, 47156, 47188, 47189, 47192, 47205, 47206, 47207, 47208, 47209, 47224, 47236, 47238, 47239, 47412, 47413, 47414, 47415, 47416, 47417, 47418, 47419, 47420, 47421, 47422, 47423, 47424, 47425, 47426, 47427, 47428, 47429, 47430, 47431, 47432, 47433, 47434, 47435, 47436, 47437, 47438, 47439, 47440, 47441, 47442, 47443, 47444, 47445, 47446, 47447, 47448, 47449, 47450, 47451, 47452, 47453, 47454, 47455, 47456, 47457, 47458, 47459, 47460, 47461, 47462, 47463, 47464, 47465, 47466, 47467, 47468, 47469, 47470, 47471, 47473, 47475, 47476, 47477, 47478, 47480, 47481, 47482, 47483, 47486, 47487, 47489, 47490, 47492, 47506, 47513, 47516, 47520, 47521, 47523, 47524, 47525, 47526, 47528, 47758, 47759, 47760, 47761, 47762, 47763, 47764, 47765, 47766, 47767, 47788, 47789, 47790, 47791, 47792, 47793, 47794, 47795, 47796, 47797, 40790, 40791, 40792, 40810, 40811, 40812, 40829, 40830, 40831, 40850, 40851, 40852, 40870, 40871, 40872, 40910, 40928, 40934, 40940, 40964, 40994, 40995, 41002, 41008, 41014, 41020, 41028, 41034, 41039, 41045, 41082, 41088, 41138, 41144, 41152, 41158, 41200, 41206, 41212, 41218, 41276, 41282, 41288, 41294, 41299, 41305, 41311, 41317, 41322, 41328, 41651, 41656, 41662, 41668, 41673, 41679, 41684, 41716, 41768, 41774, 41855, 41860, 41865, 41870, 41875, 41916, 41922, 41928, 41935, 41941, 41947, 41954, 41960, 41966, 41972, 41994, 41999, 42006, 42012, 42018, 42527, 42533, 42539, 42561, 42566, 42572, 42580, 42585, 42591, 42599, 42604, 42609, 42616, 42622, 42854, 40883, 40884, 40890, 40978, 40979, 40984, 41052, 41056, 41061, 41066, 41071, 41076, 41226, 41231, 41236, 41618, 41622, 41626, 41631, 41636, 41641, 41833, 41837, 41841, 41882, 41886, 41894, 41899, 41904, 41910, 42041, 42042, 42043, 42044, 42045, 42046, 42047, 42076, 42077, 42078, 42079, 42080, 42081, 42082, 42118, 42119, 42133, 42134, 42135, 42136, 42137, 42210, 42229, 42234, 42244, 42250, 42257, 42262, 42267, 42272, 42277, 42282, 42287, 42292, 42319, 42324, 42329, 42334, 42348, 42354, 42366, 42386, 42392, 42487, 42492, 42498, 42504, 42515, 42521, 44423, 44424, 46374, 46958, 46959, 46960, 46961, 46962, 46963, 46970, 46972, 46974, 46976, 46979, 46985, 46988, 46990, 46992, 46994, 46996, 46997, 46999, 47000, 47041, 47042, 47043, 47051, 47052, 47053, 47054, 47055, 47056, 47057, 47069, 47070, 47071, 47072, 47073, 47079, 47080, 47081, 47082, 47083, 47089, 47090, 47092, 47093, 47094, 47104, 47105, 47106, 47107, 47108, 47114, 47115, 47116, 47121, 47126, 47138, 47139, 47140, 47141, 47142, 47148, 47149, 47150, 47151, 47152, 47182, 47183, 47184, 47186, 47187, 47193, 47194, 47195, 47203, 47204, 47223, 47225, 47233, 47234, 47235, 47251, 47252, 47253, 47254, 47255, 47256, 47257, 47258, 47259, 47260, 47261, 47262, 47263, 47264, 47265, 47266, 47267, 47268, 47269, 47270, 47271, 47272, 47273, 47274, 47275, 47276, 47277, 47278, 47279, 47280, 47281, 47282, 47283, 47284, 47285, 47286, 47287, 47288, 47289, 47290, 47291, 47292, 47293, 47294, 47295, 47296, 47297, 47298, 47299, 47300, 47301, 47302, 47303, 47304, 47305, 47306, 47307, 47308, 47309, 47310, 47311, 47312, 47313, 47314, 47315, 47316, 47317, 47318, 47319, 47320, 47321, 47322, 47323, 47324, 47325, 47326, 47327, 47328, 47329, 47330, 47570, 47571, 47572, 47573, 47574, 47575, 47576, 47577, 47579, 47580, 47581, 47582, 47583, 47584, 47585, 47586, 47587, 47588, 47589, 47590, 47591, 47592, 47593, 47594, 47595, 47596, 47597, 47598, 47599, 47600, 47601, 47602, 47603, 47604, 47605, 47606, 47658, 47674, 47675, 47677, 47678, 47681, 47682, 47684, 47685, 47686, 47687, 47688, 47689, 47690, 47691, 47692, 47693, 47694, 47695, 47696, 47697, 47698, 47699, 47701, 47702, 47704, 47705, 47706, 47707, 47708, 47709, 47710, 47712, 47713, 47714, 47715, 47716, 47729, 47730, 47731, 47732, 47733, 47734, 47735, 47753, 47754, 47755, 47756, 47757, 47768, 47769, 47770, 47771, 47772, 47778, 47779, 47780, 47781, 47782, 42483, 47659, 47660, 47661, 47662, 47664, 47665, 47666, 47667, 47668, 47670, 47671, 47672, 47673, 47578, 47607, 47608, 47609, 47610, 47611, 47612, 47613, 47614, 47615, 47616, 47617, 47618, 47619, 47620, 47621, 47663, 47669, 47676, 47679, 47680, 47683, 47700, 47703, 47711, 47717, 47718, 47719, 47720, 47721, 47724, 47725, 47726, 47727, 47728, 47736, 47737, 47738, 47739, 47740, 47741, 47742, 47743, 47744, 47745, 47746, 47747, 47748, 47749, 47750, 47751, 47752, 47773, 47774, 47775, 47776, 47777, 47783, 47784, 47785, 47786, 47787, 47798, 47799, 47800, 47243, 47244, 47245, 47248, 47249, 47250, 47493, 47494, 47495, 47496, 47497, 47498, 47500, 47501, 47502, 47503, 47504, 47508, 47509, 47510, 47511, 47512, 47514, 47522, 47527, 47529, 47560, 47561, 47562, 47563, 47564, 47565, 47566, 47567, 47568, 47569, 47170, 47171, 47172, 47173, 47174, 47175, 47176, 47177, 47178, 47181, 47185, 47197, 47199, 47200, 47201, 47202, 47210, 47211, 47212, 47213, 47214, 47215, 47216, 47217, 47218, 47219, 47220, 47221, 47222, 47226, 47227, 47228, 47229, 47230, 47231, 47232, 35664, 35665, 47622, 47623, 47624, 47625, 47626, 47627, 47628, 47629, 47630, 47631, 47632, 47633, 47634, 47635, 47636, 47637, 47638, 47639, 47640, 47641, 47642, 47643, 47644, 47645, 47646, 47647, 47648, 47649, 47650, 47651, 47652, 47653, 47654, 47655, 47656, 47657, 46399, 46400, 46401, 46402, 46403, 36919, 36922, 36925, 36928, 36931, 36934, 47241, 47242, 47557, 47558, 47559, 46897, 46898, 46899, 46901, 46902, 46904, 46905, 46909, 46911, 46912, 46913, 46915, 46916, 46917, 46918, 46920, 46922, 46923, 46924, 46925, 46926, 46928, 46929, 46932, 46933, 46935, 46937, 46938, 46940, 46941, 46942, 46943, 46944, 46948, 46949, 46950, 46951, 46952, 46953, 46956, 47007, 47010, 47015, 47017, 47018, 47019, 47020, 47021, 47022, 47499, 47556, 37335, 46849, 46812, 47541, 44177, 46708, 46813, 46814, 46847, 47030, 47246, 46691, 46102, 46815, 46816, 47101, 47179, 47180, 47100, 46735, 37892, 37893, 37894, 37895, 37896, 37897, 37494, 37495, 37906, 46544, 46545, 46780, 46802, 46820, 46821, 46843, 46874, 46707, 46817, 46818, 47395, 33616, 46362, 46364, 46367, 46380, 46381, 46382, 46396, 46397, 46693, 46775, 46779, 46783, 46784, 46793, 46796, 46797, 46830, 46859, 46870, 46875, 46876, 46877, 46878, 46879, 46880, 46881, 46882, 46883, 46884, 46885, 46889, 46893, 46895, 46954, 46955, 47006, 47009, 47029, 47033, 47035, 47036, 47037, 47048, 47196, 45188, 45189, 45191, 46978, 48029, 48031, 48033, 48035, 48037, 48057, 48058, 48059, 48060, 48061, 48082, 48083, 48084, 48085, 48086, 48087, 48088, 48089, 48090, 48091, 48138, 48139, 48140, 48141, 48142, 48143, 48144, 48145, 48146, 48147, 48168, 48169, 48170, 48171, 48172, 48173, 48174, 48175, 48176, 48177, 48198, 48199, 48200, 48201, 48202, 48203, 48204, 48205, 48206, 48207, 48228, 48229, 48230, 48231, 48232, 48233, 48234, 48235, 48236, 48237, 48260, 48261, 48262, 48263, 48264, 48265, 48266, 48267, 48268, 48269, 48290, 48291, 48292, 48293, 48294, 48305, 48306, 48307, 48308, 48309, 48321, 48322, 48323, 48324, 48325, 48326, 48327, 48328, 48329, 48330, 48351, 48352, 48353, 48354, 48355, 48356, 48357, 48358, 48359, 48360, 48381, 48382, 48383, 48384, 48385, 48396, 48397, 48398, 48399, 48400, 48402, 48404, 48406, 48408, 48410, 48412, 48414, 48420, 48422, 48424, 48426, 48428, 48432, 48433, 48435, 48438, 48440, 48442, 48444, 48447, 48451, 48453, 48455, 48466, 48467, 48468, 48469, 48470, 48486, 48487, 48488, 48489, 48490, 48491, 48492, 48493, 48494, 48495, 48507, 48509, 48511, 48513, 48515, 48517, 48519, 48521, 48523, 48543, 48544, 48545, 48546, 48547, 48548, 48549, 48550, 48551, 48552, 48580, 48581, 48582, 48583, 48584, 48585, 48586, 48587, 48588, 48589, 48612, 48613, 48614, 48615, 48616, 48617, 48618, 48619, 48620, 48621, 48642, 48643, 48644, 48645, 48646, 48647, 48648, 48649, 48650, 48651, 48666, 48667, 48668, 48669, 48670, 48671, 48672, 48673, 48674, 48675, 49191, 49187, 47803, 47804, 47805, 47806, 47807, 47915, 47916, 47917, 47918, 47919, 47920, 47921, 47922, 47923, 47924, 47925, 47926, 47927, 47928, 47929, 47930, 47931, 47932, 47933, 47934, 47935, 47937, 47938, 47939, 47940, 47941, 47942, 47943, 47944, 47945, 47946, 47947, 47948, 47949, 47950, 47951, 47952, 47953, 47954, 47955, 47956, 47957, 47958, 47959, 47960, 47961, 47962, 47963, 47964, 47965, 47966, 47967, 47968, 47969, 47970, 47971, 47972, 47973, 47974, 47975, 47976, 47977, 47978, 47979, 47983, 47984, 47985, 47986, 47987, 47988, 47989, 47990, 47991, 47992, 47993, 47994, 47995, 47996, 47997, 47998, 47999, 48000, 48001, 48002, 48003, 48004, 48005, 48006, 48007, 48008, 48009, 48010, 48011, 48012, 48013, 48014, 48015, 48016, 48017, 48018, 48019, 48020, 48021, 48022, 48023, 48024, 48025, 48026, 48027, 48028, 48030, 48032, 48034, 48036, 48038, 48039, 48040, 48041, 48042, 48043, 48044, 48045, 48046, 48047, 48048, 48049, 48050, 48051, 48052, 48053, 48054, 48055, 48056, 48062, 48063, 48064, 48065, 48066, 48077, 48078, 48079, 48080, 48081, 48092, 48093, 48094, 48095, 48096, 48133, 48134, 48135, 48136, 48137, 48148, 48149, 48150, 48151, 48152, 48163, 48164, 48165, 48166, 48167, 48178, 48179, 48180, 48181, 48182, 48193, 48194, 48195, 48196, 48197, 48208, 48209, 48210, 48211, 48212, 48223, 48224, 48225, 48226, 48227, 48238, 48239, 48240, 48241, 48242, 48255, 48256, 48257, 48258, 48259, 48270, 48271, 48272, 48273, 48274, 48285, 48286, 48287, 48288, 48289, 48300, 48301, 48302, 48303, 48304, 48316, 48317, 48318, 48319, 48320, 48331, 48332, 48333, 48334, 48335, 48346, 48347, 48348, 48349, 48350, 48361, 48362, 48363, 48364, 48365, 48376, 48377, 48378, 48379, 48380, 48391, 48392, 48393, 48394, 48395, 48430, 48446, 48450, 48452, 48454, 48461, 48462, 48463, 48464, 48465, 48481, 48482, 48483, 48484, 48485, 48496, 48497, 48498, 48499, 48500, 48538, 48539, 48540, 48541, 48542, 48553, 48554, 48555, 48556, 48557, 48575, 48576, 48577, 48578, 48579, 48590, 48591, 48592, 48593, 48594, 48607, 48608, 48609, 48610, 48611, 48622, 48623, 48624, 48625, 48626, 48637, 48638, 48639, 48640, 48641, 48657, 48658, 48659, 48660, 48661, 48693, 48695, 48697, 48699, 48701, 48703, 48705, 48708, 48709, 48710, 48711, 48712, 48713, 48714, 48722, 48724, 49179, 49181, 49183, 49185, 49189, 49233, 49234, 49237, 49238, 47801, 47802, 47808, 47809, 47810, 47811, 47812, 47813, 47814, 47815, 47816, 47829, 47830, 47832, 47834, 47835, 47836, 47837, 47838, 47849, 47850, 47851, 47852, 47853, 47854, 47855, 47856, 47857, 47858, 47859, 47860, 47861, 47862, 47863, 47864, 47865, 47866, 47867, 47868, 47869, 47870, 47871, 47872, 47873, 47874, 47875, 47876, 47877, 47878, 47879, 47880, 47881, 47882, 47883, 47884, 47885, 47886, 47887, 47888, 47889, 47890, 47891, 47892, 47893, 47894, 47895, 47896, 47897, 47898, 47899, 47900, 47901, 47902, 47903, 47904, 47905, 47906, 47907, 47908, 47909, 47910, 47911, 47913, 47914, 47936, 47980, 47981, 47982, 48067, 48068, 48069, 48070, 48071, 48072, 48073, 48074, 48075, 48076, 48097, 48098, 48099, 48100, 48101, 48102, 48129, 48130, 48131, 48132, 48153, 48154, 48155, 48156, 48157, 48158, 48159, 48160, 48161, 48162, 48183, 48184, 48185, 48186, 48187, 48188, 48189, 48190, 48191, 48192, 48213, 48214, 48215, 48216, 48217, 48218, 48219, 48220, 48221, 48222, 48243, 48244, 48245, 48246, 48247, 48250, 48251, 48252, 48253, 48254, 48275, 48276, 48277, 48278, 48279, 48280, 48281, 48282, 48283, 48284, 48295, 48296, 48297, 48298, 48299, 48310, 48312, 48313, 48314, 48315, 48336, 48337, 48338, 48339, 48340, 48341, 48342, 48343, 48344, 48345, 48366, 48367, 48368, 48369, 48370, 48371, 48372, 48373, 48374, 48375, 48386, 48387, 48388, 48389, 48390, 48429, 48436, 48445, 48448, 48449, 48456, 48457, 48458, 48459, 48460, 48472, 48474, 48476, 48478, 48480, 48501, 48502, 48503, 48504, 48505, 48529, 48531, 48533, 48535, 48537, 48558, 48559, 48560, 48561, 48562, 48564, 48566, 48568, 48572, 48574, 48595, 48596, 48597, 48598, 48599, 48602, 48603, 48604, 48605, 48606, 48627, 48628, 48629, 48630, 48631, 48632, 48633, 48634, 48635, 48636, 48652, 48653, 48654, 48655, 48656, 48983, 48987, 48988, 48990, 48991, 48992, 48993, 48994, 48997, 48998, 49231, 49232, 49235, 49236, 48663, 48954, 48955, 48956, 48957, 48974, 48975, 48976, 48977, 48978, 48979, 48980, 48981, 48982, 48999, 49000, 49040, 49074, 49076, 49078, 49080, 49116, 49118, 49120, 48677, 48683, 48685, 48687, 48689, 48691, 48716, 48718, 49227, 49177, 49110, 48933, 49050, 49112, 47828, 48679, 48681, 49052, 49054, 49286, 49285, 49044, 49096, 49098, 49282, 49284, 49290, 49192, 49193, 49084, 49283, 48112, 48114, 48116, 48118, 48120, 48122, 48124, 48126, 48418, 48720, 49209);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
