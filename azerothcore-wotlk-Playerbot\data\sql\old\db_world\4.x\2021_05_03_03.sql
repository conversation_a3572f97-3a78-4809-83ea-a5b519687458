-- DB update 2021_05_03_02 -> 2021_05_03_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_03_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_03_02 2021_05_03_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1619468769521968763'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1619468769521968763');

DELETE FROM `creature` WHERE (`id` = 511) AND (`guid` IN (4444));
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `wander_distance`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`) VALUES
(4444, 511, 0, 0, 0, 1, 1, 828, 1, -11012, -1350.91, 53.658, 5.70029, 300, 0, 0, 787, 0, 2, 0, 0, 0, '', 0);

DELETE FROM `creature_addon` WHERE (`guid` IN (4444));
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `isLarge`, `auras`) VALUES
(4444, 44440, 0, 0, 4097, 0, 0, NULL);

DELETE FROM `waypoint_data` WHERE `id` = 44440;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(44440,1,-11012,-1350.91,53.658,0,0,0,0,100,0),
(44440,2,-11008.1,-1349.59,53.658,0,0,0,0,100,0),
(44440,3,-11004.2,-1348.28,53.658,0,0,0,0,100,0),
(44440,4,-11002,-1347.52,53.658,0,0,0,0,100,0),
(44440,5,-11002,-1347.52,53.658,0,0,0,0,100,0),
(44440,6,-11007.7,-1347.67,53.908,0,0,0,0,100,0),
(44440,7,-11003.4,-1346.67,53.908,0,0,0,0,100,0),
(44440,8,-11005.2,-1349.33,53.908,0,0,0,0,100,0),
(44440,9,-11004.7,-1350.54,53.658,0,0,0,0,100,0),
(44440,10,-11000.4,-1348.08,53.908,0,0,0,0,100,0),
(44440,11,-11000.5,-1348.3,53.908,0,0,0,0,100,0),
(44440,12,-11003,-1351.5,53.908,0,0,0,0,100,0),
(44440,13,-11001.4,-1349.49,53.908,0,0,0,0,100,0),
(44440,14,-11000.5,-1348.37,53.908,0,0,0,0,100,0),
(44440,15,-10998.5,-1350.32,53.908,0,0,0,0,100,0),
(44440,16,-11003.1,-1351.9,53.908,0,0,0,0,100,0),
(44440,17,-11003.5,-1347.61,53.908,0,0,0,0,100,0),
(44440,18,-11003,-1348.9,53.658,0,0,0,0,100,0),
(44440,19,-11005,-1349.43,53.908,0,0,0,0,100,0),
(44440,20,-11005.3,-1349.96,53.908,0,0,0,0,100,0),
(44440,21,-11008.6,-1352.75,53.908,0,0,0,0,100,0),
(44440,22,-11011.2,-1349.45,53.658,0,0,0,0,100,0),
(44440,23,-11008.1,-1347.37,53.908,0,0,0,0,100,0),
(44440,24,-10999.8,-1347.31,53.908,0,0,0,0,100,0),
(44440,25,-11000.1,-1347.87,53.908,0,0,0,0,100,0),
(44440,26,-11001,-1348.77,53.908,0,0,0,0,100,0),
(44440,27,-11002.3,-1350.48,53.658,0,0,0,0,100,0),
(44440,28,-11007.4,-1355.68,53.9021,0,0,0,0,100,0),
(44440,29,-11008.2,-1349.79,53.908,0,0,0,0,100,0),
(44440,30,-10999.2,-1339.94,53.659,0,0,0,0,100,0),
(44440,31,-10991,-1325.43,51.8941,0,0,0,0,100,0),
(44440,32,-10974.7,-1319.76,52.2399,0,0,0,0,100,0),
(44440,33,-10967.6,-1304.02,52.8301,0,0,0,0,100,0),
(44440,34,-10951.6,-1294.9,53.0798,0,0,0,0,100,0),
(44440,35,-10975.7,-1278.12,52.9166,0,0,0,0,100,0),
(44440,36,-10995.9,-1278.58,52.5571,0,0,0,0,100,0),
(44440,37,-11003.2,-1291.67,53.3562,0,0,0,0,100,0),
(44440,38,-11011.8,-1287.97,52.9221,0,0,0,0,100,0),
(44440,39,-11028.2,-1302.74,52.3512,0,0,0,0,100,0),
(44440,40,-11031.2,-1319.99,53.39,0,0,0,0,100,0),
(44440,41,-11023.3,-1327.44,53.1966,0,0,0,0,100,0),
(44440,42,-11012.7,-1317.23,53.104,0,0,0,0,100,0),
(44440,43,-10992.5,-1326.81,51.8687,0,0,0,0,100,0),
(44440,44,-10999.9,-1342.19,53.6589,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
