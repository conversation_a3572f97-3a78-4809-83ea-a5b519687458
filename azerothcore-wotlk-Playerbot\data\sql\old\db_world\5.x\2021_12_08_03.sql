-- DB update 2021_12_08_02 -> 2021_12_08_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_08_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_08_02 2021_12_08_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1638745963138176600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1638745963138176600');

-- Positions are sniffed, but position/spell relation cant be confirmed (serverside spells)
DELETE FROM `spell_target_position` WHERE ID IN (21886, 21900, 21901, 21902, 21903, 21904, 21905, 21906, 21907);
INSERT INTO `spell_target_position` (`ID`, `EffectIndex`, `MapID`, `PositionX`, `PositionY`, `PositionZ`, `Orientation`, `VerifiedBuild`) VALUES
(21886, 0, 409, 871.53997802734375, -839.114990234375, -228.99200439453125, 0, 0),
(21900, 0, 409, 843.50897216796875, -798.31298828125,    -229.4320068359375, 0, 0), 
(21901, 0, 409, 827.2760009765625, -874.02801513671875, -229.593994140625, 0, 0),
(21902, 0, 409, 864.572998046875, -806.4630126953125, -229.785995483398437, 0, 0),
(21903, 0, 409, 819.75701904296875, -807.176025390625, -229.033004760742187, 0, 0),
(21904, 0, 409, 811.0009765625, -822.281982421875, -229.311004638671875, 0, 0),
(21905, 0, 409, 892.63299560546875, -790.40997314453125, -228.927993774414062, 0, 0),
(21906, 0, 409, 906.69500732421875, -828.61102294921875, -229.927993774414062, 0, 0),
(21907, 0, 409, 862.86199951171875, -866.95501708984375, -228.9429931640625, 0, 0);

DELETE FROM `spell_script_names` WHERE `spell_id` = 21908;
INSERT INTO `spell_script_names` (`spell_id`, `ScriptName`) VALUES
(21908, 'spell_ragnaros_lava_burst_randomizer');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_08_03' WHERE sql_rev = '1638745963138176600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
