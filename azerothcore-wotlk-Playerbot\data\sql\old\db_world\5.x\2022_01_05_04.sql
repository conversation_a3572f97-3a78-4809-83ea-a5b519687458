-- DB update 2022_01_05_03 -> 2022_01_05_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_05_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_05_03 2022_01_05_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641421162754592797'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641421162754592797');

-- grammar fix
UPDATE `game_event` SET `description`='Children of Goldshire' WHERE  `eventEntry`=74;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_05_04' WHERE sql_rev = '1641421162754592797';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
