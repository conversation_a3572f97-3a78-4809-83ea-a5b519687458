-- DB update 2021_05_04_07 -> 2021_05_05_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_04_07';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_04_07 2021_05_05_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1620067514646031100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1620067514646031100');

UPDATE `creature` SET `spawntimesecs`=300 WHERE `id` IN (
877, -- Saltscale Forager
879, -- Saltscale Hunter
871, -- Saltscale Warrior
873, -- Saltscale Oracle
875); -- Saltscale Tide Lord


--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
