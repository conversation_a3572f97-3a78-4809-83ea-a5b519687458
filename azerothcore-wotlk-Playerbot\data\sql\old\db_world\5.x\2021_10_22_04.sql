-- DB update 2021_10_22_03 -> 2021_10_22_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_22_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_22_03 2021_10_22_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1634802877481310300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1634802877481310300');

UPDATE `gameobject` SET `orientation`=0.27, `position_z`=131.54, `position_y`=-905.33, `position_x`=-7872.48 WHERE `guid`=72780;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_22_04' WHERE sql_rev = '1634802877481310300';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
