-- DB update 2021_10_23_00 -> 2021_10_23_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_23_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_23_00 2021_10_23_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1634155543518607100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1634155543518607100');

UPDATE `creature` SET `position_x`=6940.03, `position_y`=-4683.13, `position_z`=708.49 WHERE `guid`=41372;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_23_01' WHERE sql_rev = '1634155543518607100';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
