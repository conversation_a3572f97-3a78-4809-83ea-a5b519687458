-- DB update 2021_12_17_02 -> 2021_12_17_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_17_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_17_02 2021_12_17_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639258188478104400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639258188478104400');

UPDATE `quest_template_addon` SET `SpecialFlags`=`SpecialFlags`|0x040 WHERE `ID` IN (7791,7792,7793,7794,7795,7798,7799,7800,7802,7803,7804,7805,7807,7808,7809,
7811,7813,7814,7817,7818,7820,7821,7822,7823,7824,7826,7827,7831,7833,7834,7835,7836,10352,10354,10356,10357,10359,10360,10361,10362);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_17_03' WHERE sql_rev = '1639258188478104400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
