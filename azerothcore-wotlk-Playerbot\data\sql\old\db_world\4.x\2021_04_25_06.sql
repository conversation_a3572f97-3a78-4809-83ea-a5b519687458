-- DB update 2021_04_25_05 -> 2021_04_25_06
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_25_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_25_05 2021_04_25_06 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1618612542462440000'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1618612542462440000');

<PERSON><PERSON><PERSON> TABLE `spell_dbc`
	CHANGE COLUMN `AttributesExB` `AttributesEx2` INT UNSIGNED NOT NULL DEFAULT 0 AFTER `AttributesEx`,
	CHANGE COLUMN `AttributesExC` `AttributesEx3` INT UNSIGNED NOT NULL DEFAULT 0 AFTER `AttributesEx2`,
	CHANGE COLUMN `AttributesExD` `AttributesEx4` INT UNSIGNED NOT NULL DEFAULT 0 AFTER `AttributesEx3`,
	CHANGE COLUMN `AttributesExE` `AttributesEx5` INT UNSIGNED NOT NULL DEFAULT 0 AFTER `AttributesEx4`,
	CHANGE COLUMN `AttributesExF` `AttributesEx6` INT UNSIGNED NOT NULL DEFAULT 0 AFTER `AttributesEx5`,
	CHANGE COLUMN `AttributesExG` `AttributesEx7` INT UNSIGNED NOT NULL DEFAULT 0 AFTER `AttributesEx6`;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
