-- DB update 2021_03_07_10 -> 2021_03_07_11
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_07_10';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_07_10 2021_03_07_11 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1614943755450721930'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1614943755450721930');

-- Improve quest Fields of Grief part 2
SET
@POOL     = '11649',
@POOLSIZE = '15',
@POOLDESC = 'Pumpkins - Quest Fields of Grief',
@GUID     = '45042,45043,45157,45194,45195,45196,45197,45198,45200,45201,2135254,2135255,2135256,2135257,2135258,2135259,2135260,2135261,2135262,2135263,2135264,2135265,2135266,2135267,2135268,2135269,2135270,2135271,2135272,2135273,2135274,2135275';

-- Create gameobjects
DELETE FROM `gameobject` WHERE FIND_IN_SET (`guid`,@GUID); 
INSERT INTO `gameobject` (`guid`,`id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`, `ScriptName`, `VerifiedBuild`) VALUES
(45042,375,0,0,0,1,1,2363.91,1348.38,33.3324,3.84784,0,0,0.938297,-0.345831,120,255,1,'',0),
(45043,375,0,0,0,1,1,2360.47,1399.62,33.0846,3.9146,0,0,0.926233,-0.376952,120,255,1,'',0),
(45157,375,0,0,0,1,1,2340.72,1352.37,33.3341,0.344954,0,0,0.171623,0.985163,120,255,1,'',0),
(45194,375,0,0,0,1,1,2263.35,1480.02,33.4491,1.97222,0,0,0.833885,0.551938,120,255,1,'',0),
(45195,375,0,0,0,1,1,2279.18,1450.29,33.4333,-1.74533,0,0,-0.766045,0.642787,120,255,1,'',0),
(45196,375,0,0,0,1,1,2348.16,1405.88,33.3189,1.46608,0,0,0.669132,0.743144,120,255,1,'',0),
(45197,375,0,0,0,1,1,2355.96,1348.1,33.4333,2.84489,0,0,0.989016,0.147808,120,255,1,'',0),
(45198,375,0,0,0,1,1,2304,1439.05,33.4333,-0.750491,0,0,-0.366501,0.930418,120,255,1,'',0),
(45200,375,0,0,0,1,1,2259.64,1430.03,33.4525,2.3911,0,0,0.930417,0.366502,120,255,1,'',0),
(45201,375,0,0,0,1,1,2293.58,1396.66,33.4333,-1.55334,0,0,-0.700908,0.713252,120,255,1,'',0),
(2135254,375,0,0,0,1,1,2284.49,1470.76,33.4333,1.22173,0,0,0.573576,0.819152,120,255,1,'',0),
(2135255,375,0,0,0,1,1,2351.13,1324.59,33.3334,0.293625,0,0,0.146286,0.989242,120,255,1,'',0),
(2135256,375,0,0,0,1,1,2431.47,1591.2,37.0613,0.488692,0,0,0.241922,0.970296,120,255,1,'',0),
(2135257,375,0,0,0,1,1,2276.21,1362.27,33.3335,3.29704,0,0,0.996981,-0.0776452,120,255,1,'',0),
(2135258,375,0,0,0,1,1,2300.79,1352.3,33.3334,3.07713,0,0,0.999481,0.0322268,120,255,1,'',0),
(2135259,375,0,0,0,1,1,2305.13,1347.16,33.3334,4.94245,0,0,0.621275,-0.783592,120,255,1,'',0),
(2135260,375,0,0,0,1,1,2318.93,1398,33.3335,2.74335,0,0,0.980241,0.197809,120,255,1,'',0),
(2135261,375,0,0,0,1,1,2329.93,1378.17,33.3335,0.446058,0,0,0.221185,0.975232,120,255,1,'',0),
(2135262,375,0,0,0,1,1,2305.16,1357.46,33.3335,4.13272,0,0,0.879701,-0.475526,120,255,1,'',0),
(2135263,375,0,0,0,1,1,2341.88,1344.28,33.3341,5.63673,0,0,0.31763,-0.948215,120,255,1,'',0),
(2135264,375,0,0,0,1,1,2265.59,1354.08,33.3335,4.40528,0,0,0.80694,-0.590634,120,255,1,'',0),
(2135265,375,0,0,0,1,1,2268.72,1403.56,33.3334,0.24895,0,0,0.124154,0.992263,120,255,1,'',0),
(2135266,375,0,0,0,1,1,2287.5,1419.42,33.3334,0.307855,0,0,0.153321,0.988177,120,255,1,'',0),
(2135267,375,0,0,0,1,1,2304.68,1471.36,33.3334,1.05791,0,0,0.504632,0.863335,120,255,1,'',0),
(2135268,375,0,0,0,1,1,2304.19,1477.65,33.3334,1.05791,0,0,0.504632,0.863335,120,255,1,'',0),
(2135269,375,0,0,0,1,1,2294.94,1477.74,33.3334,6.25332,0,0,0.0149322,-0.999889,120,255,1,'',0),
(2135270,375,0,0,0,1,1,2333.19,1478.31,33.3334,0.468867,0,0,0.232292,0.972646,120,255,1,'',0),
(2135271,375,0,0,0,1,1,2339.75,1485.74,33.435,0.417816,0,0,0.207392,0.978258,120,255,1,'',0),
(2135272,375,0,0,0,1,1,2362.05,1474.94,33.3341,5.06267,0,0,0.57308,-0.8195,120,255,1,'',0),
(2135273,375,0,0,0,1,1,2337,1446.25,33.3341,4.10919,0,0,0.885234,-0.465147,120,255,1,'',0),
(2135274,375,0,0,0,1,1,2337.31,1453.2,33.3341,2.26351,0,0,0.905159,0.425073,120,255,1,'',0),
(2135275,375,0,0,0,1,1,2419.53,1561.74,32.748,3.99817,0,0,0.905159,0.425073,120,255,1,'',0);

-- Create pool(s)
DELETE FROM `pool_template` WHERE `entry`=@POOL;
INSERT INTO `pool_template` (`entry`,`max_limit`,`description`) VALUES (@POOL,@POOLSIZE,@POOLDESC);

-- Add gameobjects to pools
DELETE FROM `pool_gameobject` WHERE FIND_IN_SET (`guid`,@GUID);
INSERT INTO `pool_gameobject` (`guid`,`pool_entry`,`chance`,`description`) VALUES
(45042,@POOL,0,@POOLDESC),
(45043,@POOL,0,@POOLDESC),
(45157,@POOL,0,@POOLDESC),
(45194,@POOL,0,@POOLDESC),
(45195,@POOL,0,@POOLDESC),
(45196,@POOL,0,@POOLDESC),
(45197,@POOL,0,@POOLDESC),
(45198,@POOL,0,@POOLDESC),
(45200,@POOL,0,@POOLDESC),
(45201,@POOL,0,@POOLDESC),
(2135254,@POOL,0,@POOLDESC),
(2135255,@POOL,0,@POOLDESC),
(2135256,@POOL,0,@POOLDESC),
(2135257,@POOL,0,@POOLDESC),
(2135258,@POOL,0,@POOLDESC),
(2135259,@POOL,0,@POOLDESC),
(2135260,@POOL,0,@POOLDESC),
(2135261,@POOL,0,@POOLDESC),
(2135262,@POOL,0,@POOLDESC),
(2135263,@POOL,0,@POOLDESC),
(2135264,@POOL,0,@POOLDESC),
(2135265,@POOL,0,@POOLDESC),
(2135266,@POOL,0,@POOLDESC),
(2135267,@POOL,0,@POOLDESC),
(2135268,@POOL,0,@POOLDESC),
(2135269,@POOL,0,@POOLDESC),
(2135270,@POOL,0,@POOLDESC),
(2135271,@POOL,0,@POOLDESC),
(2135272,@POOL,0,@POOLDESC),
(2135273,@POOL,0,@POOLDESC),
(2135274,@POOL,0,@POOLDESC),
(2135275,@POOL,0,@POOLDESC);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
