-- DB update 2019_07_15_00 -> 2019_07_17_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_07_15_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_07_15_00 2019_07_17_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1562236589279335600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1562236589279335600');

DELETE FROM `game_event_gameobject` WHERE `guid` IN (78033,78066,78105,78106,78107,78109,78111,78113,78117,78120);
INSERT INTO `game_event_gameobject` (`eventEntry`, `guid`)
VALUES
(12,78033),
(12,78066),
(12,78105),
(12,78106),
(12,78107),
(12,78109),
(12,78111),
(12,78113),
(12,78117),
(12,78120);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
