-- DB update 2019_05_11_01 -> 2019_05_11_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_05_11_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_05_11_01 2019_05_11_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1557397608779068000'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) <PERSON><PERSON>UES ('1557397608779068000');

-- <PERSON><PERSON><PERSON> and Blighted Elk creatures 
DELETE FROM `creature` WHERE `guid` IN(97981, 52318, 119440, 119499, 97934, 119442, 97955, 97973, 119466, 119430, 97948, 119436, 119494, 97976, 97946, 97947, 97950, 119431, 119475, 119441, 97974, 119496, 119462, 119461, 97969, 97959, 97958, 119497, 119455, 97971, 119493, 119433, 97951, 119439, 119437, 119438, 97966, 119439, 97965, 97964, 119468, 97963, 119465, 97952, 119432) AND `id` IN(26643, 26616);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
