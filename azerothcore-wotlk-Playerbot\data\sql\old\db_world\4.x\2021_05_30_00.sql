-- DB update 2021_05_29_00 -> 2021_05_30_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_29_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_29_00 2021_05_30_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1619539350697557200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1619539350697557200');

DELETE FROM `creature_template_resistance` WHERE `School`=1 AND `<PERSON>reatureID` IN (157,329,521,764,765,766,1501,1502,1812,1813,2359,2745,2761,2776,2794,2887,2919,3516,
3568,3630,3631,3950,4028,4029,4030,4034,4423,4535,4661,5056,5291,
5317,5320,5349,5356,5481,5485,5756,5761,5806,5834,5853,5881,5890,6074,6129,6220,6239,6492,6498,6499,6500,6550,6748,7031,7032,7104,7132,7226,7266,
7768,7780,8211,8213,8281,8303,8400,8441,8667,8905,8906,8911,8981,8982,9017,9025,9026,9377,
9397,9453,9598,10077,10442,10641,10882,10955,11357,11440,11441,11442,11443,11444,11480,11483,11484,11489,11491,11576,11577,11578,
11669,11744,11745,11783,11784,11862,11871,13278,13279,13282,13285,13322,13456,13656,13696,13736,14025,14232,14234,14362,14399,14400,
14454,14458,14460,14461,14464,15339,17359);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
