-- DB update 2022_01_04_04 -> 2022_01_04_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_04_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_04_04 2022_01_04_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1640901414425887700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1640901414425887700');
-- Pathing for 49855 Ancient Protector Entry: 2041
-- 0x2044EC002001FE4000001F00004B281D .go xyz 9740.571 930.2613 1295.0767
SET @NPC := 49855;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=9740.571,`position_y`=930.2613,`position_z`=1295.0767 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,9740.571,930.2613,1295.0767,0,0,0,0,100,0),
(@PATH,2,9751.314,922.57074,1296.2407,0,0,0,0,100,0),
(@PATH,3,9762.808,915.2426,1297.0116,0,0,0,0,100,0),
(@PATH,4,9775.641,909.7506,1298.0605,0,0,0,0,100,0),
(@PATH,5,9797.529,907.546,1298.3296,0,0,0,0,100,0),
(@PATH,6,9815.451,907.81866,1301.7942,0,0,0,0,100,0),
(@PATH,7,9830.997,910.7991,1304.5513,0,0,0,0,100,0),
(@PATH,8,9842.348,909.1058,1305.5488,0,0,0,0,100,0),
(@PATH,9,9851.48,904.05054,1306.2025,0,0,0,0,100,0),
(@PATH,10,9860.84,900.54694,1306.9669,0,0,0,0,100,0),
(@PATH,11,9876.581,896.76935,1308.97,0,0,0,0,100,0),
(@PATH,12,9879.31,887.9422,1308.4781,0,0,0,0,100,0),
(@PATH,13,9878.167,869.97754,1307.147,0,0,0,0,100,0),
(@PATH,14,9876.162,846.7657,1307.2538,0,0,0,0,100,0),
(@PATH,15,9878.985,862.76917,1307.2538,0,0,0,0,100,0),
(@PATH,16,9881.287,874.13165,1307.1306,0,0,0,0,100,0),
(@PATH,17,9886.876,890.9596,1307.4216,0,0,0,0,100,0),
(@PATH,18,9886.917,901.6405,1307.5547,0,0,0,0,100,0),
(@PATH,19,9885.125,910.7803,1307.5472,0,0,0,0,100,0),
(@PATH,20,9881.225,920.6048,1307.5472,0,0,0,0,100,0),
(@PATH,21,9877.249,928.8137,1307.8824,0,0,0,0,100,0),
(@PATH,22,9869.623,940.4069,1307.502,0,0,0,0,100,0),
(@PATH,23,9861.668,951.5018,1306.664,0,0,0,0,100,0),
(@PATH,24,9853.609,964.5092,1305.9897,0,0,0,0,100,0),
(@PATH,25,9848.224,980.79083,1305.2173,0,0,0,0,100,0),
(@PATH,26,9846.4,995.74457,1305.4417,0,0,0,0,100,0),
(@PATH,27,9845.461,1011.1368,1305.5576,0,0,0,0,100,0),
(@PATH,28,9845.183,1025.0865,1305.0756,0,0,0,0,100,0),
(@PATH,29,9846.616,1035.3206,1304.952,0,0,0,0,100,0),
(@PATH,30,9849.457,1047.5076,1305.202,0,0,0,0,100,0),
(@PATH,31,9854.014,1061.109,1306.6183,0,0,0,0,100,0),
(@PATH,32,9849.858,1047.8787,1305.202,0,0,0,0,100,0),
(@PATH,33,9847.079,1035.6018,1304.952,0,0,0,0,100,0),
(@PATH,34,9830.054,1021.5452,1308.0634,0,0,0,0,100,0),
(@PATH,35,9819.646,1017.9954,1304.4775,0,0,0,0,100,0),
(@PATH,36,9807.053,1015.4848,1303.7786,0,0,0,0,100,0),
(@PATH,37,9798.432,1014.7752,1301.6472,0,0,0,0,100,0),
(@PATH,38,9792.107,1011.0315,1299.3912,0,0,0,0,100,0),
(@PATH,39,9783.028,1005.5374,1299.1001,0,0,0,0,100,0),
(@PATH,40,9770.062,986.4863,1296.7517,0,0,0,0,100,0),
(@PATH,41,9755.872,967.0536,1293.4655,0,0,0,0,100,0),
(@PATH,42,9748.623,951.8928,1293.5898,0,0,0,0,100,0),
(@PATH,43,9743.74,942.14435,1293.872,0,0,0,0,100,0);
-- Pathing for 49845 Teldrassil Sentinel  Entry: 3571
-- 0x2044EC0020037CC000001F0000CB281B .go xyz 9766.8545 923.6628 1298.7427
SET @NPC := 49845;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=9766.8545,`position_y`=923.6628,`position_z`=1298.7427 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,9766.8545,923.6628,1298.7427,0,0,0,0,100,0),
(@PATH,2,9767.08,926.65344,1299.7211,0,0,0,0,100,0),
(@PATH,3,9767.567,931.50574,1301.5139,0,0,0,0,100,0),
(@PATH,4,9767.894,937.83234,1303.9685,0,0,0,0,100,0),
(@PATH,5,9770.466,943.25195,1306.0593,0,0,0,0,100,0),
(@PATH,6,9775.844,947.5946,1306.6843,0,0,0,0,100,0),
(@PATH,7,9779.382,948.08606,1307.727,0,0,0,0,100,0),
(@PATH,8,9821.772,959.40314,1308.7938,0,0,0,0,100,0),
(@PATH,9,9841.023,965.9455,1307.5565,0,0,0,0,100,0),
(@PATH,10,9821.772,959.40314,1308.7938,0,0,0,0,100,0),
(@PATH,11,9779.382,948.08606,1307.727,0,0,0,0,100,0),
(@PATH,12,9775.844,947.5946,1306.6843,0,0,0,0,100,0),
(@PATH,13,9770.48,943.2821,1306.0859,0,0,0,0,100,0),
(@PATH,14,9767.908,937.8623,1304.0593,0,0,0,0,100,0),
(@PATH,15,9767.567,931.50574,1301.5139,0,0,0,0,100,0),
(@PATH,16,9767.08,926.65344,1299.7211,0,0,0,0,100,0);
-- Pathing for 46394 Moon Priestess Amara + Pack Entry: 2151 (Pack GUID/Entry 46414/10604, 46416/10606)
-- 0x2044EC00200219C000001F00004BCD49 .go xyz 9852.3955 972.15344 1305.4957
SET @NPC := 46394;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=9852.3955,`position_y`=972.15344,`position_z`=1305.4957 WHERE `guid`=@NPC;
UPDATE `creature` SET `position_x`=9852.3955,`position_y`=972.15344,`position_z`=1305.4957 WHERE `guid` IN (46414, 46416); -- Pack start position looks slightly less ackward
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,6080,0,0,0,0, ''); -- Keep Mount 6080 and byte values at 0
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,9852.3955,972.15344,1305.4957,0,0,0,0,100,0),
(@PATH,2,9847.123,989.14954,1305.4417,0,0,0,0,100,0),
(@PATH,3,9846.506,1012.964,1305.5756,0,0,0,0,100,0),
(@PATH,4,9846.332,1037.0192,1304.952,0,0,0,0,100,0),
(@PATH,5,9855.715,1065.145,1307.0819,0,0,0,0,100,0),
(@PATH,6,9875.571,1085.3315,1308.1165,0,0,0,0,100,0),
(@PATH,7,9885.151,1115.7675,1307.9541,0,0,0,0,100,0),
(@PATH,8,9904.472,1131.4282,1307.9814,0,0,0,0,100,0),
(@PATH,9,9915.528,1149.1774,1307.9814,0,0,0,0,100,0),
(@PATH,10,9917.77,1168.7291,1307.9814,0,0,0,0,100,0),
(@PATH,11,9914.973,1187.4163,1308.3931,0,0,0,0,100,0),
(@PATH,12,9908.051,1201.9841,1308.7415,0,0,0,0,100,0),
(@PATH,13,9911.591,1218.5352,1307.8665,0,0,0,0,100,0),
(@PATH,14,9927.637,1240.7294,1307.899,0,0,0,0,100,0),
(@PATH,15,9946.544,1246.6996,1307.7858,0,0,0,0,100,0),
(@PATH,16,9979.224,1242.9996,1308.0115,0,0,0,0,100,0),
(@PATH,17,9992.099,1246.3619,1307.8583,0,0,0,0,100,0),
(@PATH,18,10003.928,1258.8636,1307.9814,0,0,0,0,100,0),
(@PATH,19,9989.489,1244.7538,1307.8583,0,0,0,0,100,0),
(@PATH,20,9970.009,1243.0006,1307.8583,0,0,0,0,100,0),
(@PATH,21,9945.0625,1246.7778,1307.86,0,0,0,0,100,0),
(@PATH,22,9926.724,1240.5552,1307.899,0,0,0,0,100,0),
(@PATH,23,9915.2705,1225.2837,1307.8665,0,0,0,0,100,0),
(@PATH,24,9909.258,1212.1692,1308.1165,0,0,0,0,100,0),
(@PATH,25,9909.906,1198.4916,1308.7302,0,0,0,0,100,0),
(@PATH,26,9918.565,1171.1912,1307.9814,0,0,0,0,100,0),
(@PATH,27,9916.184,1154.4274,1307.9814,0,0,0,0,100,0),
(@PATH,28,9911.298,1140.1096,1307.9808,0,0,0,0,100,0),
(@PATH,29,9900.599,1128.3715,1307.9814,0,0,0,0,100,0),
(@PATH,30,9882.727,1112.5925,1307.9644,0,0,0,0,100,0),
(@PATH,31,9880.341,1096.326,1308.1165,0,0,0,0,100,0),
(@PATH,32,9870.386,1078.8763,1308.1165,0,0,0,0,100,0),
(@PATH,33,9857.499,1067.3463,1307.4017,0,0,0,0,100,0),
(@PATH,34,9846.153,1039.0817,1304.952,0,0,0,0,100,0),
(@PATH,35,9846.939,991.32574,1305.4417,0,0,0,0,100,0);
/* Fix the Wildlife immediately surrounding Dolanaar */
DELETE FROM `creature` WHERE `guid` IN (46465, 46439, 47917, 47932, 47927, 47916, 47931, 47913);
DELETE FROM `creature_addon` WHERE `guid` IN (46465, 47927, 47932);
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `wander_distance`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`) VALUES
/* Deer Entry 883 */
/* Deer 46465 was extra and not re-added */
(46439, 883, 1, 0, 0, 1, 1, 0, 0, 9813.695, 1051.731, 1303.1115, 5.677378654479980468, 420, 34.73601, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 9813.695 1051.731 1303.1115
/* Rabbit Entry 721 */
/* Rabbits 47932, 47927 were extra and not re-added */ 
(47917, 721, 1, 0, 0, 1, 1, 0, 0, 9721.547, 982.2083, 1295.0421, 0.852224707603454589, 420, 2.278661, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 9721.547 982.2083 1295.0421
(47916, 721, 1, 0, 0, 1, 1, 0, 0, 9766.002, 1003.5132, 1298.7433, 3.922023296356201171, 420, 2.229228, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 9766.002 1003.5132 1298.7433
(47913, 721, 1, 0, 0, 1, 1, 0, 0, 9663.498, 996.78253, 1290.7375, 2.356194496154785156, 420, 2.236296, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz 9663.498 996.78253 1290.7375
(47931, 721, 1, 0, 0, 1, 1, 0, 0, 9747.966, 879.4171, 1296.1412, 3.358324289321899414, 420, 2.812372, 0, 1, 0, 1, 0, 0, 0, '', 0); -- .go xyz 9747.966 879.4171 1296.1412

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_04_05' WHERE sql_rev = '1640901414425887700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
