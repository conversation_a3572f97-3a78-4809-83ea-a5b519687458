-- DB update 2021_02_14_02 -> 2021_02_14_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_02_14_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_02_14_02 2021_02_14_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1612957477367642592'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1612957477367642592');

-- Lower respawn rate for Tirisfal Pumpkin

UPDATE `gameobject` SET `spawntimesecs`=300 WHERE `guid` IN (45042, 45043, 45157, 45194, 45195, 45196, 45197, 45198, 45200, 45201);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
