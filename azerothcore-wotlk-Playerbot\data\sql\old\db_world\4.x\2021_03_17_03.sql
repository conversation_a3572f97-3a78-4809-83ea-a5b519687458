-- DB update 2021_03_17_02 -> 2021_03_17_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_17_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_17_02 2021_03_17_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1615833271064623900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1615833271064623900');

DELETE FROM `warden_checks` WHERE `id` IN (88, 134, 261, 402, 437, 777);
INSERT INTO `warden_checks` (`id`, `type`, `data`, `str`, `address`, `length`, `result`, `comment`) VALUES
(88,191,'29EC91C4D87891FECDED381CE65A86A259F00DD788833E4D','',174688,37,'',NULL),
(134,191,'5803CA69E5B7F1DF08D95219894D75F52EDB1AD429E562D9','',174688,37,'',NULL),
(261,191,'FB13A8360C8E23B83ED7309625A7EFBAEE7DCF737068C5A6','',174688,37,'',NULL),
(402,243,'','',4609675,5,'5E5DC20800','Loggin Result'),
(437,243,'','',11287980,8,'04000000903C9F00','Login State - (May be false positive)'),
(777,191,'AD5A8CBF55EC436DA968EE0B9744C93F65D9E0D6E3C1B136','',174688,37,'',NULL);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
