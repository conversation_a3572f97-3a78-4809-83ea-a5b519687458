-- DB update 2021_06_02_11 -> 2021_06_02_12
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_02_11';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_02_11 2021_06_02_12 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1622133343331446367'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1622133343331446367');

CREATE TEMPORARY TABLE `herb_varieties` (
    `normal` MEDIUMINT(8) UNSIGNED, 
    `special` MEDIUMINT(8) UNSIGNED, -- drops Root Samples
    PRIMARY KEY (`normal`, `special`) 
);

INSERT INTO `herb_varieties` VALUES
(1617, 3725), -- Silverleaf
(1618, 3724), -- Peacebloom
(1619, 3726), -- Earthroot
(1620, 3727), -- Mageroyal
(1621, 3729), -- Briarthorn
(1622, 3730); -- Bruiseweed

-- Turn all 'special' herb nodes into 'normal' nodes
UPDATE `gameobject` g, `herb_varieties` hv
SET g.id = hv.normal
WHERE g.id = hv.special;

-- Only Barrens herb nodes should be the 'special' variety
UPDATE `gameobject` g, `herb_varieties` hv
SET g.id = hv.special
WHERE g.id = hv.normal
AND g.guid IN (2928,2859,2857,2836,2835,2826,2825,2824,2822,2817,2814,2807,2789,2865,2880,2907,2906,2905,2902,2898,2893,2887,2886,2885,2884,2883,2881,2783,2781,2732,2729,2728,2725,2724,2723,2722,2719,2717,2716,2699,2695,2737,2743,2772,2770,2767,2766,2763,2762,2760,2758,2756,2754,2751,2747,2691,2930,3452,3303,3276,3275,3274,3272,3271,3270,3262,3261,3260,3245,3226,3328,3329,3451,3410,3409,3406,3376,3375,3368,3361,3352,3351,3350,3342,3201,3157,3056,3049,3047,3032,3028,3015,3006,2971,2955,2954,2952,2951,3058,3098,3143,3135,3134,3107,3106,3105,3104,3103,3102,3101,3100,3099,2950,2487,2446,2445,2436,2434,2433,2430,2429,2427,2426,2424,2423,2419,2447,2450,2486,2483,2482,2475,2472,2471,2470,2464,2463,2462,2461,2455,2406,2405,2383,2382,2378,2377,2375,2369,2364,2363,2362,2361,2358,2355,2384,2385,2401,2400,2397,2395,2393,2392,2391,2390,2389,2388,2387,2386,2351,2488,2678,2611,2610,2599,2591,2590,2574,2569,2567,2565,2564,2562,2559,2615,2620,2677,2668,2667,2659,2652,2648,2646,2644,2643,2634,2625,2621,2554,2551,2512,2509,2508,2507,2506,2504,2503,2501,2496,2495,2494,2492,2513,2515,2549,2542,2539,2538,2537,2536,2535,2534,2528,2523,2522,2520,2489,3453,35024,14488,14487,14486,14485,14482,14481,14480,14479,14478,14477,14476,14475,14489,30205,33557,33553,32519,32017,32015,32014,32011,31978,31975,31974,30894,30889,14474,14473,14451,14450,14302,14301,14298,14297,14236,14204,14203,14202,14200,14199,14452,14453,14472,14471,14470,14468,14467,14464,14462,14460,14458,14457,14456,14454,14187,63421,87484,86381,86380,86379,86378,86377,86376,86375,86374,86373,86372,86371,86370,87283,87284,87296,87295,87294,87293,87292,87291,87290,87289,87288,87287,87286,87285,86369,86368,86353,86352,86351,65122,65121,63430,63429,63428,63427,63426,63425,63423,86354,86355,86367,86366,86365,86364,86363,86362,86361,86360,86359,86358,86357,86356,63422,13563,13546,13545,3862,3854,3852,3827,3813,3812,3793,3739,3736,3735,13547,13549,13562,13561,13560,13558,13557,13556,13555,13554,13553,13552,13551,13550,3734,3708,3524,3505,3498,3497,3496,3495,3494,3478,3469,3465,3464,3458,3538,3549,3703,3694,3693,3692,3667,3621,3595,3591,3579,3575,3574,3558,3455,13564,14184,13691,13690,13688,13686,13685,13675,13674,13673,13639,13629,13628,13624,13692,13693,14008,13969,13821,13820,13701,13700,13699,13698,13697,13696,13695,13694,13622,13620,13583,13582,13581,13580,13579,13576,13575,13572,13570,13568,13567,13566,13584,13587,13619,13605,13602,13601,13600,13599,13598,13597,13595,13591,13590,13588,13565,1756,1661,1649,1644,1642,1634,1633,1630,1622,1606,1594,1592,1584,1662,1663,1755,1742,1737,1730,1714,1704,1702,1693,1677,1668,1667,1665,1575,1573,1512,1511,1509,1506,1505,1490,1485,1481,1480,1475,1471,1470,1513,1514,1567,1563,1560,1559,1550,1544,1539,1538,1536,1528,1524,1522,1464,1757,1908,1859,1857,1856,1855,1854,1853,1852,1846,1845,1844,1843,1842,1861,1864,1907,1905,1903,1902,1901,1900,1899,1898,1897,1896,1868,1866,1837,1833,1793,1792,1787,1783,1782,1781,1779,1776,1767,1765,1764,1761,1796,1797,1830,1823,1816,1815,1814,1813,1809,1808,1807,1804,1802,1798,1759,935,852,847,824,812,795,785,782,775,731,730,729,723,878,879,928,927,918,913,910,909,908,907,906,905,904,892,705,700,585,562,542,533,532,520,507,489,488,484,482,473,589,593,696,689,681,671,659,650,632,630,620,606,605,604,462,945,1459,1373,1372,1355,1346,1258,1255,1234,1218,1193,1170,1169,1168,1375,1376,1440,1434,1429,1419,1414,1413,1412,1411,1410,1404,1398,1395,1166,1146,1039,1034,1026,1013,1011,1004,1002,998,994,983,950,947,1040,1051,1138,1131,1130,1107,1106,1100,1089,1087,1086,1085,1082,1078,946,1911,2229,2205,2204,2203,2202,2201,2200,2199,2198,2196,2194,2193,2192,2206,2207,2228,2227,2226,2225,2223,2222,2220,2219,2213,2212,2211,2209,2191,2188,2165,2162,2161,2160,2159,2156,2154,2153,2152,2151,2147,2146,2166,2168,2186,2183,2182,2181,2180,2179,2178,2177,2176,2174,2171,2170,2145,2230,2349,2315,2314,2313,2312,2311,2310,2308,2304,2297,2296,2293,2292,2317,2318,2348,2347,2345,2344,2343,2341,2340,2339,2337,2334,2333,2328,2291,2290,2262,2261,2259,2258,2257,2253,2247,2246,2245,2234,2233,2232,2263,2264,2289,2287,2285,2281,2280,2277,2274,2272,2271,2270,2268,2267,2231,2016,1988,1987,1986,1984,1983,1980,1979,1978,1977,1975,1972,1968,1989,1990,2009,2007,2006,2005,2004,2003,1999,1998,1997,1995,1992,1991,1967,1965,1934,1932,1931,1930,1928,1926,1925,1921,1920,1919,1918,1917,1935,1936,1963,1961,1960,1959,1958,1957,1950,1946,1945,1944,1943,1942,1913,2017,2144,2107,2106,2104,2103,2099,2098,2095,2094,2092,2091,2089,2088,2108,2109,2143,2142,2141,2139,2138,2136,2135,2130,2128,2125,2123,2122,2085,2084,2060,2050,2041,2040,2030,2029,2028,2023,2022,2021,2020,2019,2061,2062,2082,2080,2079,2078,2072,2071,2069,2068,2067,2066,2064,2063,2018);

-- Remove Root Sample drops from normal herb varieties
DELETE FROM `gameobject_loot_template`
WHERE `Item` = 5056
AND `Entry` IN (
    SELECT `Data1` FROM `herb_varieties` hv JOIN `gameobject_template` gt ON hv.normal = gt.Entry 
);

-- Delete before insert
DELETE FROM `gameobject_loot_template` 
WHERE (`Entry`, `Item`) IN ((2514, 2452), (2515, 2452), (2515, 5056));
-- Add all missing entries
INSERT INTO `gameobject_loot_template` VALUES
(2514, 2452, 0, 20, 0, 1, 0, 1, 2, 'Mageroyal - Swiftthistle'),
(2515, 2452, 0, 40, 0, 1, 0, 1, 2, 'Briarthorn - Swiftthistle'),
(2515, 5056, 0, 100, 1, 1, 0, 1, 1, 'Briarthorn - Root Sample');

-- Adjust mincount and maxcount where necessary
UPDATE `gameobject_loot_template`
SET `MinCount` = 1, `MaxCount` = 3
WHERE `Item` IN (765, 2447, 2449, 785, 2450, 2453)
AND `Entry` IN (
    SELECT `Data1` FROM `herb_varieties` hv JOIN `gameobject_template` gt ON hv.special = gt.Entry
);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_02_12' WHERE sql_rev = '1622133343331446367';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
