-- DB update 2021_09_01_22 -> 2021_09_01_23
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_01_22';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_01_22 2021_09_01_23 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1630312205613208089'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1630312205613208089');

-- add multiple movements for spindlewebs
UPDATE `creature` SET `MovementType` = 1, `wander_distance` = 5 WHERE `id` = 16350 AND `guid` IN (82212, 82255, 82461, 82463, 82467, 82468, 82471, 82472, 82475, 82476, 82719);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_01_23' WHERE sql_rev = '1630312205613208089';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
