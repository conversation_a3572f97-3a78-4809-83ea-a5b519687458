-- DB update 2021_10_10_20 -> 2021_10_10_21
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_20';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_20 2021_10_10_21 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633536347071508400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633536347071508400');

DELETE FROM `creature_loot_template` WHERE `item`=785 AND `entry` NOT IN (764,765,766,832,1953,1954,1955,1956,2022,2025,2027,2029,2030,2156,2157,2166,3535,3638,
3780,3781,3782,3783,3784,3834,3844,3917,3919,3928,3931,4020,4021,4028,4029,4030,4541,5055,5354,5761,5775,5780,5781,6218,6219,7584,10641,12220,12223,13141,13142,
14424,14433,14448,15635,15636,15637,15810,17352,17353);

UPDATE `creature_template` SET `lootid`=0 WHERE `entry` IN (10321,7957);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_21' WHERE sql_rev = '1633536347071508400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
