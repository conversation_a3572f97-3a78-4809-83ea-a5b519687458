-- DB update 2022_01_15_01 -> 2022_01_15_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_15_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_15_01 2022_01_15_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1642127857406476700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1642127857406476700');
/* Delete the Old Pools */
DELETE FROM `pool_template` WHERE  `entry`=957;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=957;
/* Some, perhaps even most of these are probbably fine, but lets just redo them anyway--These are the old spawns */
DELETE FROM `gameobject` WHERE `guid` IN (10981, 10983, 10985, 10987, 10989, 10992, 10997, 11004, 11006, 11007, 11009, 11064, 11092, 11093, 11094, 11096, 11120, 11121, 11138, 11139, 11159,11160, 11205, 11208, 11210, 11211, 11212, 11213, 11214, 11215, 11216, 11217, 11218, 11219, 11220, 11221, 11224, 11225, 11232, 11235, 11237, 11314, 11320, 11325, 11326, 11333, 11335, 11338, 11559, 11561, 11565, 11570, 11573, 11586, 11612, 11620, 11621, 11622, 11623, 11626, 11628, 11635,11930, 11932, 11934, 11935, 11936, 11940, 11941, 11947, 11948, 11953, 11954, 11959, 11962, 12064, 12081, 12085, 12086, 12087, 49629, 49630, 49631, 49632, 49633, 49634, 49635, 49636, 49637, 49638, 49639, 49640, 49641, 49642, 49643, 49644, 49645, 49646, 49647, 49648, 49650, 49651, 49652,49653, 49654, 49655, 49656, 49657, 49658, 49659, 49660, 49661, 49662, 49663, 49664, 49665, 49666, 49667, 49668, 49669, 49670, 49671, 49672, 49673, 49674, 49675, 49676, 49677, 49678, 49679, 49680, 49682, 49683, 49684, 86190, 86240, 87069, 87073, 87074, 87077);
/* "Clear" space for inserts--this space should be already free */
DELETE FROM `gameobject` WHERE guid BETWEEN 10229 AND 10494;
INSERT INTO `gameobject` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`, `VerifiedBuild`) VALUES
/* Peacebloom */
(10229, 1618, 1, 141, 188, 1, 1, 9671.11328125, 1024.103271484375, 1285.06982421875, 4.712389945983886718, 0, 0, -0.70710659027099609, 0.707106947898864746, 120, 255, 1, 0), -- 1618 (Area: 188 - Difficulty: 0) .go xyz 9671.11328125 1024.103271484375 1285.06982421875
(10230, 1618, 1, 141, 260, 1, 1, 9807.6064453125, 400.04351806640625, 1308.7264404296875, 3.804818391799926757, 0, 0, -0.94551849365234375, 0.325568377971649169, 120, 255, 1, 0), -- 1618 (Area: 260 - Difficulty: 0) .go xyz 9807.6064453125 400.04351806640625 1308.7264404296875
(10231, 1618, 1, 141, 736, 1, 1, 10018.3701171875, 1455.0125732421875, 1281.1414794921875, 2.338739633560180664, 0, 0, 0.920504570007324218, 0.3907318115234375, 120, 255, 1, 0), -- 1618 (Area: 736 - Difficulty: 0) .go xyz 10018.3701171875 1455.0125732421875 1281.1414794921875
(10232, 1618, 1, 141, 736, 1, 1, 9957.9013671875, 1544.87744140625, 1311.751953125, 0.925023794174194335, 0, 0, 0.446197509765625, 0.894934535026550292, 120, 255, 1, 0), -- 1618 (Area: 736 - Difficulty: 0) .go xyz 9957.9013671875 1544.87744140625 1311.751953125
(10233, 1618, 1, 141, 266, 1, 1, 10272.6845703125, 1584.3392333984375, 1289.1995849609375, 0.349065244197845458, 0, 0, 0.173647880554199218, 0.984807789325714111, 120, 255, 1, 0), -- 1618 (Area: 266 - Difficulty: 0) .go xyz 10272.6845703125 1584.3392333984375 1289.1995849609375
(10234, 1618, 1, 141, 0, 1, 1, 10707.7734375, 1461.23876953125, 1318.5797119140625, 4.118979454040527343, 0, 0, -0.88294696807861328, 0.469472706317901611, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10707.7734375 1461.23876953125 1318.5797119140625
(10235, 1618, 1, 141, 0, 1, 1, 10891.9111328125, 1447.8546142578125, 1302.3328857421875, 0.349065244197845458, 0, 0, 0.173647880554199218, 0.984807789325714111, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10891.9111328125 1447.8546142578125 1302.3328857421875
(10236, 1618, 1, 141, 0, 1, 1, 10963.33203125, 1386.6824951171875, 1317.3980712890625, 6.03883981704711914, 0, 0, -0.12186908721923828, 0.9925462007522583, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10963.33203125 1386.6824951171875 1317.3980712890625
(10237, 1618, 1, 141, 0, 1, 1, 9971.0302734375, 534.07159423828125, 1310.9573974609375, 2.984498262405395507, 0, 0, 0.996916770935058593, 0.078466430306434631, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9971.0302734375 534.07159423828125 1310.9573974609375
(10238, 1618, 1, 141, 259, 1, 1, 9608.7607421875, 731.520751953125, 1266.3760986328125, 1.256635904312133789, 0, 0, 0.587784767150878906, 0.809017360210418701, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9608.7607421875 731.520751953125 1266.3760986328125
(10239, 1618, 1, 141, 186, 1, 1, 9572.130859375, 1011.37188720703125, 1265.125732421875, 0.645771682262420654, 0, 0, 0.317304611206054687, 0.948323667049407958, 120, 255, 1, 0), -- 1618 (Area: 186 - Difficulty: 0) .go xyz 9572.130859375 1011.37188720703125 1265.125732421875
(10240, 1618, 1, 141, 0, 1, 1, 9620.759765625, 409.377105712890625, 1329.7255859375, 4.572763919830322265, 0, 0, -0.75470924377441406, 0.656059443950653076, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9620.759765625 409.377105712890625 1329.7255859375
(10241, 1618, 1, 141, 0, 1, 1, 9751.25, 668.6114501953125, 1295.52978515625, 0.820303261280059814, 0, 0, 0.398748397827148437, 0.917060375213623046, 120, 255, 0, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9751.25 668.6114501953125 1295.52978515625
(10242, 1618, 1, 141, 0, 1, 1, 9969.015625, 874.02471923828125, 1323.3104248046875, 3.490667104721069335, 0, 0, -0.98480701446533203, 0.173652306199073791, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9969.015625 874.02471923828125 1323.3104248046875
(10243, 1618, 1, 141, 260, 1, 1, 9993.0361328125, 347.29486083984375, 1316.0609130859375, 3.508116960525512695, 0, 0, -0.98325443267822265, 0.182238012552261352, 120, 255, 1, 0), -- 1618 (Area: 260 - Difficulty: 0) .go xyz 9993.0361328125 347.29486083984375 1316.0609130859375
(10244, 1618, 1, 141, 259, 1, 1, 9565.166015625, 999.2802734375, 1265.683837890625, 1.989672422409057617, 0, 0, 0.838669776916503906, 0.544640243053436279, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9565.166015625, 999.2802734375, 1265.683837890625
(10245, 1618, 1, 141, 0, 1, 1, 9713.9921875, 1110.656005859375, 1278.7012939453125, 4.834563255310058593, 0, 0, -0.66261959075927734, 0.748956084251403808, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9713.9921875 1110.656005859375 1278.7012939453125
(10246, 1618, 1, 141, 0, 1, 1, 9712.4970703125, 1217.5220947265625, 1276.0064697265625, 6.248279094696044921, 0, 0, -0.01745223999023437, 0.999847710132598876, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9712.4970703125 1217.5220947265625 1276.0064697265625
(10247, 1618, 1, 141, 0, 1, 1, 9751.36328125, 753.60699462890625, 1298.5269775390625, 0.733038187026977539, 0, 0, 0.358367919921875, 0.933580458164215087, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9751.36328125 753.60699462890625 1298.5269775390625
(10248, 1618, 1, 141, 0, 1, 1, 9794.4580078125, 639.79669189453125, 1298.025634765625, 6.248279094696044921, 0, 0, -0.01745223999023437, 0.999847710132598876, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9794.4580078125 639.79669189453125 1298.025634765625
(10249, 1618, 1, 141, 0, 1, 1, 10276.58203125, 1447.970947265625, 1339.287353515625, 4.031712055206298828, 0, 0, -0.90258502960205078, 0.430511653423309326, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10276.58203125 1447.970947265625 1339.287353515625
(10250, 1618, 1, 141, 0, 1, 1, 10186.6669921875, 1720.8482666015625, 1325.3656005859375, 3.961898565292358398, 0, 0, -0.91705989837646484, 0.398749500513076782, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0)
(10251, 1618, 1, 141, 259, 1, 1, 9629.9716796875, 886.94921875, 1267.634033203125, 2.199114561080932617, 0, 0, 0.8910064697265625, 0.453990638256072998, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9629.9716796875 886.94921875 1267.634033203125
(10252, 1618, 1, 141, 259, 1, 1, 9585.0283203125, 687.4664306640625, 1265.352783203125, 1.291541695594787597, 0, 0, 0.60181427001953125, 0.798636078834533691, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9585.0283203125 687.4664306640625 1265.352783203125
(10253, 1618, 1, 141, 259, 1, 1, 9377.8271484375, 852.03741455078125, 1262.0494384765625, 3.804818391799926757, 0, 0, -0.94551849365234375, 0.325568377971649169, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9377.8271484375 852.03741455078125 1262.0494384765625
(10254, 1618, 1, 141, 259, 1, 1, 9254.4345703125, 881.7796630859375, 1320.56982421875, 3.45575571060180664, 0, 0, -0.98768806457519531, 0.156436234712600708, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9254.4345703125 881.7796630859375 1320.56982421875
(10255, 1618, 1, 141, 259, 1, 1, 9283.94140625, 718.516845703125, 1316.1448974609375, 2.094393253326416015, 0, 0, 0.866024971008300781, 0.50000077486038208, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9283.94140625 718.516845703125 1316.1448974609375
(10256, 1618, 1, 141, 0, 1, 1, 9288.6982421875, 1024.8759765625, 1303.3809814453125, 5.777040958404541015, 0, 0, -0.25037956237792968, 0.968147754669189453, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9288.6982421875 1024.8759765625 1303.3809814453125
(10257, 1618, 1, 141, 0, 1, 1, 9224.158203125, 1283.26611328125, 1308.5594482421875, 3.071766138076782226, 0, 0, 0.999390602111816406, 0.034906134009361267, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9224.158203125 1283.26611328125 1308.5594482421875
(10258, 1618, 1, 141, 0, 1, 1, 9325.935546875, 1137.3082275390625, 1253.567626953125, 0.15707901120185852, 0, 0, 0.078458786010742187, 0.996917366981506347, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9325.935546875 1137.3082275390625 1253.567626953125
(10259, 1618, 1, 141, 259, 1, 1, 9388.5595703125, 961.885986328125, 1281.6497802734375, 6.056293010711669921, 0, 0, -0.11320304870605468, 0.993571877479553222, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9388.5595703125 961.885986328125 1281.6497802734375
(10260, 1618, 1, 141, 0, 1, 1, 9752.4453125, 571.13336181640625, 1301.969482421875, 1.169368624687194824, 0, 0, 0.551936149597167968, 0.833886384963989257, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9752.4453125 571.13336181640625 1301.969482421875
(10261, 1618, 1, 141, 0, 1, 1, 9401.2265625, 599.2208251953125, 1320.75439453125, 4.433136463165283203, 0, 0, -0.79863548278808593, 0.60181504487991333, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9401.2265625 599.2208251953125 1320.75439453125
(10262, 1618, 1, 141, 0, 1, 1, 9858.4462890625, 707.570556640625, 1307.0150146484375, 0.349065244197845458, 0, 0, 0.173647880554199218, 0.984807789325714111, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9858.4462890625 707.570556640625 1307.0150146484375
(10263, 1618, 1, 141, 0, 1, 1, 9972.625, 1105.914794921875, 1325.890625, 0.977383077144622802, 0, 0, 0.469470977783203125, 0.882947921752929687, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9972.625 1105.914794921875 1325.890625
(10264, 1618, 1, 141, 0, 1, 1, 9811.974609375, 1193.6087646484375, 1284.7237548828125, 5.532694816589355468, 0, 0, -0.3665008544921875, 0.93041771650314331, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9811.974609375 1193.6087646484375 1284.7237548828125
(10265, 1618, 1, 141, 0, 1, 1, 9952.220703125, 1302.1639404296875, 1295.13916015625, 4.223697185516357421, 0, 0, -0.85716724395751953, 0.515038192272186279, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9952.220703125 1302.1639404296875 1295.13916015625
(10266, 1618, 1, 141, 0, 1, 1, 9948.92578125, 1773.70947265625, 1333.29345703125, 0.890116631984710693, 0, 0, 0.430510520935058593, 0.902585566043853759, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9948.92578125 1773.70947265625 1333.29345703125
(10267, 1618, 1, 141, 0, 1, 1, 9225.501953125, 1110.92138671875, 1314.6236572265625, 0.994837164878845214, 0, 0, 0.477158546447753906, 0.878817260265350341, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9225.501953125 1110.92138671875 1314.6236572265625
(10268, 1618, 1, 141, 0, 1, 1, 9252.9775390625, 1513.8984375, 1293.7093505859375, 1.902408957481384277, 0, 0, 0.814115524291992187, 0.580702960491180419, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9252.9775390625 1513.8984375 1293.7093505859375
(10269, 1618, 1, 141, 0, 1, 1, 9671.166015625, 573.36602783203125, 1307.384033203125, 1.570795774459838867, 0, 0, 0.707106590270996093, 0.707106947898864746, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9671.166015625 573.36602783203125 1307.384033203125
(10270, 1618, 1, 141, 0, 1, 1, 9733.2666015625, 371.358062744140625, 1317.7779541015625, 3.78736734390258789, 0, 0, -0.94832324981689453, 0.317305892705917358, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9733.2666015625 371.358062744140625 1317.7779541015625
(10271, 1618, 1, 141, 0, 1, 1, 9649.37890625, 791.61956787109375, 1270.9383544921875, 0.296705186367034912, 0, 0, 0.147809028625488281, 0.989015936851501464, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9649.37890625 791.61956787109375 1270.9383544921875
(10272, 1618, 1, 141, 0, 1, 1, 10074.53125, 355.456390380859375, 1323.1656494140625, 3.52557229995727539, 0, 0, -0.98162651062011718, 0.190812408924102783, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10074.53125 355.456390380859375 1323.1656494140625
(10273, 1618, 1, 141, 0, 1, 1, 10074.296875, 478.53521728515625, 1321.9244384765625, 5.916667938232421875, 0, 0, -0.18223476409912109, 0.98325502872467041, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10074.296875 478.53521728515625 1321.9244384765625
(10274, 1618, 1, 141, 260, 1, 1, 9836.166015625, 269.979217529296875, 1325.0338134765625, 0.27925160527229309, 0, 0, 0.139172554016113281, 0.990268170833587646, 120, 255, 1, 0), -- 1618 (Area: 260 - Difficulty: 0) .go xyz 9836.166015625 269.979217529296875 1325.0338134765625
(10275, 1618, 1, 141, 0, 1, 1, 10000.525390625, 729.53778076171875, 1325.133544921875, 4.834563255310058593, 0, 0, -0.66261959075927734, 0.748956084251403808, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10000.525390625 729.53778076171875 1325.133544921875
(10276, 1618, 1, 141, 0, 1, 1, 9957.3955078125, 715.48382568359375, 1317.46435546875, 2.408554315567016601, 0, 0, 0.933580398559570312, 0.358368009328842163, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9957.3955078125 715.48382568359375 1317.46435546875
(10277, 1618, 1, 141, 0, 1, 1, 9722.8037109375, 510.406036376953125, 1309.251220703125, 2.932138919830322265, 0, 0, 0.994521141052246093, 0.104535527527332305, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9722.8037109375 510.406036376953125 1309.251220703125
(10278, 1618, 1, 141, 0, 1, 1, 10027.037109375, 1698.2066650390625, 1327.9859619140625, 0.663223206996917724, 0, 0, 0.325567245483398437, 0.945518851280212402, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10027.037109375 1698.2066650390625 1327.9859619140625
(10279, 1618, 1, 141, 0, 1, 1, 10303.4130859375, 1863.9761962890625, 1326.919921875, 1.343901276588439941, 0, 0, 0.622513771057128906, 0.78260880708694458, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10303.4130859375 1863.9761962890625 1326.919921875
(10280, 1618, 1, 141, 264, 1, 1, 10875.7802734375, 1875.412353515625, 1337.423095703125, 0.506144583225250244, 0, 0, 0.250379562377929687, 0.968147754669189453, 120, 255, 1, 0), -- 1618 (Area: 264 - Difficulty: 0) .go xyz 10875.7802734375 1875.412353515625 1337.423095703125
(10281, 1618, 1, 141, 0, 1, 1, 9519.013671875, 1287.271728515625, 1300.6756591796875, 4.747295856475830078, 0, 0, -0.69465827941894531, 0.719339847564697265, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9519.013671875 1287.271728515625 1300.6756591796875
(10282, 1618, 1, 141, 0, 1, 1, 9589.166015625, 1156.161865234375, 1270.1533203125, 2.338739633560180664, 0, 0, 0.920504570007324218, 0.3907318115234375, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9589.166015625 1156.161865234375 1270.1533203125
(10283, 1618, 1, 141, 0, 1, 1, 9389.2724609375, 1675.056640625, 1301.1221923828125, 5.078907966613769531, 0, 0, -0.56640625, 0.824126183986663818, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9389.2724609375 1675.056640625 1301.1221923828125
(10284, 1618, 1, 141, 0, 1, 1, 9342.0068359375, 1491.8572998046875, 1273.5406494140625, 2.234017848968505859, 0, 0, 0.898793220520019531, 0.438372820615768432, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9342.0068359375 1491.8572998046875 1273.5406494140625
(10285, 1618, 1, 141, 259, 1, 1, 9554.921875, 1119.7664794921875, 1265.2049560546875, 3.45575571060180664, 0, 0, -0.98768806457519531, 0.156436234712600708, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9554.921875 1119.7664794921875 1265.2049560546875
(10286, 1618, 1, 141, 259, 1, 1, 9420.384765625, 1420.450439453125, 1292.663818359375, 3.124123096466064453, 0, 0, 0.99996185302734375, 0.008734640665352344, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9420.384765625 1420.450439453125 1292.663818359375
(10287, 1618, 1, 141, 265, 1, 1, 10652.18359375, 1592.201416015625, 1292.3408203125, 0.314158439636230468, 0, 0, 0.156434059143066406, 0.987688362598419189, 120, 255, 1, 0), -- 1618 (Area: 265 - Difficulty: 0) .go xyz 10652.18359375 1592.201416015625 1292.3408203125
(10288, 1618, 1, 141, 0, 1, 1, 9380.3935546875, 761.618408203125, 1262.3944091796875, 1.884953022003173828, 0, 0, 0.809016227722167968, 0.587786316871643066, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9380.3935546875 761.618408203125 1262.3944091796875
(10289, 1618, 1, 141, 0, 1, 1, 9591.626953125, 1213.8699951171875, 1271.77587890625, 0.052358884364366531, 0, 0, 0.02617645263671875, 0.999657332897186279, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9591.626953125 1213.8699951171875 1271.77587890625
(10290, 1618, 1, 141, 266, 1, 1, 10494.78125, 1564.34619140625, 1302.4080810546875, 3.45575571060180664, 0, 0, -0.98768806457519531, 0.156436234712600708, 120, 255, 1, 0), -- 1618 (Area: 266 - Difficulty: 0) .go xyz 10494.78125 1564.34619140625 1302.4080810546875
(10291, 1618, 1, 141, 266, 1, 1, 10575.39453125, 1527.817626953125, 1314.226318359375, 5.009094715118408203, 0, 0, -0.59482288360595703, 0.80385679006576538, 120, 255, 1, 0), -- 1618 (Area: 266 - Difficulty: 0) .go xyz 10575.39453125 1527.817626953125 1314.226318359375
(10292, 1618, 1, 141, 264, 1, 1, 10698.2021484375, 1725.6705322265625, 1305.6693115234375, 2.757613182067871093, 0, 0, 0.981626510620117187, 0.190812408924102783, 120, 255, 1, 0), -- 1618 (Area: 264 - Difficulty: 0) .go xyz 10698.2021484375 1725.6705322265625 1305.6693115234375
(10293, 1618, 1, 141, 264, 1, 1, 10546.1611328125, 1812.1893310546875, 1315.6636962890625, 1.727874636650085449, 0, 0, 0.760405540466308593, 0.649448513984680175, 120, 255, 1, 0), -- 1618 (Area: 264 - Difficulty: 0) .go xyz 10546.1611328125 1812.1893310546875 1315.6636962890625
(10294, 1618, 1, 141, 0, 1, 1, 10203.029296875, 1883.8984375, 1332.1568603515625, 4.345870018005371093, 0, 0, -0.82412624359130859, 0.566406130790710449, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10203.029296875 1883.8984375 1332.1568603515625
(10295, 1618, 1, 141, 266, 1, 1, 10316.0634765625, 1688.0108642578125, 1309.6285400390625, 2.617989301681518554, 0, 0, 0.965925216674804687, 0.258821308612823486, 120, 255, 1, 0), -- 1618 (Area: 266 - Difficulty: 0) .go xyz 10316.0634765625 1688.0108642578125 1309.6285400390625
(10296, 1618, 1, 141, 0, 1, 1, 9784.025390625, 1750.6673583984375, 1312.5098876953125, 6.178466320037841796, 0, 0, -0.05233573913574218, 0.998629570007324218, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9784.025390625 1750.6673583984375 1312.5098876953125
(10297, 1618, 1, 141, 478, 1, 1, 9484.0146484375, 1774.7125244140625, 1308.9373779296875, 2.879789113998413085, 0, 0, 0.991444587707519531, 0.130528271198272705, 120, 255, 1, 0), -- 1618 (Area: 478 - Difficulty: 0) .go xyz 9484.0146484375 1774.7125244140625 1308.9373779296875
(10298, 1618, 1, 141, 261, 1, 1, 9148.8369140625, 1716.3450927734375, 1320.6187744140625, 4.607671737670898437, 0, 0, -0.74314403533935546, 0.669131457805633544, 120, 255, 1, 0), -- 1618 (Area: 261 - Difficulty: 0) .go xyz 9148.8369140625 1716.3450927734375 1320.6187744140625
(10299, 1618, 1, 141, 261, 1, 1, 9319.453125, 1619.8275146484375, 1292.81591796875, 5.253442287445068359, 0, 0, -0.49242305755615234, 0.870355963706970214, 120, 255, 1, 0), -- 1618 (Area: 261 - Difficulty: 0) .go xyz 9319.453125 1619.8275146484375 1292.81591796875
(10300, 1618, 1, 141, 0, 1, 1, 9452.79296875, 1552.37158203125, 1298.682373046875, 4.258606910705566406, 0, 0, -0.84804725646972656, 0.529920578002929687, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 9452.79296875 1552.37158203125 1298.682373046875
(10301, 1618, 1, 141, 478, 1, 1, 9554.314453125, 1848.1334228515625, 1305.8724365234375, 1.675513744354248046, 0, 0, 0.743144035339355468, 0.669131457805633544, 120, 255, 1, 0), -- 1618 (Area: 478 - Difficulty: 0) .go xyz 9554.314453125 1848.1334228515625 1305.8724365234375
(10302, 1618, 1, 141, 264, 1, 1, 10640.2392578125, 1982.078369140625, 1327.1099853515625, 2.059488296508789062, 0, 0, 0.857167243957519531, 0.515038192272186279, 120, 255, 1, 0), -- 1618 (Area: 264 - Difficulty: 0) .go xyz 10640.2392578125 1982.078369140625 1327.1099853515625
(10303, 1618, 1, 141, 0, 1, 1, 10371.787109375, 1475.0758056640625, 1328.361328125, 4.118979454040527343, 0, 0, -0.88294696807861328, 0.469472706317901611, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10371.787109375 1475.0758056640625 1328.361328125
(10304, 1618, 1, 141, 478, 1, 1, 9714.8935546875, 1848.43994140625, 1300.76806640625, 2.286378860473632812, 0, 0, 0.909960746765136718, 0.414694398641586303, 120, 255, 1, 0), -- 1618 (Area: 478 - Difficulty: 0) .go xyz 9714.8935546875 1848.43994140625 1300.76806640625
(10305, 1618, 1, 141, 259, 1, 1, 9435.7060546875, 883.28765869140625, 1257.541748046875, 5.777040958404541015, 0, 0, -0.25037956237792968, 0.968147754669189453, 120, 255, 1, 0), -- 1618 (Area: 259 - Difficulty: 0) .go xyz 9435.7060546875 883.28765869140625 1257.541748046875
(10306, 1618, 1, 141, 0, 1, 1, 10437.5400390625, 1470.952392578125, 1321.982177734375, 0.191985160112380981, 0, 0, 0.095845222473144531, 0.995396256446838378, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10437.5400390625, 1470.952392578125, 1321.982177734375
(10307, 1618, 1, 141, 0, 1, 1, 10763.5029296875, 1524.3497314453125, 1307.418212890625, 3.22885894775390625, 0, 0, -0.99904823303222656, 0.043619260191917419, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10763.5029296875 1524.3497314453125 1307.418212890625
(10308, 1618, 1, 141, 0, 1, 1, 10833.3896484375, 1563.9302978515625, 1282.4984130859375, 0.488691210746765136, 0, 0, 0.241921424865722656, 0.970295846462249755, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10833.3896484375 1563.9302978515625 1282.4984130859375
(10309, 1618, 1, 141, 266, 1, 1, 10362.046875, 1568.599609375, 1297.3709716796875, 4.48549652099609375, 0, 0, -0.7826080322265625, 0.622514784336090087, 120, 255, 1, 0), -- 1618 (Area: 266 - Difficulty: 0) .go xyz 10362.046875 1568.599609375 1297.3709716796875
(10310, 1618, 1, 141, 0, 1, 1, 10364.103515625, 1886.423583984375, 1324.4443359375, 2.495818138122558593, 0, 0, 0.948323249816894531, 0.317305892705917358, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10364.103515625 1886.423583984375 1324.4443359375
(10311, 1618, 1, 141, 264, 1, 1, 10751.0576171875, 1945.5816650390625, 1336.0291748046875, 3.78736734390258789, 0, 0, -0.94832324981689453, 0.317305892705917358, 120, 255, 1, 0), -- 1618 (Area: 264 - Difficulty: 0) .go xyz 10751.0576171875 1945.5816650390625 1336.0291748046875
(10312, 1618, 1, 141, 265, 1, 1, 10860.474609375, 1691.736083984375, 1301.1016845703125, 2.234017848968505859, 0, 0, 0.898793220520019531, 0.438372820615768432, 120, 255, 1, 0), -- 1618 (Area: 265 - Difficulty: 0) .go xyz 10860.474609375 1691.736083984375 1301.1016845703125
(10313, 1618, 1, 141, 0, 1, 1, 10705.169921875, 1416.0718994140625, 1326.143310546875, 4.694936752319335937, 0, 0, -0.71325016021728515, 0.700909554958343505, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10705.169921875 1416.0718994140625 1326.143310546875
(10314, 1618, 1, 141, 0, 1, 1, 10464.7236328125, 1914.9720458984375, 1320.1695556640625, 4.799657344818115234, 0, 0, -0.67558956146240234, 0.737277925014495849, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10464.7236328125 1914.9720458984375 1320.1695556640625
(10315, 1618, 1, 141, 0, 1, 1, 10587.7900390625, 1479.6971435546875, 1318.1904296875, 2.548179388046264648, 0, 0, 0.956304550170898437, 0.292372345924377441, 120, 255, 1, 0), -- 1618 (Area: 0 - Difficulty: 0) .go xyz 10587.7900390625 1479.6971435546875 1318.1904296875
/* Mageroyal */
(10316, 1620, 1, 141, 141, 1, 1, 9659.95703125, 488.111236572265625, 1308.966552734375, 6.14356088638305664, 0, 0, -0.06975555419921875, 0.997564136981964111, 120, 255, 1, 0), -- 1620 (Area: 141 - Difficulty: 0) .go xyz 9659.95703125 488.111236572265625 1308.966552734375
(10317, 1620, 1, 141, 260, 1, 1, 9916.576171875, 280.483306884765625, 1323.364501953125, 3.630291461944580078, 0, 0, -0.97029495239257812, 0.241925001144409179, 120, 255, 1, 0), -- 1620 (Area: 260 - Difficulty: 0) .go xyz 9916.576171875 280.483306884765625 1323.364501953125
(10318, 1620, 1, 141, 0, 1, 1, 9711.4755859375, 1167.0989990234375, 1272.271240234375, 5.777040958404541015, 0, 0, -0.25037956237792968, 0.968147754669189453, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 9711.4755859375 1167.0989990234375 1272.271240234375
(10319, 1620, 1, 141, 0, 1, 1, 10301.5439453125, 1411.99609375, 1341.858642578125, 5.305802345275878906, 0, 0, -0.46947097778320312, 0.882947921752929687, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 10301.5439453125 1411.99609375 1341.858642578125
(10320, 1620, 1, 141, 259, 1, 1, 9582.6181640625, 886.09710693359375, 1255.1126708984375, 4.502951622009277343, 0, 0, -0.7771453857421875, 0.629321098327636718, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9582.6181640625 886.09710693359375 1255.1126708984375
(10321, 1620, 1, 141, 259, 1, 1, 9706.318359375, 642.515625, 1296.7720947265625, 0.104719325900077819, 0, 0, 0.052335739135742187, 0.998629570007324218, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9706.318359375 642.515625 1296.7720947265625
(10322, 1620, 1, 141, 0, 1, 1, 9561.7392578125, 531.11846923828125, 1322.4774169921875, 3.857182979583740234, 0, 0, -0.93667125701904296, 0.350209832191467285, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 9561.7392578125 531.11846923828125 1322.4774169921875
(10323, 1620, 1, 141, 0, 1, 1, 9876.361328125, 1270.87353515625, 1294.9664306640625, 0.802850961685180664, 0, 0, 0.390730857849121093, 0.920504987239837646, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 9876.361328125 1270.87353515625 1294.9664306640625,
(10324, 1620, 1, 141, 259, 1, 1, 9359.2119140625, 801.1923828125, 1266.381103515625, 1.902408957481384277, 0, 0, 0.814115524291992187, 0.580702960491180419, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9359.2119140625 801.1923828125 1266.381103515625
(10325, 1620, 1, 141, 0, 1, 1, 10019.4130859375, 483.160491943359375, 1314.035400390625, 2.199114561080932617, 0, 0, 0.8910064697265625, 0.453990638256072998, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 10019.4130859375 483.160491943359375 1314.035400390625
(10326, 1620, 1, 141, 259, 1, 1, 9373.69140625, 599.0340576171875, 1320.14453125, 6.073746204376220703, 0, 0, -0.10452842712402343, 0.994521915912628173, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9373.69140625 599.0340576171875 1320.14453125
(10327, 1620, 1, 141, 259, 1, 1, 9184.6181640625, 1317.0889892578125, 1317.0001220703125, 0.488691210746765136, 0, 0, 0.241921424865722656, 0.970295846462249755, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9184.6181640625 1317.0889892578125 1317.0001220703125
(10328, 1620, 1, 141, 264, 1, 1, 10862.3486328125, 1653.27783203125, 1279.3314208984375, 3.543023586273193359, 0, 0, -0.97992420196533203, 0.199370384216308593, 120, 255, 1, 0), -- 1620 (Area: 264 - Difficulty: 0) .go xyz 10862.3486328125 1653.27783203125 1279.3314208984375
(10329, 1620, 1, 141, 0, 1, 1, 9490.3544921875, 652.11676025390625, 1268.9429931640625, 2.775068521499633789, 0, 0, 0.983254432678222656, 0.182238012552261352, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 9490.3544921875 652.11676025390625 1268.9429931640625
(10330, 1620, 1, 141, 259, 1, 1, 9381.884765625, 1286.79541015625, 1253.2764892578125, 3.874631166458129882, 0, 0, -0.93358039855957031, 0.358368009328842163, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9381.884765625 1286.79541015625 1253.2764892578125
(10331, 1620, 1, 141, 0, 1, 1, 9777.3017578125, 1797.908447265625, 1305.3375244140625, 5.864306926727294921, 0, 0, -0.20791149139404296, 0.978147625923156738, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 9777.3017578125 1797.908447265625 1305.3375244140625
(10332, 1620, 1, 141, 0, 1, 1, 9339.0029296875, 645.5791015625, 1314.521240234375, 6.021387100219726562, 0, 0, -0.13052558898925781, 0.991444945335388183, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 9339.0029296875 645.5791015625 1314.521240234375
(10333, 1620, 1, 141, 264, 1, 1, 10673.4814453125, 1686.3297119140625, 1291.7044677734375, 2.530723094940185546, 0, 0, 0.953716278076171875, 0.300707906484603881, 120, 255, 0, 0), -- 1620 (Area: 264 - Difficulty: 0) .go xyz 10673.4814453125 1686.3297119140625 1291.7044677734375
(10334, 1620, 1, 141, 0, 1, 1, 10033.7197265625, 369.977325439453125, 1315.2420654296875, 5.305802345275878906, 0, 0, -0.46947097778320312, 0.882947921752929687, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 10033.7197265625 369.977325439453125 1315.2420654296875
(10335, 1620, 1, 141, 261, 1, 1, 9249.2373046875, 1688.65869140625, 1313.279541015625, 2.530723094940185546, 0, 0, 0.953716278076171875, 0.300707906484603881, 120, 255, 1, 0), -- 1620 (Area: 261 - Difficulty: 0) .go xyz 9249.2373046875 1688.65869140625 1313.279541015625
(10336, 1620, 1, 141, 478, 1, 1, 9745.4443359375, 1840.7528076171875, 1301.9996337890625, 2.129300594329833984, 0, 0, 0.874619483947753906, 0.484810054302215576, 120, 255, 1, 0), -- 1620 (Area: 478 - Difficulty: 0) .go xyz 9745.4443359375 1840.7528076171875 1301.9996337890625
(10337, 1620, 1, 141, 736, 1, 1, 10033.7119140625, 1481.7578125, 1282.495361328125, 5.323255538940429687, 0, 0, -0.46174812316894531, 0.887011110782623291, 120, 255, 1, 0), -- 1620 (Area: 736 - Difficulty: 0) .go xyz 10033.7119140625 1481.7578125 1282.495361328125
(10338, 1620, 1, 141, 266, 1, 1, 10341.9853515625, 1611.627197265625, 1297.23095703125, 0.191985160112380981, 0, 0, 0.095845222473144531, 0.995396256446838378, 120, 255, 1, 0), -- 1620 (Area: 266 - Difficulty: 0) .go xyz 10341.9853515625 1611.627197265625 1297.23095703125
(10339, 1620, 1, 141, 0, 1, 1, 9406.2314453125, 1474.9085693359375, 1283.5806884765625, 0.349065244197845458, 0, 0, 0.173647880554199218, 0.984807789325714111, 120, 255, 1, 0), -- 1620 (Area: 0 - Difficulty: 0) .go xyz 9406.2314453125 1474.9085693359375 1283.5806884765625
(10340, 1620, 1, 141, 259, 1, 1, 9588.15625, 1090.762451171875, 1267.09326171875, 0.942476630210876464, 0, 0, 0.453989982604980468, 0.891006767749786376, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9588.15625 1090.762451171875 1267.09326171875
(10341, 1620, 1, 141, 259, 1, 1, 9438.2099609375, 1088.396728515625, 1251.7989501953125, 5.270895957946777343, 0, 0, -0.48480892181396484, 0.87462007999420166, 120, 255, 1, 0), -- 1620 (Area: 259 - Difficulty: 0) .go xyz 9438.2099609375 1088.396728515625 1251.7989501953125
/* Silverleaf */
(10342, 1617, 1, 141, 0, 1, 1, 9665.755859375, 637.53948974609375, 1303.6201171875, 3.595378875732421875, 0, 0, -0.97437000274658203, 0.224951311945915222, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9665.755859375 637.53948974609375 1303.6201171875
(10343, 1617, 1, 141, 0, 1, 1, 9861.1220703125, 373.45660400390625, 1313.1622314453125, 0.663223206996917724, 0, 0, 0.325567245483398437, 0.945518851280212402, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9861.1220703125 373.45660400390625 1313.1622314453125
(10344, 1617, 1, 141, 0, 1, 1, 10024.9580078125, 1116.0640869140625, 1327.8892822265625, 3.612837791442871093, 0, 0, -0.97236919403076171, 0.233448356389999389, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10024.9580078125 1116.0640869140625 1327.8892822265625
(10345, 1617, 1, 141, 266, 1, 1, 10285.75390625, 1553.265380859375, 1302.6097412109375, 3.071766138076782226, 0, 0, 0.999390602111816406, 0.034906134009361267, 120, 255, 1, 0), -- 1617 (Area: 266 - Difficulty: 0) .go xyz 10285.75390625 1553.265380859375 1302.6097412109375
(10346, 1617, 1, 141, 0, 1, 1, 10890.8173828125, 1502.1102294921875, 1289.8687744140625, 3.194002151489257812, 0, 0, -0.99965667724609375, 0.026201646775007247, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10890.8173828125 1502.1102294921875 1289.8687744140625
(10347, 1617, 1, 141, 0, 1, 1, 10984.529296875, 1349.239990234375, 1336.566650390625, 5.724681377410888671, 0, 0, -0.27563667297363281, 0.961261868476867675, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10984.529296875 1349.239990234375 1336.566650390625,
(10348, 1617, 1, 141, 0, 1, 1, 10924.9228515625, 1360.7318115234375, 1320.4771728515625, 5.515241622924804687, 0, 0, -0.37460613250732421, 0.927184045314788818, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10924.9228515625 1360.7318115234375 1320.4771728515625
(10349, 1617, 1, 141, 0, 1, 1, 9717.3251953125, 701.681640625, 1296.6136474609375, 2.408554315567016601, 0, 0, 0.933580398559570312, 0.358368009328842163, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9717.3251953125 701.681640625 1296.6136474609375
(10350, 1617, 1, 141, 186, 1, 1, 9789.7236328125, 1101.5103759765625, 1291.6488037109375, 1.623155713081359863, 0, 0, 0.725374221801757812, 0.688354730606079101, 120, 255, 1, 0), -- 1617 (Area: 186 - Difficulty: 0) .go xyz 9789.7236328125 1101.5103759765625 1291.6488037109375
(10351, 1617, 1, 141, 0, 1, 1, 10009.8876953125, 765.82806396484375, 1335.188720703125, 1.32644820213317871, 0, 0, 0.615660667419433593, 0.788011372089385986, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10009.8876953125 765.82806396484375 1335.188720703125
(10352, 1617, 1, 141, 260, 1, 1, 9857.97265625, 267.933319091796875, 1326.4664306640625, 1.919861555099487304, 0, 0, 0.819151878356933593, 0.573576688766479492, 120, 255, 1, 0), -- 1617 (Area: 260 - Difficulty: 0) .go xyz 9857.97265625 267.933319091796875 1326.4664306640625
(10353, 1617, 1, 141, 260, 1, 1, 10005.224609375, 391.58441162109375, 1311.9559326171875, 1.884953022003173828, 0, 0, 0.809016227722167968, 0.587786316871643066, 120, 255, 1, 0), -- 1617 (Area: 260 - Difficulty: 0) .go xyz 10005.224609375 391.58441162109375 1311.9559326171875
(10354, 1617, 1, 141, 0, 1, 1, 9974.9619140625, 939.73114013671875, 1325.986328125, 3.281238555908203125, 0, 0, -0.99756336212158203, 0.069766148924827575, 120, 255, 0, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9974.9619140625 939.73114013671875 1325.986328125
(10355, 1617, 1, 141, 0, 1, 1, 9776.3193359375, 1165.2174072265625, 1281.208740234375, 2.967041015625, 0, 0, 0.996193885803222656, 0.087165042757987976, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9776.3193359375 1165.2174072265625 1281.208740234375
(10356, 1617, 1, 141, 0, 1, 1, 10054.455078125, 1197.8089599609375, 1333.7474365234375, 3.054326534271240234, 0, 0, 0.999048233032226562, 0.043619260191917419, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10054.455078125 1197.8089599609375 1333.7474365234375
(10357, 1617, 1, 141, 259, 1, 1, 9432.66796875, 954.89312744140625, 1266.922119140625, 0.331610709428787231, 0, 0, 0.16504669189453125, 0.986285746097564697, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9432.66796875 954.89312744140625 1266.922119140625
(10358, 1617, 1, 141, 0, 1, 1, 10044.1591796875, 1254.34912109375, 1325.015869140625, 4.956737518310546875, 0, 0, -0.61566066741943359, 0.788011372089385986, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10044.1591796875 1254.34912109375 1325.015869140625
(10359, 1617, 1, 141, 0, 1, 1, 10231.955078125, 1437.9483642578125, 1332.42041015625, 1.029743075370788574, 0, 0, 0.492423057556152343, 0.870355963706970214, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10231.955078125 1437.9483642578125 1332.42041015625
(10360, 1617, 1, 141, 0, 1, 1, 10267.4619140625, 1420.6258544921875, 1338.2886962890625, 1.064649581909179687, 0, 0, 0.507537841796875, 0.861629426479339599, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10267.4619140625 1420.6258544921875 1338.2886962890625
(10361, 1617, 1, 141, 259, 1, 1, 9592.2724609375, 847.688720703125, 1253.8590087890625, 3.351046562194824218, 0, 0, -0.99452114105224609, 0.104535527527332305, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9592.2724609375 847.688720703125 1253.8590087890625
(10362, 1617, 1, 141, 259, 1, 1, 9314.8173828125, 943.72906494140625, 1302.863525390625, 3.159062385559082031, 0, 0, -0.99996185302734375, 0.008734640665352344, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9314.8173828125 943.72906494140625 1302.863525390625
(10363, 1617, 1, 141, 0, 1, 1, 9218.6748046875, 963.2950439453125, 1323.899658203125, 5.567600727081298828, 0, 0, -0.35020732879638671, 0.936672210693359375, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9218.6748046875 963.2950439453125 1323.899658203125
(10364, 1617, 1, 141, 0, 1, 1, 9258.88671875, 1237.6925048828125, 1296.8572998046875, 3.717553615570068359, 0, 0, -0.95881938934326171, 0.284016460180282592, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9258.88671875 1237.6925048828125 1296.8572998046875
(10365, 1617, 1, 141, 0, 1, 1, 9376.216796875, 675.45880126953125, 1306.641357421875, 2.740161895751953125, 0, 0, 0.979924201965332031, 0.199370384216308593, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9376.216796875 675.45880126953125 1306.641357421875
(10366, 1617, 1, 141, 259, 1, 1, 9424.6728515625, 552.7025146484375, 1321.8255615234375, 5.881760597229003906, 0, 0, -0.19936752319335937, 0.979924798011779785, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9424.6728515625 552.7025146484375 1321.8255615234375
(10367, 1617, 1, 141, 0, 1, 1, 9454.830078125, 526.04498291015625, 1322.1998291015625, 0.733038187026977539, 0, 0, 0.358367919921875, 0.933580458164215087, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9454.830078125 526.04498291015625 1322.1998291015625
(10368, 1617, 1, 141, 259, 1, 1, 9650.3193359375, 626.2193603515625, 1307.93505859375, 6.003933906555175781, 0, 0, -0.13917255401611328, 0.990268170833587646, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9650.3193359375 626.2193603515625 1307.93505859375
(10369, 1617, 1, 141, 0, 1, 1, 9984.5654296875, 455.7041015625, 1310.00830078125, 2.460912704467773437, 0, 0, 0.942641258239746093, 0.333807557821273803, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9984.5654296875 455.7041015625 1310.00830078125
(10370, 1617, 1, 141, 0, 1, 1, 10015.5166015625, 257.208984375, 1325.7459716796875, 0.593410074710845947, 0, 0, 0.292370796203613281, 0.95630502700805664, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10015.5166015625 257.208984375 1325.7459716796875
(10371, 1617, 1, 141, 0, 1, 1, 10094.560546875, 312.465728759765625, 1326.69287109375, 4.293513298034667968, 0, 0, -0.8386697769165039, 0.544640243053436279, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10094.560546875 312.465728759765625 1326.69287109375
(10372, 1617, 1, 141, 0, 1, 1, 9965.7490234375, 275.020294189453125, 1323.5888671875, 2.007128477096557617, 0, 0, 0.84339141845703125, 0.537299633026123046, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9965.7490234375 275.020294189453125 1323.5888671875
(10373, 1617, 1, 141, 186, 1, 1, 9700.6640625, 807.92840576171875, 1293.1968994140625, 2.478367090225219726, 0, 0, 0.94551849365234375, 0.325568377971649169, 120, 255, 1, 0), -- 1617 (Area: 186 - Difficulty: 0) .go xyz 9700.6640625 807.92840576171875 1293.1968994140625
(10374, 1617, 1, 141, 0, 1, 1, 9580.501953125, 466.71044921875, 1326.58349609375, 0.052358884364366531, 0, 0, 0.02617645263671875, 0.999657332897186279, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9580.501953125 466.71044921875 1326.58349609375
(10375, 1617, 1, 141, 0, 1, 1, 9704, 425.908203125, 1313.4649658203125, 3.43830275535583496, 0, 0, -0.98901557922363281, 0.147811368107795715, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9704 425.908203125 1313.4649658203125
(10376, 1617, 1, 141, 0, 1, 1, 9625.064453125, 552.7281494140625, 1320.633056640625, 5.427974700927734375, 0, 0, -0.41469287872314453, 0.909961462020874023, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9625.064453125 552.7281494140625 1320.633056640625
(10377, 1617, 1, 141, 0, 1, 1, 9851.658203125, 1319.947265625, 1310.04296875, 2.356194972991943359, 0, 0, 0.923879623413085937, 0.382683247327804565, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9851.658203125 1319.947265625 1310.04296875
(10378, 1617, 1, 141, 0, 1, 1, 9739.7783203125, 1308.271240234375, 1311.259765625, 1.151916384696960449, 0, 0, 0.544638633728027343, 0.838670849800109863, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9739.7783203125 1308.271240234375 1311.259765625
(10379, 1617, 1, 141, 0, 1, 1, 9528.67578125, 618.71258544921875, 1271.981201171875, 1.535889506340026855, 0, 0, 0.694658279418945312, 0.719339847564697265, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9528.67578125 618.71258544921875 1271.981201171875
(10380, 1617, 1, 141, 0, 1, 1, 9330.7568359375, 625.45452880859375, 1319.3170166015625, 5.148722648620605468, 0, 0, -0.53729915618896484, 0.843391716480255126, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9330.7568359375 625.45452880859375 1319.3170166015625
(10381, 1617, 1, 141, 0, 1, 1, 9212.603515625, 870.442626953125, 1328.3079833984375, 1.169368624687194824, 0, 0, 0.551936149597167968, 0.833886384963989257, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9212.603515625 870.442626953125 1328.3079833984375
(10382, 1617, 1, 141, 0, 1, 1, 9368.1279296875, 1000.9525146484375, 1284.5257568359375, 5.305802345275878906, 0, 0, -0.46947097778320312, 0.882947921752929687, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9368.1279296875 1000.9525146484375 1284.5257568359375
(10383, 1617, 1, 141, 0, 1, 1, 9365.505859375, 1035.8172607421875, 1282.3231201171875, 4.694936752319335937, 0, 0, -0.71325016021728515, 0.700909554958343505, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9365.505859375 1035.8172607421875 1282.3231201171875
(10384, 1617, 1, 141, 0, 1, 1, 9320.732421875, 1109.5045166015625, 1254.4906005859375, 3.612837791442871093, 0, 0, -0.97236919403076171, 0.233448356389999389, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9320.732421875 1109.5045166015625 1254.4906005859375
(10385, 1617, 1, 141, 0, 1, 1, 9206.34765625, 1088.71923828125, 1318.267333984375, 2.757613182067871093, 0, 0, 0.981626510620117187, 0.190812408924102783, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9206.34765625 1088.71923828125 1318.267333984375
(10386, 1617, 1, 141, 0, 1, 1, 9166.6923828125, 1459.6007080078125, 1321.0106201171875, 0.24434557557106018, 0, 0, 0.121869087219238281, 0.9925462007522583, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9166.6923828125 1459.6007080078125 1321.0106201171875
(10387, 1617, 1, 141, 0, 1, 1, 9253.4189453125, 1450.94580078125, 1298.0260009765625, 5.654868602752685546, 0, 0, -0.30901622772216796, 0.95105677843093872, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9253.4189453125 1450.94580078125 1298.0260009765625
(10388, 1617, 1, 141, 0, 1, 1, 9310.7919921875, 1457.736083984375, 1278.418701171875, 2.740161895751953125, 0, 0, 0.979924201965332031, 0.199370384216308593, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9310.7919921875 1457.736083984375 1278.418701171875
(10389, 1617, 1, 141, 0, 1, 1, 9140.6533203125, 1516.4666748046875, 1323.4847412109375, 2.617989301681518554, 0, 0, 0.965925216674804687, 0.258821308612823486, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9140.6533203125 1516.4666748046875 1323.4847412109375
(10390, 1617, 1, 141, 0, 1, 1, 9739.9736328125, 313.833984375, 1324.6346435546875, 2.391098499298095703, 0, 0, 0.930417060852050781, 0.366502493619918823, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9739.9736328125 313.833984375 1324.6346435546875
(10391, 1617, 1, 141, 0, 1, 1, 9999.8291015625, 1540.5162353515625, 1303.59375, 6.073746204376220703, 0, 0, -0.10452842712402343, 0.994521915912628173, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9999.8291015625 1540.5162353515625 1303.59375
(10392, 1617, 1, 141, 0, 1, 1, 10102.6123046875, 442.6328125, 1325.60400390625, 5.183629035949707031, 0, 0, -0.52249813079833984, 0.852640450000762939, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10102.6123046875 442.6328125 1325.60400390625
(10393, 1617, 1, 141, 260, 1, 1, 9798.7734375, 556.0716552734375, 1299.6007080078125, 2.548179388046264648, 0, 0, 0.956304550170898437, 0.292372345924377441, 120, 255, 1, 0), -- 1617 (Area: 260 - Difficulty: 0) .go xyz 9798.7734375 556.0716552734375 1299.6007080078125
(10394, 1617, 1, 141, 0, 1, 1, 10043.9306640625, 446.943359375, 1315.5047607421875, 0.314158439636230468, 0, 0, 0.156434059143066406, 0.987688362598419189, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10043.9306640625 446.943359375 1315.5047607421875
(10395, 1617, 1, 141, 0, 1, 1, 9749.4140625, 479.152008056640625, 1306.3353271484375, 6.213373661041259765, 0, 0, -0.03489875793457031, 0.999390840530395507, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9749.4140625 479.152008056640625 1306.3353271484375
(10396, 1617, 1, 141, 259, 1, 1, 9265.931640625, 829.3360595703125, 1319.48681640625, 4.188792228698730468, 0, 0, -0.86602497100830078, 0.50000077486038208, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9265.931640625 829.3360595703125 1319.48681640625
(10397, 1617, 1, 141, 0, 1, 1, 10582.4052734375, 1740.15869140625, 1310.46923828125, 2.844882726669311523, 0, 0, 0.989015579223632812, 0.147811368107795715, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10582.4052734375 1740.15869140625 1310.46923828125
(10398, 1617, 1, 141, 264, 1, 1, 10978.2958984375, 1656.533447265625, 1277.513916015625, 4.677483558654785156, 0, 0, -0.71933937072753906, 0.694658815860748291, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10978.2958984375 1656.533447265625 1277.513916015625
(10399, 1617, 1, 141, 259, 1, 1, 9414.1650390625, 789.2694091796875, 1257.32373046875, 2.792518377304077148, 0, 0, 0.984807014465332031, 0.173652306199073791, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9414.1650390625 789.2694091796875 1257.32373046875
(10400, 1617, 1, 141, 259, 1, 1, 9352.404296875, 1300.1986083984375, 1255.4085693359375, 0.541050612926483154, 0, 0, 0.267237663269042968, 0.96363067626953125, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9352.404296875 1300.1986083984375 1255.4085693359375
(10401, 1617, 1, 141, 0, 1, 1, 9996.68359375, 1737.0430908203125, 1332.6927490234375, 2.687806606292724609, 0, 0, 0.974370002746582031, 0.224951311945915222, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9996.68359375 1737.0430908203125 1332.6927490234375
(10402, 1617, 1, 141, 0, 1, 1, 9813.5126953125, 1854.14501953125, 1313.865478515625, 5.427974700927734375, 0, 0, -0.41469287872314453, 0.909961462020874023, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9813.5126953125 1854.14501953125 1313.865478515625
(10403, 1617, 1, 141, 0, 1, 1, 9973.1474609375, 1399.072021484375, 1288.22998046875, 1.780233979225158691, 0, 0, 0.7771453857421875, 0.629321098327636718, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9973.1474609375 1399.072021484375 1288.22998046875
(10404, 1617, 1, 141, 0, 1, 1, 10391.126953125, 1895.3546142578125, 1322.1214599609375, 3.194002151489257812, 0, 0, -0.99965667724609375, 0.026201646775007247, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10391.126953125 1895.3546142578125 1322.1214599609375
(10405, 1617, 1, 141, 0, 1, 1, 10451.0830078125, 1962.9661865234375, 1319.1943359375, 5.270895957946777343, 0, 0, -0.48480892181396484, 0.87462007999420166, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10451.0830078125 1962.9661865234375 1319.1943359375
(10406, 1617, 1, 141, 264, 1, 1, 10735.33203125, 1789.8822021484375, 1330.7567138671875, 1.343901276588439941, 0, 0, 0.622513771057128906, 0.78260880708694458, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10735.33203125 1789.8822021484375 1330.7567138671875
(10407, 1617, 1, 141, 261, 1, 1, 9161.5830078125, 1649.240234375, 1322.218505859375, 2.740161895751953125, 0, 0, 0.979924201965332031, 0.199370384216308593, 120, 255, 1, 0), -- 1617 (Area: 261 - Difficulty: 0) .go xyz 9161.5830078125 1649.240234375 1322.218505859375
(10408, 1617, 1, 141, 0, 1, 1, 9475.3330078125, 1633.30908203125, 1302.8424072265625, 4.049167633056640625, 0, 0, -0.89879322052001953, 0.438372820615768432, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9475.3330078125 1633.30908203125 1302.8424072265625
(10409, 1617, 1, 141, 259, 1, 1, 9624.8330078125, 1133.307373046875, 1270.8377685546875, 4.677483558654785156, 0, 0, -0.71933937072753906, 0.694658815860748291, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9624.8330078125 1133.307373046875 1270.8377685546875
(10410, 1617, 1, 141, 0, 1, 1, 9417.5029296875, 1602.5328369140625, 1287.219970703125, 2.495818138122558593, 0, 0, 0.948323249816894531, 0.317305892705917358, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9417.5029296875 1602.5328369140625 1287.219970703125
(10411, 1617, 1, 141, 0, 1, 1, 10400.0283203125, 1457.4852294921875, 1330.8665771484375, 3.543023586273193359, 0, 0, -0.97992420196533203, 0.199370384216308593, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10400.0283203125 1457.4852294921875 1330.8665771484375
(10412, 1617, 1, 141, 264, 1, 1, 10955.126953125, 1880.5440673828125, 1331.0242919921875, 2.356194972991943359, 0, 0, 0.923879623413085937, 0.382683247327804565, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10955.126953125 1880.5440673828125 1331.0242919921875
(10413, 1617, 1, 141, 264, 1, 1, 10756.6767578125, 2016.909912109375, 1326.022705078125, 5.724681377410888671, 0, 0, -0.27563667297363281, 0.961261868476867675, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10756.6767578125 2016.909912109375 1326.022705078125
(10414, 1617, 1, 141, 264, 1, 1, 10744.9111328125, 1962.50390625, 1336.5377197265625, 5.445427894592285156, 0, 0, -0.40673637390136718, 0.913545548915863037, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10744.9111328125 1962.50390625 1336.5377197265625
(10415, 1617, 1, 141, 0, 1, 1, 10399.974609375, 1534.3336181640625, 1305.906005859375, 3.94444584846496582, 0, 0, -0.92050457000732421, 0.3907318115234375, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10399.974609375 1534.3336181640625 1305.906005859375
(10416, 1617, 1, 141, 266, 1, 1, 10254.3349609375, 1735.676513671875, 1320.5699462890625, 2.268925428390502929, 0, 0, 0.906307220458984375, 0.422619491815567016, 120, 255, 1, 0), -- 1617 (Area: 266 - Difficulty: 0) .go xyz 10254.3349609375 1735.676513671875 1320.5699462890625
(10417, 1617, 1, 141, 0, 1, 1, 10287.345703125, 1901.044921875, 1325.1207275390625, 1.867502212524414062, 0, 0, 0.803856849670410156, 0.594822824001312255, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10287.345703125 1901.044921875 1325.1207275390625
(10418, 1617, 1, 141, 0, 1, 1, 9879.130859375, 1741.4779052734375, 1327.4722900390625, 5.183629035949707031, 0, 0, -0.52249813079833984, 0.852640450000762939, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9879.130859375 1741.4779052734375 1327.4722900390625
(10419, 1617, 1, 141, 478, 1, 1, 9627.125, 1850.4205322265625, 1300.5699462890625, 3.735006093978881835, 0, 0, -0.95630455017089843, 0.292372345924377441, 120, 255, 1, 0), -- 1617 (Area: 478 - Difficulty: 0) .go xyz 9627.125 1850.4205322265625 1300.5699462890625
(10420, 1617, 1, 141, 261, 1, 1, 9102.1162109375, 1824.7584228515625, 1328.044921875, 2.111847877502441406, 0, 0, 0.870355606079101562, 0.492423713207244873, 120, 255, 1, 0), -- 1617 (Area: 261 - Difficulty: 0) .go xyz 9102.1162109375 1824.7584228515625 1328.044921875
(10421, 1617, 1, 141, 0, 1, 1, 9533.693359375, 1028.5513916015625, 1260.1793212890625, 4.97418975830078125, 0, 0, -0.60876083374023437, 0.793353796005249023, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9533.693359375 1028.5513916015625 1260.1793212890625
(10422, 1617, 1, 141, 0, 1, 1, 9181.5166015625, 1573.8363037109375, 1313.0816650390625, 2.356194972991943359, 0, 0, 0.923879623413085937, 0.382683247327804565, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9181.5166015625 1573.8363037109375 1313.0816650390625
(10423, 1617, 1, 141, 0, 1, 1, 9207.5390625, 1722.6893310546875, 1321.7681884765625, 3.001946926116943359, 0, 0, 0.997563362121582031, 0.069766148924827575, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 9207.5390625 1722.6893310546875 1321.7681884765625
(10424, 1617, 1, 141, 264, 1, 1, 10864.8642578125, 2005.2589111328125, 1317.5108642578125, 4.869470596313476562, 0, 0, -0.64944744110107421, 0.760406434535980224, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10864.8642578125 2005.2589111328125 1317.5108642578125
(10425, 1617, 1, 141, 264, 1, 1, 10565.580078125, 1949.7222900390625, 1325.7205810546875, 1.762782454490661621, 0, 0, 0.771624565124511718, 0.636078238487243652, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10565.580078125 1949.7222900390625 1325.7205810546875
(10426, 1617, 1, 141, 264, 1, 1, 10602.9873046875, 1824.259765625, 1319.875244140625, 0.349065244197845458, 0, 0, 0.173647880554199218, 0.984807789325714111, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10602.9873046875 1824.259765625 1319.875244140625
(10427, 1617, 1, 141, 478, 1, 1, 9678.4501953125, 1734.9405517578125, 1300.4393310546875, 0.593410074710845947, 0, 0, 0.292370796203613281, 0.95630502700805664, 120, 255, 1, 0), -- 1617 (Area: 478 - Difficulty: 0) .go xyz 9678.4501953125 1734.9405517578125 1300.4393310546875
(10428, 1617, 1, 141, 0, 1, 1, 10610.1982421875, 1432.73974609375, 1325.7232666015625, 2.234017848968505859, 0, 0, 0.898793220520019531, 0.438372820615768432, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10610.1982421875 1432.73974609375 1325.7232666015625
(10429, 1617, 1, 141, 264, 1, 1, 10910.62109375, 1759.303955078125, 1323.8536376953125, 5.532694816589355468, 0, 0, -0.3665008544921875, 0.93041771650314331, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10910.62109375 1759.303955078125 1323.8536376953125
(10430, 1617, 1, 141, 264, 1, 1, 10811.6611328125, 1750.1575927734375, 1324.849609375, 0.907570242881774902, 0, 0, 0.438370704650878906, 0.898794233798980712, 120, 255, 1, 0), -- 1617 (Area: 264 - Difficulty: 0) .go xyz 10811.6611328125 1750.1575927734375 1324.849609375
(10431, 1617, 1, 141, 0, 1, 1, 10819.689453125, 1454.479248046875, 1316.140380859375, 0.506144583225250244, 0, 0, 0.250379562377929687, 0.968147754669189453, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10819.689453125 1454.479248046875 1316.140380859375
(10432, 1617, 1, 141, 0, 1, 1, 10636.2939453125, 1676.3455810546875, 1292.87939453125, 6.09120035171508789, 0, 0, -0.09584522247314453, 0.995396256446838378, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10636.2939453125 1676.3455810546875 1292.87939453125
(10433, 1617, 1, 141, 0, 1, 1, 10966.4892578125, 1432.73046875, 1307.2252197265625, 2.234017848968505859, 0, 0, 0.898793220520019531, 0.438372820615768432, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10966.4892578125 1432.73046875 1307.2252197265625
(10434, 1617, 1, 141, 0, 1, 1, 10485.2763671875, 1441.4779052734375, 1329.9053955078125, 0.837757468223571777, 0, 0, 0.406736373901367187, 0.913545548915863037, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10485.2763671875 1441.4779052734375 1329.9053955078125
(10435, 1617, 1, 141, 0, 1, 1, 10508.9970703125, 1601.7239990234375, 1296.1651611328125, 1.797688722610473632, 0, 0, 0.7826080322265625, 0.622514784336090087, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10508.9970703125 1601.7239990234375 1296.1651611328125
(10436, 1617, 1, 141, 0, 1, 1, 10719.095703125, 1513.9405517578125, 1304.8387451171875, 0.034906249493360519, 0, 0, 0.017452239990234375, 0.999847710132598876, 120, 255, 1, 0), -- 1617 (Area: 0 - Difficulty: 0) .go xyz 10719.095703125 1513.9405517578125 1304.8387451171875
(10437, 1617, 1, 141, 478, 1, 1, 9730.2490234375, 1797.8094482421875, 1298.1533203125, 1.32644820213317871, 0, 0, 0.615660667419433593, 0.788011372089385986, 120, 255, 1, 0), -- 1617 (Area: 478 - Difficulty: 0) .go xyz 9730.2490234375 1797.8094482421875 1298.1533203125
(10438, 1617, 1, 141, 259, 1, 1, 9482.814453125, 1166.18310546875, 1254.8502197265625, 0.27925160527229309, 0, 0, 0.139172554016113281, 0.990268170833587646, 120, 255, 1, 0), -- 1617 (Area: 259 - Difficulty: 0) .go xyz 9482.814453125 1166.18310546875 1254.8502197265625
/* Earthroot */
(10439, 1619, 1, 141, 736, 1, 1, 9949.361328125, 1517.51611328125, 1319.11181640625, 1.518436193466186523, 0, 0, 0.6883544921875, 0.725374460220336914, 120, 255, 1, 0), -- 1619 (Area: 736 - Difficulty: 0) .go xyz 9949.361328125 1517.51611328125 1319.11181640625
(10440, 1619, 1, 141, 0, 1, 1, 10202.0849609375, 1773.146484375, 1338.0302734375, 5.026549339294433593, 0, 0, -0.5877847671508789, 0.809017360210418701, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10202.0849609375 1773.146484375 1338.0302734375
(10441, 1619, 1, 141, 0, 1, 1, 10299.0458984375, 1367.8687744140625, 1349.3394775390625, 6.178466320037841796, 0, 0, -0.05233573913574218, 0.998629570007324218, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10299.0458984375 1367.8687744140625 1349.3394775390625
(10442, 1619, 1, 141, 0, 1, 1, 10568.0126953125, 1458.8131103515625, 1333.834228515625, 4.188792228698730468, 0, 0, -0.86602497100830078, 0.50000077486038208, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10568.0126953125 1458.8131103515625 1333.834228515625
(10443, 1619, 1, 141, 0, 1, 1, 10696.2548828125, 1382.2010498046875, 1332.308349609375, 1.989672422409057617, 0, 0, 0.838669776916503906, 0.544640243053436279, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10696.2548828125 1382.2010498046875 1332.308349609375
(10444, 1619, 1, 141, 0, 1, 1, 10772.7783203125, 1442.7789306640625, 1334.5614013671875, 4.660029888153076171, 0, 0, -0.72537422180175781, 0.688354730606079101, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10772.7783203125, 1442.7789306640625, 1334.5614013671875
(10445, 1619, 1, 141, 0, 1, 1, 9675.7783203125, 1158.034912109375, 1279.328857421875, 4.782202720642089843, 0, 0, -0.68199825286865234, 0.731353819370269775, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9675.7783203125 1158.034912109375 1279.328857421875
(10446, 1619, 1, 141, 0, 1, 1, 9777.892578125, 1245.0712890625, 1293.4765625, 4.694936752319335937, 0, 0, -0.71325016021728515, 0.700909554958343505, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9777.892578125 1245.0712890625 1293.4765625
(10447, 1619, 1, 141, 0, 1, 1, 9922.0693359375, 1723.8187255859375, 1341.3470458984375, 1.832594871520996093, 0, 0, 0.793353080749511718, 0.608761727809906005, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9922.0693359375 1723.8187255859375 1341.3470458984375
(10448, 1619, 1, 141, 259, 1, 1, 9453.4892578125, 619.6982421875, 1321.916259765625, 5.811946868896484375, 0, 0, -0.2334451675415039, 0.972369968891143798, 120, 255, 1, 0), -- 1619 (Area: 259 - Difficulty: 0) .go xyz 9453.4892578125 619.6982421875 1321.916259765625
(10449, 1619, 1, 141, 259, 1, 1, 9308.93359375, 820.34808349609375, 1310.399169921875, 0.872663915157318115, 0, 0, 0.422617912292480468, 0.906307935714721679, 120, 255, 1, 0), -- 1619 (Area: 259 - Difficulty: 0) .go xyz 9308.93359375 820.34808349609375 1310.399169921875
(10450, 1619, 1, 141, 0, 1, 1, 9915.7705078125, 1375.333740234375, 1314.6527099609375, 0.226892471313476562, 0, 0, 0.113203048706054687, 0.993571877479553222, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9915.7705078125 1375.333740234375 1314.6527099609375
(10451, 1619, 1, 141, 0, 1, 1, 10245.8056640625, 1532.8255615234375, 1332.7755126953125, 3.211419343948364257, 0, 0, -0.9993906021118164, 0.034906134009361267, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10245.8056640625 1532.8255615234375 1332.7755126953125
(10452, 1619, 1, 141, 0, 1, 1, 10007.62890625, 1518.3870849609375, 1299.6229248046875, 0.663223206996917724, 0, 0, 0.325567245483398437, 0.945518851280212402, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10007.62890625 1518.3870849609375 1299.6229248046875
(10453, 1619, 1, 141, 0, 1, 1, 9771.6103515625, 1338.1766357421875, 1332.7840576171875, 3.996806621551513671, 0, 0, -0.90996074676513671, 0.414694398641586303, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9771.6103515625 1338.1766357421875 1332.7840576171875
(10454, 1619, 1, 141, 0, 1, 1, 9704.1494140625, 1320.092041015625, 1331.1981201171875, 0.174532130360603332, 0, 0, 0.087155342102050781, 0.996194720268249511, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9704.1494140625 1320.092041015625 1331.1981201171875
(10455, 1619, 1, 141, 259, 1, 1, 9388.2705078125, 710.32476806640625, 1295.316162109375, 4.520402908325195312, 0, 0, -0.77162456512451171, 0.636078238487243652, 120, 255, 1, 0), -- 1619 (Area: 259 - Difficulty: 0) .go xyz 9388.2705078125 710.32476806640625 1295.316162109375
(10456, 1619, 1, 141, 0, 1, 1, 9433.322265625, 1253.6151123046875, 1263.435791015625, 4.206246376037597656, 0, 0, -0.86162853240966796, 0.50753939151763916, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9433.322265625 1253.6151123046875 1263.435791015625
(10457, 1619, 1, 141, 0, 1, 1, 9321.900390625, 1359.369140625, 1294.4144287109375, 6.14356088638305664, 0, 0, -0.06975555419921875, 0.997564136981964111, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9321.900390625 1359.369140625 1294.4144287109375
(10458, 1619, 1, 141, 0, 1, 1, 9327.2275390625, 1255.2874755859375, 1257.5950927734375, 5.602506637573242187, 0, 0, -0.33380699157714843, 0.942641437053680419, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9327.2275390625 1255.2874755859375 1257.5950927734375
(10459, 1619, 1, 141, 478, 1, 1, 9591.8271484375, 1923.882568359375, 1323.4888916015625, 4.834563255310058593, 0, 0, -0.66261959075927734, 0.748956084251403808, 120, 255, 1, 0), -- 1619 (Area: 478 - Difficulty: 0) .go xyz 9591.8271484375 1923.882568359375 1323.4888916015625
(10460, 1619, 1, 141, 264, 1, 1, 10613.443359375, 1781.4302978515625, 1328.4791259765625, 5.986480236053466796, 0, 0, -0.14780902862548828, 0.989015936851501464, 120, 255, 1, 0), -- 1619 (Area: 264 - Difficulty: 0) .go xyz 10613.443359375 1781.4302978515625 1328.4791259765625
(10461, 1619, 1, 141, 264, 1, 1, 10826.08984375, 1799.000244140625, 1344.517578125, 2.72271275520324707, 0, 0, 0.978147506713867187, 0.207912087440490722, 120, 255, 1, 0), -- 1619 (Area: 264 - Difficulty: 0) .go xyz 10826.08984375 1799.000244140625 1344.517578125
(10462, 1619, 1, 141, 0, 1, 1, 9396.748046875, 1073.8248291015625, 1274.1181640625, 2.164205789566040039, 0, 0, 0.882946968078613281, 0.469472706317901611, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9396.748046875 1073.8248291015625 1274.1181640625
(10463, 1619, 1, 141, 0, 1, 1, 9355.7451171875, 1552.3116455078125, 1281.6705322265625, 2.495818138122558593, 0, 0, 0.948323249816894531, 0.317305892705917358, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9355.7451171875 1552.3116455078125 1281.6705322265625
(10464, 1619, 1, 141, 141, 1, 1, 9300.1708984375, 1681.1754150390625, 1320.1173095703125, 0.872663915157318115, 0, 0, 0.422617912292480468, 0.906307935714721679, 120, 255, 1, 0), -- 1619 (Area: 141 - Difficulty: 0) .go xyz 9300.1708984375 1681.1754150390625 1320.1173095703125
(10465, 1619, 1, 141, 0, 1, 1, 10395.0576171875, 1966.03125, 1330.9617919921875, 2.757613182067871093, 0, 0, 0.981626510620117187, 0.190812408924102783, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10395.0576171875 1966.03125 1330.9617919921875
(10466, 1619, 1, 141, 0, 1, 1, 10479.9296875, 2025.1378173828125, 1343.84912109375, 3.385940074920654296, 0, 0, -0.99254608154296875, 0.121869951486587524, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10479.9296875 2025.1378173828125 1343.84912109375
(10467, 1619, 1, 141, 0, 1, 1, 10491.8818359375, 1846.27392578125, 1324.7264404296875, 0.907570242881774902, 0, 0, 0.438370704650878906, 0.898794233798980712, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10491.8818359375 1846.27392578125 1324.7264404296875
(10468, 1619, 1, 141, 265, 1, 1, 10766.18359375, 1554.2437744140625, 1316.4422607421875, 5.445427894592285156, 0, 0, -0.40673637390136718, 0.913545548915863037, 120, 255, 1, 0), -- 1619 (Area: 265 - Difficulty: 0) .go xyz 10766.18359375 1554.2437744140625 1316.4422607421875
(10469, 1619, 1, 141, 736, 1, 1, 9832.1142578125, 1345.9051513671875, 1326.740234375, 4.188792228698730468, 0, 0, -0.86602497100830078, 0.50000077486038208, 120, 255, 1, 0), -- 1619 (Area: 736 - Difficulty: 0) .go xyz 9832.1142578125 1345.9051513671875 1326.740234375
(10470, 1619, 1, 141, 0, 1, 1, 9473.4423828125, 1698.4815673828125, 1305.1339111328125, 1.867502212524414062, 0, 0, 0.803856849670410156, 0.594822824001312255, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9473.4423828125 1698.4815673828125 1305.1339111328125
(10471, 1619, 1, 141, 0, 1, 1, 9554.361328125, 1212.6767578125, 1277.5430908203125, 4.869470596313476562, 0, 0, -0.64944744110107421, 0.760406434535980224, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9554.361328125 1212.6767578125 1277.5430908203125
(10472, 1619, 1, 141, 259, 1, 1, 9353.0107421875, 1180.5889892578125, 1256.8233642578125, 1.186823248863220214, 0, 0, 0.559192657470703125, 0.829037725925445556, 120, 255, 1, 0), -- 1619 (Area: 259 - Difficulty: 0) .go xyz 9353.0107421875 1180.5889892578125 1256.8233642578125
(10473, 1619, 1, 141, 0, 1, 1, 10000.666015625, 1646.465087890625, 1334.5228271484375, 3.45575571060180664, 0, 0, -0.98768806457519531, 0.156436234712600708, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10000.666015625 1646.465087890625 1334.5228271484375
(10474, 1619, 1, 141, 0, 1, 1, 10428.3779296875, 1419.3570556640625, 1343.3973388671875, 2.234017848968505859, 0, 0, 0.898793220520019531, 0.438372820615768432, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10428.3779296875 1419.3570556640625 1343.3973388671875
(10475, 1619, 1, 141, 265, 1, 1, 10627.2587890625, 1713.47119140625, 1321.153076171875, 4.241150379180908203, 0, 0, -0.85264015197753906, 0.522498607635498046, 120, 255, 1, 0), -- 1619 (Area: 265 - Difficulty: 0) .go xyz 10627.2587890625, 1713.47119140625, 1321.153076171875
(10476, 1619, 1, 141, 0, 1, 1, 10412.7451171875, 1750.10302734375, 1315.65869140625, 0.733038187026977539, 0, 0, 0.358367919921875, 0.933580458164215087, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10412.7451171875 1750.10302734375 1315.65869140625
(10477, 1619, 1, 141, 266, 1, 1, 10180.755859375, 1557.9566650390625, 1330.01416015625, 2.426007747650146484, 0, 0, 0.936672210693359375, 0.350207358598709106, 120, 255, 1, 0), -- 1619 (Area: 266 - Difficulty: 0) .go xyz 10180.755859375 1557.9566650390625 1330.01416015625
(10478, 1619, 1, 141, 478, 1, 1, 9682.3603515625, 1759.061767578125, 1299.8004150390625, 1.134462952613830566, 0, 0, 0.537299156188964843, 0.843391716480255126, 120, 255, 1, 0), -- 1619 (Area: 478 - Difficulty: 0) .go xyz 9682.3603515625 1759.061767578125 1299.8004150390625
(10479, 1619, 1, 141, 0, 1, 1, 9814.6953125, 1788.98583984375, 1320.239501953125, 3.019413232803344726, 0, 0, 0.998134613037109375, 0.061051756143569946, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9814.6953125 1788.98583984375 1320.239501953125
(10480, 1619, 1, 141, 478, 1, 1, 9418.4423828125, 1792.6944580078125, 1338.545166015625, 5.93412017822265625, 0, 0, -0.17364788055419921, 0.984807789325714111, 120, 255, 1, 0), -- 1619 (Area: 478 - Difficulty: 0) .go xyz 9418.4423828125 1792.6944580078125 1338.545166015625
(10481, 1619, 1, 141, 0, 1, 1, 9277.126953125, 1530.89453125, 1289.3433837890625, 5.811946868896484375, 0, 0, -0.2334451675415039, 0.972369968891143798, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 9277.126953125 1530.89453125 1289.3433837890625
(10482, 1619, 1, 141, 261, 1, 1, 9117.62890625, 1737.7332763671875, 1327.2015380859375, 4.136432647705078125, 0, 0, -0.87881660461425781, 0.477159708738327026, 120, 255, 1, 0), -- 1619 (Area: 261 - Difficulty: 0) .go xyz 9117.62890625 1737.7332763671875 1327.2015380859375
(10483, 1619, 1, 141, 261, 1, 1, 9135.1669921875, 1864.746337890625, 1338.6793212890625, 4.1538848876953125, 0, 0, -0.8746194839477539, 0.484810054302215576, 120, 255, 1, 0), -- 1619 (Area: 261 - Difficulty: 0) .go xyz 9135.1669921875 1864.746337890625 1338.6793212890625
(10484, 1619, 1, 141, 478, 1, 1, 9489.673828125, 1845.863525390625, 1336.5867919921875, 0.418878614902496337, 0, 0, 0.207911491394042968, 0.978147625923156738, 120, 255, 1, 0), -- 1619 (Area: 478 - Difficulty: 0) .go xyz 9489.673828125 1845.863525390625 1336.5867919921875
(10485, 1619, 1, 141, 261, 1, 1, 9252.0439453125, 1765.5712890625, 1340.3778076171875, 4.206246376037597656, 0, 0, -0.86162853240966796, 0.50753939151763916, 120, 255, 1, 0), -- 1619 (Area: 261 - Difficulty: 0) .go xyz 9252.0439453125 1765.5712890625 1340.3778076171875
(10486, 1619, 1, 141, 265, 1, 1, 10791.6240234375, 1707.8931884765625, 1310.3560791015625, 4.642575740814208984, 0, 0, -0.731353759765625, 0.681998312473297119, 120, 255, 1, 0), -- 1619 (Area: 265 - Difficulty: 0) .go xyz 10791.6240234375 1707.8931884765625 1310.3560791015625
(10487, 1619, 1, 141, 264, 1, 1, 10617.2861328125, 2046.9752197265625, 1337.8548583984375, 3.961898565292358398, 0, 0, -0.91705989837646484, 0.398749500513076782, 120, 255, 1, 0), -- 1619 (Area: 264 - Difficulty: 0) .go xyz 10617.2861328125 2046.9752197265625 1337.8548583984375
(10488, 1619, 1, 141, 264, 1, 1, 10566.2412109375, 2027.4342041015625, 1338.7587890625, 4.223697185516357421, 0, 0, -0.85716724395751953, 0.515038192272186279, 120, 255, 1, 0), -- 1619 (Area: 264 - Difficulty: 0) .go xyz 10566.2412109375 2027.4342041015625 1338.7587890625
(10489, 1619, 1, 141, 0, 1, 1, 10883.6689453125, 1340.940185546875, 1336.9639892578125, 1.85004889965057373, 0, 0, 0.798635482788085937, 0.60181504487991333, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10883.6689453125 1340.940185546875 1336.9639892578125
(10490, 1619, 1, 141, 264, 1, 1, 10906.3583984375, 1902.089599609375, 1338.25634765625, 5.288348197937011718, 0, 0, -0.4771585464477539, 0.878817260265350341, 120, 255, 1, 0), -- 1619 (Area: 264 - Difficulty: 0) .go xyz 10906.3583984375 1902.089599609375 1338.25634765625
(10491, 1619, 1, 141, 264, 1, 1, 10930.90234375, 1772.327392578125, 1327.6578369140625, 4.031712055206298828, 0, 0, -0.90258502960205078, 0.430511653423309326, 120, 255, 1, 0), -- 1619 (Area: 264 - Difficulty: 0) .go xyz 10930.90234375 1772.327392578125 1327.6578369140625
(10492, 1619, 1, 141, 0, 1, 1, 10498.09765625, 1537.847900390625, 1314.1116943359375, 5.183629035949707031, 0, 0, -0.52249813079833984, 0.852640450000762939, 120, 255, 1, 0), -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10498.09765625 1537.847900390625 1314.1116943359375
(10493, 1619, 1, 141, 265, 1, 1, 10951.0966796875, 1516.40283203125, 1283.5743408203125, 5.585053920745849609, 0, 0, -0.34202003479003906, 0.939692676067352294, 120, 255, 1, 0), -- 1619 (Area: 265 - Difficulty: 0) .go xyz 10951.0966796875 1516.40283203125 1283.5743408203125
(10494, 1619, 1, 141, 0, 1, 1, 10610.3310546875, 1560.1141357421875, 1314.************, 5.742135047912597656, 0, 0, -0.26723766326904296, 0.96363067626953125, 120, 255, 1, 0); -- 1619 (Area: 0 - Difficulty: 0) .go xyz 10610.3310546875 1560.1141357421875 1314.************
/* Teldrassil Herb Zone Pools */
/* Teldrassil Peacebloom 1 */
DELETE FROM `pool_template` WHERE  `entry`=416;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=416;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(416, 9, 'Teldrassil Peacebloom Pool 1 of 5--9 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10233, 416, 0, 'Teldrassil Peacebloom'),
(10234, 416, 0, 'Teldrassil Peacebloom'),
(10235, 416, 0, 'Teldrassil Peacebloom'),
(10236, 416, 0, 'Teldrassil Peacebloom'),
(10249, 416, 0, 'Teldrassil Peacebloom'),
(10250, 416, 0, 'Teldrassil Peacebloom'),
(10279, 416, 0, 'Teldrassil Peacebloom'),
(10280, 416, 0, 'Teldrassil Peacebloom'),
(10287, 416, 0, 'Teldrassil Peacebloom'),
(10290, 416, 0, 'Teldrassil Peacebloom'),
(10291, 416, 0, 'Teldrassil Peacebloom'),
(10292, 416, 0, 'Teldrassil Peacebloom'),
(10293, 416, 0, 'Teldrassil Peacebloom'),
(10294, 416, 0, 'Teldrassil Peacebloom'),
(10295, 416, 0, 'Teldrassil Peacebloom'),
(10302, 416, 0, 'Teldrassil Peacebloom'),
(10303, 416, 0, 'Teldrassil Peacebloom'),
(10306, 416, 0, 'Teldrassil Peacebloom'),
(10307, 416, 0, 'Teldrassil Peacebloom'),
(10308, 416, 0, 'Teldrassil Peacebloom'),
(10309, 416, 0, 'Teldrassil Peacebloom'),
(10310, 416, 0, 'Teldrassil Peacebloom'),
(10311, 416, 0, 'Teldrassil Peacebloom'),
(10312, 416, 0, 'Teldrassil Peacebloom'),
(10313, 416, 0, 'Teldrassil Peacebloom'),
(10314, 416, 0, 'Teldrassil Peacebloom'),
(10315, 416, 0, 'Teldrassil Peacebloom');
/* Teldrassil Peacebloom 2 */
DELETE FROM `pool_template` WHERE  `entry`=417;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=417;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(417, 4, 'Teldrassil Peacebloom Pool 2 of 5--4 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10266, 417, 0, 'Teldrassil Peacebloom'),
(10268, 417, 0, 'Teldrassil Peacebloom'),
(10278, 417, 0, 'Teldrassil Peacebloom'),
(10283, 417, 0, 'Teldrassil Peacebloom'),
(10284, 417, 0, 'Teldrassil Peacebloom'),
(10286, 417, 0, 'Teldrassil Peacebloom'),
(10296, 417, 0, 'Teldrassil Peacebloom'),
(10297, 417, 0, 'Teldrassil Peacebloom'),
(10298, 417, 0, 'Teldrassil Peacebloom'),
(10299, 417, 0, 'Teldrassil Peacebloom'),
(10300, 417, 0, 'Teldrassil Peacebloom'),
(10301, 417, 0, 'Teldrassil Peacebloom'),
(10304, 417, 0, 'Teldrassil Peacebloom');
/* Teldrassil Peacebloom 3 */
DELETE FROM `pool_template` WHERE  `entry`=418;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=418;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(418, 4, 'Teldrassil Peacebloom Pool 3 of 5--4 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10229, 418, 0, 'Teldrassil Peacebloom'),
(10231, 418, 0, 'Teldrassil Peacebloom'),
(10232, 418, 0, 'Teldrassil Peacebloom'),
(10245, 418, 0, 'Teldrassil Peacebloom'),
(10246, 418, 0, 'Teldrassil Peacebloom'),
(10263, 418, 0, 'Teldrassil Peacebloom'),
(10264, 418, 0, 'Teldrassil Peacebloom'),
(10265, 418, 0, 'Teldrassil Peacebloom'),
(10281, 418, 0, 'Teldrassil Peacebloom'),
(10282, 418, 0, 'Teldrassil Peacebloom'),
(10289, 418, 0, 'Teldrassil Peacebloom');
/* Teldrassil Peacebloom 4 */
DELETE FROM `pool_template` WHERE  `entry`=419;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=419;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(419, 6, 'Teldrassil Peacebloom Pool 4 of 5--6 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10238, 419, 0, 'Teldrassil Peacebloom'),
(10239, 419, 0, 'Teldrassil Peacebloom'),
(10244, 419, 0, 'Teldrassil Peacebloom'),
(10251, 419, 0, 'Teldrassil Peacebloom'),
(10252, 419, 0, 'Teldrassil Peacebloom'),
(10253, 419, 0, 'Teldrassil Peacebloom'),
(10254, 419, 0, 'Teldrassil Peacebloom'),
(10255, 419, 0, 'Teldrassil Peacebloom'),
(10256, 419, 0, 'Teldrassil Peacebloom'),
(10257, 419, 0, 'Teldrassil Peacebloom'),
(10258, 419, 0, 'Teldrassil Peacebloom'),
(10259, 419, 0, 'Teldrassil Peacebloom'),
(10261, 419, 0, 'Teldrassil Peacebloom'),
(10267, 419, 0, 'Teldrassil Peacebloom'),
(10271, 419, 0, 'Teldrassil Peacebloom'),
(10285, 419, 0, 'Teldrassil Peacebloom'),
(10288, 419, 0, 'Teldrassil Peacebloom'),
(10305, 419, 0, 'Teldrassil Peacebloom');
/* Teldrassil Peacebloom 5 */
DELETE FROM `pool_template` WHERE  `entry`=420;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=420;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(420, 7, 'Teldrassil Peacebloom Pool 5 of 5--7 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10230, 420, 0, 'Teldrassil Peacebloom'),
(10237, 420, 0, 'Teldrassil Peacebloom'),
(10240, 420, 0, 'Teldrassil Peacebloom'),
(10241, 420, 0, 'Teldrassil Peacebloom'),
(10242, 420, 0, 'Teldrassil Peacebloom'),
(10243, 420, 0, 'Teldrassil Peacebloom'),
(10247, 420, 0, 'Teldrassil Peacebloom'),
(10248, 420, 0, 'Teldrassil Peacebloom'),
(10260, 420, 0, 'Teldrassil Peacebloom'),
(10262, 420, 0, 'Teldrassil Peacebloom'),
(10269, 420, 0, 'Teldrassil Peacebloom'),
(10270, 420, 0, 'Teldrassil Peacebloom'),
(10272, 420, 0, 'Teldrassil Peacebloom'),
(10273, 420, 0, 'Teldrassil Peacebloom'),
(10274, 420, 0, 'Teldrassil Peacebloom'),
(10275, 420, 0, 'Teldrassil Peacebloom'),
(10276, 420, 0, 'Teldrassil Peacebloom'),
(10277, 420, 0, 'Teldrassil Peacebloom');
/* Teldrassil Mageroyal 1 */
DELETE FROM `pool_template` WHERE  `entry`=421;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=421;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(421, 2, 'Teldrassil Mageroyal Pool 1 of 3--2 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10319, 421, 0, 'Teldrassil Mageroyal'),
(10328, 421, 0, 'Teldrassil Mageroyal'),
(10331, 421, 0, 'Teldrassil Mageroyal'),
(10333, 421, 0, 'Teldrassil Mageroyal'),
(10335, 421, 0, 'Teldrassil Mageroyal'),
(10336, 421, 0, 'Teldrassil Mageroyal'),
(10337, 421, 0, 'Teldrassil Mageroyal'),
(10338, 421, 0, 'Teldrassil Mageroyal'),
(10339, 421, 0, 'Teldrassil Mageroyal');
/* Teldrassil Mageroyal 2 */
DELETE FROM `pool_template` WHERE  `entry`=422;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=422;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(422, 2, 'Teldrassil Mageroyal Pool 2 of 3--2 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10320, 422, 0, 'Teldrassil Mageroyal'),
(10322, 422, 0, 'Teldrassil Mageroyal'),
(10324, 422, 0, 'Teldrassil Mageroyal'),
(10326, 422, 0, 'Teldrassil Mageroyal'),
(10327, 422, 0, 'Teldrassil Mageroyal'),
(10329, 422, 0, 'Teldrassil Mageroyal'),
(10330, 422, 0, 'Teldrassil Mageroyal'),
(10332, 422, 0, 'Teldrassil Mageroyal'),
(10340, 422, 0, 'Teldrassil Mageroyal'),
(10341, 422, 0, 'Teldrassil Mageroyal');
/* Teldrassil Mageroyal 3 */
DELETE FROM `pool_template` WHERE  `entry`=423;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=423;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(423, 2, 'Teldrassil Mageroyal Pool 3 of 3--2 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10316, 423, 0, 'Teldrassil Mageroyal'),
(10317, 423, 0, 'Teldrassil Mageroyal'),
(10318, 423, 0, 'Teldrassil Mageroyal'),
(10321, 423, 0, 'Teldrassil Mageroyal'),
(10323, 423, 0, 'Teldrassil Mageroyal'),
(10325, 423, 0, 'Teldrassil Mageroyal'),
(10334, 423, 0, 'Teldrassil Mageroyal');
/* Teldrassil Silverleaf 1 */
DELETE FROM `pool_template` WHERE  `entry`=424;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=424;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(424, 10, 'Teldrassil Silverleaf Pool 1 of 5--10 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10345, 424, 0, 'Teldrassil Silverleaf'),
(10346, 424, 0, 'Teldrassil Silverleaf'),
(10347, 424, 0, 'Teldrassil Silverleaf'),
(10348, 424, 0, 'Teldrassil Silverleaf'),
(10359, 424, 0, 'Teldrassil Silverleaf'),
(10360, 424, 0, 'Teldrassil Silverleaf'),
(10397, 424, 0, 'Teldrassil Silverleaf'),
(10398, 424, 0, 'Teldrassil Silverleaf'),
(10404, 424, 0, 'Teldrassil Silverleaf'),
(10405, 424, 0, 'Teldrassil Silverleaf'),
(10406, 424, 0, 'Teldrassil Silverleaf'),
(10411, 424, 0, 'Teldrassil Silverleaf'),
(10412, 424, 0, 'Teldrassil Silverleaf'),
(10413, 424, 0, 'Teldrassil Silverleaf'),
(10414, 424, 0, 'Teldrassil Silverleaf'),
(10415, 424, 0, 'Teldrassil Silverleaf'),
(10416, 424, 0, 'Teldrassil Silverleaf'),
(10417, 424, 0, 'Teldrassil Silverleaf'),
(10424, 424, 0, 'Teldrassil Silverleaf'),
(10425, 424, 0, 'Teldrassil Silverleaf'),
(10426, 424, 0, 'Teldrassil Silverleaf'),
(10428, 424, 0, 'Teldrassil Silverleaf'),
(10429, 424, 0, 'Teldrassil Silverleaf'),
(10430, 424, 0, 'Teldrassil Silverleaf'),
(10431, 424, 0, 'Teldrassil Silverleaf'),
(10432, 424, 0, 'Teldrassil Silverleaf'),
(10433, 424, 0, 'Teldrassil Silverleaf'),
(10434, 424, 0, 'Teldrassil Silverleaf'),
(10435, 424, 0, 'Teldrassil Silverleaf'),
(10436, 424, 0, 'Teldrassil Silverleaf');
/* Teldrassil Silverleaf 2 */
DELETE FROM `pool_template` WHERE  `entry`=425;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=425;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(425, 8, 'Teldrassil Silverleaf Pool 2 of 5--8 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10386, 425, 0, 'Teldrassil Silverleaf'),
(10387, 425, 0, 'Teldrassil Silverleaf'),
(10401, 425, 0, 'Teldrassil Silverleaf'),
(10402, 425, 0, 'Teldrassil Silverleaf'),
(10407, 425, 0, 'Teldrassil Silverleaf'),
(10408, 425, 0, 'Teldrassil Silverleaf'),
(10410, 425, 0, 'Teldrassil Silverleaf'),
(10418, 425, 0, 'Teldrassil Silverleaf'),
(10419, 425, 0, 'Teldrassil Silverleaf'),
(10420, 425, 0, 'Teldrassil Silverleaf'),
(10422, 425, 0, 'Teldrassil Silverleaf'),
(10423, 425, 0, 'Teldrassil Silverleaf'),
(10427, 425, 0, 'Teldrassil Silverleaf'),
(10437, 425, 0, 'Teldrassil Silverleaf');
/* Teldrassil Silverleaf 3 */
DELETE FROM `pool_template` WHERE  `entry`=426;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=426;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(426, 5, 'Teldrassil Silverleaf Pool 3 of 5--5 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10344, 426, 0, 'Teldrassil Silverleaf'),
(10350, 426, 0, 'Teldrassil Silverleaf'),
(10355, 426, 0, 'Teldrassil Silverleaf'),
(10356, 426, 0, 'Teldrassil Silverleaf'),
(10358, 426, 0, 'Teldrassil Silverleaf'),
(10377, 426, 0, 'Teldrassil Silverleaf'),
(10378, 426, 0, 'Teldrassil Silverleaf'),
(10388, 426, 0, 'Teldrassil Silverleaf'),
(10389, 426, 0, 'Teldrassil Silverleaf'),
(10391, 426, 0, 'Teldrassil Silverleaf'),
(10403, 426, 0, 'Teldrassil Silverleaf'),
(10409, 426, 0, 'Teldrassil Silverleaf');
/* Teldrassil Silverleaf 4 */
DELETE FROM `pool_template` WHERE  `entry`=427;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=427;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(427, 6, 'Teldrassil Silverleaf Pool 4 of 5--6 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10357, 427, 0, 'Teldrassil Silverleaf'),
(10361, 427, 0, 'Teldrassil Silverleaf'),
(10362, 427, 0, 'Teldrassil Silverleaf'),
(10363, 427, 0, 'Teldrassil Silverleaf'),
(10364, 427, 0, 'Teldrassil Silverleaf'),
(10365, 427, 0, 'Teldrassil Silverleaf'),
(10366, 427, 0, 'Teldrassil Silverleaf'),
(10367, 427, 0, 'Teldrassil Silverleaf'),
(10379, 427, 0, 'Teldrassil Silverleaf'),
(10380, 427, 0, 'Teldrassil Silverleaf'),
(10381, 427, 0, 'Teldrassil Silverleaf'),
(10382, 427, 0, 'Teldrassil Silverleaf'),
(10383, 427, 0, 'Teldrassil Silverleaf'),
(10384, 427, 0, 'Teldrassil Silverleaf'),
(10385, 427, 0, 'Teldrassil Silverleaf'),
(10396, 427, 0, 'Teldrassil Silverleaf'),
(10399, 427, 0, 'Teldrassil Silverleaf'),
(10400, 427, 0, 'Teldrassil Silverleaf'),
(10421, 427, 0, 'Teldrassil Silverleaf'),
(10438, 427, 0, 'Teldrassil Silverleaf');
/* Teldrassil Silverleaf 5 */
DELETE FROM `pool_template` WHERE  `entry`=428;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=428;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(428, 7, 'Teldrassil Silverleaf Pool 5 of 5--7 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10342, 428, 0, 'Teldrassil Silverleaf'),
(10343, 428, 0, 'Teldrassil Silverleaf'),
(10349, 428, 0, 'Teldrassil Silverleaf'),
(10351, 428, 0, 'Teldrassil Silverleaf'),
(10352, 428, 0, 'Teldrassil Silverleaf'),
(10353, 428, 0, 'Teldrassil Silverleaf'),
(10354, 428, 0, 'Teldrassil Silverleaf'),
(10368, 428, 0, 'Teldrassil Silverleaf'),
(10369, 428, 0, 'Teldrassil Silverleaf'),
(10370, 428, 0, 'Teldrassil Silverleaf'),
(10371, 428, 0, 'Teldrassil Silverleaf'),
(10372, 428, 0, 'Teldrassil Silverleaf'),
(10373, 428, 0, 'Teldrassil Silverleaf'),
(10374, 428, 0, 'Teldrassil Silverleaf'),
(10375, 428, 0, 'Teldrassil Silverleaf'),
(10376, 428, 0, 'Teldrassil Silverleaf'),
(10390, 428, 0, 'Teldrassil Silverleaf'),
(10392, 428, 0, 'Teldrassil Silverleaf'),
(10393, 428, 0, 'Teldrassil Silverleaf'),
(10394, 428, 0, 'Teldrassil Silverleaf'),
(10395, 428, 0, 'Teldrassil Silverleaf');
/* Teldrassil Earthroot 1 */
DELETE FROM `pool_template` WHERE  `entry`=429;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=429;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(429, 9, 'Teldrassil Earthroot Pool 1 of 4--9 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10440, 429, 0, 'Teldrassil Earthroot'),
(10441, 429, 0, 'Teldrassil Earthroot'),
(10442, 429, 0, 'Teldrassil Earthroot'),
(10443, 429, 0, 'Teldrassil Earthroot'),
(10444, 429, 0, 'Teldrassil Earthroot'),
(10451, 429, 0, 'Teldrassil Earthroot'),
(10460, 429, 0, 'Teldrassil Earthroot'),
(10461, 429, 0, 'Teldrassil Earthroot'),
(10465, 429, 0, 'Teldrassil Earthroot'),
(10466, 429, 0, 'Teldrassil Earthroot'),
(10467, 429, 0, 'Teldrassil Earthroot'),
(10468, 429, 0, 'Teldrassil Earthroot'),
(10474, 429, 0, 'Teldrassil Earthroot'),
(10475, 429, 0, 'Teldrassil Earthroot'),
(10476, 429, 0, 'Teldrassil Earthroot'),
(10477, 429, 0, 'Teldrassil Earthroot'),
(10486, 429, 0, 'Teldrassil Earthroot'),
(10487, 429, 0, 'Teldrassil Earthroot'),
(10488, 429, 0, 'Teldrassil Earthroot'),
(10489, 429, 0, 'Teldrassil Earthroot'),
(10490, 429, 0, 'Teldrassil Earthroot'),
(10491, 429, 0, 'Teldrassil Earthroot'),
(10492, 429, 0, 'Teldrassil Earthroot'),
(10493, 429, 0, 'Teldrassil Earthroot'),
(10494, 429, 0, 'Teldrassil Earthroot');
/* Teldrassil Earthroot 2 */
DELETE FROM `pool_template` WHERE  `entry`=430;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=430;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(430, 4, 'Teldrassil Earthroot Pool 2 of 4--4 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10459, 430, 0, 'Teldrassil Earthroot'),
(10463, 430, 0, 'Teldrassil Earthroot'),
(10464, 430, 0, 'Teldrassil Earthroot'),
(10470, 430, 0, 'Teldrassil Earthroot'),
(10478, 430, 0, 'Teldrassil Earthroot'),
(10479, 430, 0, 'Teldrassil Earthroot'),
(10480, 430, 0, 'Teldrassil Earthroot'),
(10481, 430, 0, 'Teldrassil Earthroot'),
(10482, 430, 0, 'Teldrassil Earthroot'),
(10483, 430, 0, 'Teldrassil Earthroot'),
(10484, 430, 0, 'Teldrassil Earthroot'),
(10485, 430, 0, 'Teldrassil Earthroot');
/* Teldrassil Earthroot 3 */
DELETE FROM `pool_template` WHERE  `entry`=431;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=431;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(431, 3, 'Teldrassil Earthroot Pool 3 of 4--3 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10439, 431, 0, 'Teldrassil Earthroot'),
(10445, 431, 0, 'Teldrassil Earthroot'),
(10446, 431, 0, 'Teldrassil Earthroot'),
(10447, 431, 0, 'Teldrassil Earthroot'),
(10450, 431, 0, 'Teldrassil Earthroot'),
(10452, 431, 0, 'Teldrassil Earthroot'),
(10453, 431, 0, 'Teldrassil Earthroot'),
(10454, 431, 0, 'Teldrassil Earthroot'),
(10469, 431, 0, 'Teldrassil Earthroot'),
(10473, 431, 0, 'Teldrassil Earthroot');
/* Teldrassil Earthroot 4 */
DELETE FROM `pool_template` WHERE  `entry`=432;
DELETE FROM `pool_gameobject` WHERE `pool_entry`=432;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(432, 4, 'Teldrassil Earthroot Pool 4 of 4--4 spawns active');
INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(10448, 432, 0, 'Teldrassil Earthroot'),
(10449, 432, 0, 'Teldrassil Earthroot'),
(10455, 432, 0, 'Teldrassil Earthroot'),
(10456, 432, 0, 'Teldrassil Earthroot'),
(10457, 432, 0, 'Teldrassil Earthroot'),
(10458, 432, 0, 'Teldrassil Earthroot'),
(10462, 432, 0, 'Teldrassil Earthroot'),
(10471, 432, 0, 'Teldrassil Earthroot'),
(10472, 432, 0, 'Teldrassil Earthroot');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_15_02' WHERE sql_rev = '1642127857406476700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
