-- DB update 2021_03_14_08 -> 2021_03_14_09
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_14_08';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_14_08 2021_03_14_09 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1615643547619605800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1615643547619605800');

DELETE FROM `creature` WHERE (`id` = 1008) AND (`guid` IN (9418));
INSERT INTO `creature` VALUES
(9418, 1008, 0, 0, 0, 1, 1, 355, 1, -4155.5625, -2854.758057, 23.074253, 5.859894, 300, 0, 0, 531, 0, 0, 0, 0, 0, '', 0);

DELETE FROM `creature` WHERE (`id` = 1007) AND (`guid` IN (9417));
INSERT INTO `creature` VALUES
(9417, 1007, 0, 0, 0, 1, 1, 3199, 1, -4194.87207, -2871.548828, 41.763573, 6.189762, 300, 0, 0, 494, 0, 0, 0, 0, 0, '', 0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
