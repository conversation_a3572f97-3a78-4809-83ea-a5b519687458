-- DB update 2021_06_01_02 -> 2021_06_01_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_01_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_01_02 2021_06_01_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1621936778445126400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1621936778445126400');

DELETE FROM `quest_poi` WHERE (`QuestID` = 1109);
INSERT INTO `quest_poi` VALUES
(1109, 0, 4, 1, 11, 1717, 0, 1, 0),
(1109, 1, -1, 0, 20, 1497, 0, 1, 0);

DELETE FROM `quest_poi_points` WHERE (`QuestID` = 1109);
INSERT INTO `quest_poi_points` VALUES
(1109, 0, 0, -4536, -1839, 0),
(1109, 0, 1, -4401, -1839, 0),
(1109, 0, 2, -4444, -1702, 0),
(1109, 0, 3, -4503, -1705, 0),
(1109, 0, 4, -4536, -1839, 0),
(1109, 1, 0, 1434, 405, 0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_01_03' WHERE sql_rev = '1621936778445126400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
