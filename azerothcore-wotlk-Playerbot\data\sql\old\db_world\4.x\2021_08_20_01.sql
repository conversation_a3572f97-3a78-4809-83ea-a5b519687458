-- DB update 2021_08_20_00 -> 2021_08_20_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_20_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_20_00 2021_08_20_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1629141581522683284'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1629141581522683284');

DELETE FROM `creature` WHERE `id` IN (1022, 1023);
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `wander_distance`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`) VALUES
(10981, 1023, 0, 0, 0, 1, 1, 949, 0, -3478.4, -1789.35, 17.335, 5.82652, 300, 10, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11021, 1023, 0, 0, 0, 1, 1, 949, 0, -3423.08, -1844.35, 17.3932, 2.99666, 300, 3, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11023, 1023, 0, 0, 0, 1, 1, 949, 0, -3445.97, -1823.6, 23.7985, 5.95543, 300, 5, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11025, 1023, 0, 0, 0, 1, 1, 949, 0, -3442.01, -1767.15, 23.8632, 2.11836, 300, 5, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11045, 1023, 0, 0, 0, 1, 1, 949, 0, -3513.64, -1809.07, 25.05, 1.52582, 300, 5, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11049, 1023, 0, 0, 0, 1, 1, 949, 0, -3421.09, -1779.82, 24.8787, 2.8217, 300, 5, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11051, 1023, 0, 0, 0, 1, 1, 949, 0, -3478.14, -1748.96, 23.9907, 5.89592, 300, 3, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11052, 1023, 0, 0, 0, 1, 1, 949, 0, -3521.59, -1783.81, 23.7833, 3.31749, 300, 3, 0, 787, 0, 1, 0, 0, 0, '', 0),
(11053, 1023, 0, 0, 0, 1, 1, 949, 0, -3490.06, -1721.54, 33.9626, 3.56658, 300, 3, 0, 787, 0, 1, 0, 0, 0, '', 0),
(3110383, 1023, 0, 0, 0, 1, 1, 0, 0, -3512.49, -1751.08, 24.5119, 5.08749, 300, 3, 0, 788, 0, 1, 0, 0, 0, '', 0),
(10956, 1022, 0, 0, 0, 1, 1, 648, 0, -3440.66, -1706.21, 57.0167, 2.98022, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(10968, 1022, 0, 0, 0, 1, 1, 648, 0, -3491.11, -1654.28, 62.576, 4.24776, 300, 3, 0, 734, 0, 1, 0, 0, 0, '', 0),
(10979, 1022, 0, 0, 0, 1, 1, 648, 0, -3554.14, -1746.71, 78.8366, 5.74131, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11022, 1022, 0, 0, 0, 1, 1, 648, 0, -3346.03, -1872.59, 26.1311, 1.9749, 300, 3, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11024, 1022, 0, 0, 0, 1, 1, 648, 0, -3377.73, -1779.3, 33.4907, 6.22871, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11031, 1022, 0, 0, 0, 1, 1, 648, 0, -3484.29, -1684.83, 54.1485, 5.57613, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11035, 1022, 0, 0, 0, 1, 1, 648, 0, -3510.5, -1874.02, 23.525, 0.612291, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11036, 1022, 0, 0, 0, 1, 1, 648, 0, -3351.6, -1813.42, 24.747, 2.67742, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11037, 1022, 0, 0, 0, 1, 1, 648, 0, -3569.1, -1836.11, 25.9402, 0.271386, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11044, 1022, 0, 0, 0, 1, 1, 648, 0, -3528.41, -1853.26, 24.22, 0.420599, 300, 10, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11046, 1022, 0, 0, 0, 1, 1, 648, 0, -3409.11, -1814.36, 17.5763, 6.00478, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11047, 1022, 0, 0, 0, 1, 1, 648, 0, -3577.12, -1862.63, 33.4269, 4.73934, 300, 3, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11048, 1022, 0, 0, 0, 1, 1, 648, 0, -3551.23, -1813.87, 25.3162, 1.6862, 300, 3, 0, 734, 0, 1, 0, 0, 0, '', 0),
(11050, 1022, 0, 0, 0, 1, 1, 648, 0, -3480.99, -1880.51, 23.4294, 0.656766, 300, 5, 0, 734, 0, 1, 0, 0, 0, '', 0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_20_01' WHERE sql_rev = '1629141581522683284';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
