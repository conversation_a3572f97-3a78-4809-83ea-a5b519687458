-- DB update 2021_10_30_00 -> 2021_10_30_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_30_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_30_00 2021_10_30_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635346546989082500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635346546989082500');

DELETE FROM `game_event_creature_quest` WHERE `quest` IN (14166,14171,14174,13952,14169,14167,14172,14175,14168,14170,14177,14173,14176);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_30_01' WHERE sql_rev = '1635346546989082500';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
