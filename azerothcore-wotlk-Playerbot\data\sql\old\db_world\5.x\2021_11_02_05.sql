-- DB update 2021_11_02_04 -> 2021_11_02_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_02_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_02_04 2021_11_02_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635585233227924918'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635585233227924918');

-- Removes 25 incorrect items from Mithril Lockbox
DELETE FROM `item_loot_template` where `entry` = 5758 and `item` IN (785, 1645, 2319, 2449, 2450, 3356, 3357, 3858, 3927, 4234, 4338, 4339, 4419, 4426, 4607, 6149, 7912, 7974, 8932, 8950, 8953, 9030, 11914, 11948, 13446);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_02_05' WHERE sql_rev = '1635585233227924918';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
