-- DB update 2021_03_16_02 -> 2021_03_16_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_16_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_16_02 2021_03_16_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1614791799466997404'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1614791799466997404');

-- Rewrite positions for herbs for zone: Bloodmyst Isle
SET
@POOL            = '11648',
@POOLSIZE        = '30',
@POOLDESC        = 'Herbs - Bloodmyst Isle',
@GUID            = '2134973,2134972,2134971,2134970,2134969,2134968,2134967,2134867,2134974,2134975,2134976,2134977,2134978,2134980,2134981,2134982,2134983,2134984,2134985,2134986,2134987,2134988,2134989,2134990,2134991,2134992,2134993,2134994,2134995,2134996,2134997,2134998,2134999,2135000,2135001,2135002,2135003,2135004,2135005,2135006,2135007,2135008,2135009,2135010,2135011,2135012,2135013,2135014,2135015,2135016,2135017,2135018,2135019,2135020,2135021,2135022,2135023,2135024,2135025,2135026,2135027,2135028,2135029,2135030,2135031,2135032,2135033,2135034,2135035,2135036,2135037,2135038,2135039,2135040,2135041,2135042,2135043,2135044,2135045,2135046,2135047,2135048,2135049,2135050,2135051,2135052,2135053,2135054,2135055,2135056,2135057,2135058,2135059,2135060,2135061,2135062,2135063,2135064,2135065,2135066,2135067,2135068,2135069,2135070,2135071,2135072,2135073,2135074,2135075,2135076,2135077,2135078,2135079,2135080,2135081,2135082,2135083,2135084,2135085,2135086,2135087,2135088,2135089,2135090,2135091,2135092,2135093,2135094,2135095,2135096,2135097,2135098,2135099,2135100,2135101,2135102,2135103,2135104,2135105,2135106,2135107,2135108,2135109,2135110,2135111,2135112,2135113,2135114,2135115,2135116,2135117,2135118,2135119,2135120,2135121,2135122,2135123,2135124,2135125,2135126,2135127,2135128,2135130,2135131,2135132,2135133,2135134,2135135,2135136,2135137,2135138,2135139,2135140,2135141,2135142,2135143,2135144,2135145,2135146,2135147,2135148,2135149,2135150,2135151,2135152,2135153,2135154,2135155,2135156,2135157,2135158,2135159,2135160,2135161,2135162,2135164,2135165,2135166,2135167,2135168,2135169,2135170,2135171,2135172,2135173,2135174,2135175,2135176,2135177,2135178,2135179,2135180,2135181,2135182,2135183,2135184,2135185,2135186,2135187,2135188,2135189,2135190,2135191,2135192,2135193,2135194,2135195,2135196,2135197,2135198,2135199,2135200,2135201,2135202,2135203,2135204,2135205,2135206,2135207,2135208,2135209,2135210,2135211,2135212,2135213,2135214,2135215,2135216,2135217,2135218,2135219,2135220,2135221,2135222,2135223,2135224,2135225,2135226,2135227,2135228,2135229,2135230,2135231,2135232,2135233,2135234,2135235,2135236,2135237,2135238,2135239,2135240,2135241,2135242,2135243,86179,86192,86213,86237,86252,86267,86931,87058,2135129,2135253';

-- Create pool(s)
DELETE FROM `pool_template` WHERE `entry`=@POOL;
INSERT INTO `pool_template` (`entry`,`max_limit`,`description`) VALUES (@POOL,@POOLSIZE,@POOLDESC);

-- Create new gameobjects
DELETE FROM `gameobject` WHERE FIND_IN_SET (`guid`,@GUID);
INSERT INTO `gameobject` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`position_x`,`position_y`,`position_z`,`orientation`,`rotation0`,`rotation1`,`rotation2`,`rotation3`,`spawntimesecs`,`animprogress`,`state`,`ScriptName`,`VerifiedBuild`) VALUES
(86179,1621,530,0,0,1,1,-2707.13,-11318,9.59332,0.349187,-0,-0,-0.173708,-0.984797,300,0,1,'',0),
(86192,1618,530,0,0,1,1,-2300.41,-11572.5,24.1164,2.94854,-0,-0,-0.995345,-0.0963771,300,0,1,'',0),
(86213,1617,530,0,0,1,1,-1462.03,-10695.1,69.7413,6.06814,-0,-0,-0.107314,0.994225,300,0,1,'',0),
(86237,2045,530,0,0,1,1,-1282.43,-11210.1,-15.7532,0.629103,-0,-0,-0.30939,-0.950935,300,0,1,'',0),
(86252,1621,530,0,0,1,1,-2037.23,-11141.8,56.2479,2.71009,-0,-0,-0.976816,-0.21408,300,0,1,'',0),
(86267,1617,530,0,0,1,1,-1861.06,-10887.4,66.7737,5.75116,-0,-0,-0.262889,0.964826,300,0,1,'',0),
(86931,1622,530,0,0,1,1,-1956.76,-10619.8,163.835,4.71097,-0,-0,-0.707607,0.706606,300,0,1,'',0),
(87058,1621,530,0,0,1,1,-1554.38,-11722.6,28.4095,1.21313,-0,-0,-0.570047,-0.821612,300,0,1,'',0),
(2134867,1619,530,0,0,1,1,-1930.66,-11040.7,65.683,4.89413,-0,-0,-0.640023,0.768356,300,0,1,'',0),
(2134967,1617,530,0,0,1,1,-1617.46,-11171.2,74.3733,5.65439,-0,-0,-0.309242,0.950984,300,0,1,'',0),
(2134968,1621,530,0,0,1,1,-2135.11,-11650.8,43.9441,2.71009,-0,-0,-0.976816,-0.21408,300,0,1,'',0),
(2134969,1621,530,0,0,1,1,-2696.26,-11344.1,13.063,6.1382,-0,-0,-0.0724296,0.997374,300,0,1,'',0),
(2134970,1617,530,0,0,1,1,-1410.83,-11843.3,19.1778,5.19478,-0,-0,-0.517737,0.85554,300,0,1,'',0),
(2134971,1620,530,0,0,1,1,-1393.43,-11510.5,15.0982,5.59565,-0,-0,-0.337038,0.941491,300,0,1,'',0),
(2134972,1621,530,0,0,1,1,-1848.01,-11712.8,31.9509,1.85825,-0,-0,-0.801096,-0.598536,300,0,1,'',0),
(2134973,1619,530,0,0,1,1,-1643.56,-11846.5,21.4463,5.02199,-0,-0,-0.589627,0.807676,300,0,1,'',0),
(2134974,1619,530,0,0,1,1,-1839.31,-11546.4,39.235,1.61745,-0,-0,-0.723407,-0.690422,300,0,1,'',0),
(2134975,1617,530,0,0,1,1,-2579.63,-11248,28.5477,0.846185,-0,-0,-0.410582,-0.911824,300,0,1,'',0),
(2134976,1617,530,0,0,1,1,-1288.67,-11362.5,19.4374,2.55772,-0,-0,-0.957689,-0.287805,300,0,1,'',0),
(2134977,1617,530,0,0,1,1,-1961.11,-11034.2,58.4832,5.2397,-0,-0,-0.49839,0.866953,300,0,1,'',0),
(2134978,1621,530,0,0,1,1,-1371.68,-11817.2,19.479,3.33904,-0,-0,-0.995131,0.0985632,300,0,1,'',0),
(2134980,1621,530,0,0,1,1,-1826.26,-11494.2,42.9483,0.86755,-0,-0,-0.420299,-0.907386,300,0,1,'',0),
(2134981,1619,530,0,0,1,1,-2417.86,-11442,32.0436,3.62587,-0,-0,-0.970828,0.239778,300,0,1,'',0),
(2134982,1620,530,0,0,1,1,-1465.21,-11256,11.92,1.20967,-0,-0,-0.568626,-0.822596,300,0,1,'',0),
(2134983,1622,530,0,0,1,1,-1460.86,-11370.2,73.3587,4.1546,-0,-0,-0.874447,0.485122,300,0,1,'',0),
(2134984,1621,530,0,0,1,1,-2267.78,-11337.6,53.104,2.5926,-0,-0,-0.962561,-0.271064,300,0,1,'',0),
(2134985,1619,530,0,0,1,1,-2513.56,-12267.4,15.9099,1.95674,-0,-0,-0.829588,-0.558376,300,0,1,'',0),
(2134986,1619,530,0,0,1,1,-1482.72,-11020.8,71.4534,4.38613,-0,-0,-0.812557,0.582881,300,0,1,'',0),
(2134987,1617,530,0,0,1,1,-2204.71,-12371.8,43.2988,3.68807,-0,-0,-0.962902,0.269852,300,0,1,'',0),
(2134988,1617,530,0,0,1,1,-1526.11,-11853.1,23.2161,4.79046,-0,-0,-0.678974,0.734162,300,0,1,'',0),
(2134989,1618,530,0,0,1,1,-1858.88,-11386.5,50.1109,5.17404,-0,-0,-0.526578,0.850127,300,0,1,'',0),
(2134990,1617,530,0,0,1,1,-1660.96,-11918.3,9.28671,0.290439,-0,-0,-0.14471,-0.989474,300,0,1,'',0),
(2134991,1622,530,0,0,1,1,-1423.88,-11461.6,73.6122,3.225,-0,-0,-0.999131,0.0416914,300,0,1,'',0),
(2134992,1621,530,0,0,1,1,-1680.53,-11451.8,43.5993,4.20298,-0,-0,-0.862457,0.506131,300,0,1,'',0),
(2134993,1621,530,0,0,1,1,-1650.08,-11197.3,71.317,3.56712,-0,-0,-0.977451,0.211162,300,0,1,'',0),
(2134994,1621,530,0,0,1,1,-2117.71,-11014.6,58.8271,3.1075,-0,-0,-0.999855,-0.0170438,300,0,1,'',0),
(2134995,1619,530,0,0,1,1,-1519.58,-10887.4,57.9747,4.95979,-0,-0,-0.614458,0.78895,300,0,1,'',0),
(2134996,1618,530,0,0,1,1,-2319.98,-12205.4,28.8935,1.50403,-0,-0,-0.683113,-0.730313,300,0,1,'',0),
(2134997,1617,530,0,0,1,1,-1560.91,-10858,55.5394,1.71484,-0,-0,-0.756155,-0.654392,300,0,1,'',0),
(2134998,1618,530,0,0,1,1,-2707.13,-12068.4,12.9119,5.29437,-0,-0,-0.47451,0.88025,300,0,1,'',0),
(2134999,1621,530,0,0,1,1,-2350.43,-11236.4,29.4389,1.38591,-0,-0,-0.638815,-0.769361,300,0,1,'',0),
(2135000,1619,530,0,0,1,1,-1850.18,-12052.1,40.5909,5.99997,-0,-0,-0.141135,0.98999,300,0,1,'',0),
(2135001,1619,530,0,0,1,1,-1602.23,-10698.1,135.578,5.94122,-0,-0,-0.17015,0.985418,300,0,1,'',0),
(2135002,1617,530,0,0,1,1,-2696.26,-11347.4,13.349,4.77663,-0,-0,-0.684032,0.729452,300,0,1,'',0),
(2135003,1618,530,0,0,1,1,-1502.18,-11079.8,75.4962,5.23625,-0,-0,-0.499887,0.86609,300,0,1,'',0),
(2135004,1620,530,0,0,1,1,-2134.89,-10757.3,68.211,5.60082,-0,-0,-0.334602,0.94236,300,0,1,'',0),
(2135005,1617,530,0,0,1,1,-2267.78,-12169.5,32.7346,0.456315,-0,-0,-0.226183,-0.974085,300,0,1,'',0),
(2135006,1621,530,0,0,1,1,-1506.53,-11579,26.4345,2.13644,-0,-0,-0.876344,-0.481686,300,0,1,'',0),
(2135007,1620,530,0,0,1,1,-2485.28,-12264.1,14.7819,1.21313,-0,-0,-0.570047,-0.821612,300,0,1,'',0),
(2135008,1618,530,0,0,1,1,-1948.06,-11115.7,58.0927,1.79024,-0,-0,-0.780283,-0.625427,300,0,1,'',0),
(2135009,1619,530,0,0,1,1,-1463.03,-11272.3,12.4479,2.76193,-0,-0,-0.982036,-0.188694,300,0,1,'',0),
(2135010,2045,530,0,0,1,1,-1125.82,-11327.7,-17.1777,6.11401,-0,-0,-0.0844871,0.996425,300,0,1,'',0),
(2135011,1618,530,0,0,1,1,-1473.91,-11425.7,68.5824,5.05655,-0,-0,-0.575584,0.817743,300,0,1,'',0),
(2135012,1618,530,0,0,1,1,-1669.66,-11999.9,13.6359,1.96365,-0,-0,-0.831513,-0.555506,300,0,1,'',0),
(2135013,1617,530,0,0,1,1,-1941.53,-11243,66.5286,4.14769,-0,-0,-0.876118,0.482097,300,0,1,'',0),
(2135014,1619,530,0,0,1,1,-1482.61,-11898.7,23.0455,3.05221,-0,-0,-0.999002,-0.0446753,300,0,1,'',0),
(2135015,1619,530,0,0,1,1,-2152.51,-11376.7,59.3134,2.06041,-0,-0,-0.857405,-0.514643,300,0,1,'',0),
(2135016,1618,530,0,0,1,1,-1589.18,-11259.3,68.6205,5.01162,-0,-0,-0.593806,0.804608,300,0,1,'',0),
(2135017,1617,530,0,0,1,1,-2524.69,-11998.7,26.303,3.24778,-0,-0,-0.998591,0.0530684,300,0,1,'',0),
(2135018,1620,530,0,0,1,1,-1571.78,-11582.3,31.5565,2.01203,-0,-0,-0.844706,-0.535231,300,0,1,'',0),
(2135019,1618,530,0,0,1,1,-1858.88,-11608.4,36.4073,0.293895,-0,-0,-0.146419,-0.989223,300,0,1,'',0),
(2135020,1618,530,0,0,1,1,-1454.33,-12505.6,8.21452,1.95328,-0,-0,-0.828622,-0.559809,300,0,1,'',0),
(2135021,1619,530,0,0,1,1,-2219.93,-12371.8,48.7326,2.54076,-0,-0,-0.955214,-0.295918,300,0,1,'',0),
(2135022,1617,530,0,0,1,1,-2119.88,-11004.8,58.5163,4.71789,-0,-0,-0.705161,0.709047,300,0,1,'',0),
(2135023,1620,530,0,0,1,1,-1117.21,-12430.5,29.5566,3.40815,-0,-0,-0.991131,0.132887,300,0,1,'',0),
(2135024,1617,530,0,0,1,1,-1260.53,-11571.6,5.5945,4.51305,-0,-0,-0.773957,0.633238,300,0,1,'',0),
(2135025,1621,530,0,0,1,1,-2050.28,-12166.3,29.7143,1.08526,-0,-0,-0.516391,-0.856353,300,0,1,'',0),
(2135026,1621,530,0,0,1,1,-2241.21,-11018.6,12.4982,5.27394,-0,-0,-0.483476,0.875358,300,0,1,'',0),
(2135027,2045,530,0,0,1,1,-1182.47,-11471.3,-2.74499,1.71075,-0,-0,-0.754818,-0.655934,300,0,1,'',0),
(2135028,1620,530,0,0,1,1,-1486.96,-10955.9,61.3125,5.19478,-0,-0,-0.517737,0.85554,300,0,1,'',0),
(2135029,1619,530,0,0,1,1,-2594.03,-11614.9,21.6348,3.9749,-0,-0,-0.914449,0.404702,300,0,1,'',0),
(2135030,1617,530,0,0,1,1,-1747.96,-11663.8,33.1563,2.91571,-0,-0,-0.993629,-0.112701,300,0,1,'',0),
(2135031,1619,530,0,0,1,1,-1926.31,-12211.9,7.41367,1.44874,-0,-0,-0.662664,-0.748916,300,0,1,'',0),
(2135032,2045,530,0,0,1,1,-1208.56,-11272.3,-16.1342,0.750054,-0,-0,-0.366298,-0.930498,300,0,1,'',0),
(2135033,1621,530,0,0,1,1,-1583.94,-11943.2,13.1134,0.975145,-0,-0,-0.468482,-0.883473,300,0,1,'',0),
(2135034,1621,530,0,0,1,1,-2154.68,-11585.5,30.529,1.20338,-0,-0,-0.566038,-0.824379,300,0,1,'',0),
(2135035,2045,530,0,0,1,1,-1284.59,-11122.2,-15.7311,2.36106,-0,-0,-0.924808,-0.380434,300,0,1,'',0),
(2135036,1620,530,0,0,1,1,-1869.76,-11109.2,58.2771,1.27187,-0,-0,-0.593931,-0.804516,300,0,1,'',0),
(2135037,1621,530,0,0,1,1,-2641.88,-12247.8,13.8454,5.29437,-0,-0,-0.47451,0.88025,300,0,1,'',0),
(2135038,1618,530,0,0,1,1,-1345.58,-12593.6,9.78936,4.43451,-0,-0,-0.798221,0.602365,300,0,1,'',0),
(2135039,1617,530,0,0,1,1,-2694.08,-11497.4,17.8847,5.29437,-0,-0,-0.47451,0.88025,300,0,1,'',0),
(2135040,1619,530,0,0,1,1,-1921.96,-11409.4,68.3989,2.99346,-0,-0,-0.997258,-0.0739968,300,0,1,'',0),
(2135041,1619,530,0,0,1,1,-1339.06,-11409.4,19.8251,5.76843,-0,-0,-0.254544,0.967061,300,0,1,'',0),
(2135042,1617,530,0,0,1,1,-1811.03,-11637.7,35.162,5.51357,-0,-0,-0.37538,0.926871,300,0,1,'',0),
(2135043,1620,530,0,0,1,1,-2319.98,-11207.1,14.5825,5.99997,-0,-0,-0.141135,0.98999,300,0,1,'',0),
(2135044,1618,530,0,0,1,1,-1678.36,-11122.3,71.8571,1.3168,-0,-0,-0.611851,-0.790973,300,0,1,'',0),
(2135045,1617,530,0,0,1,1,-2022.01,-11464.8,59.3152,4.6073,-0,-0,-0.743268,0.668994,300,0,1,'',0),
(2135046,1618,530,0,0,1,1,-1769.71,-11885.7,17.4719,2.42672,-0,-0,-0.936797,-0.349874,300,0,1,'',0),
(2135047,1622,530,0,0,1,1,-1410.83,-10629.6,111.272,1.78049,-0,-0,-0.777227,-0.62922,300,0,1,'',0),
(2135048,1621,530,0,0,1,1,-2694.08,-11455,19.812,1.24768,-0,-0,-0.584157,-0.81164,300,0,1,'',0),
(2135049,1621,530,0,0,1,1,-1510.88,-11340.8,68.2522,1.72175,-0,-0,-0.758412,-0.651776,300,0,1,'',0),
(2135050,1619,530,0,0,1,1,-1637.03,-10802.5,63.1975,0.407935,-0,-0,-0.202556,-0.979271,300,0,1,'',0),
(2135051,1618,530,0,0,1,1,-2333.03,-12091.2,31.9315,1.32717,-0,-0,-0.615943,-0.787791,300,0,1,'',0),
(2135052,1620,530,0,0,1,1,-1887.16,-10831.9,66.3456,3.6155,-0,-0,-0.972058,0.234742,300,0,1,'',0),
(2135053,1618,530,0,0,1,1,-2280.83,-11748.7,17.9522,0.349187,-0,-0,-0.173708,-0.984797,300,0,1,'',0),
(2135054,1617,530,0,0,1,1,-1619.63,-10688.3,132.138,0.964311,-0,-0,-0.46369,-0.885998,300,0,1,'',0),
(2135055,1617,530,0,0,1,1,-2465.71,-11164.7,13.9838,3.92306,-0,-0,-0.92463,0.380868,300,0,1,'',0),
(2135056,1617,530,0,0,1,1,-1366.35,-12302.1,10.7032,2.37205,-0,-0,-0.926884,-0.375348,300,0,1,'',0),
(2135057,1617,530,0,0,1,1,-2533.13,-11415.9,45.7812,3.45308,-0,-0,-0.987897,0.155114,300,0,1,'',0),
(2135058,1617,530,0,0,1,1,-2672.76,-12069.8,16.6562,5.28557,-0,-0,-0.47838,0.878153,300,0,1,'',0),
(2135059,1617,530,0,0,1,1,-1369.51,-11621.4,11.9918,0.176399,-0,-0,-0.0880854,-0.996113,300,0,1,'',0),
(2135060,1621,530,0,0,1,1,-2406.98,-12260.9,28.2949,2.00166,-0,-0,-0.84192,-0.539603,300,0,1,'',0),
(2135061,2045,530,0,0,1,1,-2014.33,-12424.3,-3.04628,3.69153,-0,-0,-0.962434,0.271515,300,0,1,'',0),
(2135062,1618,530,0,0,1,1,-1834.96,-11977,16.2158,6.17276,-0,-0,-0.0551861,0.998476,300,0,1,'',0),
(2135063,1618,530,0,0,1,1,-1985.03,-11513.8,59.4635,3.2872,-0,-0,-0.997351,0.0727408,300,0,1,'',0),
(2135064,1620,530,0,0,1,1,-2667.98,-11787.8,2.06947,5.14294,-0,-0,-0.539734,0.841835,300,0,1,'',0),
(2135065,1619,530,0,0,1,1,-1193.33,-11761.7,5.5204,2.17791,-0,-0,-0.886142,-0.463413,300,0,1,'',0),
(2135066,1619,530,0,0,1,1,-1602.23,-11712.8,36.7399,0.491737,-0,-0,-0.243399,-0.969926,300,0,1,'',0),
(2135067,2045,530,0,0,1,1,-1554.38,-12655.6,-16.1362,4.21335,-0,-0,-0.859822,0.510595,300,0,1,'',0),
(2135068,1617,530,0,0,1,1,-1641.24,-11478.9,45.5116,4.8536,-0,-0,-0.655461,0.755229,300,0,1,'',0),
(2135069,1618,530,0,0,1,1,-1326.01,-12277.2,13.6575,4.04056,-0,-0,-0.900672,0.4345,300,0,1,'',0),
(2135070,1617,530,0,0,1,1,-1625.46,-11790.3,24.5884,5.00801,-0,-0,-0.595261,0.803533,300,0,1,'',0),
(2135071,1617,530,0,0,1,1,-2122.06,-10779.7,67.9923,3.86777,-0,-0,-0.934804,0.355163,300,0,1,'',0),
(2135072,1617,530,0,0,1,1,-1643.56,-11027.6,68.7976,1.71075,-0,-0,-0.754818,-0.655934,300,0,1,'',0),
(2135073,1621,530,0,0,1,1,-2526.67,-11751.3,11.6404,4.2677,-0,-0,-0.84563,0.533769,300,0,1,'',0),
(2135074,1621,530,0,0,1,1,-1428.23,-10659,78.0609,4.62112,-0,-0,-0.738626,0.674115,300,0,1,'',0),
(2135075,2045,530,0,0,1,1,-1075.8,-11680.2,-18.8188,5.70969,-0,-0,-0.282836,0.959168,300,0,1,'',0),
(2135076,2045,530,0,0,1,1,-1169.41,-11337.6,-16.1122,1.44812,-0,-0,-0.66243,-0.749124,300,0,1,'',0),
(2135077,1618,530,0,0,1,1,-1215.08,-12593.6,9.41198,1.19647,-0,-0,-0.563186,-0.82633,300,0,1,'',0),
(2135078,1618,530,0,0,1,1,-1267.28,-11406.1,8.04105,0.235147,-0,-0,-0.117303,-0.993096,300,0,1,'',0),
(2135079,1618,530,0,0,1,1,-1950.23,-11680.1,38.415,5.57405,-0,-0,-0.347186,0.937796,300,0,1,'',0),
(2135080,1617,530,0,0,1,1,-1467.38,-11517,48.2519,3.51183,-0,-0,-0.982915,0.184062,300,0,1,'',0),
(2135081,1621,530,0,0,1,1,-2480.93,-11862.8,18.922,4.26173,-0,-0,-0.84722,0.531242,300,0,1,'',0),
(2135082,1621,530,0,0,1,1,-1717.51,-10978.7,76.8152,0.63947,-0,-0,-0.314315,-0.949319,300,0,1,'',0),
(2135083,1618,530,0,0,1,1,-2683.21,-11960.7,11.0611,5.29437,-0,-0,-0.47451,0.88025,300,0,1,'',0),
(2135084,1618,530,0,0,1,1,-2737.58,-11415.9,15.6514,5.31227,-0,-0,-0.466611,0.884463,300,0,1,'',0),
(2135085,1620,530,0,0,1,1,-1889.33,-11259.3,57.5026,5.2397,-0,-0,-0.49839,0.866953,300,0,1,'',0),
(2135086,1621,530,0,0,1,1,-2430.91,-11376.7,32.5411,1.50403,-0,-0,-0.683113,-0.730313,300,0,1,'',0),
(2135087,2045,530,0,0,1,1,-1158.3,-11579,-15.1796,5.42286,-0,-0,-0.417019,0.908898,300,0,1,'',0),
(2135088,1617,530,0,0,1,1,-2615.82,-12221.3,16.5007,0.186444,-0,-0,-0.0930872,-0.995658,300,0,1,'',0),
(2135089,1620,530,0,0,1,1,-2135.11,-10747.1,68.1443,0.798435,-0,-0,-0.388697,-0.921365,300,0,1,'',0),
(2135090,1617,530,0,0,1,1,-1652.26,-10802.5,56.839,0.331908,-0,-0,-0.165193,-0.986261,300,0,1,'',0),
(2135091,1620,530,0,0,1,1,-1765.36,-11435.5,45.5945,4.43797,-0,-0,-0.797179,0.603743,300,0,1,'',0),
(2135092,1617,530,0,0,1,1,-1637.03,-11419.1,47.2873,1.32717,-0,-0,-0.615943,-0.787791,300,0,1,'',0),
(2135093,1617,530,0,0,1,1,-1856.25,-12164.1,20.0455,1.97527,-0,-0,-0.834727,-0.550664,300,0,1,'',0),
(2135094,1620,530,0,0,1,1,-1882.81,-11999.9,18.6896,2.12953,-0,-0,-0.874674,-0.484712,300,0,1,'',0),
(2135095,1617,530,0,0,1,1,-1813.21,-11543.1,34.4599,5.27599,-0,-0,-0.48258,0.875852,300,0,1,'',0),
(2135096,1617,530,0,0,1,1,-1476.08,-11380,67.9703,1.3347,-0,-0,-0.618907,-0.785464,300,0,1,'',0),
(2135097,1622,530,0,0,1,1,-1961.11,-10962.4,63.9686,5.36411,-0,-0,-0.443533,0.896258,300,0,1,'',0),
(2135098,1619,530,0,0,1,1,-2561.41,-11295.2,42.7596,0.41139,-0,-0,-0.204248,-0.978919,300,0,1,'',0),
(2135099,2045,530,0,0,1,1,-1093.18,-11141.9,-79.661,5.28808,-0,-0,-0.477274,0.878754,300,0,1,'',0),
(2135100,2045,530,0,0,1,1,-1082.07,-11206.7,-52.43,5.28808,-0,-0,-0.477274,0.878754,300,0,1,'',0),
(2135101,1618,530,0,0,1,1,-1584.83,-11471.3,52.7556,1.62153,-0,-0,-0.724814,-0.688944,300,0,1,'',0),
(2135102,1617,530,0,0,1,1,-2390.11,-11669.2,18.2109,1.99224,-0,-0,-0.839368,-0.543564,300,0,1,'',0),
(2135103,1618,530,0,0,1,1,-1571.78,-11768.2,27.3129,1.13364,-0,-0,-0.536953,-0.843612,300,0,1,'',0),
(2135104,1620,530,0,0,1,1,-1652.26,-11066.8,73.5354,1.73149,-0,-0,-0.761578,-0.648073,300,0,1,'',0),
(2135105,1622,530,0,0,1,1,-1389.08,-11422.4,74.4322,2.3576,-0,-0,-0.924149,-0.382032,300,0,1,'',0),
(2135106,2045,530,0,0,1,1,-1965.17,-12336,-2.07199,0.570355,-0,-0,-0.281328,-0.959612,300,0,1,'',0),
(2135107,2045,530,0,0,1,1,-1584.62,-12215.2,-12.6227,2.58914,-0,-0,-0.962092,-0.272727,300,0,1,'',0),
(2135108,1618,530,0,0,1,1,-1432.58,-11598.6,16.0804,4.03019,-0,-0,-0.902912,0.429825,300,0,1,'',0),
(2135109,1620,530,0,0,1,1,-2530.96,-11347.4,23.1518,2.64789,-0,-0,-0.969686,-0.244353,300,0,1,'',0),
(2135110,1621,530,0,0,1,1,-1448.87,-12031.2,7.6451,2.25535,-0,-0,-0.903417,-0.428763,300,0,1,'',0),
(2135111,1621,530,0,0,1,1,-2613.61,-11595.3,20.9579,1.34099,-0,-0,-0.621373,-0.783515,300,0,1,'',0),
(2135112,1621,530,0,0,1,1,-2087.26,-11474.6,62.2446,1.02997,-0,-0,-0.492522,-0.8703,300,0,1,'',0),
(2135113,1621,530,0,0,1,1,-1397.78,-11458.3,75.0448,0.280072,-0,-0,-0.139579,-0.990211,300,0,1,'',0),
(2135114,2045,530,0,0,1,1,-1623.98,-12287,-16.0935,1.7252,-0,-0,-0.759537,-0.650464,300,0,1,'',0),
(2135115,1618,530,0,0,1,1,-2476.58,-12198.9,31.7401,1.89799,-0,-0,-0.812831,-0.5825,300,0,1,'',0),
(2135116,1617,530,0,0,1,1,-1998.08,-10916.7,59.3806,1.15846,-0,-0,-0.547379,-0.836885,300,0,1,'',0),
(2135117,1617,530,0,0,1,1,-1467.38,-11477.9,69.6047,1.49712,-0,-0,-0.680586,-0.732669,300,0,1,'',0),
(2135118,1618,530,0,0,1,1,-2694.08,-11549.6,13.6134,6.02416,-0,-0,-0.129151,0.991625,300,0,1,'',0),
(2135119,1622,530,0,0,1,1,-1149.83,-12378.3,27.231,2.07423,-0,-0,-0.860941,-0.508705,300,0,1,'',0),
(2135120,1617,530,0,0,1,1,-2163.38,-11980.3,32.9521,1.33125,-0,-0,-0.617549,-0.786532,300,0,1,'',0),
(2135121,1621,530,0,0,1,1,-2019.83,-11192.4,67.0625,2.47746,-0,-0,-0.945371,-0.325997,300,0,1,'',0),
(2135122,1619,530,0,0,1,1,-1497.83,-11471.3,67.6907,4.26518,-0,-0,-0.846301,0.532706,300,0,1,'',0),
(2135123,2045,530,0,0,1,1,-1194.94,-10965.4,-64.7655,1.85462,-0,-0,-0.80001,-0.599987,300,0,1,'',0),
(2135124,1620,530,0,0,1,1,-1428.23,-11321.3,14.515,1.19239,-0,-0,-0.561498,-0.827478,300,0,1,'',0),
(2135125,1620,530,0,0,1,1,-1121.56,-12505.6,20.9255,3.97144,-0,-0,-0.915147,0.403121,300,0,1,'',0),
(2135126,1619,530,0,0,1,1,-1758.83,-11393,56.4666,4.9978,-0,-0,-0.599353,0.800485,300,0,1,'',0),
(2135127,1619,530,0,0,1,1,-2330.86,-11239.7,35.9709,1.14401,-0,-0,-0.541319,-0.840817,300,0,1,'',0),
(2135128,1618,530,0,0,1,1,-1815.38,-10835.2,68.1865,1.32717,-0,-0,-0.615943,-0.787791,300,0,1,'',0),
(2135129,1620,530,0,0,1,1,-2141.63,-12352.2,23.4826,1.7252,-0,-0,-0.759537,-0.650464,300,0,1,'',0),
(2135130,1617,530,0,0,1,1,-2421.11,-12078.3,31.6773,5.1599,-0,-0,-0.532575,0.846383,300,0,1,'',0),
(2135131,1617,530,0,0,1,1,-1791.04,-11952,13.5017,4.63526,-0,-0,-0.733844,0.679318,300,0,1,'',0),
(2135132,1618,530,0,0,1,1,-1721.86,-11265.8,60.5676,4.66259,-0,-0,-0.724491,0.689284,300,0,1,'',0),
(2135133,1620,530,0,0,1,1,-2517.91,-12172.8,28.1672,1.10254,-0,-0,-0.52377,-0.85186,300,0,1,'',0),
(2135134,1621,530,0,0,1,1,-2141.63,-11458.3,86.8059,4.19952,-0,-0,-0.86333,0.50464,300,0,1,'',0),
(2135135,1621,530,0,0,1,1,-1950.23,-11076.6,56.594,1.61745,-0,-0,-0.723407,-0.690422,300,0,1,'',0),
(2135136,1617,530,0,0,1,1,-1426.06,-12472.9,7.66336,2.70318,-0,-0,-0.976071,-0.217455,300,0,1,'',0),
(2135137,1617,530,0,0,1,1,-2417.86,-11331.1,29.6214,2.25048,-0,-0,-0.90237,-0.430961,300,0,1,'',0),
(2135138,1618,530,0,0,1,1,-1536.98,-10933,60.7212,1.09909,-0,-0,-0.522297,-0.852763,300,0,1,'',0),
(2135139,2045,530,0,0,1,1,-1280.2,-11030.9,-19.1916,4.83884,-0,-0,-0.661018,0.75037,300,0,1,'',0),
(2135140,1618,530,0,0,1,1,-1780.58,-12182.6,11.6772,2.00166,-0,-0,-0.84192,-0.539603,300,0,1,'',0),
(2135141,1618,530,0,0,1,1,-1449.98,-11761.7,20.8669,5.07383,-0,-0,-0.568498,0.822685,300,0,1,'',0),
(2135142,1618,530,0,0,1,1,-1393.43,-11882.4,20.9838,4.60385,-0,-0,-0.744423,0.667708,300,0,1,'',0),
(2135143,1619,530,0,0,1,1,-1713.16,-12019.4,18.1,4.95288,-0,-0,-0.617181,0.786822,300,0,1,'',0),
(2135144,1620,530,0,0,1,1,-2343.91,-11295.2,31.5475,5.31227,-0,-0,-0.466611,0.884463,300,0,1,'',0),
(2135145,1622,530,0,0,1,1,-2041.58,-11229.9,83.6546,6.15893,-0,-0,-0.0620857,0.998071,300,0,1,'',0),
(2135146,1618,530,0,0,1,1,-2089.43,-10942.8,66.9577,3.91615,-0,-0,-0.92594,0.37767,300,0,1,'',0),
(2135147,1617,530,0,0,1,1,-1551.74,-11330.2,67.8829,4.80396,-0,-0,-0.674003,0.738729,300,0,1,'',0),
(2135148,1620,530,0,0,1,1,-2487.46,-12270.7,13.7173,5.2397,-0,-0,-0.49839,0.866953,300,0,1,'',0),
(2135149,1620,530,0,0,1,1,-2256.91,-11468.1,32.9544,4.8492,-0,-0,-0.657119,0.753787,300,0,1,'',0),
(2135150,1618,530,0,0,1,1,-2624.48,-11229.9,18.1179,2.81031,-0,-0,-0.986313,-0.164885,300,0,1,'',0),
(2135151,1618,530,0,0,1,1,-1410.83,-11298.4,12.7623,1.08872,-0,-0,-0.51787,-0.855459,300,0,1,'',0),
(2135152,1617,530,0,0,1,1,-1523.91,-11945.3,19.0846,5.33002,-0,-0,-0.458745,0.888568,300,0,1,'',0),
(2135153,1617,530,0,0,1,1,-2676.76,-11647.9,14.6764,6.16506,-0,-0,-0.0590288,0.998256,300,0,1,'',0),
(2135154,1622,530,0,0,1,1,-1504.36,-11262.5,68.9758,4.48981,-0,-0,-0.781265,0.624199,300,0,1,'',0),
(2135155,1622,530,0,0,1,1,-1623.98,-10982,65.8469,0.155665,-0,-0,-0.0777537,-0.996973,300,0,1,'',0),
(2135156,2045,530,0,0,1,1,-1179.61,-11239.1,-22.6852,3.72954,-0,-0,-0.9571,0.289757,300,0,1,'',0),
(2135157,1620,530,0,0,1,1,-2293.88,-11334.3,35.3803,0.580722,-0,-0,-0.286298,-0.958141,300,0,1,'',0),
(2135158,1617,530,0,0,1,1,-1968.06,-12127.8,16.1206,0.514119,-0,-0,-0.254238,-0.967142,300,0,1,'',0),
(2135159,1617,530,0,0,1,1,-2322.69,-11149.7,13.9408,2.61066,-0,-0,-0.96497,-0.262359,300,0,1,'',0),
(2135160,2045,530,0,0,1,1,-1471.79,-12202.1,-15.6931,1.49367,-0,-0,-0.679318,-0.733844,300,0,1,'',0),
(2135161,1618,530,0,0,1,1,-2015.53,-12132.2,24.1932,0.467308,-0,-0,-0.231534,-0.972827,300,0,1,'',0),
(2135162,1620,530,0,0,1,1,-1310.78,-12512.1,25.8649,4.79046,-0,-0,-0.678974,0.734162,300,0,1,'',0),
(2135164,1617,530,0,0,1,1,-1491.73,-10991.5,63.8269,2.53699,-0,-0,-0.954654,-0.297717,300,0,1,'',0),
(2135165,1618,530,0,0,1,1,-1682.71,-11373.5,52.6333,1.50341,-0,-0,-0.682885,-0.730526,300,0,1,'',0),
(2135166,1617,530,0,0,1,1,-2133.67,-12243.7,37.8712,2.95844,-0,-0,-0.99581,-0.0914505,300,0,1,'',0),
(2135167,1618,530,0,0,1,1,-2587.51,-11716,11.3197,4.61767,-0,-0,-0.73979,0.672837,300,0,1,'',0),
(2135168,1620,530,0,0,1,1,-1696.11,-10949.6,65.4333,1.96444,-0,-0,-0.831731,-0.555179,300,0,1,'',0),
(2135169,1622,530,0,0,1,1,-1419.53,-10724.2,77.2397,1.66237,-0,-0,-0.738732,-0.674,300,0,1,'',0),
(2135170,1619,530,0,0,1,1,-2104.66,-11203.8,80.0422,2.3576,-0,-0,-0.924149,-0.382032,300,0,1,'',0),
(2135171,1620,530,0,0,1,1,-1702.28,-10776.4,61.5738,5.46087,-0,-0,-0.39967,0.916659,300,0,1,'',0),
(2135172,1622,530,0,0,1,1,-1306.43,-12365.3,30.1242,2.08115,-0,-0,-0.862694,-0.505726,300,0,1,'',0),
(2135173,1622,530,0,0,1,1,-2258.52,-11264.9,57.6211,2.52953,-0,-0,-0.953537,-0.301277,300,0,1,'',0),
(2135174,1620,530,0,0,1,1,-2141.63,-11288.6,60.9258,5.65094,-0,-0,-0.310884,0.950448,300,0,1,'',0),
(2135175,1621,530,0,0,1,1,-1928.48,-11344.1,64.75,2.24357,-0,-0,-0.900876,-0.434077,300,0,1,'',0),
(2135176,1617,530,0,0,1,1,-2250.38,-11817.2,32.8012,4.26518,-0,-0,-0.846301,0.532706,300,0,1,'',0),
(2135177,1617,530,0,0,1,1,-1021.51,-12414.2,8.65259,1.5587,-0,-0,-0.702817,-0.71137,300,0,1,'',0),
(2135178,1619,530,0,0,1,1,-1393.43,-10707.9,80.8998,0.0416248,-0,-0,-0.0208106,-0.999784,300,0,1,'',0),
(2135179,1618,530,0,0,1,1,-2085.08,-11585.5,47.2228,2.18482,-0,-0,-0.887738,-0.460348,300,0,1,'',0),
(2135180,1617,530,0,0,1,1,-2017.66,-12208.7,30.5607,1.79024,-0,-0,-0.780283,-0.625427,300,0,1,'',0),
(2135181,1622,530,0,0,1,1,-1560.91,-11190.8,64.2225,4.66259,-0,-0,-0.724491,0.689284,300,0,1,'',0),
(2135182,1621,530,0,0,1,1,-1945.88,-11504,55.3071,3.04876,-0,-0,-0.998923,-0.0464017,300,0,1,'',0),
(2135183,2045,530,0,0,1,1,-1173.86,-11109.4,-76.4126,4.65568,-0,-0,-0.726869,0.686776,300,0,1,'',0),
(2135184,1618,530,0,0,1,1,-2491.81,-12381.6,14.7942,0.584178,-0,-0,-0.287954,-0.957644,300,0,1,'',0),
(2135185,1621,530,0,0,1,1,-2249.88,-11527.4,29.0807,3.49926,-0,-0,-0.984052,0.177882,300,0,1,'',0),
(2135186,1619,530,0,0,1,1,-1761.01,-12045.5,35.1073,0.798435,-0,-0,-0.388697,-0.921365,300,0,1,'',0),
(2135187,1617,530,0,0,1,1,-1800.86,-12027.7,35.6586,1.8749,-0,-0,-0.806051,-0.591846,300,0,1,'',0),
(2135188,1617,530,0,0,1,1,-1278.05,-12317,20.2078,1.41732,-0,-0,-0.650817,-0.759234,300,0,1,'',0),
(2135189,1621,530,0,0,1,1,-2530.96,-12322.9,13.0096,5.24661,-0,-0,-0.495391,0.86867,300,0,1,'',0),
(2135190,1621,530,0,0,1,1,-1230.31,-11820.4,7.39731,4.31702,-0,-0,-0.832211,0.554459,300,0,1,'',0),
(2135191,1619,530,0,0,1,1,-2541.83,-11820.4,16.3119,1.65954,-0,-0,-0.737777,-0.675044,300,0,1,'',0),
(2135192,1619,530,0,0,1,1,-2654.19,-11360,21.3803,0.491186,-0,-0,-0.243132,-0.969993,300,0,1,'',0),
(2135193,2045,530,0,0,1,1,-2061.16,-12518.6,-1.65164,4.0371,-0,-0,-0.901422,0.432942,300,0,1,'',0),
(2135194,1621,530,0,0,1,1,-1371.68,-11934.6,16.6441,3.57749,-0,-0,-0.976344,0.216226,300,0,1,'',0),
(2135195,1618,530,0,0,1,1,-2302.58,-11376.7,35.9057,5.5369,-0,-0,-0.364544,0.931186,300,0,1,'',0),
(2135196,1621,530,0,0,1,1,-1695.76,-11171.2,68.8867,1.85307,-0,-0,-0.799542,-0.60061,300,0,1,'',0),
(2135197,1621,530,0,0,1,1,-1734.91,-10936.3,66.7224,0.750054,-0,-0,-0.366298,-0.930498,300,0,1,'',0),
(2135198,1620,530,0,0,1,1,-1169.41,-12580.6,7.75433,3.86777,-0,-0,-0.934804,0.355163,300,0,1,'',0),
(2135199,1622,530,0,0,1,1,-2052.46,-10649.2,143.705,4.31011,-0,-0,-0.834122,0.551579,300,0,1,'',0),
(2135200,1620,530,0,0,1,1,-1921.96,-11432.2,62.1217,2.81722,-0,-0,-0.986877,-0.161476,300,0,1,'',0),
(2135201,1619,530,0,0,1,1,-2272.13,-11823.7,24.3933,0.00361177,-0,-0,-0.00179882,-0.999998,300,0,1,'',0),
(2135202,2045,530,0,0,1,1,-1162.99,-11048,-36.5187,1.89799,-0,-0,-0.812831,-0.5825,300,0,1,'',0),
(2135203,1618,530,0,0,1,1,-2065.51,-11096.2,58.4633,4.67296,-0,-0,-0.720909,0.69303,300,0,1,'',0),
(2135204,1618,530,0,0,1,1,-1299.91,-11536.6,8.3855,0.176399,-0,-0,-0.0880854,-0.996113,300,0,1,'',0),
(2135205,1621,530,0,0,1,1,-2343.91,-11918.3,21.6656,3.50837,-0,-0,-0.983231,0.182363,300,0,1,'',0),
(2135206,1617,530,0,0,1,1,-2437.43,-11794.3,17.1093,4.9045,-0,-0,-0.636032,0.771663,300,0,1,'',0),
(2135207,1617,530,0,0,1,1,-2565.76,-11898.7,23.502,5.25353,-0,-0,-0.492386,0.870377,300,0,1,'',0),
(2135208,1617,530,0,0,1,1,-2450.48,-12153.2,33.0983,2.93472,-0,-0,-0.994655,-0.103254,300,0,1,'',0),
(2135209,1620,530,0,0,1,1,-1215.08,-12352.2,21.231,2.06732,-0,-0,-0.859178,-0.511677,300,0,1,'',0),
(2135210,1619,530,0,0,1,1,-2774.56,-11389.8,8.3653,4.48981,-0,-0,-0.781265,0.624199,300,0,1,'',0),
(2135211,1617,530,0,0,1,1,-2430.91,-12358.7,17.4983,3.97144,-0,-0,-0.915147,0.403121,300,0,1,'',0),
(2135212,1622,530,0,0,1,1,-1558.73,-11428.9,64.5557,3.32867,-0,-0,-0.995628,0.0934034,300,0,1,'',0),
(2135213,1622,530,0,0,1,1,-2004.61,-11376.7,72.244,4.25827,-0,-0,-0.848137,0.529778,300,0,1,'',0),
(2135214,1620,530,0,0,1,1,-2080.73,-10701.4,69.971,5.05655,-0,-0,-0.575584,0.817743,300,0,1,'',0),
(2135215,1618,530,0,0,1,1,-1112.86,-12355.5,17.155,5.29845,-0,-0,-0.472713,0.881217,300,0,1,'',0),
(2135216,1620,530,0,0,1,1,-1339.06,-11435.5,21.5694,1.96365,-0,-0,-0.831513,-0.555506,300,0,1,'',0),
(2135217,1618,530,0,0,1,1,-2122.06,-11983.6,32.7691,0.394112,-0,-0,-0.195783,-0.980647,300,0,1,'',0),
(2135218,1622,530,0,0,1,1,-1188.98,-12424,99.3119,3.03839,-0,-0,-0.998669,-0.0515792,300,0,1,'',0),
(2135219,1621,530,0,0,1,1,-2198.18,-12052.1,31.2993,4.89413,-0,-0,-0.640023,0.768356,300,0,1,'',0),
(2135220,1619,530,0,0,1,1,-2696.26,-11905.3,9.85909,3.49455,-0,-0,-0.984468,0.175563,300,0,1,'',0),
(2135221,1619,530,0,0,1,1,-2472.23,-11719.3,13.263,2.13644,-0,-0,-0.876344,-0.481686,300,0,1,'',0),
(2135222,1620,530,0,0,1,1,-1606.58,-11885.7,9.57745,0.75351,-0,-0,-0.367905,-0.929863,300,0,1,'',0),
(2135223,1618,530,0,0,1,1,-1273.81,-11915,11.0469,5.93777,-0,-0,-0.171852,0.985123,300,0,1,'',0),
(2135224,1617,530,0,0,1,1,-1774.06,-10880.8,64.5519,6.1071,-0,-0,-0.08793,0.996127,300,0,1,'',0),
(2135225,1617,530,0,0,1,1,-1093.24,-12563.2,17.0382,2.07471,-0,-0,-0.861061,-0.508502,300,0,1,'',0),
(2135226,1620,530,0,0,1,1,-1815.38,-12163,15.3641,3.68461,-0,-0,-0.963367,0.268187,300,0,1,'',0),
(2135227,1619,530,0,0,1,1,-2058.98,-11008.1,65.9719,5.59565,-0,-0,-0.337038,0.941491,300,0,1,'',0),
(2135228,2045,530,0,0,1,1,-1297.65,-12734.2,-23.8489,5.11184,-0,-0,-0.55276,0.833341,300,0,1,'',0),
(2135229,1620,530,0,0,1,1,-1286.86,-12362,28.8518,2.94854,-0,-0,-0.995345,-0.0963771,300,0,1,'',0),
(2135230,1618,530,0,0,1,1,-1841.48,-11001.5,66.5308,0.352643,-0,-0,-0.175409,-0.984496,300,0,1,'',0),
(2135231,1620,530,0,0,1,1,-1484.78,-10955.9,61.5994,3.80902,-0,-0,-0.944832,0.327555,300,0,1,'',0),
(2135232,1622,530,0,0,1,1,-1249.88,-12453.4,99.3294,2.80685,-0,-0,-0.986026,-0.166589,300,0,1,'',0),
(2135233,2045,530,0,0,1,1,-2145.99,-12542,-2.02263,0.687851,-0,-0,-0.337185,-0.941438,300,0,1,'',0),
(2135234,1617,530,0,0,1,1,-2544.01,-11683.4,14.7319,2.47164,-0,-0,-0.944419,-0.328745,300,0,1,'',0),
(2135235,1617,530,0,0,1,1,-2650.03,-11930.3,14.3314,2.56982,-0,-0,-0.959412,-0.282008,300,0,1,'',0),
(2135236,1617,530,0,0,1,1,-2374.36,-11389.8,29.1075,0.000155854,0,0,0,1,300,0,1,'',0),
(2135237,1622,530,0,0,1,1,-1578.31,-10789.5,51.9039,1.19302,-0,-0,-0.561757,-0.827302,300,0,1,'',0),
(2135238,2045,530,0,0,1,1,-1565.33,-12397.9,-15.6282,5.3572,-0,-0,-0.446628,0.89472,300,0,1,'',0),
(2135239,2045,530,0,0,1,1,-1147.64,-11406.1,-7.59959,3.52219,-0,-0,-0.981947,0.189154,300,0,1,'',0),
(2135240,1618,530,0,0,1,1,-2476.58,-11738.9,12.8856,2.09151,-0,-0,-0.865304,-0.501248,300,0,1,'',0),
(2135241,1622,530,0,0,1,1,-1933.73,-10740.4,115.984,3.87955,-0,-0,-0.932696,0.360664,300,0,1,'',0),
(2135242,1621,530,0,0,1,1,-2254.73,-12283.7,52.1955,5.36757,-0,-0,-0.441984,0.897023,300,0,1,'',0),
(2135243,1621,530,0,0,1,1,-2225.64,-11577.9,24.2487,0.94986,-0,-0,-0.457276,-0.889325,300,0,1,'',0),
(2135253,1622,530,0,0,1,1,-1295.56,-12577.3,15.7576,4.42415,-0,-0,-0.801333,0.598219,300,0,1,'',0);

-- Add gameobjects to pools
DELETE FROM `pool_gameobject` WHERE FIND_IN_SET (`guid`,@GUID);
INSERT INTO `pool_gameobject` (`guid`,`pool_entry`,`chance`,`description`) VALUES
(86179,@POOL,0,@POOLDESC),
(86192,@POOL,0,@POOLDESC),
(86213,@POOL,0,@POOLDESC),
(86237,@POOL,0,@POOLDESC),
(86252,@POOL,0,@POOLDESC),
(86267,@POOL,0,@POOLDESC),
(86931,@POOL,0,@POOLDESC),
(87058,@POOL,0,@POOLDESC),
(2134867,@POOL,0,@POOLDESC),
(2134967,@POOL,0,@POOLDESC),
(2134968,@POOL,0,@POOLDESC),
(2134969,@POOL,0,@POOLDESC),
(2134970,@POOL,0,@POOLDESC),
(2134971,@POOL,0,@POOLDESC),
(2134972,@POOL,0,@POOLDESC),
(2134973,@POOL,0,@POOLDESC),
(2134974,@POOL,0,@POOLDESC),
(2134975,@POOL,0,@POOLDESC),
(2134976,@POOL,0,@POOLDESC),
(2134977,@POOL,0,@POOLDESC),
(2134978,@POOL,0,@POOLDESC),
(2134980,@POOL,0,@POOLDESC),
(2134981,@POOL,0,@POOLDESC),
(2134982,@POOL,0,@POOLDESC),
(2134983,@POOL,0,@POOLDESC),
(2134984,@POOL,0,@POOLDESC),
(2134985,@POOL,0,@POOLDESC),
(2134986,@POOL,0,@POOLDESC),
(2134987,@POOL,0,@POOLDESC),
(2134988,@POOL,0,@POOLDESC),
(2134989,@POOL,0,@POOLDESC),
(2134990,@POOL,0,@POOLDESC),
(2134991,@POOL,0,@POOLDESC),
(2134992,@POOL,0,@POOLDESC),
(2134993,@POOL,0,@POOLDESC),
(2134994,@POOL,0,@POOLDESC),
(2134995,@POOL,0,@POOLDESC),
(2134996,@POOL,0,@POOLDESC),
(2134997,@POOL,0,@POOLDESC),
(2134998,@POOL,0,@POOLDESC),
(2134999,@POOL,0,@POOLDESC),
(2135000,@POOL,0,@POOLDESC),
(2135001,@POOL,0,@POOLDESC),
(2135002,@POOL,0,@POOLDESC),
(2135003,@POOL,0,@POOLDESC),
(2135004,@POOL,0,@POOLDESC),
(2135005,@POOL,0,@POOLDESC),
(2135006,@POOL,0,@POOLDESC),
(2135007,@POOL,0,@POOLDESC),
(2135008,@POOL,0,@POOLDESC),
(2135009,@POOL,0,@POOLDESC),
(2135010,@POOL,0,@POOLDESC),
(2135011,@POOL,0,@POOLDESC),
(2135012,@POOL,0,@POOLDESC),
(2135013,@POOL,0,@POOLDESC),
(2135014,@POOL,0,@POOLDESC),
(2135015,@POOL,0,@POOLDESC),
(2135016,@POOL,0,@POOLDESC),
(2135017,@POOL,0,@POOLDESC),
(2135018,@POOL,0,@POOLDESC),
(2135019,@POOL,0,@POOLDESC),
(2135020,@POOL,0,@POOLDESC),
(2135021,@POOL,0,@POOLDESC),
(2135022,@POOL,0,@POOLDESC),
(2135023,@POOL,0,@POOLDESC),
(2135024,@POOL,0,@POOLDESC),
(2135025,@POOL,0,@POOLDESC),
(2135026,@POOL,0,@POOLDESC),
(2135027,@POOL,0,@POOLDESC),
(2135028,@POOL,0,@POOLDESC),
(2135029,@POOL,0,@POOLDESC),
(2135030,@POOL,0,@POOLDESC),
(2135031,@POOL,0,@POOLDESC),
(2135032,@POOL,0,@POOLDESC),
(2135033,@POOL,0,@POOLDESC),
(2135034,@POOL,0,@POOLDESC),
(2135035,@POOL,0,@POOLDESC),
(2135036,@POOL,0,@POOLDESC),
(2135037,@POOL,0,@POOLDESC),
(2135038,@POOL,0,@POOLDESC),
(2135039,@POOL,0,@POOLDESC),
(2135040,@POOL,0,@POOLDESC),
(2135041,@POOL,0,@POOLDESC),
(2135042,@POOL,0,@POOLDESC),
(2135043,@POOL,0,@POOLDESC),
(2135044,@POOL,0,@POOLDESC),
(2135045,@POOL,0,@POOLDESC),
(2135046,@POOL,0,@POOLDESC),
(2135047,@POOL,0,@POOLDESC),
(2135048,@POOL,0,@POOLDESC),
(2135049,@POOL,0,@POOLDESC),
(2135050,@POOL,0,@POOLDESC),
(2135051,@POOL,0,@POOLDESC),
(2135052,@POOL,0,@POOLDESC),
(2135053,@POOL,0,@POOLDESC),
(2135054,@POOL,0,@POOLDESC),
(2135055,@POOL,0,@POOLDESC),
(2135056,@POOL,0,@POOLDESC),
(2135057,@POOL,0,@POOLDESC),
(2135058,@POOL,0,@POOLDESC),
(2135059,@POOL,0,@POOLDESC),
(2135060,@POOL,0,@POOLDESC),
(2135061,@POOL,0,@POOLDESC),
(2135062,@POOL,0,@POOLDESC),
(2135063,@POOL,0,@POOLDESC),
(2135064,@POOL,0,@POOLDESC),
(2135065,@POOL,0,@POOLDESC),
(2135066,@POOL,0,@POOLDESC),
(2135067,@POOL,0,@POOLDESC),
(2135068,@POOL,0,@POOLDESC),
(2135069,@POOL,0,@POOLDESC),
(2135070,@POOL,0,@POOLDESC),
(2135071,@POOL,0,@POOLDESC),
(2135072,@POOL,0,@POOLDESC),
(2135073,@POOL,0,@POOLDESC),
(2135074,@POOL,0,@POOLDESC),
(2135075,@POOL,0,@POOLDESC),
(2135076,@POOL,0,@POOLDESC),
(2135077,@POOL,0,@POOLDESC),
(2135078,@POOL,0,@POOLDESC),
(2135079,@POOL,0,@POOLDESC),
(2135080,@POOL,0,@POOLDESC),
(2135081,@POOL,0,@POOLDESC),
(2135082,@POOL,0,@POOLDESC),
(2135083,@POOL,0,@POOLDESC),
(2135084,@POOL,0,@POOLDESC),
(2135085,@POOL,0,@POOLDESC),
(2135086,@POOL,0,@POOLDESC),
(2135087,@POOL,0,@POOLDESC),
(2135088,@POOL,0,@POOLDESC),
(2135089,@POOL,0,@POOLDESC),
(2135090,@POOL,0,@POOLDESC),
(2135091,@POOL,0,@POOLDESC),
(2135092,@POOL,0,@POOLDESC),
(2135093,@POOL,0,@POOLDESC),
(2135094,@POOL,0,@POOLDESC),
(2135095,@POOL,0,@POOLDESC),
(2135096,@POOL,0,@POOLDESC),
(2135097,@POOL,0,@POOLDESC),
(2135098,@POOL,0,@POOLDESC),
(2135099,@POOL,0,@POOLDESC),
(2135100,@POOL,0,@POOLDESC),
(2135101,@POOL,0,@POOLDESC),
(2135102,@POOL,0,@POOLDESC),
(2135103,@POOL,0,@POOLDESC),
(2135104,@POOL,0,@POOLDESC),
(2135105,@POOL,0,@POOLDESC),
(2135106,@POOL,0,@POOLDESC),
(2135107,@POOL,0,@POOLDESC),
(2135108,@POOL,0,@POOLDESC),
(2135109,@POOL,0,@POOLDESC),
(2135110,@POOL,0,@POOLDESC),
(2135111,@POOL,0,@POOLDESC),
(2135112,@POOL,0,@POOLDESC),
(2135113,@POOL,0,@POOLDESC),
(2135114,@POOL,0,@POOLDESC),
(2135115,@POOL,0,@POOLDESC),
(2135116,@POOL,0,@POOLDESC),
(2135117,@POOL,0,@POOLDESC),
(2135118,@POOL,0,@POOLDESC),
(2135119,@POOL,0,@POOLDESC),
(2135120,@POOL,0,@POOLDESC),
(2135121,@POOL,0,@POOLDESC),
(2135122,@POOL,0,@POOLDESC),
(2135123,@POOL,0,@POOLDESC),
(2135124,@POOL,0,@POOLDESC),
(2135125,@POOL,0,@POOLDESC),
(2135126,@POOL,0,@POOLDESC),
(2135127,@POOL,0,@POOLDESC),
(2135128,@POOL,0,@POOLDESC),
(2135129,@POOL,0,@POOLDESC),
(2135130,@POOL,0,@POOLDESC),
(2135131,@POOL,0,@POOLDESC),
(2135132,@POOL,0,@POOLDESC),
(2135133,@POOL,0,@POOLDESC),
(2135134,@POOL,0,@POOLDESC),
(2135135,@POOL,0,@POOLDESC),
(2135136,@POOL,0,@POOLDESC),
(2135137,@POOL,0,@POOLDESC),
(2135138,@POOL,0,@POOLDESC),
(2135139,@POOL,0,@POOLDESC),
(2135140,@POOL,0,@POOLDESC),
(2135141,@POOL,0,@POOLDESC),
(2135142,@POOL,0,@POOLDESC),
(2135143,@POOL,0,@POOLDESC),
(2135144,@POOL,0,@POOLDESC),
(2135145,@POOL,0,@POOLDESC),
(2135146,@POOL,0,@POOLDESC),
(2135147,@POOL,0,@POOLDESC),
(2135148,@POOL,0,@POOLDESC),
(2135149,@POOL,0,@POOLDESC),
(2135150,@POOL,0,@POOLDESC),
(2135151,@POOL,0,@POOLDESC),
(2135152,@POOL,0,@POOLDESC),
(2135153,@POOL,0,@POOLDESC),
(2135154,@POOL,0,@POOLDESC),
(2135155,@POOL,0,@POOLDESC),
(2135156,@POOL,0,@POOLDESC),
(2135157,@POOL,0,@POOLDESC),
(2135158,@POOL,0,@POOLDESC),
(2135159,@POOL,0,@POOLDESC),
(2135160,@POOL,0,@POOLDESC),
(2135161,@POOL,0,@POOLDESC),
(2135162,@POOL,0,@POOLDESC),
(2135164,@POOL,0,@POOLDESC),
(2135165,@POOL,0,@POOLDESC),
(2135166,@POOL,0,@POOLDESC),
(2135167,@POOL,0,@POOLDESC),
(2135168,@POOL,0,@POOLDESC),
(2135169,@POOL,0,@POOLDESC),
(2135170,@POOL,0,@POOLDESC),
(2135171,@POOL,0,@POOLDESC),
(2135172,@POOL,0,@POOLDESC),
(2135173,@POOL,0,@POOLDESC),
(2135174,@POOL,0,@POOLDESC),
(2135175,@POOL,0,@POOLDESC),
(2135176,@POOL,0,@POOLDESC),
(2135177,@POOL,0,@POOLDESC),
(2135178,@POOL,0,@POOLDESC),
(2135179,@POOL,0,@POOLDESC),
(2135180,@POOL,0,@POOLDESC),
(2135181,@POOL,0,@POOLDESC),
(2135182,@POOL,0,@POOLDESC),
(2135183,@POOL,0,@POOLDESC),
(2135184,@POOL,0,@POOLDESC),
(2135185,@POOL,0,@POOLDESC),
(2135186,@POOL,0,@POOLDESC),
(2135187,@POOL,0,@POOLDESC),
(2135188,@POOL,0,@POOLDESC),
(2135189,@POOL,0,@POOLDESC),
(2135190,@POOL,0,@POOLDESC),
(2135191,@POOL,0,@POOLDESC),
(2135192,@POOL,0,@POOLDESC),
(2135193,@POOL,0,@POOLDESC),
(2135194,@POOL,0,@POOLDESC),
(2135195,@POOL,0,@POOLDESC),
(2135196,@POOL,0,@POOLDESC),
(2135197,@POOL,0,@POOLDESC),
(2135198,@POOL,0,@POOLDESC),
(2135199,@POOL,0,@POOLDESC),
(2135200,@POOL,0,@POOLDESC),
(2135201,@POOL,0,@POOLDESC),
(2135202,@POOL,0,@POOLDESC),
(2135203,@POOL,0,@POOLDESC),
(2135204,@POOL,0,@POOLDESC),
(2135205,@POOL,0,@POOLDESC),
(2135206,@POOL,0,@POOLDESC),
(2135207,@POOL,0,@POOLDESC),
(2135208,@POOL,0,@POOLDESC),
(2135209,@POOL,0,@POOLDESC),
(2135210,@POOL,0,@POOLDESC),
(2135211,@POOL,0,@POOLDESC),
(2135212,@POOL,0,@POOLDESC),
(2135213,@POOL,0,@POOLDESC),
(2135214,@POOL,0,@POOLDESC),
(2135215,@POOL,0,@POOLDESC),
(2135216,@POOL,0,@POOLDESC),
(2135217,@POOL,0,@POOLDESC),
(2135218,@POOL,0,@POOLDESC),
(2135219,@POOL,0,@POOLDESC),
(2135220,@POOL,0,@POOLDESC),
(2135221,@POOL,0,@POOLDESC),
(2135222,@POOL,0,@POOLDESC),
(2135223,@POOL,0,@POOLDESC),
(2135224,@POOL,0,@POOLDESC),
(2135225,@POOL,0,@POOLDESC),
(2135226,@POOL,0,@POOLDESC),
(2135227,@POOL,0,@POOLDESC),
(2135228,@POOL,0,@POOLDESC),
(2135229,@POOL,0,@POOLDESC),
(2135230,@POOL,0,@POOLDESC),
(2135231,@POOL,0,@POOLDESC),
(2135232,@POOL,0,@POOLDESC),
(2135233,@POOL,0,@POOLDESC),
(2135234,@POOL,0,@POOLDESC),
(2135235,@POOL,0,@POOLDESC),
(2135236,@POOL,0,@POOLDESC),
(2135237,@POOL,0,@POOLDESC),
(2135238,@POOL,0,@POOLDESC),
(2135239,@POOL,0,@POOLDESC),
(2135240,@POOL,0,@POOLDESC),
(2135241,@POOL,0,@POOLDESC),
(2135242,@POOL,0,@POOLDESC),
(2135243,@POOL,0,@POOLDESC),
(2135253,@POOL,0,@POOLDESC);

-- Respawn rates of gameobjects is 5 minutes
UPDATE `gameobject` SET `spawntimesecs`=300 WHERE FIND_IN_SET (`guid`,@GUID);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
