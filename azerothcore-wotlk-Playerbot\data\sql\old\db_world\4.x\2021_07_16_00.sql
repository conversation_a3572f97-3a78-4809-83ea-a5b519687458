-- DB update 2021_07_15_01 -> 2021_07_16_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_15_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_15_01 2021_07_16_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1625958180512949700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1625958180512949700');

-- move ore node above to a nearby hill
UPDATE `gameobject` SET `position_x` = -781.1, `position_y` = 185.2, `position_z` = 59.6  WHERE `id` = 103711 AND `guid` = 75523;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_16_00' WHERE sql_rev = '1625958180512949700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
