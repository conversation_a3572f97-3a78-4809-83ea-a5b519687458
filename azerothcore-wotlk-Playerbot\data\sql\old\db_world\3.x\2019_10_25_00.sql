-- DB update 2019_10_24_00 -> 2019_10_25_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_10_24_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_10_24_00 2019_10_25_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1571086874024573263'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1571086874024573263');

-- K3 Perimeter Turret: Shoot when the victim is in range between 0-20 yards
UPDATE `smart_scripts` SET `event_param1` = 0, `comment` = 'K3 Perimeter Turret - Victim In Range 0-20 Yards - Cast ''Energy Surge''' WHERE `entryorguid` = 29483 AND `source_type` = 0 AND `id` = 0;

-- Improved Land Mine: Detonate if the victim is in range between 0-10 yards
DELETE FROM `smart_scripts` WHERE `source_type` = 0 AND `entryorguid` = 29475;
INSERT INTO `smart_scripts` (`entryorguid`, `source_type`, `id`, `link`, `event_type`, `event_phase_mask`, `event_chance`, `event_flags`, `event_param1`, `event_param2`, `event_param3`, `event_param4`, `event_param5`, `action_type`, `action_param1`, `action_param2`, `action_param3`, `action_param4`, `action_param5`, `action_param6`, `target_type`, `target_param1`, `target_param2`, `target_param3`, `target_param4`, `target_x`, `target_y`, `target_z`, `target_o`, `comment`)
VALUES
(29475,0,0,1,9,0,100,0,0,10,250,250,0,11,54537,0,0,0,0,0,2,0,0,0,0,0,0,0,0,'Improved Land Mine - Victim In Range 0-10 Yards - Cast Spell Detonation'),
(29475,0,1,0,61,0,100,0,0,0,0,0,0,41,500,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Improved Land Mine - Linked - Despawn'),
(29475,0,2,0,5,0,100,0,0,0,0,29618,0,33,29618,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Improved Land Mine - On Kill - Kill Credit'),
(29475,0,3,0,5,0,100,0,0,0,0,29619,0,33,29618,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Improved Land Mine - On Kill - Kill Credit');

-- Garm Invader: Set active after respawn; start random waypoint movement after 1-60 seconds;
-- disable auto attack / combat movement while following the waypoint path
UPDATE `creature_template` SET `AIName` = 'SmartAI' WHERE `entry` = 29619;
DELETE FROM `smart_scripts` WHERE `source_type` = 0 AND `entryorguid` = 29619;
DELETE FROM `smart_scripts` WHERE `source_type` = 9 AND `entryorguid` BETWEEN 2961900 AND 2961904;
INSERT INTO `smart_scripts` (`entryorguid`, `source_type`, `id`, `link`, `event_type`, `event_phase_mask`, `event_chance`, `event_flags`, `event_param1`, `event_param2`, `event_param3`, `event_param4`, `event_param5`, `action_type`, `action_param1`, `action_param2`, `action_param3`, `action_param4`, `action_param5`, `action_param6`, `target_type`, `target_param1`, `target_param2`, `target_param3`, `target_param4`, `target_x`, `target_y`, `target_z`, `target_o`, `comment`)
VALUES
(29619,0,0,1,11,0,100,0,0,0,0,0,0,48,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - On Respawn - Set Active'),
(29619,0,1,2,61,0,100,0,0,0,0,0,0,20,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - Linked - Enable Auto Attack'),
(29619,0,2,0,61,0,100,0,0,0,0,0,0,21,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - Linked - Enable Combat Movement'),
(29619,0,3,4,1,0,100,0,1000,60000,0,0,0,20,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - OOC - Disable Auto Attack'),
(29619,0,4,5,61,0,100,0,0,0,0,0,0,21,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - Linked - Disable Combat Movement'),
(29619,0,5,0,61,0,100,0,0,0,0,0,0,88,2961900,2961904,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - Linked - Call Random Action List'),
(29619,0,6,0,58,0,100,0,0,0,0,0,0,41,1000,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - On Waypoint Path Ended - Despawn After 1 Second'),
(29619,0,7,0,6,0,100,0,0,0,0,0,0,41,5000,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Garm Invader - On Death - Despawn After 5 Seconds'),

(2961900,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961900,0,0,0,2,1,0,0,0,0,0,0,0,0,'Garm Invader - On Script - Start Waypoint Movement'),
(2961901,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961901,0,0,0,2,1,0,0,0,0,0,0,0,0,'Garm Invader - On Script - Start Waypoint Movement'),
(2961902,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961902,0,0,0,2,1,0,0,0,0,0,0,0,0,'Garm Invader - On Script - Start Waypoint Movement'),
(2961903,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961903,0,0,0,2,1,0,0,0,0,0,0,0,0,'Garm Invader - On Script - Start Waypoint Movement'),
(2961904,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961904,0,0,0,2,1,0,0,0,0,0,0,0,0,'Garm Invader - On Script - Start Waypoint Movement');

-- Snowblind Follower: Start random waypoint movement after 1-60 seconds;
-- disable auto attack / combat movement while following the waypoint path
DELETE FROM `smart_scripts` WHERE `source_type` = 0 AND `entryorguid` = 29618;
DELETE FROM `smart_scripts` WHERE `source_type` = 9 AND `entryorguid` BETWEEN 2961800 AND 2961809;
INSERT INTO `smart_scripts` (`entryorguid`, `source_type`, `id`, `link`, `event_type`, `event_phase_mask`, `event_chance`, `event_flags`, `event_param1`, `event_param2`, `event_param3`, `event_param4`, `event_param5`, `action_type`, `action_param1`, `action_param2`, `action_param3`, `action_param4`, `action_param5`, `action_param6`, `target_type`, `target_param1`, `target_param2`, `target_param3`, `target_param4`, `target_x`, `target_y`, `target_z`, `target_o`, `comment`)
VALUES
(29618,0,0,1,11,0,100,0,0,0,0,0,0,20,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Respawn - Enable Auto Attack'),
(29618,0,1,0,61,0,100,0,0,0,0,0,0,21,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Snowblind Follower - Linked - Enable Combat Movement'),
(29618,0,2,3,1,0,100,0,1000,60000,0,0,0,20,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Snowblind Follower - OOC - Disable Auto Attack'),
(29618,0,3,4,61,0,100,0,0,0,0,0,0,21,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Snowblind Follower - Linked - Disable Combat Movement'),
(29618,0,4,0,61,0,100,0,0,0,0,0,0,88,2961800,2961809,0,0,0,0,1,0,0,0,0,0,0,0,0,'Snowblind Follower - Linked - Call Random Action List'),
(29618,0,5,0,58,0,100,0,0,0,0,0,0,41,1000,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Waypoint Path Ended - Despawn After 1 Second'),
(29618,0,6,0,6,0,100,0,0,0,0,0,0,41,5000,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Death - Despawn After 5 Seconds'),

(2961800,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961800,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961801,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961801,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961802,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961802,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961803,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961803,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961804,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961804,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961805,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961900,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961806,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961901,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961807,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961902,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961808,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961903,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement'),
(2961809,9,0,0,0,0,100,0,0,0,0,0,0,53,1,2961904,0,0,0,2,1,0,0,0,0,0,0,0,0,'Snowblind Follower - On Script - Start Waypoint Movement');

-- Snowblind Follower: Respawn after 60 seconds
-- Garm Invader: Respawn after 120 seconds (this will ensure that the event stops if no player
-- is near because they are only set active after respawn)
DELETE FROM `creature` WHERE `id` IN (29618,29619);
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `spawndist`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`)
VALUES
(112692,29618,571,0,0,1,1,27172,1,6349.05,-1392.1,426.088,1.50626,60,0,0,11001,3643,0,0,0,0,'',0),
(112767,29618,571,0,0,1,1,27181,1,6306.93,-1356.93,427.18,1.85726,60,0,0,11001,3643,0,0,0,0,'',0),
(112768,29618,571,0,0,1,1,27172,1,6302.19,-1373.33,425.856,1.24859,60,0,0,11001,3643,0,0,0,0,'',0),
(112769,29618,571,0,0,1,1,27181,1,6312.21,-1355.74,427.519,1.59023,60,0,0,11001,3643,0,0,0,0,'',0),
(112770,29618,571,0,0,1,1,27172,1,6304.37,-1390.55,425.727,1.08758,60,0,0,11001,3643,0,0,0,0,'',0),
(112771,29618,571,0,0,1,1,27172,1,6310.29,-1360.13,426.987,1.88716,60,0,0,11001,3643,0,0,0,0,'',0),
(112772,29618,571,0,0,1,1,27181,1,6307.83,-1394.57,425.451,1.47637,60,0,0,11001,3643,0,0,0,0,'',0),
(112773,29618,571,0,0,1,1,27181,1,6321.97,-1403.42,424.803,1.46852,60,0,0,11001,3643,0,0,0,0,'',0),
(112774,29618,571,0,0,1,1,27172,1,6357.25,-1398.59,426.763,1.53134,60,0,0,11001,3643,0,0,0,0,'',0),
(112775,29618,571,0,0,1,1,27172,1,6328.89,-1354.51,427.306,1.67118,60,0,0,11001,3643,0,0,0,0,'',0),
(112776,29618,571,0,0,1,1,27181,1,6340.09,-1406.16,426.213,1.11117,60,0,0,11001,3643,0,0,0,0,'',0),
(112777,29618,571,0,0,1,1,27172,1,6310.89,-1367.74,426.18,1.59421,60,0,0,11001,3643,0,0,0,0,'',0),
(112778,29618,571,0,0,1,1,27181,1,6348.41,-1396.53,426.218,1.06404,60,0,0,11001,3643,0,0,0,0,'',0),
(112779,29618,571,0,0,1,1,27172,1,6323.67,-1356.15,427.335,1.91619,60,0,0,11001,3643,0,0,0,0,'',0),
(112780,29618,571,0,0,1,1,27181,1,6310.4,-1389.39,425.764,1.57456,60,0,0,11001,3643,0,0,0,0,'',0),
(112781,29618,571,0,0,1,1,27172,1,6313.78,-1394.06,425.344,1.70415,60,0,0,11001,3643,0,0,0,0,'',0),
(112782,29618,571,0,0,1,1,27172,1,6299.45,-1359.4,426.234,1.36641,60,0,0,11001,3643,0,0,0,0,'',0),
(112783,29618,571,0,0,1,1,27172,1,6327.02,-1398.41,425.138,1.49602,60,0,0,11001,3643,0,0,0,0,'',0),
(112784,29618,571,0,0,1,1,27181,1,6332.65,-1405.52,425.409,1.79443,60,0,0,11001,3643,0,0,0,0,'',0),
(112785,29618,571,0,0,1,1,27172,1,6305.71,-1375.74,425.948,1.29576,60,0,0,11001,3643,0,0,0,0,'',0),
(112786,29618,571,0,0,1,1,27181,1,6356.61,-1358.35,427.621,1.86125,60,0,0,11001,3643,0,0,0,0,'',0),
(112787,29618,571,0,0,1,1,27172,1,6322.78,-1361.79,427.093,1.60835,60,0,0,11001,3643,0,0,0,0,'',0),
(112788,29618,571,0,0,1,1,27172,1,6335.47,-1401.98,425.725,1.4646,60,0,0,11001,3643,0,0,0,0,'',0),
(112789,29618,571,0,0,1,1,27172,1,6359.76,-1384.97,427.074,1.83372,60,0,0,11001,3643,0,0,0,0,'',0),
(112790,29618,571,0,0,1,1,27181,1,6321.69,-1374.08,426.113,1.70181,60,0,0,11001,3643,0,0,0,0,'',0),
(112791,29618,571,0,0,1,1,27181,1,6317.28,-1367.91,426.407,1.2722,60,0,0,11001,3643,0,0,0,0,'',0),
(112792,29618,571,0,0,1,1,27172,1,6326.97,-1385.12,425.609,1.65309,60,0,0,11001,3643,0,0,0,0,'',0),
(112793,29618,571,0,0,1,1,27181,1,6334.32,-1362.64,427.029,1.77092,60,0,0,11001,3643,0,0,0,0,'',0),
(112794,29618,571,0,0,1,1,27181,1,6316.01,-1361.55,427.002,1.7615,60,0,0,11001,3643,0,0,0,0,'',0),
(112796,29618,571,0,0,1,1,27172,1,6348.57,-1387.33,426.051,1.59421,60,0,0,11001,3643,0,0,0,0,'',0),
(112797,29618,571,0,0,1,1,27181,1,6350.43,-1366.74,426.792,1.91314,60,0,0,11001,3643,0,0,0,0,'',0),
(112798,29618,571,0,0,1,1,27181,1,6362.59,-1374.65,427.32,1.57457,60,0,0,11001,3643,0,0,0,0,'',0),
(112799,29618,571,0,0,1,1,27172,1,6352.5,-1375.54,426.754,1.74343,60,0,0,11001,3643,0,0,0,0,'',0),
(112800,29618,571,0,0,1,1,27172,1,6348.38,-1357.24,427.509,2.04817,60,0,0,11001,3643,0,0,0,0,'',0),
(112801,29618,571,0,0,1,1,27181,1,6317.63,-1352.1,427.664,1.54991,60,0,0,11001,3643,0,0,0,0,'',0),
(112802,29618,571,0,0,1,1,27181,1,6320.96,-1381.32,425.773,1.6272,60,0,0,11001,3643,0,0,0,0,'',0),
(112803,29618,571,0,0,1,1,27181,1,6336.86,-1356.33,427.349,1.91858,60,0,0,11001,3643,0,0,0,0,'',0),
(112804,29618,571,0,0,1,1,27181,1,6335.56,-1393.41,425.689,1.50387,60,0,0,11001,3643,0,0,0,0,'',0),
(112805,29618,571,0,0,1,1,27181,1,6322.97,-1368.42,426.584,1.22507,60,0,0,11001,3643,0,0,0,0,'',0),
(112806,29618,571,0,0,1,1,27181,1,6362.61,-1366.75,427.417,1.66329,60,0,0,11001,3643,0,0,0,0,'',0),
(112807,29618,571,0,0,1,1,27181,1,6352.39,-1361.81,427.076,1.59813,60,0,0,11001,3643,0,0,0,0,'',0),
(112808,29618,571,0,0,1,1,27172,1,6316.96,-1398.99,424.903,1.64131,60,0,0,11001,3643,0,0,0,0,'',0),
(112809,29618,571,0,0,1,1,27172,1,6330.85,-1392.1,425.499,1.53921,60,0,0,11001,3643,0,0,0,0,'',0),
(112810,29618,571,0,0,1,1,27172,1,6344.21,-1379.38,426.122,1.15431,60,0,0,11001,3643,0,0,0,0,'',0),
(112811,29618,571,0,0,1,1,27172,1,6309.63,-1381.45,426.078,1.48089,60,0,0,11001,3643,0,0,0,0,'',0),
(112812,29618,571,0,0,1,1,27181,1,6315.14,-1386.72,425.77,1.60049,60,0,0,11001,3643,0,0,0,0,'',0),
(112813,29618,571,0,0,1,1,27172,1,6347.2,-1382,426.096,1.76937,60,0,0,11001,3643,0,0,0,0,'',0),
(112814,29618,571,0,0,1,1,27172,1,6360.43,-1391.31,427.029,1.49841,60,0,0,11001,3643,0,0,0,0,'',0),
(112815,29618,571,0,0,1,1,27172,1,6333.1,-1382.41,425.814,1.13868,60,0,0,11001,3643,0,0,0,0,'',0),
(112816,29618,571,0,0,1,1,27181,1,6332.79,-1397.32,425.53,1.52738,60,0,0,11001,3643,0,0,0,0,'',0),
(112817,29618,571,0,0,1,1,27181,1,6351.51,-1380.95,426.456,1.54946,60,0,0,11001,3643,0,0,0,0,'',0),
(112819,29618,571,0,0,1,1,27181,1,6355.76,-1380.07,426.917,1.70961,60,0,0,11001,3643,0,0,0,0,'',0),
(112821,29618,571,0,0,1,1,27172,1,6334.94,-1370.96,426.667,1.15096,60,0,0,11001,3643,0,0,0,0,'',0),
(112822,29618,571,0,0,1,1,27181,1,6357.37,-1364.7,427.202,1.76777,60,0,0,11001,3643,0,0,0,0,'',0),
(112823,29618,571,0,0,1,1,27172,1,6364.54,-1359.25,428.188,1.34334,60,0,0,11001,3643,0,0,0,0,'',0),
(112824,29618,571,0,0,1,1,27172,1,6329.74,-1361.53,427.164,1.44538,60,0,0,11001,3643,0,0,0,0,'',0),
(112826,29618,571,0,0,1,1,27172,1,6341.98,-1367.52,426.667,1.79477,60,0,0,11001,3643,0,0,0,0,'',0),
(112827,29618,571,0,0,1,1,27181,1,6356.66,-1373.71,427.047,1.63193,60,0,0,11001,3643,0,0,0,0,'',0),
(112828,29618,571,0,0,1,1,27181,1,6363.46,-1381.95,427.474,1.68298,60,0,0,11001,3643,0,0,0,0,'',0),
(112829,29618,571,0,0,1,1,27172,1,6351.63,-1403.87,427.001,1.72788,60,0,0,11001,3643,0,0,0,0,'',0),
(112830,29618,571,0,0,1,1,27181,1,6360.04,-1378.69,427.346,1.34334,60,0,0,11001,3643,0,0,0,0,'',0),
(112831,29618,571,0,0,1,1,27181,1,6305.91,-1384.51,425.97,1.43263,60,0,0,11001,3643,0,0,0,0,'',0),
(112832,29618,571,0,0,1,1,27172,1,6355.52,-1393.39,426.498,1.40024,60,0,0,11001,3643,0,0,0,0,'',0),
(112833,29618,571,0,0,1,1,27181,1,6340.15,-1377.93,426.152,1.34917,60,0,0,11001,3643,0,0,0,0,'',0),
(112834,29618,571,0,0,1,1,27172,1,6340.01,-1373.24,426.415,1.49204,60,0,0,11001,3643,0,0,0,0,'',0),
(112835,29618,571,0,0,1,1,27172,1,6318.37,-1391.53,425.457,1.44098,60,0,0,11001,3643,0,0,0,0,'',0),
(112836,29618,571,0,0,1,1,27181,1,6352.84,-1387.34,426.339,1.44098,60,0,0,11001,3643,0,0,0,0,'',0),
(112837,29618,571,0,0,1,1,27172,1,6355.96,-1388.35,426.585,1.23137,60,0,0,11001,3643,0,0,0,0,'',0),
(112838,29618,571,0,0,1,1,27181,1,6340.95,-1385.33,425.816,1.16853,60,0,0,11001,3643,0,0,0,0,'',0),
(112840,29618,571,0,0,1,1,27172,1,6360.15,-1371.44,427.16,1.63341,60,0,0,11001,3643,0,0,0,0,'',0),
(112841,29618,571,0,0,1,1,27172,1,6335.58,-1377.98,426.131,1.56272,60,0,0,11001,3643,0,0,0,0,'',0),
(112842,29618,571,0,0,1,1,27181,1,6345.25,-1388.16,425.883,1.73551,60,0,0,11001,3643,0,0,0,0,'',0),
(112843,29618,571,0,0,1,1,27181,1,6336.48,-1386.95,425.705,1.39386,60,0,0,11001,3643,0,0,0,0,'',0),
(112844,29618,571,0,0,1,1,27172,1,6312.03,-1400.4,424.935,1.67661,60,0,0,11001,3643,0,0,0,0,'',0),
(112892,29619,571,0,0,1,1,24857,1,6301.45,-1382.77,425.798,1.1504,120,0,0,11379,0,0,0,0,0,'',0),
(112893,29619,571,0,0,1,1,24857,1,6304.12,-1364.95,426.24,1.46307,120,0,0,11379,0,0,0,0,0,'',0),
(112894,29619,571,0,0,1,1,24857,1,6314.34,-1374.65,426.078,1.93204,120,0,0,11379,0,0,0,0,0,'',0),
(112895,29619,571,0,0,1,1,24857,1,6309.2,-1350.59,427.742,1.5156,120,0,0,11379,0,0,0,0,0,'',0),
(112896,29619,571,0,0,1,1,24857,1,6328.53,-1375.4,426.328,1.80074,120,0,0,11379,0,0,0,0,0,'',0),
(112897,29619,571,0,0,1,1,24857,1,6322.79,-1391.31,425.408,1.65941,120,0,0,11379,0,0,0,0,0,'',0),
(112898,29619,571,0,0,1,1,24857,1,6347.43,-1372.53,426.542,1.55935,120,0,0,11379,0,0,0,0,0,'',0),
(112899,29619,571,0,0,1,1,24857,1,6334.17,-1348.96,427.77,1.47633,120,0,0,11379,0,0,0,0,0,'',0),
(112900,29619,571,0,0,1,1,24857,1,6340.1,-1364.46,426.961,1.38125,120,0,0,11379,0,0,0,0,0,'',0),
(112901,29619,571,0,0,1,1,24857,1,6340.52,-1395.74,425.959,1.37668,120,0,0,11379,0,0,0,0,0,'',0),
(112903,29619,571,0,0,1,1,24857,1,6344.19,-1352.25,428.078,1.56665,120,0,0,11379,0,0,0,0,0,'',0);

-- Waypoint paths: 5 for Garm Invaders, 5 for Snowblind Followers (they will also use the paths of the Garm Invaders!)
DELETE FROM `waypoints` WHERE `entry` IN (2961800,2961801,2961802,2961803,2961804,2961900,2961901,2961902,2961903,2961904);
INSERT INTO `waypoints` (`entry`, `pointid`, `position_x`, `position_y`, `position_z`, `point_comment`)
VALUES
(2961800,1,6315.08,-1349.14,427.685,''),
(2961800,2,6311.85,-1337.78,427.862,''),
(2961800,3,6309.37,-1326.59,427.403,''),
(2961800,4,6307.66,-1314.02,428.048,''),
(2961800,5,6306.05,-1302.78,428.125,''),
(2961800,6,6303.16,-1290.42,426.443,''),
(2961800,7,6300.35,-1279.18,426.779,''),
(2961800,8,6296.24,-1268.47,427.236,''),
(2961800,9,6291.25,-1258.14,427.314,''),
(2961800,10,6287.14,-1246.16,427.539,''),
(2961800,11,6287.04,-1234.09,427.369,''),
(2961800,12,6285.57,-1222.06,426.478,''),
(2961800,13,6283.66,-1209.74,425.443,''),
(2961800,14,6281.42,-1197.16,424.673,''),
(2961800,15,6278.53,-1186.38,424.762,''),
(2961800,16,6275.27,-1174.93,424.125,''),

(2961801,1,6313.75,-1349.82,427.773,''),
(2961801,2,6307.36,-1342.37,427.725,''),
(2961801,3,6298.25,-1337.01,426.774,''),
(2961801,4,6289.51,-1330.62,426.356,''),
(2961801,5,6281.44,-1322.63,426.615,''),
(2961801,6,6274.53,-1314.44,426.45,''),
(2961801,7,6267.28,-1304.19,425.892,''),
(2961801,8,6262.27,-1293.81,425.818,''),
(2961801,9,6260.76,-1281.12,425.847,''),
(2961801,10,6260.6,-1268.01,426.734,''),
(2961801,11,6260.76,-1253.8,428.236,''),
(2961801,12,6260.54,-1244.07,428.724,''),
(2961801,13,6260.33,-1234.01,428.772,''),
(2961801,14,6260.82,-1220.78,428.757,''),
(2961801,15,6261.78,-1208.46,427.949,''),
(2961801,16,6262.8,-1196.58,426.344,''),
(2961801,17,6264.2,-1185.73,425.815,''),
(2961801,18,6265.7,-1176.44,424.464,''),

(2961802,1,6316.15,-1348.73,427.568,''),
(2961802,2,6317.08,-1340.6,427.258,''),
(2961802,3,6317.79,-1330.46,428.1,''),
(2961802,4,6316.61,-1319.16,428.244,''),
(2961802,5,6315.25,-1308.64,428.875,''),
(2961802,6,6314.03,-1298.32,428.111,''),
(2961802,7,6312.97,-1288.64,427.236,''),
(2961802,8,6311.41,-1278.39,427.645,''),
(2961802,9,6308.49,-1267.29,427.73,''),
(2961802,10,6305.21,-1255.03,427.299,''),
(2961802,11,6302.89,-1244.79,427.55,''),
(2961802,12,6301.09,-1233.67,427.075,''),
(2961802,13,6300.25,-1225.08,425.73,''),
(2961802,14,6306.03,-1217.58,425.49,''),
(2961802,15,6314.01,-1210.59,426.51,''),
(2961802,16,6319.67,-1202.59,426.422,''),
(2961802,17,6322.42,-1193.07,425.804,''),
(2961802,18,6323.8,-1183.54,425.76,''),
(2961802,19,6324.48,-1175.48,425.556,''),
(2961802,20,6324.79,-1166.19,425.223,''),

(2961803,1,6342.3,-1348.57,428.451,''),
(2961803,2,6348.92,-1341.18,429.426,''),
(2961803,3,6355.97,-1333.41,429.389,''),
(2961803,4,6363.37,-1323.81,429.414,''),
(2961803,5,6368.89,-1313.76,430.32,''),
(2961803,6,6373.84,-1303.41,431.224,''),
(2961803,7,6376.82,-1293.15,432.417,''),
(2961803,8,6376.79,-1282.38,433.009,''),
(2961803,9,6372.82,-1271.73,432.339,''),
(2961803,10,6369.34,-1262.07,431.078,''),
(2961803,11,6370.88,-1251.5,429.61,''),
(2961803,12,6373.46,-1240.65,428.322,''),
(2961803,13,6375.5,-1230.13,427.312,''),
(2961803,14,6377.23,-1219.77,426.556,''),
(2961803,15,6378.9,-1209.73,426.153,''),
(2961803,16,6380.45,-1199.35,425.959,''),
(2961803,17,6382.01,-1187.97,426.054,''),
(2961803,18,6383.35,-1179.88,426.415,''),

(2961804,1,6339.98,-1347.78,428.367,''),
(2961804,2,6340.43,-1339.92,429.081,''),
(2961804,3,6339.42,-1329.47,428.935,''),
(2961804,4,6338.77,-1317.36,428.223,''),
(2961804,5,6339.56,-1305.82,427.858,''),
(2961804,6,6342.22,-1293.87,427.728,''),
(2961804,7,6345.89,-1282.88,428.073,''),
(2961804,8,6349.55,-1272.14,428.729,''),
(2961804,9,6347.04,-1260,428.701,''),
(2961804,10,6343.92,-1247.14,428.441,''),
(2961804,11,6342.79,-1234.73,427.411,''),
(2961804,12,6342.23,-1222.28,427.765,''),
(2961804,13,6345.7,-1210.05,427.609,''),
(2961804,14,6349.52,-1198.99,427.245,''),
(2961804,15,6351.78,-1188.07,426.764,''),
(2961804,16,6354.24,-1177.53,426.659,''),
(2961804,17,6356.17,-1168.77,426.759,''),

(2961900,1,6334.46,-1344.04,428.17,''),
(2961900,2,6334.41,-1334.01,428.724,''),
(2961900,3,6331.47,-1321.38,428.34,''),
(2961900,4,6331.22,-1309.17,428.168,''),
(2961900,5,6334.87,-1297.77,427.649,''),
(2961900,6,6339.93,-1285.3,427.786,''),
(2961900,7,6346.35,-1272.87,428.302,''),
(2961900,8,6351.63,-1261.7,429.053,''),
(2961900,9,6357.53,-1250.24,428.454,''),
(2961900,10,6366.15,-1238.41,427.638,''),
(2961900,11,6369.68,-1226.41,426.865,''),
(2961900,12,6369.56,-1213.07,426.468,''),
(2961900,13,6368.46,-1198.92,426.39,''),
(2961900,14,6366.45,-1185.84,426.669,''),
(2961900,15,6365.61,-1174.07,427.081,''),

(2961901,1,6333.1,-1343.65,428.042,''),
(2961901,2,6332.9,-1333.86,428.595,''),
(2961901,3,6329.05,-1321.68,428.336,''),
(2961901,4,6325.82,-1309.9,428.472,''),
(2961901,5,6320.24,-1296.94,427.868,''),
(2961901,6,6314.9,-1285.57,427.382,''),
(2961901,7,6308.9,-1275.14,427.737,''),
(2961901,8,6303.29,-1263.89,427.541,''),
(2961901,9,6298.45,-1251.26,427.313,''),
(2961901,10,6296.23,-1238.79,427.393,''),
(2961901,11,6293.76,-1225.45,426.15,''),
(2961901,12,6290.19,-1214.32,425.515,''),
(2961901,13,6287.23,-1203.67,424.846,''),
(2961901,14,6285.03,-1191.74,424.811,''),
(2961901,15,6282.53,-1180.86,424.545,''),
(2961901,16,6281.15,-1170.46,423.832,''),

(2961902,1,6332.24,-1343.42,427.947,''),
(2961902,2,6332.19,-1333.4,428.572,''),
(2961902,3,6327.87,-1321.37,428.321,''),
(2961902,4,6323.6,-1310.36,428.607,''),
(2961902,5,6317.67,-1298.17,428.055,''),
(2961902,6,6312.12,-1287.25,427.15,''),
(2961902,7,6306.52,-1276.23,427.524,''),
(2961902,8,6300.78,-1265.41,427.535,''),
(2961902,9,6295.13,-1252.38,427.316,''),
(2961902,10,6289.84,-1240.73,427.363,''),
(2961902,11,6283.71,-1229.64,427.267,''),
(2961902,12,6276.24,-1219.8,427.089,''),
(2961902,13,6269.34,-1211.03,427.125,''),
(2961902,14,6261.85,-1202.21,426.889,''),
(2961902,15,6256.36,-1196.26,428.488,''),
(2961902,16,6251.75,-1188.7,426.944,''),
(2961902,17,6246.64,-1180.16,423.851,''),

(2961903,1,6315.91,-1344.21,427.405,''),
(2961903,2,6315.71,-1332.21,428.013,''),
(2961903,3,6313.93,-1321.33,427.871,''),
(2961903,4,6310.85,-1311.41,428.605,''),
(2961903,5,6309.81,-1300.52,428.265,''),
(2961903,6,6310.59,-1290.22,427.145,''),
(2961903,7,6314.54,-1278.9,427.823,''),
(2961903,8,6320.71,-1269.1,428.155,''),
(2961903,9,6328.52,-1260.69,428.129,''),
(2961903,10,6335.23,-1250.8,428.974,''),
(2961903,11,6337.27,-1239.25,427.811,''),
(2961903,12,6336.57,-1227.03,427.501,''),
(2961903,13,6335.18,-1214.64,427.804,''),
(2961903,14,6334.01,-1202.56,427.066,''),
(2961903,15,6332.92,-1190.36,426.384,''),
(2961903,16,6331.77,-1179.49,425.861,''),
(2961903,17,6330.55,-1167.96,425.365,''),

(2961904,1,6314.18,-1344.96,427.639,''),
(2961904,2,6305.02,-1337.72,427.602,''),
(2961904,3,6293.17,-1330.32,426.479,''),
(2961904,4,6284.32,-1322.07,426.52,''),
(2961904,5,6275.97,-1312.66,426.288,''),
(2961904,6,6269.18,-1303.58,425.696,''),
(2961904,7,6264.9,-1292.49,425.766,''),
(2961904,8,6263.29,-1280.8,425.953,''),
(2961904,9,6263.99,-1267.7,426.803,''),
(2961904,10,6264.07,-1253.93,428.333,''),
(2961904,11,6269.49,-1244.2,429.111,''),
(2961904,12,6277.29,-1236.07,428.322,''),
(2961904,13,6281.59,-1224.6,426.931,''),
(2961904,14,6286.5,-1212.79,425.636,''),
(2961904,15,6291.56,-1200.22,424.523,''),
(2961904,16,6296.4,-1188.14,424.484,''),
(2961904,17,6299.57,-1177.37,424.932,''),
(2961904,18,6300.07,-1168.64,424.854,'');

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
