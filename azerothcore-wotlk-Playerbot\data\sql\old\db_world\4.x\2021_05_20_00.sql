-- DB update 2021_05_19_00 -> 2021_05_20_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_19_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_19_00 2021_05_20_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1621164461277933296'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1621164461277933296');

-- https://github.com/azerothcore/azerothcore-wotlk/issues/5860
SET @LIZZARIK := 14056;
SET @JORB := 14057;
SET @PATH := @LIZZARIK * 10;

DELETE FROM `creature_formations` WHERE `memberGUID` IN (@LIZZARIK, @JORB);
INSERT INTO `creature_formations` (`leaderGUID`, `memberGUID`, `dist`, `angle`, `groupAI`, `point_1`, `point_2`) VALUES 
(@LIZZARIK, @LIZZARIK, 0, 0, 7, 0, 0),
(@LIZZARIK, @JORB, 3, 270, 7, 0, 0);

UPDATE `creature` SET `MovementType` = 2 WHERE `guid` = @LIZZARIK;
DELETE FROM `creature_addon` WHERE `guid` = @LIZZARIK;
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`,`bytes2`,`emote`, `isLarge`, `auras`) VALUES (@LIZZARIK, @PATH, 0, 0, 1, 0, 0, '');
DELETE FROM `waypoint_data` WHERE `id`= @PATH;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES
(@PATH, 1, -971.002, -3637.83, 18.3557, 0, 0, 0, 0, 100, 0),
(@PATH, 2, -947.621, -3615.81, 18.2985, 0, 0, 0, 0, 100, 0),
(@PATH, 3, -948.348, -3601.82, 21.4885, 0, 0, 0, 0, 100, 0),
(@PATH, 4, -951.091, -3576.32, 28.5154, 0, 0, 0, 0, 100, 0),
(@PATH, 5, -960.074, -3559.78, 33.3683, 0, 0, 0, 0, 100, 0),
(@PATH, 6, -977.578, -3539.13, 41.1652, 0, 0, 0, 0, 100, 0),
(@PATH, 7, -981.632, -3505.06, 51.7568, 0, 0, 0, 0, 100, 0),
(@PATH, 8, -990.287, -3489.86, 56.1907, 0, 0, 0, 0, 100, 0),
(@PATH, 9, -1009.21, -3476.6, 61.0037, 0, 0, 0, 0, 100, 0),
(@PATH, 10, -1011.67, -3445.71, 69.1353, 0, 0, 0, 0, 100, 0),
(@PATH, 11, -1015.53, -3418.91, 74.9623, 0, 0, 0, 0, 100, 0),
(@PATH, 12, -1013.59, -3398.01, 79.9853, 0, 0, 0, 0, 100, 0),
(@PATH, 13, -1005.86, -3357.5, 89.1514, 0, 0, 0, 0, 100, 0),
(@PATH, 14, -994.613, -3339.77, 92.7049, 0, 0, 0, 0, 100, 0),
(@PATH, 15, -977.402, -3300.56, 95.5519, 0, 0, 0, 0, 100, 0),
(@PATH, 16, -956.785, -3260.92, 95.7678, 0, 0, 0, 0, 100, 0),
(@PATH, 17, -942.771, -3231.14, 92.4046, 0, 0, 0, 0, 100, 0),
(@PATH, 18, -914.23, -3207.59, 91.6668, 0, 0, 0, 0, 100, 0),
(@PATH, 19, -890.844, -3173.92, 91.8806, 0, 0, 0, 0, 100, 0),
(@PATH, 20, -881.558, -3136.73, 93.4591, 0, 0, 0, 0, 100, 0),
(@PATH, 21, -880.002, -3095.59, 95.0651, 0, 0, 0, 0, 100, 0),
(@PATH, 22, -861.655, -3081.23, 95.7913, 0, 0, 0, 0, 100, 0),
(@PATH, 23, -826.653, -3073.49, 96.7211, 0, 0, 0, 0, 100, 0),
(@PATH, 24, -793.992, -3049.39, 95.7768, 0, 0, 0, 0, 100, 0),
(@PATH, 25, -780.383, -3032.5, 94.5305, 0, 0, 0, 0, 100, 0),
(@PATH, 26, -750.016, -2997.16, 92.5198, 0, 0, 0, 0, 100, 0),
(@PATH, 27, -717.704, -2978.19, 93.8972, 0, 0, 0, 0, 100, 0),
(@PATH, 28, -705.702, -2956.07, 95.6205, 0, 0, 0, 0, 100, 0),
(@PATH, 29, -660.878, -2941.75, 95.7878, 0, 0, 0, 0, 100, 0),
(@PATH, 30, -638.13, -2918.34, 95.7878, 0, 0, 0, 0, 100, 0),
(@PATH, 31, -609.909, -2887.25, 94.2815, 0, 0, 0, 0, 100, 0),
(@PATH, 32, -585.127, -2863.9, 91.6669, 0, 0, 0, 0, 100, 0),
(@PATH, 33, -538.114, -2821.97, 91.6669, 0, 0, 0, 0, 100, 0),
(@PATH, 34, -502.894, -2787.92, 91.6669, 0, 0, 0, 0, 100, 0),
(@PATH, 35, -487.106, -2769.15, 91.6669, 0, 0, 0, 0, 100, 0),
(@PATH, 36, -482.668, -2734.47, 92.6048, 0, 0, 0, 0, 100, 0),
(@PATH, 37, -476.658, -2696.71, 95.2357, 0, 0, 0, 0, 100, 0),
(@PATH, 38, -458.515, -2694.82, 96.7358, 0, 0, 0, 0, 100, 0),
(@PATH, 39, -460.498, -2694.38, 96.6337, 0, 1800000, 0, 0, 100, 0), -- 30 min wait at the Crossroads
(@PATH, 40, -460.501, -2692.71, 96.4521, 0, 0, 0, 0, 100, 0),
(@PATH, 41, -473.144, -2686.97, 95.6511, 0, 0, 0, 0, 100, 0),
(@PATH, 42, -481.334, -2703.67, 94.8162, 0, 0, 0, 0, 100, 0),
(@PATH, 43, -483.754, -2744.08, 92.2063, 0, 0, 0, 0, 100, 0),
(@PATH, 44, -494.621, -2777.4, 91.6669, 0, 0, 0, 0, 100, 0),
(@PATH, 45, -542.997, -2827.38, 91.6669, 0, 0, 0, 0, 100, 0),
(@PATH, 46, -578.113, -2858.62, 91.6669, 0, 0, 0, 0, 100, 0),
(@PATH, 47, -604.699, -2881.37, 93.372, 0, 0, 0, 0, 100, 0),
(@PATH, 48, -664.066, -2943.15, 95.786, 0, 0, 0, 0, 100, 0),
(@PATH, 49, -704.679, -2955.15, 95.6378, 0, 0, 0, 0, 100, 0),
(@PATH, 50, -715.766, -2976.38, 94.0882, 0, 0, 0, 0, 100, 0),
(@PATH, 51, -748.189, -2992.87, 92.3774, 0, 0, 0, 0, 100, 0),
(@PATH, 52, -775.61, -3024.67, 93.6501, 0, 0, 0, 0, 100, 0),
(@PATH, 53, -797.865, -3051.67, 95.9174, 0, 0, 0, 0, 100, 0),
(@PATH, 54, -825.973, -3073.18, 96.7328, 0, 0, 0, 0, 100, 0),
(@PATH, 55, -870.34, -3085.27, 95.3921, 0, 0, 0, 0, 100, 0),
(@PATH, 56, -880.951, -3101.5, 95.0275, 0, 0, 0, 0, 100, 0),
(@PATH, 57, -884.414, -3148.29, 93.1932, 0, 0, 0, 0, 100, 0),
(@PATH, 58, -908.083, -3199.35, 91.6677, 0, 0, 0, 0, 100, 0),
(@PATH, 59, -922.595, -3217.02, 91.6677, 0, 0, 0, 0, 100, 0),
(@PATH, 60, -940.509, -3228.14, 92.0426, 0, 0, 0, 0, 100, 0),
(@PATH, 61, -955.648, -3257.04, 95.6808, 0, 0, 0, 0, 100, 0),
(@PATH, 62, -974.5, -3294.57, 95.7564, 0, 0, 0, 0, 100, 0),
(@PATH, 63, -986.333, -3326.08, 94.6731, 0, 0, 0, 0, 100, 0),
(@PATH, 64, -1004.65, -3355.36, 89.6631, 0, 0, 0, 0, 100, 0),
(@PATH, 65, -1012.79, -3389.33, 81.9381, 0, 0, 0, 0, 100, 0),
(@PATH, 66, -1016.84, -3425.25, 73.592, 0, 0, 0, 0, 100, 0),
(@PATH, 67, -1011.42, -3456.68, 66.7076, 0, 0, 0, 0, 100, 0),
(@PATH, 68, -1012.99, -3469.64, 63.2832, 0, 0, 0, 0, 100, 0),
(@PATH, 69, -987.473, -3493.9, 55.1532, 0, 0, 0, 0, 100, 0),
(@PATH, 70, -979.047, -3519.31, 47.2141, 0, 0, 0, 0, 100, 0),
(@PATH, 71, -975.939, -3541.65, 40.2592, 0, 0, 0, 0, 100, 0),
(@PATH, 72, -952.379, -3570.32, 30.0782, 0, 0, 0, 0, 100, 0),
(@PATH, 73, -946.539, -3611.8, 19.0936, 0, 0, 0, 0, 100, 0),
(@PATH, 74, -962.584, -3629.4, 18.2448, 0, 0, 0, 0, 100, 0),
(@PATH, 75, -972.594, -3639.53, 18.4471, 0, 0, 0, 0, 100, 0),
(@PATH, 76, -977.058, -3633.07, 19.1832, 0, 0, 0, 0, 100, 0),
(@PATH, 77, -974.034, -3634.92, 19.0109, 0, 1800000, 0, 0, 100, 0); -- 30 min wait in Ratchet

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
