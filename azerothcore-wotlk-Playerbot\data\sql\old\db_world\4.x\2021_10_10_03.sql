-- DB update 2021_10_10_02 -> 2021_10_10_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_02 2021_10_10_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633029730342507800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633029730342507800');

DELETE FROM `creature` WHERE `guid` BETWEEN 13213 AND 1313;
INSERT INTO `creature` VALUES
(94122,23971,1,0,0,1,1,0,0,356.938,-4739.97,9.74088,4.39618,300,0,0,42,0,0,0,0,0,'',0),
(94167,23971,1,0,0,1,1,0,0,358.325,-4741.16,9.68917,3.57544,300,0,0,71,0,0,0,0,0,'',0),
(94168,23971,1,0,0,1,1,0,0,355.094,-4740.15,9.83298,5.26763,300,0,0,42,0,0,0,0,0,'',0),
(94178,23971,1,0,0,1,1,0,0,358.327,-4742.84,9.64674,3.05708,300,0,0,55,0,0,0,0,0,'',0);

DELETE FROM `game_event_creature` WHERE `guid` BETWEEN 13213 AND 1313;
INSERT INTO `game_event_creature` VALUES
(12,94122),
(12,94167),
(12,94168),
(12,94178);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_03' WHERE sql_rev = '1633029730342507800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
