-- DB update 2021_07_12_00 -> 2021_07_12_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_12_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_12_00 2021_07_12_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1625916764471568068'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1625916764471568068');

-- Deletes lvl (min:10/avg:10.68/max:11) RLT 24074 from lvl 44 Blisterpaw Hyena (ID 5426), lvl 43 Fire Roc (ID 5429)
DELETE FROM `creature_loot_template` WHERE `Entry` IN (5426, 5429) AND `Reference` = 24074;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_12_01' WHERE sql_rev = '1625916764471568068';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
