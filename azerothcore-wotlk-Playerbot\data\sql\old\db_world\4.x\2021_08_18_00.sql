-- DB update 2021_08_17_03 -> 2021_08_18_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_17_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_17_03 2021_08_18_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1628999064175305912'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1628999064175305912');

-- Change Primitive Owlbeast spawn to above ground, add random movement
UPDATE `creature` SET `position_x` = 182.04, `position_y` = -3549.64, `position_z` = 130, `MovementType` = 1, `wander_distance` = 5 WHERE `id` = 2928 AND `guid` = 92955;


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_18_00' WHERE sql_rev = '1628999064175305912';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
