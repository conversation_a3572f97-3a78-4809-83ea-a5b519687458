-- DB update 2021_03_28_00 -> 2021_03_28_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_28_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_28_00 2021_03_28_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1616925470382883324'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1616925470382883324');

DELETE FROM `gossip_menu` WHERE <PERSON>uID IN (7111, 7112, 7113, 7114, 7115, 7118, 7119, 7120, 7121, 7122, 7123, 7124, 7125);

INSERT INTO `gossip_menu` VALUES 
-- Hunt<PERSON> <PERSON>
(7111, 8369),
(7112, 8370),
-- <PERSON>im<PERSON>t <PERSON>shatter
(7113, 8371),
(7114, 8372),
(7115, 8373),
-- Rohan the Assassin
(7118, 8380),
(7119, 8381),
(7120, 8382),
(7121, 8383),
(7122, 8384),
-- Rayne
(7123, 8385),
(7124, 8386),
(7125, 8387);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
