-- DB update 2021_03_18_02 -> 2021_03_18_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_18_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_18_02 2021_03_18_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1615775571176561100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1615775571176561100');

DELETE FROM `gameobject_addon` WHERE `guid` = 26628;
INSERT INTO `gameobject_addon` VALUES (26628, 0, 0);
UPDATE `gameobject` SET `position_x` = -8640.98, `position_y` = 760, `position_z` = 98.38 WHERE `guid` = 26628;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
