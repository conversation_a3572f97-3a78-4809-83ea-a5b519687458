-- DB update 2021_10_13_02 -> 2021_10_13_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_13_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_13_02 2021_10_13_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1632245299092353800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1632245299092353800');

DELETE FROM `graveyard_zone` WHERE `ID` = 101 AND `GhostZone` = 135;
INSERT INTO `graveyard_zone` (`ID`, `GhostZone`, `Faction`, `Comment`) VALUES
(101, 135, 469, 'Frostmane Hold, Kharanos GY - Dun Morogh');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_13_03' WHERE sql_rev = '1632245299092353800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
