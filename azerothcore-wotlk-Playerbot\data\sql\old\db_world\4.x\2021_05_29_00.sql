-- DB update 2021_05_25_08 -> 2021_05_29_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_25_08';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_25_08 2021_05_29_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1621448294726234300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1621448294726234300');
SET @PATH_ID := 798570;
DELETE FROM `waypoint_data` WHERE `id`=@PATH_ID AND `point`=14;
INSERT INTO `waypoint_data` VALUES (@PATH_ID, 14, -9038, 463.829, 93.2955, 0, 0, 0, 0, 100, 0);
DELETE FROM `waypoint_data` WHERE `id`=@PATH_ID AND `point`=20;
INSERT INTO `waypoint_data` VALUES (@PATH_ID, 20, -9038, 463.829, 93.2955, 0, 0, 0, 0, 100, 0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
