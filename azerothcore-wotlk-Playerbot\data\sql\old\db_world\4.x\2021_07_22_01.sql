-- DB update 2021_07_22_00 -> 2021_07_22_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_22_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_22_00 2021_07_22_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1626462660514898600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1626462660514898600');

UPDATE `creature_template` SET `AIName` = 'SmartAI' WHERE `entry` = 8978;

DELETE FROM `smart_scripts` WHERE (`source_type` = 0 AND `entryorguid` = 8978);
INSERT INTO `smart_scripts` (`entryorguid`, `source_type`, `id`, `link`, `event_type`, `event_phase_mask`, `event_chance`, `event_flags`, `event_param1`, `event_param2`, `event_param3`, `event_param4`, `event_param5`, `action_type`, `action_param1`, `action_param2`, `action_param3`, `action_param4`, `action_param5`, `action_param6`, `target_type`, `target_param1`, `target_param2`, `target_param3`, `target_param4`, `target_x`, `target_y`, `target_z`, `target_o`, `comment`) VALUES
(8978, 0, 0, 0, 0, 0, 100, 0, 0, 0, 2300, 3900, 0, 11, 6660, 64, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Thauris Balgarr - In Combat CMC - Cast \'Shoot\''),
(8978, 0, 1, 0, 0, 0, 100, 0, 7000, 11000, 21700, 35200, 0, 11, 6533, 1, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Thauris Balgarr - In Combat - Cast \'Net\''),
(8978, 0, 2, 0, 0, 0, 100, 0, 1000, 3000, 19200, 32700, 0, 11, 11802, 1, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Thauris Balgarr - In Combat - Cast \'Dark Iron Land Mine\'');


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_22_01' WHERE sql_rev = '1626462660514898600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
