-- DB update 2021_06_02_10 -> 2021_06_02_11
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_02_10';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_02_10 2021_06_02_11 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1622192313812244100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1622192313812244100');

-- Set Gatekeeper Rageroar to 'Timbermaw Hold' faction
UPDATE `creature_template` SET `faction` = 414 WHERE (`entry` = 6651);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_02_11' WHERE sql_rev = '1622192313812244100';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
