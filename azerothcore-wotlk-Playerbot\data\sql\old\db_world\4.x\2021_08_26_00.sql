-- DB update 2021_08_25_05 -> 2021_08_26_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_25_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_25_05 2021_08_26_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1629418018641557053'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1629418018641557053');

# Update Scarlet Friar preventing evade / floor falling
UPDATE `creature` SET `position_z` = 81 WHERE (`id` = 1538) AND (`guid` IN (44770));

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_26_00' WHERE sql_rev = '1629418018641557053';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
