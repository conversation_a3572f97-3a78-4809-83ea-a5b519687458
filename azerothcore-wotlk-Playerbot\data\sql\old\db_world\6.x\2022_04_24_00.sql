-- DB update 2022_04_23_14 -> 2022_04_24_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_04_23_14';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_04_23_14 2022_04_24_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1650744666752700000'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1650744666752700000');

UPDATE `spell_dbc` SET `AttributesEx3`=`AttributesEx3`|0x00100000 WHERE `id` IN (22282,22283,22285,22286,22287,22288);
UPDATE `smart_scripts` SET `event_type`=11 WHERE `entryorguid`=12460 AND `source_type`=0 AND `id`=1;

DELETE FROM `spell_custom_attr` WHERE `spell_id` IN (22282,22283,22285,22286,22287,22288);
INSERT INTO `spell_custom_attr` VALUES
(22282,0x00000800),
(22283,0x00000800),
(22285,0x00000800),
(22286,0x00000800),
(22287,0x00000800),
(22288,0x00000800);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_04_24_00' WHERE sql_rev = '1650744666752700000';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
