-- DB update 2021_11_30_02 -> 2021_12_02_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_30_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_30_02 2021_12_02_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1637899043891697627'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1637899043891697627');

-- Training quest should not be available until after Quest 8325 Reclaiming Sunstrider Isle is complete
UPDATE `quest_template_addon` SET `PrevQuestID`=8325 WHERE `ID` IN (8328,8563,8564,9392,9393,9676);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_02_00' WHERE sql_rev = '1637899043891697627';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
