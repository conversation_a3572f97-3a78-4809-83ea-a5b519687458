-- DB update 2022_01_13_00 -> 2022_01_13_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_13_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_13_00 2022_01_13_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641686782891083700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641686782891083700');

<PERSON>TER TABLE `broadcast_text`
    CHANGE `Language` `LanguageID` MEDIUMINT,
    CHANGE `EmoteID2` `EmoteID3` MEDIUMINT,
    CHANGE `EmoteID1` `EmoteID2` MEDIUMINT,
    CHANGE `EmoteID0` `EmoteID1` MEDIUMINT,
    CHANGE `EmoteDelay2` `EmoteDelay3` MEDIUMINT,
    CHANGE `EmoteDelay1` `EmoteDelay2` MEDIUMINT,
    CHANGE `EmoteDelay0` `EmoteDelay1` MEDIUMINT,
    CHANGE `SoundId` `SoundEntriesId` MEDIUMINT,
    CHANGE `Unk1` `EmotesID` MEDIUMINT,
    CHANGE `Unk2` `Flags` MEDIUMINT;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_13_01' WHERE sql_rev = '1641686782891083700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
