-- DB update 2021_10_10_19 -> 2021_10_10_20
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_19';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_19 2021_10_10_20 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633535837441039700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633535837441039700');

DELETE FROM `creature_loot_template` WHERE `item`=13465 AND `entry` NOT IN (1806,1813,6510,6520,6521,6551,6552,6557,6559,6560,7031,7092,7104,7132,7138,7139,7149,
8766,9376,9477,9598,9878,11458,11459,11461,11465,11722,11744,11745,11746,13021,13136,13196,13197,13285,14303,14460,14462);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_20' WHERE sql_rev = '1633535837441039700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
