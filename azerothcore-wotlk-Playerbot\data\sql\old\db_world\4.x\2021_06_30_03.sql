-- DB update 2021_06_30_02 -> 2021_06_30_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_30_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_30_02 2021_06_30_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1621501368347745883'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1621501368347745883');

UPDATE `creature`
SET `modelid` = 1744, `spawntimesecs` = 275, `wander_distance` = 5, `curhealth` = 178, `movementtype` = 1
WHERE `guid` BETWEEN 40489 AND 40496 AND `id` = 3254;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_30_03' WHERE sql_rev = '1621501368347745883';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
