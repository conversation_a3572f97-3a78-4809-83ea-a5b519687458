-- DB update 2021_04_11_22 -> 2021_04_11_23
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_11_22';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_11_22 2021_04_11_23 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1617946428490261300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1617946428490261300');
-- New GUIDs game_event 87, CREATURE 300007 - 300015*/

-- Demetria (Scarlet Oracle) Event
SET @DEMETRIAEVENT := 87; -- EVENT NEW GUID
DELETE FROM `game_event` WHERE `eventEntry` = @DEMETRIAEVENT;
INSERT INTO `game_event` (`eventEntry`,`start_time`,`end_time`,`occurence`,`length`,`holiday`,`description`,`world_event`,`announce`) VALUES 
(@DEMETRIAEVENT,'2008-01-02 11:55:00','2030-12-31 06:00:00',5184000,90,0,'Scarlet Oracle',0,2);

DELETE FROM `game_event_creature` WHERE `eventEntry`= @DEMETRIAEVENT;
INSERT INTO `game_event_creature` (`eventEntry`,`guid`) VALUES
(@DEMETRIAEVENT,42575),
(@DEMETRIAEVENT,300007),
(@DEMETRIAEVENT,300008),
(@DEMETRIAEVENT,300009),
(@DEMETRIAEVENT,300010),
(@DEMETRIAEVENT,300011),
(@DEMETRIAEVENT,300012),
(@DEMETRIAEVENT,300013),
(@DEMETRIAEVENT,300014),
(@DEMETRIAEVENT,300015);


-- Demetria (Scarlet Oracle) - Spawn during event only
UPDATE `creature` SET `MovementType` = 2,`position_x` = 1914.09,`position_y` = -3205.833,`position_z` = 124.891 WHERE `guid` = 42575;
UPDATE `creature_template` SET `faction` = 7,`speed_walk` = 1 WHERE `entry` = 12339;

DELETE FROM `smart_scripts` WHERE `entryorguid` = 12339 AND `source_type` = 0 AND `id` > 5;
DELETE FROM `smart_scripts` WHERE `entryorguid` IN (1233900,1233901,1233902) AND `source_type` = 9;
INSERT INTO `smart_scripts` (`entryorguid`,`source_type`,`id`,`link`,`event_type`,`event_phase_mask`,`event_chance`,`event_flags`,`event_param1`,`event_param2`,`event_param3`,`event_param4`,`event_param5`,`action_type`,`action_param1`,`action_param2`,`action_param3`,`action_param4`,`action_param5`,`action_param6`,`target_type`,`target_param1`,`target_param2`,`target_param3`,`target_param4`,`target_x`,`target_y`,`target_z`,`target_o`,`comment`) VALUES 
(12339,0,6,7,11,0,100,0,0,0,0,0,0,48,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Respawn - Set Active On'),
(12339,0,7,8,61,0,100,0,1000,1000,0,0,0,22,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Linked - Set Phase 1'),
(12339,0,8,0,61,0,100,0,25000,25000,0,0,0,80,1233900,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Linked - Run Script'),
(12339,0,9,0,1,0,100,0,0,0,0,0,0,22,2,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - OOC - Set Phase 2'),
(12339,0,10,0,76,2,100,0,45612,0,14,5000,0,111,87,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On GO Distance (Phase 2) - Stop Event'),
(12339,0,11,53,1,2,100,0,0,0,0,0,0,11,16592,32,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - OOC (Phase 2) - Cast \'Shadowform\''),
(12339,0,12,42,4,0,100,0,0,0,0,0,0,22,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Aggro - Set Phase 1'),
(12339,0,13,0,6,0,100,0,0,0,0,0,0,45,1,1,0,0,0,0,10,53171,11878,1,0,0,0,0,0,'Demetria - On Just Died - Set Data 1 1 \'Nathanos Blightcaller\''),
(12339,0,14,45,6,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300007,12352,1,0,0,0,0,0,'Demetria - Script - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,15,16,69,0,100,0,87,0,0,0,0,70,2,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Game Event \'Demetria\' End - Respawn In 2 secs'),
(12339,0,16,17,61,0,100,0,0,0,0,0,0,48,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Game Event \'Demetria\' End - Set Active Off'),
(12339,0,17,0,61,0,100,0,0,0,0,0,0,22,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Game Event \'Demetria\' End - Set Phase 0'),
(12339,0,18,0,8,0,100,0,118,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Polymorph\' - Run Script'),
(12339,0,19,0,8,0,100,0,12824,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Polymorph\' - Run Script'),
(12339,0,20,0,8,0,100,0,12825,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Polymorph\' - Run Script'),
(12339,0,21,0,8,0,100,0,12826,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Polymorph\' - Run Script'),
(12339,0,22,0,8,0,100,0,28271,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Polymorph\' - Run Script'),
(12339,0,23,0,8,0,100,0,28272,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Polymorph\' - Run Script'),
(12339,0,24,0,8,0,100,0,19503,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Scatter Shot\' - Run Script'),
(12339,0,25,0,8,0,100,0,20066,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Repentance\' - Run Script'),
(12339,0,26,0,8,0,100,0,51514,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Hex\' - Run Script'),
(12339,0,27,0,8,0,100,0,5782,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Fear\' - Run Script'),
(12339,0,28,0,8,0,100,0,6213,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Fear\' - Run Script'),
(12339,0,29,0,8,0,100,0,6215,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Fear\' - Run Script'),
(12339,0,30,0,8,0,100,0,5484,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Howl of Terror\' - Run Script'),
(12339,0,31,0,8,0,100,0,17928,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Howl of Terror\' - Run Script'),
(12339,0,32,0,8,0,100,0,8122,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Psychic Scream\' - Run Script'),
(12339,0,33,0,8,0,100,0,8124,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Psychic Scream\' - Run Script'),
(12339,0,34,0,8,0,100,0,10888,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Psychic Scream\' - Run Script'),
(12339,0,35,0,8,0,100,0,10890,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Psychic Scream\' - Run Script'),
(12339,0,36,0,8,0,100,0,408,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Kidney Shot\' - Run Script'),
(12339,0,37,0,8,0,100,0,8643,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Kidney Shot\' - Run Script'),
(12339,0,38,0,8,0,100,0,12809,0,5000,5000,0,80,1233902,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Spellhit \'Concussion Blow\' - Run Script'),
(12339,0,39,0,32,0,100,0,0,2000,1000,1000,0,22,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Damage (Phase 3) - Set Phase 1'),
(12339,0,40,41,74,1,100,0,0,1,1000,1000,0,45,1,3,0,0,0,0,11,12352,15,0,0,0,0,0,0,'Demetria - Target HP Percentage - Set Data 1 3 \'Scarlet Trooper\''),
(12339,0,41,0,61,1,100,0,0,0,0,0,0,80,1233901,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Linked - Run Script'),
(12339,0,42,43,61,0,100,0,0,0,0,0,0,45,1,4,0,0,0,0,9,12352,0,200,0,0,0,0,0,'Demetria - Linked - Set Data 1 4 \'Scarlet Trooper\''),
(12339,0,43,0,61,0,100,0,0,0,0,0,0,39,20,0,0,0,0,0,9,12352,0,200,0,0,0,0,0,'Demetria - Linked - Call for Help'),
(12339,0,44,0,7,0,100,0,0,0,0,0,0,45,1,5,0,0,0,0,11,12352,50,0,0,0,0,0,0,'Demetria - On Evade - Set Data 1 5 \'Scarlet Trooper\''),
(12339,0,45,46,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300008,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,46,47,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300009,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,47,48,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300010,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,48,49,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300011,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,49,50,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300012,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,50,51,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300013,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,51,52,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300014,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,52,0,61,0,100,0,0,0,0,0,0,45,1,2,0,0,0,0,10,300015,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,53,54,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300007,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 5 \'Scarlet Trooper\''),
(12339,0,54,55,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300008,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,55,56,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300009,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,56,57,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300010,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,57,58,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300011,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,58,59,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300012,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,59,60,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300013,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,60,61,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300014,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,61,0,61,2,100,0,0,0,0,0,0,45,1,5,0,0,0,0,10,300015,12352,1,0,0,0,0,0,'Demetria - Linked - Set Data 1 2 \'Scarlet Trooper\''),
(12339,0,62,0,38,1,100,0,1,1,0,0,0,24,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - On Data Set 1 5 - Evade'),

(1233900,9,0,0,0,0,100,0,0,0,0,0,0,18,2,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Script - Set Unit Flags'),
(1233900,9,1,0,0,0,100,0,0,0,0,0,0,62,0,0,0,0,0,2,1,0,0,0,0,1567.16,-5611,114.19,1.084,'Demetria - Script - Teleport'),
(1233900,9,2,0,0,0,100,0,1000,1000,0,0,0,28,16592,0,0,0,0,2,1,0,0,0,0,0,0,0,0,'Demetria - Script - Remove \'Shadowform\' Aura'),
(1233900,9,3,0,0,0,100,0,44000,44000,0,0,0,11,16592,33,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Script - Cast \'Shadowform\''),
(1233900,9,4,0,00,0,100,0,0,0,0,0,0,2,89,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Script - Set Faction 89'),
(1233900,9,5,0,0,0,100,0,0,0,0,0,0,19,2,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Script - Remove Unit Flags'),
(1233900,9,6,0,0,0,100,0,1000,1000,0,0,0,45,1,1,0,0,0,0,10,300007,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,7,0,0,0,100,0,0,0,0,0,0,45,1,1,0,0,0,0,10,300008,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,8,0,0,0,100,0,2000,2000,0,0,0,45,1,1,0,0,0,0,10,300009,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,9,0,0,0,100,0,0,0,0,0,0,45,1,1,0,0,0,0,10,300010,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,10,0,0,0,100,0,2000,2000,0,0,0,45,1,1,0,0,0,0,10,300011,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,11,0,0,0,100,0,0,0,0,0,0,45,1,1,0,0,0,0,10,300012,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,12,0,0,0,100,0,2000,2000,0,0,0,45,1,1,0,0,0,0,10,300013,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,13,0,0,0,100,0,0,0,0,0,0,45,1,1,0,0,0,0,10,300014,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,14,0,0,0,100,0,2000,2000,0,0,0,45,1,1,0,0,0,0,10,300015,12352,1,0,0,0,0,0,'Demetria - Set Data 1 1 \'Scarlet Trooper\''),
(1233900,9,15,0,0,0,100,0,0,0,0,0,0,22,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Script - Set Phase 0'),

(1233901,9,0,0,0,0,100,0,1000,1000,0,0,0,11,19721,1,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Linked - Cast Resurrect Trooper'),
(1233901,9,1,0,0,0,100,0,0,0,0,0,0,64,1,0,0,0,0,0,7,0,0,0,0,0,0,0,0,'Demetria - Linked - Store Target'),
(1233901,9,2,0,0,0,100,0,1000,1000,0,0,0,100,1,0,0,0,0,0,19,12352,100,0,0,0,0,0,0,'Demetria - Linked - Send Target'),
(1233901,9,3,0,0,0,100,0,2000,2000,0,0,0,39,100,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Linked - Call for Help'),

(1233902,9,0,0,0,0,100,0,0,0,0,0,0,22,4,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Demetria - Script - Set Phase 3');

UPDATE `creature_template_addon` SET `path_id` = 12339 WHERE `entry` = 12339;

DELETE FROM `waypoint_data` WHERE `id` = 12339;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES 
(12339,1,1567.42,-5610.38,114.189,0,0,0,0,100,0),
(12339,2,1564.17,-5609.18,114.183,0,0,0,0,100,0),
(12339,3,1566.34,-5605.47,114.183,0,0,0,0,100,0),
(12339,4,1569.38,-5606.32,114.188,0,0,0,0,100,0),
(12339,5,1573.89,-5597.87,111.171,0,0,0,0,100,0),
(12339,6,1578.76,-5588.57,111.171,0,0,0,0,100,0),
(12339,7,1585.31,-5576.2,111.171,0,0,0,0,100,0),
(12339,8,1594.85,-5557.49,111.171,0,0,0,0,100,0),
(12339,9,1603.55,-5557.01,111.171,0,0,0,0,100,0),
(12339,10,1610.81,-5541.87,111.171,0,0,0,0,100,0),
(12339,11,1604.87,-5538.01,111.171,0,0,0,0,100,0),
(12339,12,1612.66,-5523.97,111.147,0,0,0,0,100,0),
(12339,13,1618.32,-5514.03,107.117,0,0,0,0,100,0),
(12339,14,1627.73,-5496.36,100.729,0,0,0,0,100,0),
(12339,15,1633,-5484.04,100.729,0,0,0,0,100,0),
(12339,16,1638.35,-5469.5,98.6581,0,0,0,0,100,0),
(12339,17,1645.41,-5450.02,92.1866,0,0,0,0,100,0),
(12339,18,1652.78,-5430.76,84.4578,0,0,0,0,100,0),
(12339,19,1657.93,-5416.62,79.8772,0,0,0,0,100,0),
(12339,20,1661.83,-5406.66,76.2934,0,0,0,0,100,0),
(12339,21,1669.8,-5383.78,73.6196,0,0,0,0,100,0),
(12339,22,1677.99,-5358.84,73.6117,0,0,0,0,100,0),
(12339,23,1695.25,-5350.09,73.6118,0,0,0,0,100,0),
(12339,24,1687.04,-5321.94,73.6112,0,0,0,0,100,0),
(12339,25,1688.58,-5304.51,73.6112,0,0,0,0,100,0),
(12339,26,1691,-5283.65,73.6122,0,0,0,0,100,0),
(12339,27,1691.91,-5262.08,73.6113,0,0,0,0,100,0),
(12339,28,1692.66,-5241.1,73.6185,0,0,0,0,100,0),
(12339,29,1692.85,-5206.13,74.6546,0,0,0,0,100,0),
(12339,30,1690.64,-5185.28,73.9301,0,0,0,0,100,0),
(12339,31,1688.08,-5167.96,73.9937,0,0,0,0,100,0),
(12339,32,1690.38,-5149.03,74.0396,0,0,0,0,100,0),
(12339,33,1697.51,-5118.86,73.6245,0,0,0,0,100,0),
(12339,34,1704.67,-5099.13,74.6873,0,0,0,0,100,0),
(12339,35,1711.01,-5080.54,77.301,0,0,0,0,100,0),
(12339,36,1718.55,-5057.22,80.1628,0,0,0,0,100,0),
(12339,37,1730.24,-5032.73,80.6236,0,0,0,0,100,0),
(12339,38,1742.91,-5011.79,79.9012,0,0,0,0,100,0),
(12339,39,1754.95,-4994.6,80.643,0,0,0,0,100,0),
(12339,40,1766.67,-4977.18,81.2274,0,0,0,0,100,0),
(12339,41,1773.03,-4950.42,81.449,0,0,0,0,100,0),
(12339,42,1777.47,-4922.77,81.5212,0,0,0,0,100,0),
(12339,43,1782.09,-4902.3,84.2517,0,0,0,0,100,0),
(12339,44,1788.34,-4877.56,87.4965,0,0,0,0,100,0),
(12339,45,1796.57,-4845.8,89.4469,0,0,0,0,100,0),
(12339,46,1809.45,-4798.58,91.0304,0,0,0,0,100,0),
(12339,47,1816.93,-4777.84,90.0115,0,0,0,0,100,0),
(12339,48,1831.75,-4734.83,90.0915,0,0,0,0,100,0),
(12339,49,1862.63,-4701.05,93.0158,0,0,0,0,100,0),
(12339,50,1889.08,-4684.05,92.355,0,0,0,0,100,0),
(12339,51,1910.74,-4672.59,91.1087,0,0,0,0,100,0),
(12339,52,1927.94,-4658.95,87.5957,0,0,0,0,100,0),
(12339,53,1935.76,-4647.33,84.3063,0,0,0,0,100,0),
(12339,54,1944.66,-4632.27,79.9795,0,0,0,0,100,0),
(12339,55,1954.98,-4609.24,74.7545,0,0,0,0,100,0),
(12339,56,1958.16,-4599.23,73.7161,0,0,0,0,100,0),
(12339,57,1968.46,-4572.57,73.6229,0,0,0,0,100,0),
(12339,58,1979.44,-4555.29,73.6229,0,0,0,0,100,0),
(12339,59,1984,-4549.99,73.6229,0,0,0,0,100,0),
(12339,60,1996.94,-4538.24,73.6229,0,0,0,0,100,0),
(12339,61,2005.36,-4531.97,73.6229,0,0,0,0,100,0),
(12339,62,2016.76,-4523.84,73.6213,0,0,0,0,100,0),
(12339,63,2027.83,-4517.97,73.6213,0,0,0,0,100,0),
(12339,64,2045.28,-4518.38,73.6213,0,0,0,0,100,0),
(12339,65,2061.31,-4534.8,73.6213,0,0,0,0,100,0),
(12339,66,2086.08,-4561.88,73.6213,0,0,0,0,100,0),
(12339,67,2105.32,-4580.45,73.6238,0,0,0,0,100,0),
(12339,68,2121.3,-4594.16,73.6238,0,0,0,0,100,0),
(12339,69,2146.73,-4612.71,73.6218,0,0,0,0,100,0),
(12339,70,2177.86,-4627.43,73.6067,0,0,0,0,100,0),
(12339,71,2197.77,-4633.93,73.6226,0,0,0,0,100,0),
(12339,72,2219.97,-4633.96,73.6226,0,0,0,0,100,0),
(12339,73,2250.74,-4627.66,73.6226,0,0,0,0,100,0),
(12339,74,2274.12,-4620.36,73.6226,0,0,0,0,100,0),
(12339,75,2304.25,-4611.22,73.6227,0,0,0,0,100,0),
(12339,76,2338.64,-4604.74,73.6227,0,0,0,0,100,0),
(12339,77,2419.64,-4612.267,73.611,0,0,0,0,100,0),
(12339,78,2457.27,-4631.39,74.092,0,0,0,0,100,0),
(12339,79,2493.9,-4652.17,75.2848,0,0,0,0,100,0),
(12339,80,2535.82,-4654.52,77.3071,0,0,0,0,100,0),
(12339,81,2567.1,-4644.61,79.4072,0,0,0,0,100,0),
(12339,82,2594.36,-4628.84,81.876,0,0,0,0,100,0),
(12339,83,2617.07,-4615.06,84.1387,0,0,0,0,100,0),
(12339,84,2689.76,-4571.94,87.2045,0,0,0,0,100,0),
(12339,85,2745.8,-4539.07,88.764,0,0,0,0,100,0),
(12339,86,2839.33,-4436.83,89.7157,0,0,0,0,100,0),
(12339,87,2880.41,-4361.54,90.2576,0,0,0,0,100,0),
(12339,88,2923.76,-4110.16,96.3698,0,0,0,0,100,0),
(12339,89,2947.2,-4033.15,99.8002,0,0,0,0,100,0),
(12339,90,2968.64,-3982.02,104.423,0,0,0,0,100,0),
(12339,91,2976.9,-3964.26,107.058,0,0,0,0,100,0),
(12339,92,2983.54,-3937.96,111.717,0,0,0,0,100,0),
(12339,93,3000.21,-3877.74,118.93,0,0,0,0,100,0),
(12339,94,3011.92,-3842.22,119.306,0,0,0,0,100,0),
(12339,95,3021.88,-3813.28,118.955,0,0,0,0,100,0),
(12339,96,3028.71,-3797.17,120.17,0,0,0,0,100,0),
(12339,97,3042.19,-3764.47,119.971,0,0,0,0,100,0),
(12339,98,3057.23,-3724.98,119.568,0,0,0,0,100,0),
(12339,99,3062.31,-3690.16,121.125,0,0,0,0,100,0),
(12339,100,3048.16,-3659.65,122.811,0,0,0,0,100,0),
(12339,101,3041.49,-3641.07,125.02,0,0,0,0,100,0),
(12339,102,3039.91,-3617.72,123.977,0,0,0,0,100,0),
(12339,103,3040.67,-3597.35,124.377,0,0,0,0,100,0),
(12339,104,3042.45,-3556.22,126.571,0,0,0,0,100,0),
(12339,105,3046.02,-3530.23,129.898,0,0,0,0,100,0),
(12339,106,3057.06,-3493.26,131.601,0,0,0,0,100,0),
(12339,107,3076.38,-3462.24,134.709,0,0,0,0,100,0),
(12339,108,3086.02,-3451.18,136,0,0,0,0,100,0),
(12339,109,3097.45,-3438.57,136.842,0,0,0,0,100,0),
(12339,110,3133.22,-3404.77,139.345,0,0,0,0,100,0),
(12339,111,3150.28,-3401.16,140.105,0,0,0,0,100,0),
(12339,112,3172.46,-3393.09,142.015,0,0,0,0,100,0),
(12339,113,3191.09,-3389.1,143.607,0,0,0,0,100,0),
(12339,114,3210.72,-3387.02,144.24,0,0,0,0,100,0),
(12339,115,3250.95,-3382.95,143.581,0,0,0,0,100,0),
(12339,116,3269.06,-3382.33,143.195,0,0,0,0,100,0),
(12339,117,3286.95,-3382.42,142.377,0,0,0,0,100,0),
(12339,118,3307.89,-3382.95,144.951,0,0,0,0,100,0),
(12339,119,3329.2,-3382.28,144.845,0,0,0,0,100,0),
(12339,120,3347.55,-3381.64,144.779,0,0,0,0,100,0),
(12339,121,3361.09,-3380.75,144.781,0,0,0,0,100,0);


-- Scarlet Trooper
DELETE FROM `creature` WHERE `guid` IN (300007,300008,300009,300010,300011,300012,300013,300014,300015);
INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES 
(300007,12352,0,0,0,1,1,0,1,1616.108,-5525.466,111.143,1.0121,300,0,1,1608,2289,0,0,0,0,'',0),
(300008,12352,0,0,0,1,1,0,1,1608.893,-5521.602,111.143,1.122,300,0,1,1608,2369,0,0,0,0,'',0),
(300009,12352,0,0,0,1,1,0,1,1617.871,-5521.95,109.917,1.157,300,0,1,1608,2289,0,0,0,0,'',0),
(300010,12352,0,0,0,1,1,0,1,1610.813,-5518.383,109.932,1.09,300,0,1,1608,2369,0,0,0,0,'',0),
(300011,12352,0,0,0,1,1,0,1,1619.71,-5518.534,108.504,1.102,300,0,1,1608,2289,0,0,0,0,'',0),
(300012,12352,0,0,0,1,1,0,1,1612.809,-5515.104,108.505,1.129,300,0,1,1608,2369,0,0,0,0,'',0),
(300013,12352,0,0,0,1,1,0,1,1622.078,-5514.877,106.922,1.081,300,0,1,1608,2289,0,0,0,0,'',0),
(300014,12352,0,0,0,1,1,0,1,1614.774,-5511.167,106.87,1.174,300,0,1,1608,2369,0,0,0,0,'',0),
(300015,12352,0,0,0,1,1,0,1,1620.735,-5509.139,105.345,1.013,300,0,1,1608,2289,0,0,0,0,'',0);

DELETE FROM `creature_template_addon` WHERE `entry` = 12352;
INSERT INTO `creature_template_addon` (`entry`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`isLarge`,`auras`) VALUES 
(12352,0,0,0,0,0,0,'');

DELETE FROM `smart_scripts` WHERE `entryorguid` = 12352 AND `source_type` = 0;
DELETE FROM `smart_scripts` WHERE `entryorguid` = 1235200 AND `source_type` = 9;
INSERT INTO `smart_scripts` (`entryorguid`,`source_type`,`id`,`link`,`event_type`,`event_phase_mask`,`event_chance`,`event_flags`,`event_param1`,`event_param2`,`event_param3`,`event_param4`,`event_param5`,`action_type`,`action_param1`,`action_param2`,`action_param3`,`action_param4`,`action_param5`,`action_param6`,`target_type`,`target_param1`,`target_param2`,`target_param3`,`target_param4`,`target_x`,`target_y`,`target_z`,`target_o`,`comment`) VALUES 
(12352,0,0,0,4,0,100,1,0,0,0,0,0,11,8258,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Aggro - Cast \'Devotion Aura\''),
(12352,0,1,0,0,0,100,0,4000,6000,7000,9000,0,11,17149,0,0,0,0,0,2,0,0,0,0,0,0,0,0,'Scarlet Trooper - In Combat - Cast \'Exorcism\''),
(12352,0,2,0,0,0,100,0,8000,11000,15000,19000,0,11,13005,0,0,0,0,0,5,0,0,0,0,0,0,0,0,'Scarlet Trooper - In Combat - Cast \'Hammer of Justice\''),
(12352,0,3,0,74,0,100,0,0,40,15000,20000,0,11,17233,1,0,0,0,0,9,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Friendly Between 0-40% Health - Cast \'Lay on Hands\''),
(12352,0,4,5,11,0,100,0,0,0,0,0,0,103,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Spawn - Rooted'),
(12352,0,5,0,61,0,100,0,0,0,0,0,0,22,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Linked - Set Phase 1'),
(12352,0,6,0,38,0,100,0,1,4,0,0,0,42,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Data Set 1 4 - Set Invincibility 1 HP'),
(12352,0,7,0,38,0,100,0,1,1,0,0,0,103,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Data Set 1 1 - Remove Root'),
(12352,0,8,9,38,1,100,0,1,2,0,0,0,41,180000,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Data Set 1 2 - Despawn after 180 seconds'),
(12352,0,9,0,61,0,100,0,0,0,0,0,0,42,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Linked - Remove Invincibility'),
(12352,0,10,0,69,0,100,0,87,0,0,0,0,70,2,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Game Event \'Demetria\' End - Respawn In 2 secs'),
(12352,0,11,12,2,1,100,0,0,1,1000,1000,0,102,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Between 0-1% Health (Phase 1) - Turn Off HP Regen'),
(12352,0,12,13,61,1,100,0,0,0,0,0,0,101,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Between 0-1% Health (Phase 1) - Set Home Current POS'),
(12352,0,13,14,61,1,100,0,0,0,0,0,0,11,29266,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Between 0-1% Health - Cast Feign Death'),
(12352,0,14,0,61,1,100,0,0,0,0,0,0,22,2,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Linked - Set Phase 2'),
(12352,0,15,0,38,2,100,0,1,3,0,0,0,80,1235200,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Data Set 1 3 (Phase 2) - Run Script'),
(12352,0,16,17,4,0,100,0,0,0,0,0,0,22,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Aggro - Set Phase 1'),
(12352,0,17,0,61,0,100,0,0,0,0,0,0,103,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Linked - Remove Root'),
(12352,0,18,0,38,2,100,0,1,2,0,0,0,37,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Data Set 1 2 - Die'),
(12352,0,19,0,38,1,100,0,1,5,0,0,0,42,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - On Data Set 1 5 - Remove Invincibility'),
(12352,0,20,0,7,0,100,0,0,0,0,0,0,45,1,1,0,0,0,0,11,12339,15,0,0,0,0,0,0,'Scarlet Trooper - On Evade - Set Data 1 1 \'Demetria\''),

(1235200,9,0,0,0,0,100,0,1000,1000,0,0,0,11,19721,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Script - Cast Resurrect Trooper'),
(1235200,9,1,0,0,0,100,0,0,0,0,0,0,11,69963,2,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Script - Cast Greater Heal'),
(1235200,9,2,0,0,0,100,0,0,0,0,0,0,102,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Script - Turn On HP Regen'),
(1235200,9,3,0,0,0,100,0,2000,2000,0,0,0,28,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Script - Remove All Auras'),
(1235200,9,4,0,0,0,100,0,2000,2000,0,0,0,49,0,0,0,0,0,0,21,100,1,0,0,0,0,0,0,'Scarlet Trooper - Script - Start Attacking'),
(1235200,9,5,0,0,0,100,0,0,0,0,0,0,22,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Scarlet Trooper - Script - Set Phase 1');

UPDATE `creature_template` SET `MovementType` = 0,`speed_walk` = 1 WHERE `entry` = 12352;

DELETE FROM `creature_formations` where leaderGUID = 42575;
INSERT INTO `creature_formations` (`leaderGUID`,`memberGUID`,`dist`,`angle`,`groupAI`,`point_1`,`point_2`) VALUES 
(42575,42575,0,0,515,0,0),
(42575,300007,3,0,515,0,0),
(42575,300008,3,40,515,0,0),
(42575,300009,3,80,515,0,0),
(42575,300010,3,120,515,0,0),
(42575,300011,3,160,515,0,0),
(42575,300012,3,200,515,0,0),
(42575,300013,3,240,515,0,0),
(42575,300014,3,280,515,0,0),
(42575,300015,3,320,515,0,0); 


-- Nathanos Blightcaller
DELETE FROM `smart_scripts` WHERE `entryorguid` = 11878 AND `source_type` = 0 AND `id` > 3;
DELETE FROM `smart_scripts` WHERE `entryorguid` = 1187800 AND `source_type` = 9;
INSERT INTO `smart_scripts` (`entryorguid`,`source_type`,`id`,`link`,`event_type`,`event_phase_mask`,`event_chance`,`event_flags`,`event_param1`,`event_param2`,`event_param3`,`event_param4`,`event_param5`,`action_type`,`action_param1`,`action_param2`,`action_param3`,`action_param4`,`action_param5`,`action_param6`,`target_type`,`target_param1`,`target_param2`,`target_param3`,`target_param4`,`target_x`,`target_y`,`target_z`,`target_o`,`comment`) VALUES 
(11878,0,4,5,19,0,100,0,6148,0,0,0,0,112,87,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Nathanos Blightcaller - On Quest \'The Scarlet Oracle, Demetria\' Taken - Game Event \'Demetria\' Start'),
(11878,0,5,0,61,0,100,0,0,0,0,0,0,48,1,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Nathanos Blightcaller - Linked - Set Active On'),
(11878,0,6,0,38,0,100,0,1,1,0,0,0,80,1187800,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Nathanos Blightcaller - On Data Set 1 1 - Run Script'),
(1187800,9,0,0,0,0,100,0,300000,300000,300000,300000,0,111,87,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Nathanos Blightcaller - Script - Stop Event'),
(1187800,9,1,0,0,0,100,0,1000,1000,0,0,0,48,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,'Nathanos Blightcaller - On Game Event \'Demetria\' End - Set Active Off');

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
