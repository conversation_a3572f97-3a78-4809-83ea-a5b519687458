-- DB update 2021_07_23_16 -> 2021_07_23_17
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_23_16';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_23_16 2021_07_23_17 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1626472277718613700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1626472277718613700');

DELETE FROM `spell_script_names` WHERE `spell_id` = 46642 AND `ScriptName` = 'spell_gen_5000_gold';
INSERT INTO `spell_script_names` (`spell_id`, `ScriptName`) VALUES
(46642, 'spell_gen_5000_gold');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_23_17' WHERE sql_rev = '1626472277718613700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
