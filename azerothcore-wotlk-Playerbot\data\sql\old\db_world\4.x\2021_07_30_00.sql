-- DB update 2021_07_29_06 -> 2021_07_30_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_29_06';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_29_06 2021_07_30_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1627113690089052600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1627113690089052600');

<PERSON>TER TABLE `creature_template` ADD COLUMN `ExperienceModifier` FLOAT NOT NULL DEFAULT 1 AFTER `ArmorModifier`;

UPDATE `creature_template` SET `ExperienceModifier` = 2 WHERE `entry` IN (639,642,643,644,645,646,647,1663,1666,1696,1716,1717,1720,1763,1853,2748,3586,3653,3654,3669,3670,3671,3673,3674,3872,3886,3887,3914,3927,3974,3975,3976,3977,3983,4274,4275,4278,4279,4420,4421,4422,4424,4428,4542,4543,4829,4830,4831,4832,4854,4887,5709,5710,5711,5712,5713,5714,5715,5716,5717,5719,5720,5721,5722,5775,6168,6229,6235,6243,6487,6906,6910,7023,7079,7206,7228,7267,7271,7272,7273,7275,7291,7354,7355,7356,7357,7358,7361,7795,7796,7797,7800,8127,8443,8567,8580,8929,8983,9016,9017,9018,9019,9024,9025,9033,9041,9042,9056,9156,9196,9236,9237,9319,9499,9502,9537,9543,9568,9736,9816,9938,10096,10220,10268,10339,10363,10393,10429,10430,10432,10433,10435,10436,10437,10438,10439,10440,10502,10503,10504,10505,10506,10507,10508,10516,10558,10584,10596,10808,10811,10813,10901,10997,11032,11058,11143,11261,11486,11487,11488,11489,11490,11492,11496,11501,11517,11518,11519,11520,11622,12201,12203,12225,12236,12258,12876,12902,13280,13282,13282,13596,13601,13601,14321,14322,14323,14324,14325,14326,14327);
UPDATE `creature_template` SET `ExperienceModifier` = 0.5 WHERE `entry` IN (7076,7077,7309,8477,8615,8658,9157,10387,10389,10441,10461,11460,11466,12473,14350,14396,15546,15720,15733,15735,15738,16032,16286);
UPDATE `creature_template` SET `ExperienceModifier` = 0.1 WHERE `entry` IN (15316,15317);
UPDATE `creature_template` SET `ExperienceModifier` = 0.25 WHERE `entry` IN (2946,6575,9496,10411,11713,14386,16230);
UPDATE `creature_template` SET `ExperienceModifier` = 0.005 WHERE `entry` = 10925;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_30_00' WHERE sql_rev = '1627113690089052600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
