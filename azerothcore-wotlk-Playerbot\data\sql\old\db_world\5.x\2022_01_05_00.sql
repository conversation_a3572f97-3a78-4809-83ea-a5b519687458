-- DB update 2022_01_04_05 -> 2022_01_05_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_04_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_04_05 2022_01_05_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1638885155008688879'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1638885155008688879');

-- Moves Shadowforge Surveyor up slightly so it's not stuck in object'
UPDATE `creature` SET `position_z` = 262 WHERE `id` = 4844 AND `guid` = 7714;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_05_00' WHERE sql_rev = '1638885155008688879';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
