-- DB update 2021_04_11_11 -> 2021_04_11_12
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_11_11';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_11_11 2021_04_11_12 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1617774706114876200'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1617774706114876200');

DELETE FROM `creature` WHERE (`id` = 16313) AND (`guid` IN (81792));
INSERT INTO `creature` VALUES
(81792, 16313, 530, 0, 0, 1, 1, 0, 0, 7937.95, -7354.2, 144.804, 5.9059, 300, 0, 0, 166, 178, 0, 0, 0, 0, '', 0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
