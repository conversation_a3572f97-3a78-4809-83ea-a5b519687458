-- DB update 2021_04_30_03 -> 2021_04_30_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_04_30_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_04_30_03 2021_04_30_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1617859274362409300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1617859274362409300');

DELETE FROM `gameobject` WHERE (`id` = 153454);
INSERT INTO `gameobject` VALUES
(45734,153454,0,0,0,1,1,3003.64,-4886.26,108.384,0.506145,0,0,0.25038,0.968148,300,100,1,'',0),
(45910,153454,0,0,0,1,1,3191.12,-4639.98,119.947,-2.94961,0,0,0.995396,-0.095846,300,100,1,'',0),
(45915,153454,0,0,0,1,1,2544.61,-3704.67,179.64,0.244346,0,0,0.121869,0.992546,300,100,1,'',0),
(45931,153454,0,0,0,1,1,2601.59,-5512.31,158.713,-0.034907,0,0,0.017452,-0.999848,300,100,1,'',0),
(45933,153454,0,0,0,1,1,1753.27,-5410.36,113.525,-0.366519,0,0,0.182236,-0.983255,300,100,1,'',0),
(45938,153454,0,0,0,1,1,1829.56,-5210.25,113.105,-1.06465,0,0,0.507538,-0.861629,300,100,1,'',0),
(49088,153454,1,0,0,1,1,6417.92,-4281.62,666.034,-0.750491,0,0,0.366501,-0.930418,300,100,1,'',0),
(49089,153454,1,0,0,1,1,6520.67,-3264.04,574.942,2.93215,0,0,0.994522,0.104528,300,100,1,'',0),
(49090,153454,1,0,0,1,1,6796.87,-2666.08,544.784,2.96706,0,0,0.996195,0.087156,300,100,1,'',0),
(49091,153454,1,0,0,1,1,6796.38,-5150.05,732.103,-2.07694,0,0,0.861629,-0.507538,300,100,1,'',0),
(85796,153454,1,0,0,1,1,6491.73,-3132.61,570.651,-1.16937,0,0,0,1,300,255,1,'',0),
(85797,153454,0,0,0,1,1,1599.41,-5315.48,91.1041,2.23402,0,0,0,1,300,255,1,'',0),
(85798,153454,1,0,0,1,1,6863.55,-5118.35,695.735,-2.33874,0,0,0,1,300,255,1,'',0),
(85799,153454,1,0,0,1,1,6779,-5047.81,722.833,-0.90757,0,0,0,1,300,255,1,'',0),
(85800,153454,1,0,0,1,1,6830.53,-3584.34,718.537,0.95993,0,0,0,1,300,255,1,'',0),
(85801,153454,1,0,0,1,1,6843.18,-2482.17,561.01,-1.98967,0,0,0,1,300,255,1,'',0),
(85802,153454,1,0,0,1,1,6617.37,-4081.4,662.525,1.50098,0,0,0,1,300,255,1,'',0),
(85803,153454,1,0,0,1,1,6834.71,-5028.53,691.339,-1.67551,0,0,0,1,300,255,1,'',0),
(87393,153454,1,0,0,1,1,6313.62,-2395.13,556.702,-2.75761,0,0,0,1,300,255,1,'',0),
(100086,153454,0,0,0,1,1,1608.74,-5415.46,76.3989,0.663223,0,0,0,0,300,0,1,'',0);

DELETE FROM `pool_gameobject` WHERE (`guid` IN (45734, 45910, 45915, 45931, 45933, 45938, 85797, 100086));

UPDATE `creature_template` SET `AIName` = 'SmartAI' WHERE (`entry` IN (9449, 9447, 9452));

DELETE FROM `smart_scripts` WHERE (`source_type` = 0 AND `entryorguid` = 9449);
INSERT INTO `smart_scripts` VALUES
(9449, 0, 0, 0, 0, 0, 100, 0, 0, 0, 3400, 4800, 0, 11, 15587, 64, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Cleric - In Combat - Cast \'Mind Blast\''),
(9449, 0, 1, 0, 74, 0, 100, 0, 0, 40, 25000, 35000, 0, 11, 15587, 1, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Cleric - On Friendly Between 0-40% Health - Cast \'Mind Blast\''),
(9449, 0, 2, 0, 2, 0, 100, 1, 0, 15, 0, 0, 0, 25, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Cleric - Between 0-15% Health - Flee For Assist (No Repeat)'),
(9449, 0, 3, 0, 1, 0, 100, 0, 100, 100, 3000, 3000, 0, 49, 0, 0, 0, 0, 0, 0, 21, 10, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Cleric - Out of Combat - Start Attacking');

DELETE FROM `smart_scripts` WHERE (`source_type` = 0 AND `entryorguid` = 9447);
INSERT INTO `smart_scripts` VALUES
(9447, 0, 0, 0, 0, 0, 100, 0, 4000, 7000, 7000, 11000, 0, 11, 14518, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Warder - In Combat - Cast \'Crusader Strike\''),
(9447, 0, 1, 0, 2, 0, 100, 0, 0, 50, 15000, 22000, 0, 11, 15493, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Warder - Between 0-50% Health - Cast \'Holy Light\''),
(9447, 0, 2, 0, 1, 0, 100, 0, 100, 100, 3000, 3000, 0, 49, 0, 0, 0, 0, 0, 0, 21, 10, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Warder - Out of Combat - Start Attacking');

DELETE FROM `smart_scripts` WHERE (`source_type` = 0 AND `entryorguid` = 9452);
INSERT INTO `smart_scripts` VALUES
(9452, 0, 0, 0, 0, 0, 100, 0, 0, 0, 3400, 4800, 0, 11, 25055, 64, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Enchanter - In Combat - Cast \'Arcane Bolt\''),
(9452, 0, 1, 0, 0, 0, 100, 0, 7000, 10000, 18000, 22000, 0, 11, 15970, 1, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Enchanter - In Combat - Cast \'Sleep\''),
(9452, 0, 2, 0, 1, 0, 100, 0, 100, 100, 3000, 3000, 0, 49, 0, 0, 0, 0, 0, 0, 21, 10, 0, 0, 0, 0, 0, 0, 0, 'Scarlet Enchanter - Out of Combat - Start Attacking');

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
