-- DB update 2020_09_10_01 -> 2020_09_10_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_09_10_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_09_10_01 2020_09_10_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1592650543169399600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1592650543169399600');

UPDATE `smart_scripts` SET `link` = 0 WHERE `entryorguid` IN (16398,8338,178908,32347,24077) AND `id` = 0;
UPDATE `smart_scripts` SET `link` = 0 WHERE `entryorguid` = 30895 AND `id` = 1;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
