-- DB update 2021_10_28_00 -> 2021_10_28_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_28_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_28_00 2021_10_28_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1625408455420649354'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1625408455420649354');

-- <PERSON><PERSON>us the Amiable
SET @ARTORIUS_WPID := 4230100;

INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@ARTORIUS_WPID,1,7909.71,-4598.67,710.008,0,0,0,0,100,0),
(@ARTORIUS_WPID,2,7934.25,-4580.27,712.914,0,0,0,0,100,0),
(@ARTORIUS_WPID,3,7951.73,-4563.34,715.987,0,0,0,0,100,0),
(@ARTORIUS_WPID,4,7959.83,-4516.89,706.184,0,0,0,0,100,0),
(@ARTORIUS_WPID,5,7951.14,-4478.79,712.931,0,0,0,0,100,0),
(@ARTORIUS_WPID,6,7926.2,-4464.49,710.084,0,0,0,0,100,0),
(@ARTORIUS_WPID,7,7908.85,-4439.49,705.869,0,0,0,0,100,0),
(@ARTORIUS_WPID,8,7893.66,-4403.93,703.423,0,0,0,0,100,0),
(@ARTORIUS_WPID,9,7888.36,-4391.52,701.7,0,0,0,0,100,0),
(@ARTORIUS_WPID,10,7881.92,-4376.5,698.441,0,0,0,0,100,0),
(@ARTORIUS_WPID,11,7873.08,-4371.23,698.733,0,0,0,0,100,0),
(@ARTORIUS_WPID,12,7850.84,-4359.52,699.886,0,0,0,0,100,0),
(@ARTORIUS_WPID,13,7831.77,-4378.83,690.036,0,0,0,0,100,0),
(@ARTORIUS_WPID,14,7830.91,-4405.68,680.437,0,0,0,0,100,0),
(@ARTORIUS_WPID,15,7836.71,-4419.45,676.768,0,0,0,0,100,0),
(@ARTORIUS_WPID,16,7818.71,-4440.08,668.523,0,0,0,0,100,0),
(@ARTORIUS_WPID,17,7718.32,-4463.39,639.431,0,0,0,0,100,0),
(@ARTORIUS_WPID,18,7687.59,-4464.45,632.542,0,0,0,0,100,0),
(@ARTORIUS_WPID,19,7628.2,-4459.5,617.491,0,0,0,0,100,0),
(@ARTORIUS_WPID,20,7614.45,-4457.25,613.87,0,0,0,0,100,0),
(@ARTORIUS_WPID,21,7589.69,-4449.63,608.208,0,0,0,0,100,0),
(@ARTORIUS_WPID,22,7559.67,-4464.89,605.417,0,0,0,0,100,0),
(@ARTORIUS_WPID,23,7562.68,-4511.61,607.263,0,0,0,0,100,0),
(@ARTORIUS_WPID,24,7600.13,-4535.22,625.163,0,0,0,0,100,0),
(@ARTORIUS_WPID,25,7607.41,-4567.44,634.095,0,0,0,0,100,0),
(@ARTORIUS_WPID,26,7632.49,-4565.44,642.315,0,0,0,0,100,0),
(@ARTORIUS_WPID,27,7641.09,-4570.59,647.803,0,0,0,0,100,0),
(@ARTORIUS_WPID,28,7651.98,-4575.47,656.282,0,0,0,0,100,0),
(@ARTORIUS_WPID,29,7654.18,-4576.26,658.338,0,0,0,0,100,0),
(@ARTORIUS_WPID,30,7668.75,-4585.71,668.173,0,0,0,0,100,0),
(@ARTORIUS_WPID,31,7683.88,-4603.07,677.795,0,0,0,0,100,0),
(@ARTORIUS_WPID,32,7702.21,-4622.01,696.909,0,0,0,0,100,0),
(@ARTORIUS_WPID,33,7711.36,-4625.5,701.57,0,0,0,0,100,0),
(@ARTORIUS_WPID,34,7731.22,-4639.03,705.861,0,0,0,0,100,0),
(@ARTORIUS_WPID,35,7733.53,-4640.61,706.981,0,0,0,0,100,0),
(@ARTORIUS_WPID,36,7735.64,-4641.46,707.987,0,0,0,0,100,0),
(@ARTORIUS_WPID,37,7750.55,-4639.44,708.368,0,0,0,0,100,0),
(@ARTORIUS_WPID,38,7767.45,-4640.24,706.967,0,0,0,0,100,0),
(@ARTORIUS_WPID,39,7809.5,-4629.89,703.353,0,0,0,0,100,0),
(@ARTORIUS_WPID,40,7834.76,-4631.76,709.151,0,0,0,0,100,0),
(@ARTORIUS_WPID,41,7848.44,-4638.85,714.236,0,0,0,0,100,0),
(@ARTORIUS_WPID,42,7860.56,-4635.37,714.247,0,0,0,0,100,0),
(@ARTORIUS_WPID,43,7869.19,-4632.91,714.093,0,0,0,0,100,0),
(@ARTORIUS_WPID,44,7907.84,-4600.24,709.876,0,0,0,0,100,0);

SET @ARTORIUS_ENTRY := 14531;
SET @ARTORIUS_GUID := 42301;

-- Set MovementType and path_id
UPDATE `creature` SET `MovementType` = 2 WHERE (`id` = @ARTORIUS_ENTRY) AND (`guid` = @ARTORIUS_GUID);
UPDATE `creature_addon` SET `path_id` = @ARTORIUS_WPID WHERE (`guid` = @ARTORIUS_GUID);

-- Allow player interaction, remove the quest item from the humanoid form, set Movement Type and change the Script Name
UPDATE `creature_template`
SET `npcflag` = 1,
    `unit_flags2` = 18432,
    `lootid` = 0,
    `MovementType` = 2,
    `ScriptName` = 'npc_artorius'
WHERE (`entry` = @ARTORIUS_ENTRY);

-- Delete the unused loot_template
DELETE FROM `creature_loot_template` WHERE `Entry` = @ARTORIUS_ENTRY;

-- Add creature text for gossip option
INSERT INTO `creature_text` (`CreatureID`, `GroupID`, `ID`, `Text`, `Type`, `Language`, `Probability`, `Emote`, `Duration`, `Sound`, `BroadcastTextId`, `comment`) VALUES
(@ARTORIUS_ENTRY, 0, 0, "I know you as Artorius the Doombringer. Show yourself, demon! Face me!", 0, 0, 0, 0, 0, 0, 9751, "Artorius the Amiable");

-- Correct Artorius evil entry speed_walk
UPDATE `creature_template` SET `speed_walk` = 1 WHERE (`entry` = 14535);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_28_01' WHERE sql_rev = '1625408455420649354';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
