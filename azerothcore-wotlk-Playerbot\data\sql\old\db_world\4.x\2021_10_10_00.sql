-- DB update 2021_10_09_05 -> 2021_10_10_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_09_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_09_05 2021_10_10_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1632592963863001500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1632592963863001500');

DELETE FROM `gameobject` WHERE `guid` IN
(7027,10128,6989,10046,11802,10090,10125,11223,10050,11595,10068,11611,4558,7033,4544,10962,10960,11329,10979,11143,11231,10166,2499);


DELETE FROM `gameobject` WHERE `guid` IN
(10139,9914,11582,31641,11551,31645,11555,10952,11783,4589,11614,11920,10078,31672,31671,10094,11207,31668,11239,1772,4560,31669,265429);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_00' WHERE sql_rev = '1632592963863001500';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
