-- DB update 2021_10_09_04 -> 2021_10_09_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_09_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_09_04 2021_10_09_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1631550138111515600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1631550138111515600');

UPDATE `creature_template` SET `InhabitType`=3 WHERE `entry` IN (28859,31734,33186,33724,36853,38265,38266,38267,37534,38219,37533,38220);
UPDATE `creature_template_addon` SET `bytes1`=0 WHERE `entry` IN (28859,31734,33186,33724,36853,38265,38266,38267,37534,38219,37533,38220);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_09_05' WHERE sql_rev = '1631550138111515600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
