-- DB update 2021_06_02_12 -> 2021_06_03_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_02_12';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_02_12 2021_06_03_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1622344663132928321'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1622344663132928321');

DELETE FROM `pool_gameobject` WHERE `pool_entry` = 994 AND `guid` = 1962;
DELETE FROM `gameobject` WHERE `id` = 1619 AND `guid` = 1962;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_03_00' WHERE sql_rev = '1622344663132928321';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
