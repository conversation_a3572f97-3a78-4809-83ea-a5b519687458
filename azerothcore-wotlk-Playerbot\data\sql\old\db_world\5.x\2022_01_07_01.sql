-- DB update 2022_01_07_00 -> 2022_01_07_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_07_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_07_00 2022_01_07_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641506166271466470'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641506166271466470');

-- Westfall

-- Pathing for Protector Weaver Entry: 488 "Missing"
SET @NPC := 89536;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-10503.801,`position_y`=1032.6725,`position_z`=95.61251 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-10503.801,1032.6725,95.61251,0,0,0,0,100,0),
(@PATH,2,-10498.092,1032.9823,95.613396,0,0,0,0,100,0),
(@PATH,3,-10494.097,1028.5104,95.61251,0,0,0,0,100,0),
(@PATH,4,-10493.688,1023.1009,95.61252,0,0,0,0,100,0),
(@PATH,5,-10497.403,1019.0657,95.61252,0,0,0,0,100,0),
(@PATH,6,-10503.215,1018.5884,95.61251,0,0,0,0,100,0),
(@PATH,7,-10506.956,1021.8047,95.61251,0,0,0,0,100,0),
(@PATH,8,-10508.083,1027.1096,95.61251,0,0,0,0,100,0);

-- Pathing for Protector Dutfield Entry: 489 "incorrect"
SET @NPC := 89534;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-10521.006,`position_y`=1024.1843,`position_z`=60.478405 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-10521.006,1024.1843,60.478405,0,0,0,0,100,0),
(@PATH,2,-10511.894,1008.2119,60.420105,0,0,0,0,100,0),
(@PATH,3,-10503.619,1006.0582,60.528526,0,0,0,0,100,0),
(@PATH,4,-10493.653,1008.3578,60.45514,0,0,0,0,100,0),
(@PATH,5,-10484.865,1014.4223,60.39485,0,0,0,0,100,0),
(@PATH,6,-10481.817,1029.5293,60.492565,0,0,0,0,100,0),
(@PATH,7,-10488.063,1041.1373,60.44566,0,0,0,0,100,0),
(@PATH,8,-10499.225,1045.555,60.52413,0,0,0,0,100,0),
(@PATH,9,-10516.6,1039.7664,60.517998,0,0,0,0,100,0);

-- Pathing for Protector Dorana Entry: 869 "Wrong starting point"
SET @NPC := 89532;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-10647.462,`position_y`=1012.3682,`position_z`=31.776527 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-10647.462,1012.3682,31.776527,0,0,0,0,100,0),
(@PATH,2,-10642.797,1016.6653,31.565102,0,0,0,0,100,0),
(@PATH,3,-10620.195,1000.852,32.99437,0,0,0,0,100,0),
(@PATH,4,-10593.989,987.9368,36.823742,0,0,0,0,100,0),
(@PATH,5,-10564.63,975.733,40.634453,0,0,0,0,100,0),
(@PATH,6,-10532.884,961.9837,40.871662,0,0,0,0,100,0),
(@PATH,7,-10500.205,952.63947,39.446247,0,0,0,0,100,0),
(@PATH,8,-10466.905,953.81274,35.898197,0,0,0,0,100,0),
(@PATH,9,-10433.159,981.96454,34.757523,0,0,0,0,100,0),
(@PATH,10,-10410.507,994.7161,32.16426,0,0,0,0,100,0),
(@PATH,11,-10400.419,992.54285,31.579058,0,0,0,0,100,0),
(@PATH,12,-10386.812,984.0738,31.147923,0,0,0,0,100,0),
(@PATH,13,-10367.392,980.61816,31.147923,0,0,0,0,100,0),
(@PATH,14,-10333.47,978.7831,31.254934,0,0,0,0,100,0),
(@PATH,15,-10300.245,965.63696,31.253038,0,0,0,0,100,0),
(@PATH,16,-10285.638,964.0901,31.185616,0,0,0,0,100,0),
(@PATH,17,-10273.856,970.6926,31.185616,0,0,0,0,100,0),
(@PATH,18,-10267.595,983.3521,31.310616,0,0,0,0,100,0),
(@PATH,19,-10260.887,989.9033,31.377512,0,0,0,0,100,0),
(@PATH,20,-10233.981,996.2642,31.49177,0,0,0,0,100,0),
(@PATH,21,-10215.859,994.50165,32.503136,0,0,0,0,100,0),
(@PATH,22,-10200.06,989.352,32.93331,0,0,0,0,100,0),
(@PATH,23,-10190.039,984.90027,33.7804,0,0,0,0,100,0),
(@PATH,24,-10166.767,986.7172,34.05408,0,0,0,0,100,0),
(@PATH,25,-10133.551,990.6491,36.048573,0,0,0,0,100,0),
(@PATH,26,-10116.651,990.1268,38.219654,0,0,0,0,100,0),
(@PATH,27,-10100.183,999.19073,37.460865,0,0,0,0,100,0),
(@PATH,28,-10116.651,990.1268,38.219654,0,0,0,0,100,0),
(@PATH,29,-10133.551,990.6491,36.048573,0,0,0,0,100,0),
(@PATH,30,-10166.767,986.7172,34.05408,0,0,0,0,100,0),
(@PATH,31,-10190.039,984.90027,33.7804,0,0,0,0,100,0),
(@PATH,32,-10200.06,989.352,32.93331,0,0,0,0,100,0),
(@PATH,33,-10215.859,994.50165,32.503136,0,0,0,0,100,0),
(@PATH,34,-10233.981,996.2642,31.49177,0,0,0,0,100,0),
(@PATH,35,-10260.887,989.9033,31.377512,0,0,0,0,100,0),
(@PATH,36,-10267.595,983.3521,31.310616,0,0,0,0,100,0),
(@PATH,37,-10273.856,970.6926,31.185616,0,0,0,0,100,0),
(@PATH,38,-10285.638,964.0901,31.185616,0,0,0,0,100,0),
(@PATH,39,-10300.245,965.63696,31.253038,0,0,0,0,100,0),
(@PATH,40,-10333.47,978.7831,31.254934,0,0,0,0,100,0),
(@PATH,41,-10367.392,980.61816,31.147923,0,0,0,0,100,0),
(@PATH,42,-10386.812,984.0738,31.147923,0,0,0,0,100,0),
(@PATH,43,-10400.419,992.54285,31.579058,0,0,0,0,100,0),
(@PATH,44,-10410.507,994.7161,32.16426,0,0,0,0,100,0),
(@PATH,45,-10433.159,981.96454,34.757523,0,0,0,0,100,0),
(@PATH,46,-10466.905,953.81274,35.898197,0,0,0,0,100,0),
(@PATH,47,-10500.205,952.63947,39.446247,0,0,0,0,100,0),
(@PATH,48,-10532.884,961.9837,40.871662,0,0,0,0,100,0),
(@PATH,49,-10564.63,975.733,40.634453,0,0,0,0,100,0),
(@PATH,50,-10593.989,987.9368,36.823742,0,0,0,0,100,0),
(@PATH,51,-10620.195,1000.852,32.99437,0,0,0,0,100,0),
(@PATH,52,-10642.797,1016.6653,31.565102,0,0,0,0,100,0);

-- Pathing for Protector Korelor Entry: 874 "incorrect"
SET @NPC := 89538;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-10634,`position_y`=1085.8853,`position_z`=33.800117 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-10634,1085.8853,33.800117,0,0,0,0,100,0),
(@PATH,2,-10634.184,1072.2194,33.925117,0,0,0,0,100,0),
(@PATH,3,-10640.825,1056.6143,33.648426,0,0,0,0,100,0),
(@PATH,4,-10655.064,1038.7388,33.542225,0,0,0,0,100,0),
(@PATH,5,-10663.531,1028.5533,32.6901,0,0,0,0,100,0),
(@PATH,6,-10699.386,1033.9723,32.90135,0,0,0,0,100,0),
(@PATH,7,-10720.374,1032.955,33.49703,0,0,0,0,100,0),
(@PATH,8,-10733.596,1026.2675,33.289467,0,0,0,0,100,0),
(@PATH,9,-10749,1018.6014,33.289467,0,0,0,0,100,0),
(@PATH,10,-10767.36,1017.565,32.289467,0,0,0,0,100,0),
(@PATH,11,-10800.84,1018.1844,32.19967,0,0,0,0,100,0),
(@PATH,12,-10833.497,1008.6335,32.798805,0,0,0,0,100,0),
(@PATH,13,-10867.013,1006.1756,31.83058,0,0,0,0,100,0),
(@PATH,14,-10899.607,999.10254,35.619026,0,0,0,0,100,0),
(@PATH,15,-10909.731,983.89575,37.800312,0,0,0,0,100,0),
(@PATH,16,-10942.452,967.619,32.45774,0,0,0,0,100,0),
(@PATH,17,-10949.246,940.71497,31.550056,0,0,0,0,100,0),
(@PATH,18,-10946.804,934.30316,31.550056,0,0,0,0,100,0),
(@PATH,19,-10914.473,901.58386,32.283516,0,0,0,0,100,0),
(@PATH,20,-10916.354,868.85645,32.455727,0,0,0,0,100,0),
(@PATH,21,-10926.304,852.3051,32.84298,0,0,0,0,100,0),
(@PATH,22,-10929.212,832.72614,32.29058,0,0,0,0,100,0),
(@PATH,23,-10919.833,800.1399,30.847464,0,0,0,0,100,0),
(@PATH,24,-10900.73,777.5663,30.75943,0,0,0,0,100,0),
(@PATH,25,-10880.348,749.30206,31.097464,0,0,0,0,100,0),
(@PATH,26,-10877.123,732.9159,31.1069,0,0,0,0,100,0),
(@PATH,27,-10871.73,699.81946,30.988356,0,0,0,0,100,0),
(@PATH,28,-10866.871,665.3965,31.031797,0,0,0,0,100,0),
(@PATH,29,-10871.73,699.81946,30.988356,0,0,0,0,100,0),
(@PATH,30,-10877.123,732.9159,31.1069,0,0,0,0,100,0),
(@PATH,31,-10880.348,749.30206,31.097464,0,0,0,0,100,0),
(@PATH,32,-10900.73,777.5663,30.75943,0,0,0,0,100,0),
(@PATH,33,-10919.833,800.1399,30.847464,0,0,0,0,100,0),
(@PATH,34,-10929.212,832.72614,32.29058,0,0,0,0,100,0),
(@PATH,35,-10926.304,852.3051,32.84298,0,0,0,0,100,0),
(@PATH,36,-10916.354,868.85645,32.455727,0,0,0,0,100,0),
(@PATH,37,-10914.473,901.58386,32.283516,0,0,0,0,100,0),
(@PATH,38,-10946.804,934.30316,31.550056,0,0,0,0,100,0),
(@PATH,39,-10949.246,940.71497,31.550056,0,0,0,0,100,0),
(@PATH,40,-10942.452,967.619,32.45774,0,0,0,0,100,0),
(@PATH,41,-10909.731,983.89575,37.800312,0,0,0,0,100,0),
(@PATH,42,-10899.607,999.10254,35.619026,0,0,0,0,100,0),
(@PATH,43,-10867.013,1006.1756,31.83058,0,0,0,0,100,0),
(@PATH,44,-10833.497,1008.6335,32.798805,0,0,0,0,100,0),
(@PATH,45,-10800.84,1018.1844,32.19967,0,0,0,0,100,0),
(@PATH,46,-10767.36,1017.565,32.289467,0,0,0,0,100,0),
(@PATH,47,-10749,1018.6014,33.289467,0,0,0,0,100,0),
(@PATH,48,-10733.596,1026.2675,33.289467,0,0,0,0,100,0),
(@PATH,49,-10720.374,1032.955,33.49703,0,0,0,0,100,0),
(@PATH,50,-10699.414,1033.9805,32.91258,0,0,0,0,100,0),
(@PATH,51,-10663.531,1028.5533,32.6901,0,0,0,0,100,0),
(@PATH,52,-10655.064,1038.7388,33.542225,0,0,0,0,100,0),
(@PATH,53,-10640.825,1056.6143,33.648426,0,0,0,0,100,0),
(@PATH,54,-10634.184,1072.2194,33.925117,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_07_01' WHERE sql_rev = '1641506166271466470';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
