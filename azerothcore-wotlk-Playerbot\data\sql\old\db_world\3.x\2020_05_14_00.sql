-- DB update 2020_05_13_00 -> 2020_05_14_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_05_13_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_05_13_00 2020_05_14_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1586466641269881700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1586466641269881700');

DELETE FROM `quest_template_addon` WHERE `id` IN (6804,6821,6822,6823,6824,7486);
INSERT INTO `quest_template_addon` (`id`, `ProvidedItemCount`) VALUES
(6804, 1);

UPDATE `quest_template_addon` SET `NextQuestID` = 0, `ExclusiveGroup` = 0 WHERE `id` = 6805;

DELETE FROM `creature_queststarter` WHERE `quest` IN (6804,6821,6822,6823,6824,7486);
DELETE FROM `creature_questender` WHERE `quest` IN (6804,6821,6822,6823,6824,7486);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
