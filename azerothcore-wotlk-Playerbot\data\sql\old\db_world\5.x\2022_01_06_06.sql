-- DB update 2022_01_06_05 -> 2022_01_06_06
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_06_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_06_05 2022_01_06_06 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1640706977785890600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1640706977785890600');

-- Setting probability to 25% for each of the texts. 5*25 = 100%
SET @PROBABILITY = 25;
UPDATE `npc_text` SET `Probability0` = @PROBABILITY, `Probability1` = @PROBABILITY, `Probability2` = @PROBABILITY, `Probability3` = @PROBABILITY, `Probability4` = @PROBABILITY WHERE `ID` IN (778);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_06_06' WHERE sql_rev = '1640706977785890600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
