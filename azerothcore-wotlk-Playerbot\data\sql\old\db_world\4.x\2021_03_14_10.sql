-- DB update 2021_03_14_09 -> 2021_03_14_10
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_14_09';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_14_09 2021_03_14_10 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1615645470313391100'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1615645470313391100');

DELETE FROM `gameobject` WHERE (`id` = 1731) AND (`guid` IN (31011));
INSERT INTO `gameobject` VALUES
(31011, 1731, 0, 0, 0, 1, 1, -9893.121094, 1447.154175, 81.898338, -0.767945, 0, 0, 0.374607, -0.927184, 900, 100, 1, '', 0);

DELETE FROM `gameobject` WHERE (`id` = 1731) AND (`guid` IN (120595));
INSERT INTO `gameobject` VALUES
(120595, 1731, 0, 0, 0, 1, 1, -9892.582031, 1420.489136, 40.933098, 1.36136, 0, 0, 0, 1, 3600, 255, 1, '', 0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
