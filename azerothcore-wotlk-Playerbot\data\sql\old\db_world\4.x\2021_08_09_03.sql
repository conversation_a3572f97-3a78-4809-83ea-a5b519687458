-- DB update 2021_08_09_02 -> 2021_08_09_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_09_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_09_02 2021_08_09_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1628101702822210800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1628101702822210800');

-- Removed script where Mosh'Ogg <PERSON>rute (1142) used Trash. Because it was the only entry it was changed to AggressorAI
UPDATE `creature_template` SET `AIName` = '' WHERE (`entry` = 1142);
DELETE FROM `smart_scripts` WHERE (`entryorguid` = 1142) AND (`source_type` = 0) AND (`id` IN (0));


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_09_03' WHERE sql_rev = '1628101702822210800';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
