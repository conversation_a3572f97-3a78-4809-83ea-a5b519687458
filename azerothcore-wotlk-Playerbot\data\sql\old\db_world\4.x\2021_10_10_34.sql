-- DB update 2021_10_10_33 -> 2021_10_10_34
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_33';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_33 2021_10_10_34 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633779726136527772'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633779726136527772');

-- Deletes invalid Wildvine drops from various NPCs
DELETE FROM `creature_loot_template` WHERE `item` = 8153 AND `entry` NOT IN (1059, 1061, 11355, 11346, 10802, 8217, 1060, 8636, 1062, 7996, 8216, 696, 784, 783, 781, 669, 780, 782, 672, 667, 670, 7995, 2648, 7809, 2639, 2643, 2646, 2641, 2644, 4466, 2645, 2642, 4467, 2647, 4465, 2640, 17235, 2654, 2651, 2653, 2649, 2652, 2650, 8218, 2530, 2605, 2534, 1490, 8219);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_34' WHERE sql_rev = '1633779726136527772';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
