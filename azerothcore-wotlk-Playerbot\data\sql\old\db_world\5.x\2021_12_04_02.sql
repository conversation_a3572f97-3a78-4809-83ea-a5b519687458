-- DB update 2021_12_04_01 -> 2021_12_04_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_04_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_04_01 2021_12_04_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1638031216146862485'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1638031216146862485');

-- Add missing random movement to Sarkoth
UPDATE `creature` SET `wander_distance`=25, `MovementType`=1 WHERE `guid`=12263;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_04_02' WHERE sql_rev = '1638031216146862485';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
