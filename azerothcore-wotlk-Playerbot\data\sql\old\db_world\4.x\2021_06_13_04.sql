-- DB update 2021_06_13_03 -> 2021_06_13_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_13_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_13_03 2021_06_13_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1623095488531317400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1623095488531317400');

SET @SCALEBANE = 4328;
SET @SCOUT = 4329;
SET @ASHTAIL = 4331;
SET @FLAMECALLER = 4334;

UPDATE `skinning_loot_template` 
SET `GroupId` = 1 
WHERE `entry` IN (@SCALEBANE, @SCOUT, @ASHTAIL, @FLAMECALLER);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_13_04' WHERE sql_rev = '1623095488531317400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
