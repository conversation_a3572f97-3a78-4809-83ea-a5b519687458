-- DB update 2021_08_12_01 -> 2021_08_13_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_12_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_12_01 2021_08_13_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1628691033880289424'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1628691033880289424');

-- <PERSON>moves skinning loot from various lvl 1 pets, critters and items
UPDATE `creature_template` SET `skinloot` = 0 WHERE `entry` IN (1419, 2230, 4781, 6728, 7507, 7508, 7509, 7557, 8662, 10116, 10577, 10657, 12202, 14453, 14646, 14869);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_13_00' WHERE sql_rev = '1628691033880289424';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
