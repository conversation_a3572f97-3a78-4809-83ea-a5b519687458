-- DB update 2021_03_04_01 -> 2021_03_05_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_03_04_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_03_04_01 2021_03_05_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1614920824592458800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1614920824592458800');

DELETE FROM creature_loot_template WHERE Entry IN 
(235, 240, 246, 255, 341, 358, 2055, 2436, 2870, 3388, 3659, 3990, 4033, 5433, 5440, 6013, 6019, 6047, 6086, 6087, 6141, 6492, 7395, 8397, 10388, 10389, 10684, 10698, 10951, 11669, 11718, 12099, 12426, 15387, 15623);

UPDATE `creature_template` SET `lootid`= 0 WHERE `entry` IN 
(235, 240, 246, 255, 341, 358, 2055, 2436, 2870, 3388, 3659, 3990, 4033, 5433, 5440, 6013, 6019, 6047, 6086, 6087, 6141, 6492, 7395, 8397, 10388, 10389, 10684, 10698, 10951, 11669, 11718, 12099, 12426, 15387, 15623);


--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
