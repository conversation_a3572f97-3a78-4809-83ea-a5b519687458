-- DB update 2022_04_24_00 -> 2022_04_24_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_04_24_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_04_24_00 2022_04_24_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1650762150293897700'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1650762150293897700');

SET @NPC := 14020;
SET @PATH := @NPC * 10;
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-7488.41,-1074.58,476.54404,0.197437033057212829,0,0,0,100,0),
(@PATH,2,-7488.41,-1074.58,476.54404,5.272660255432128906,0,0,0,100,0),
(@PATH,3,-7488.41,-1074.58,476.54404,0,0,0,0,100,0),
(@PATH,4,-7488.41,-1074.58,476.54404,0.46780097484588623,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_04_24_01' WHERE sql_rev = '1650762150293897700';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
