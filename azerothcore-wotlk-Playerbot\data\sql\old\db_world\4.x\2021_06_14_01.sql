-- DB update 2021_06_14_00 -> 2021_06_14_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_14_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_14_00 2021_06_14_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1623093110337112400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1623093110337112400');

SET @NPC = 30504;

UPDATE `creature` 
SET `position_x` = -4960.708496, `position_y` = -3828.589600, `position_z` = 43.382378, `wander_distance` = 5, `MovementType` = 1 
WHERE `guid` = @NPC;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_14_01' WHERE sql_rev = '1623093110337112400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
