-- DB update 2021_06_17_05 -> 2021_06_17_06
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_17_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_17_05 2021_06_17_06 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1623196877899764500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1623196877899764500');

-- Update unreachable copper vein
UPDATE `gameobject`
SET `position_x`=-9144, `position_y`=-2078, `position_z`=125, `orientation`=3.369
WHERE `guid`=5149 AND `id`=1731;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_17_06' WHERE sql_rev = '1623196877899764500';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
