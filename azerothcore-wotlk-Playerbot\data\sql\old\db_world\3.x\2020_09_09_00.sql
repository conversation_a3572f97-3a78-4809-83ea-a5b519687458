-- DB update 2020_09_07_01 -> 2020_09_09_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_09_07_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_09_07_01 2020_09_09_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1598880766137253600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1598880766137253600');
/*
 * General: Build Update
 * Update by Knindza | <www.azerothcore.org>
 * Copyright (C) <www.shadowburn.net> & <www.lichbane.com>
*/

/* Content 3.3.0 */ 
SET @Build := 11159;

UPDATE `creature_template` SET `VerifiedBuild` = @Build WHERE `entry` IN (30280, 34038, 34436, 34364, 36213, 36217, 36224, 36225, 36226, 36273, 36476, 36477, 36478, 36494, 36497, 36499, 36502, 36508, 36516, 36517, 36522, 36535, 36536, 36551, 36564, 36595, 36610, 36612, 36619, 36620, 36624, 36642, 36656, 36657, 36658, 36661, 36666, 36669, 36670, 36672, 36723, 36724, 36725, 36731, 36764, 36765, 36766, 36767, 36770, 36771, 36772, 36773, 36774, 36776, 36788, 36794, 36796, 36805, 36807, 36808, 36811, 36829, 36830, 36839, 36840, 36841, 36842, 36844, 36847, 36851, 36855, 36856, 36871, 36874, 36877, 36879, 36881, 36886, 36888, 36889, 36891, 36892, 36893, 36896, 36907, 36913, 36939, 36940, 36941, 36944, 36945, 36946, 36947, 36948, 36950, 36954, 36961, 36967, 36969, 36971, 36978, 36983, 36990, 36998, 37003, 37004, 37007, 37011, 37012, 37014, 37016, 37017, 37021, 37026, 37027, 37029, 37030, 37031, 37032, 37033, 37035, 37068, 37069, 37107, 37116, 37119, 37120, 37144, 37146, 37148, 37149, 37183, 37184, 37187, 37189, 37215, 37223, 37225, 37226, 37227, 37230, 37488, 37519, 37528, 37540, 37544, 37545, 37546, 37547, 37554, 37577, 37578, 37579, 37581, 37583, 37584, 37586, 37587, 37588, 37589, 37592, 37593, 37596, 37670, 37687, 37688, 37689, 37696, 37711, 37712, 37713, 37728, 37729, 37742, 37755, 37776, 37779, 37780, 37813, 37833, 37852, 37865, 37890, 37906, 37920, 37928, 37941, 37942, 37949, 37965, 37967, 37991, 37992, 37993, 37994, 37996, 38009, 38010, 38017, 38028, 38054, 38112, 38113, 38135, 38136, 38161, 38172, 38173, 38175, 38176, 38177, 38181, 38189, 38194, 38200, 38222, 38284, 38316, 38391, 38453, 38487, 38493, 38500, 38505, 38508, 38567, 38841, 38858, 36626, 36627, 36678, 36880, 36897, 36899, 36916, 37006, 37022, 37023, 37025, 37038, 37098, 37217, 37562, 37571, 37595, 37662, 37663, 37664, 37665, 37666, 37672, 37690, 37697, 37744, 37782, 37901, 37930, 37935, 37936, 37955, 37968, 37970, 37972, 37973, 37986, 38104, 38107, 38159, 38201, 38228, 38229, 38231, 38232, 38234, 38294, 38319, 38332, 38369, 38410, 38422, 38451, 38454, 38458, 38763, 38867, 36955, 36993, 37221, 37496, 37497, 37498, 37572, 37575, 37576, 37580, 37582, 37591, 37597, 37774, 38160, 38188, 37158, 37915, 37888, 38751, 36511, 37846, 36838, 37094, 37190, 37200, 37997, 37998, 37999, 38182, 38283, 38711, 38712, 38840, 38163, 36957, 36960, 36968, 36970, 36982, 37034, 37117, 37182, 37188, 37830, 37879, 37902, 37903, 37904, 36272, 36296, 36565, 36568, 36789, 36791, 36812, 36885, 37501, 37502, 37503, 37533, 37534, 37674, 37863, 37868, 37886, 37887, 37907, 37918, 37934, 37945, 37985, 38041, 38068, 38208, 38293, 38325, 38334, 38335, 38336, 38374, 38433, 38439, 38456, 38461, 38482, 38492, 38494, 38752, 39172, 39509, 37122, 37123, 37124, 37125, 37126, 37127, 37129, 37132, 37133, 37134, 37491, 37495, 38125, 38154, 38184, 38551, 38557, 38558, 39173, 37715, 38032, 37232, 37493, 37494, 38370, 38490, 38039, 38040, 38501, 38193, 37720, 37550, 37551, 37549, 38599, 38603, 38524, 38525, 38563, 38544, 38564, 38568, 37564, 36617, 37565, 37627, 37629, 37613, 36938, 38220, 37645, 37646, 37647, 37648, 37649, 37650, 37651, 37652, 37609, 37638, 37644, 37612, 37637, 37640, 37635, 37656, 37622, 37626, 37641, 37642, 37636, 37639, 37618, 37567, 37677, 37568, 36498, 37607, 38249, 38025, 38026, 37731, 37730, 37569, 37614, 37606, 37566, 37563, 37608, 37624, 37625, 37623, 37797, 37604, 37603, 37605, 37628, 37809, 38267, 38233, 38057, 38058, 38075, 38073, 38076, 38072, 38074, 38077, 39168, 38157, 38406, 38261, 38408, 38407, 38082, 38086, 38079, 38031, 38061, 38059, 38081, 38078, 38085, 38083, 38080, 38092, 38091, 38090, 38087, 38088, 38096, 38256, 38097, 38093, 38084, 38089, 38638, 38444, 38128, 38481, 38445, 38446, 38402, 38393, 38394, 38596, 38398, 38396, 38586, 38550, 37506, 37655, 38138, 38123, 38108, 38062, 38064, 38063, 38418, 38103, 38602, 38098, 38100, 38102, 38480, 38099, 38101, 38479, 38604, 38312, 38600, 38297, 37959, 38399, 38400, 38105, 38419, 38775, 38395, 38404, 38262, 38403, 38405, 38095, 38257, 38174, 38169, 38197, 38198, 38219, 38171, 38167, 38166, 38168, 38170, 38397, 38298, 38299, 38303, 38304, 38258, 38126, 38000, 38132, 38133, 38131, 38185, 38204, 37882, 38362, 38266, 38459, 39167, 38639, 38685, 38691, 38689, 38687, 38693, 35362, 38637, 38699, 38582, 38628, 38625, 38597, 38633, 39001, 38585, 38549, 37505, 38760, 38758, 38296, 37958, 38769, 38771, 38776, 38631, 38679, 38683, 38675, 38681, 38677, 38722, 38727, 38725, 38724, 38726, 38723, 40533, 36504, 38635, 40165, 38265, 38460, 39166, 38640, 38692, 38690, 38688, 38694, 38156, 38700, 38583, 38629, 38626, 38598, 38632, 39000, 38431, 38390, 37504, 38761, 38759, 38106, 37957, 38770, 38772, 35588, 38777, 38630, 38680, 38684, 38676, 38682, 38678, 38721, 38737, 38735, 38734, 38736, 38733, 39137, 36503, 38634, 37172, 37917, 38016, 38023, 38042, 38044, 38045, 38295, 38471, 37214, 38030, 36597, 38485, 38472, 38006, 37984, 37671, 36908, 36909, 38338, 38337, 38491, 37966, 38339, 38328, 38248, 40295, 40281, 39372, 36609, 36633, 36701, 37695, 37698, 37799, 38757, 38995, 39371, 37228, 37229, 37531, 37532, 38199, 38421, 38429, 38430, 38548, 38556, 40160, 40413, 40405, 39048, 36853, 36980, 37186, 38223, 40703, 35876, 35878, 38589, 38191, 40725, 36823, 36824, 38285, 38392, 38579, 38584, 38667, 38462, 38483, 40246, 38434, 38435, 38436, 38401, 38784, 38785, 40198, 40436, 40443, 40446, 37919, 36991, 37205, 37211, 37509, 37510, 37512, 37523, 37527, 37538, 37539, 37541, 37542, 37552, 37707, 37745, 37763, 37764, 37765, 37781, 37881, 38047, 38048, 38052, 38056, 37826, 37857, 37893, 37976, 40625, 40001, 37657, 37721, 37937, 38043, 38049, 38129, 38130, 38134, 38137, 38139, 38151, 38320, 38321, 38322, 38349, 38350, 38351, 38352, 38486, 38495, 38496, 38529, 38552, 38590, 38601, 38605, 38606, 38607, 38608, 38609, 38610, 38686, 38695, 38701, 38702, 38717, 38778, 38786, 38787, 38788, 38789, 38790, 38970, 38971, 38972, 38973, 38974, 38975, 39021, 39046, 39120, 39121, 39122, 39189, 39190, 39217, 39284, 39285, 39286, 39287, 39288, 39289, 39296, 39299, 39300, 39301, 39302, 39303, 39304, 39305, 39306, 39307, 39309, 39310, 39311, 40104, 40404, 40670, 40671, 40672);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
