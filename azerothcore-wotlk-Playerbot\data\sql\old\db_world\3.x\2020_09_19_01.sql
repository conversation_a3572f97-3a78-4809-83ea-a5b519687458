-- DB update 2020_09_19_00 -> 2020_09_19_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_09_19_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_09_19_00 2020_09_19_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1598878893000939000'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1598878893000939000');
/*
 * General: Build Update
 * Update by Knindza | <www.azerothcore.org>
 * Copyright (C) <www.shadowburn.net> & <www.lichbane.com>
*/

/* Content 3.1.x */ 
SET @Build := 9767;

UPDATE `item_template` SET `VerifiedBuild` = @Build WHERE `entry` IN (46017, 45132, 45133, 45134, 45135, 45136, 45241, 45242, 45243, 45244, 45245, 45442, 45443, 45444, 45445, 45446, 45457, 45459, 45460, 45461, 45462, 45470, 45471, 45472, 45473, 45474, 45484, 45485, 45486, 45487, 45488, 45494, 45495, 45496, 45497, 45516, 45517, 45518, 45519, 45520, 45533, 45534, 45535, 45536, 45537, 45570, 45587, 45588, 45594, 45599, 45607, 45608, 45609, 45610, 45611, 45612, 45613, 45614, 45615, 45616, 45617, 45618, 45619, 45620, 45663, 45665, 45937, 45938, 45939, 45948, 45949, 45950, 45951, 45952, 45953, 45954, 45955, 45956, 45957, 45958, 45959, 45960, 45961, 45962, 45963, 45964, 45965, 45966, 45967, 45968, 45969, 45970, 45971, 40787, 40788, 40789, 40807, 40808, 40809, 40826, 40827, 40828, 40847, 40848, 40849, 40866, 40868, 40869, 40907, 40927, 40933, 40939, 40963, 40992, 40993, 41001, 41007, 41013, 41019, 41027, 41033, 41038, 41044, 41081, 41087, 41137, 41143, 41151, 41157, 41199, 41205, 41211, 41217, 41275, 41281, 41287, 41293, 41298, 41304, 41310, 41316, 41321, 41327, 41650, 41655, 41661, 41667, 41672, 41678, 41683, 41715, 41767, 41773, 41854, 41859, 41864, 41869, 41874, 41915, 41921, 41927, 41934, 41940, 41946, 41953, 41959, 41965, 41971, 41993, 41998, 42005, 42011, 42017, 42209, 42228, 42233, 42243, 42249, 42256, 42261, 42266, 42271, 42276, 42281, 42286, 42291, 42318, 42323, 42328, 42333, 42347, 42353, 42364, 42385, 42391, 42486, 42491, 42496, 42503, 42514, 42520, 42526, 42532, 42538, 42560, 42565, 42571, 44421, 44422, 45110, 45137, 45142, 45147, 45165, 45170, 45171, 45233, 45234, 45246, 45256, 45257, 45261, 45266, 45294, 45315, 45327, 45437, 45448, 45449, 45463, 45479, 45489, 45498, 45511, 45521, 45527, 45605, 45868, 45870, 45876, 45877, 45886, 45887, 45930, 45947, 45990, 46033, 46035, 46036, 46067, 46097, 42451, 45086, 45296, 40881, 40882, 40889, 40976, 40977, 40983, 41051, 41055, 41060, 41065, 41070, 41075, 41225, 41230, 41235, 41617, 41621, 41625, 41630, 41635, 41640, 41832, 41840, 41881, 41885, 41893, 41898, 41903, 41909, 42034, 42035, 42036, 42037, 42038, 42039, 42040, 42069, 42070, 42071, 42072, 42073, 42074, 42075, 42116, 42117, 42124, 42126, 45106, 45107, 45108, 45109, 45111, 45112, 45113, 45115, 45116, 45117, 45118, 45119, 45138, 45139, 45140, 45141, 45143, 45146, 45148, 45149, 45150, 45151, 45157, 45158, 45161, 45162, 45164, 45166, 45167, 45168, 45185, 45186, 45187, 45193, 45224, 45225, 45226, 45227, 45228, 45232, 45235, 45236, 45237, 45238, 45239, 45240, 45247, 45248, 45249, 45250, 45251, 45252, 45253, 45258, 45259, 45260, 45262, 45263, 45264, 45265, 45267, 45268, 45269, 45271, 45272, 45273, 45274, 45275, 45293, 45295, 45297, 45300, 45319, 45320, 45325, 45326, 45334, 45434, 45435, 45438, 45439, 45440, 45441, 45447, 45450, 45451, 45452, 45453, 45454, 45455, 45456, 45466, 45467, 45468, 45469, 45480, 45481, 45482, 45483, 45490, 45491, 45492, 45493, 45501, 45502, 45503, 45504, 45505, 45507, 45508, 45512, 45513, 45514, 45515, 45522, 45523, 45524, 45525, 45529, 45530, 45531, 45532, 45538, 45539, 45540, 45541, 45542, 45543, 45544, 45547, 45548, 45549, 45550, 45551, 45552, 45553, 45554, 45555, 45556, 45557, 45558, 45559, 45560, 45561, 45562, 45563, 45564, 45565, 45566, 45567, 45819, 45820, 45821, 45822, 45823, 45824, 45825, 45826, 45827, 45828, 45829, 45830, 45831, 45833, 45834, 45835, 45836, 45837, 45838, 45839, 45840, 45841, 45842, 45843, 45844, 45845, 45846, 45847, 45848, 45867, 45869, 45871, 45888, 45928, 45929, 45931, 45933, 45943, 45945, 45946, 45982, 45988, 45989, 45993, 46032, 46034, 46037, 46038, 46039, 46040, 46041, 46042, 46043, 46044, 46045, 46046, 46047, 46048, 46049, 46050, 46051, 46068, 46095, 46096, 46111, 46113, 46115, 46116, 46117, 46118, 46119, 46120, 46121, 46122, 46123, 46124, 46125, 46126, 46127, 46129, 46130, 46132, 46133, 46134, 46135, 46136, 46137, 46139, 46140, 46141, 46142, 46143, 46144, 46145, 46146, 46148, 46149, 46150, 46151, 46152, 46153, 46154, 46155, 46156, 46157, 46158, 46159, 46160, 46161, 46162, 46163, 46164, 46165, 46166, 46167, 46168, 46169, 46170, 46172, 46173, 46174, 46175, 46176, 46177, 46178, 46179, 46180, 46181, 46182, 46183, 46184, 46185, 46186, 46187, 46188, 46189, 46190, 46191, 46192, 46193, 46194, 46195, 46196, 46197, 46198, 46199, 46200, 46201, 46202, 46203, 46204, 46205, 46206, 46207, 46208, 46209, 46210, 46211, 46212, 46312, 46320, 46321, 46322, 46323, 46373, 45282, 45283, 45284, 45285, 45286, 45287, 45288, 45289, 45291, 45292, 45298, 45299, 45301, 45302, 45303, 45304, 45305, 45306, 45307, 45308, 45309, 45310, 45311, 45312, 45313, 45314, 45316, 45317, 45318, 45321, 45322, 45324, 45329, 45330, 45331, 45332, 45333, 45335, 45336, 45337, 45338, 45339, 45340, 45341, 45342, 45343, 45344, 45345, 45346, 45347, 45348, 45349, 45351, 45352, 45353, 45354, 45355, 45356, 45357, 45358, 45359, 45360, 45361, 45362, 45363, 45364, 45365, 45367, 45368, 45369, 45370, 45371, 45372, 45373, 45374, 45375, 45376, 45377, 45378, 45379, 45380, 45381, 45382, 45383, 45384, 45385, 45386, 45387, 45388, 45389, 45390, 45391, 45392, 45393, 45394, 45395, 45396, 45397, 45398, 45399, 45400, 45401, 45402, 45403, 45404, 45405, 45406, 45408, 45409, 45410, 45411, 45412, 45413, 45414, 45415, 45416, 45417, 45418, 45419, 45420, 45421, 45422, 45423, 45424, 45425, 45426, 45427, 45428, 45429, 45430, 45431, 45432, 45433, 45458, 45464, 45675, 45676, 45677, 45679, 45680, 45682, 45685, 45686, 45687, 45694, 45695, 45696, 45697, 45698, 45699, 45700, 45701, 45702, 45703, 45704, 45707, 45708, 45709, 45711, 45712, 45713, 45832, 45864, 45865, 45866, 45872, 45873, 45874, 45892, 45893, 45894, 45895, 45927, 45934, 45935, 45936, 45940, 45941, 45972, 45973, 45974, 45975, 45976, 45996, 45997, 46008, 46009, 46010, 46011, 46012, 46013, 46014, 46015, 46016, 46018, 46019, 46021, 46022, 46024, 46025, 46028, 46030, 46031, 46131, 46313, 46339, 46340, 46341, 46342, 46343, 46344, 46345, 46346, 46347, 46350, 46351, 45688, 45689, 45690, 45691, 46057, 46058, 46059, 46060, 46061, 46062, 46063, 46064, 46065, 46066, 46071, 46072, 46073, 46074, 46075, 46076, 46077, 46078, 46079, 46080, 46081, 46082, 46083, 46084, 46085, 46086, 46087, 46088, 45859, 45074, 45075, 45076, 45077, 45078, 45085, 45128, 45129, 45130, 45203, 45204, 45205, 45208, 45210, 45212, 45214, 45222, 45131, 45152, 45153, 45154, 45155, 45156, 45159, 45160, 45163, 45181, 45182, 45183, 45184, 45206, 45207, 45209, 45211, 45213, 45215, 45216, 45217, 45218, 45219, 45220, 45221, 45223, 45808, 45809, 45810, 45811, 45812, 45813, 45994, 45995, 46324, 45849, 45850, 45851, 45852, 45853, 45854, 45983, 45052, 45631, 45627, 45626, 45858, 45991, 45992, 45998, 45861, 45037, 45574, 45577, 45578, 45579, 45580, 45581, 45582, 45583, 45584, 45585, 45664, 45666, 45667, 45668, 45669, 45670, 45671, 45672, 45673, 45674, 44800, 44802, 44803, 45067, 45073, 46069, 46070, 46106, 45061, 45176, 45177, 45178, 45179, 42579, 42584, 42589, 42598, 42603, 42608, 42615, 42621, 42853, 45114, 45144, 45145, 45169, 45254, 45255, 45270, 45436, 45509, 45510, 46138, 45088, 45089, 45090, 45091, 45092, 45093, 45094, 45095, 45096, 45097, 45098, 45099, 45100, 45101, 45102, 45103, 45104, 45105, 45059, 45038, 45624, 45632, 45633, 45634, 45635, 45636, 45637, 45638, 45639, 45640, 45641, 45642, 45643, 45644, 45645, 45646, 45647, 45648, 45649, 45650, 45651, 45652, 45653, 45654, 45655, 45656, 45657, 45658, 45659, 45660, 45661, 45862, 45879, 45880, 45881, 45882, 45883, 45987, 46052, 46053, 41719, 41747, 45087, 45773, 45984, 45986, 46109, 46110, 45912, 44820, 45909, 46004, 46005, 46007, 46376, 46377, 46378, 46379, 45978, 45979, 45980, 45981, 45999, 46000, 46001, 46002, 46003, 46359, 46360, 45774, 46027, 46348, 45932, 44842, 44843, 45693, 45725, 45801, 45802, 46171, 46108, 45977, 44793, 45056, 45060, 45054, 44791, 45050, 45125, 45586, 45589, 45590, 45591, 45592, 45593, 45595, 45596, 45597, 46743, 46744, 46745, 46746, 46747, 46748, 46749, 46750, 46751, 46752, 33004, 46006, 45601, 45602, 45604, 45622, 45623, 45731, 45733, 45735, 45740, 45741, 45755, 45757, 45758, 45760, 45761, 45768, 45769, 45771, 45772, 45775, 45776, 45778, 45782, 45783, 45785, 45789, 45790, 45792, 45793, 45795, 45799, 45800, 45804, 45732, 45738, 45762, 45794, 45806, 46099, 46100, 46308, 44977, 45621, 37490, 37491, 37492, 37493, 44835, 45039, 45896, 45897, 45506, 45706, 45857, 45875, 45878, 44794, 44965, 44970, 44971, 44973, 44974, 44980, 44982, 44983, 44984, 44998, 45002, 45011, 45013, 45014, 45015, 45016, 45017, 45018, 45019, 45020, 45021, 45022, 45047, 45057, 45063, 45180, 45606, 46026, 46098, 46349, 44817, 45127, 45500, 45714, 45715, 45716, 45717, 45718, 45719, 45720, 45721, 45722, 45723, 44599, 44601, 44792, 44806, 44818, 44822, 44834, 44836, 44837, 44838, 44839, 44840, 44853, 44854, 44855, 44978, 44981, 44986, 44987, 45000, 45003, 45005, 45045, 45046, 45058, 45062, 45064, 45070, 45072, 45080, 45082, 45083, 45121, 45122, 45192, 45278, 45281, 45323, 45328, 45628, 45724, 45784, 45786, 45787, 45788, 45791, 45798, 45814, 45815, 45816, 45817, 45855, 45902, 45903, 45904, 45905, 46029, 46114, 46765, 46766, 46767, 38605, 45190, 45194, 45195, 45196, 45197, 45198, 45199, 45200, 45201, 45202, 45603, 45625, 45734, 45736, 45737, 45739, 45742, 45743, 45744, 45745, 45746, 45747, 45753, 45756, 45764, 45766, 45767, 45770, 45777, 45779, 45780, 45781, 45797, 45803, 45805, 45907, 46023, 46361, 46368, 46369, 46372, 45796);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
