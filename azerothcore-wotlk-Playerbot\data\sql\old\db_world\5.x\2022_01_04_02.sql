-- DB update 2022_01_04_01 -> 2022_01_04_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_04_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_04_01 2022_01_04_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641313085677965557'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641313085677965557');

-- Removes Handful of Copper Bolt loot from creatures who are not suppose to have it
DELETE FROM `creature_loot_template` WHERE (`Entry` IN (2044, 2673, 6250)) AND (`Item`= 4359);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_04_02' WHERE sql_rev = '1641313085677965557';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
