-- DB update 2021_12_11_06 -> 2021_12_11_07
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_11_06';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_11_06 2021_12_11_07 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1638793339933122752'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1638793339933122752');

-- Remove money drop from Imp Minion 12922
UPDATE `creature_template` SET `maxgold` = 0 WHERE `entry` = 12922;


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_11_07' WHERE sql_rev = '1638793339933122752';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
