-- DB update 2021_10_10_35 -> 2021_10_10_36
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_35';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_35 2021_10_10_36 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633798793231677400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633798793231677400');

DELETE FROM `event_scripts` WHERE `id` = 4975 AND `command` = 10;

DELETE FROM `spell_script_names` WHERE `spell_id` = 16796 AND `ScriptName` = 'spell_q5056_summon_shy_rotam';
INSERT INTO `spell_script_names` (`spell_id`, `ScriptName`) VALUES
(16796, 'spell_q5056_summon_shy_rotam');

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_36' WHERE sql_rev = '1633798793231677400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
