-- DB update 2019_07_30_00 -> 2019_08_02_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2019_07_30_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2019_07_30_00 2019_08_02_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1563373473299990251'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1563373473299990251');

-- Snowblind Diggers

-- Set respawn time to 5 minutes
UPDATE `creature` SET `spawntimesecs` = 300 WHERE `id` = 29413;

-- Delete mining emote for template
DELETE FROM `creature_template_addon` WHERE `entry` = 29413;

-- Add mining emote for a few specific creatures
DELETE FROM `creature_addon` WHERE `guid` IN (209173,209174,209175,209176,209178,209169,209167);
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `auras`)
VALUES
(209173,0,0,0,0,233,NULL),
(209174,0,0,0,0,233,NULL),
(209175,0,0,0,0,233,NULL),
(209176,0,0,0,0,233,NULL),
(209178,0,0,0,0,233,NULL),
(209169,0,0,0,0,233,NULL),
(209167,0,0,0,0,233,NULL);

-- Relocate creatures and change movement
UPDATE `creature` SET `position_x` = 6618.42, `position_y` = -1289.41, `position_z` = 395.232, `orientation` = 0.56943 WHERE `guid` = 209181;
UPDATE `creature` SET `position_x` = 6616.73, `position_y` = -1275.12, `position_z` = 394.475, `orientation` = 0.263124 WHERE `guid` = 209182;
UPDATE `creature` SET `position_x` = 6607.84, `position_y` = -1272.13, `position_z` = 394.94, `orientation` = 3.91365 WHERE `guid` = 209171;
UPDATE `creature` SET `position_x` = 6599.4, `position_y` = -1287.01, `position_z` = 394.472, `orientation` = 0.748491 WHERE `guid` = 209172;
UPDATE `creature` SET `position_x` = 6582.9, `position_y` = -1296.13, `position_z` = 396.028, `orientation` = 3.68195 WHERE `guid` = 209173;
UPDATE `creature` SET `position_x` = 6594.04, `position_y` = -1255.13, `position_z` = 396.758, `orientation` = 0.520726 WHERE `guid` = 209174;
UPDATE `creature` SET `position_x` = 6577.77, `position_y` = -1235.15, `position_z` = 401.003, `orientation` = 2.03497 WHERE `guid` = 209175;
UPDATE `creature` SET `position_x` = 6554.56, `position_y` = -1244.51, `position_z` = 397.976, `orientation` = 3.82019 WHERE `guid` = 209176;
UPDATE `creature` SET `position_x` = 6571.99, `position_y` = -1250.95, `position_z` = 397.345, `orientation` = 1.66034, `spawndist` = 5, `MovementType` = 1 WHERE `guid` = 209177;
UPDATE `creature` SET `position_x` = 6549.79, `position_y` = -1267.83, `position_z` = 398.194, `orientation` = 4.25608 WHERE `guid` = 209178;
UPDATE `creature` SET `position_x` = 6562.21, `position_y` = -1279.44, `position_z` = 394.641, `orientation` = 4.90796 WHERE `guid` = 209170;
UPDATE `creature` SET `position_x` = 6549.84, `position_y` = -1297.54, `position_z` = 395.775, `orientation` = 5.08073 WHERE `guid` = 209169;
UPDATE `creature` SET `position_x` = 6507.95, `position_y` = -1007.34, `position_z` = 436.823, `orientation` = 2.40489 WHERE `guid` = 209167;
UPDATE `creature` SET `position_x` = 6630.43, `position_y` = -1015.39, `position_z` = 423.77, `orientation` = 2.91488, `MovementType` = 2 WHERE `guid` = 209163;
UPDATE `creature` SET `position_x` = 6677.18, `position_y` = -1246.83, `position_z` = 396.086, `orientation` = 1.71887, `MovementType` = 2 WHERE `guid` = 209180;
UPDATE `creature` SET `position_x` = 6578.26, `position_y` = -1277.9, `position_z` = 393.034, `orientation` = 6.17209, `MovementType` = 2 WHERE `guid` = 209165;
UPDATE `creature` SET `MovementType` = 2 WHERE `guid` = 209162;

-- Add paths
DELETE FROM `creature_addon` WHERE `guid` IN (209163,209180,209165,209162);
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `auras`)
VALUES
(209163,2091630,0,0,0,0,NULL),
(209180,2091800,0,0,0,0,NULL),
(209165,2091650,0,0,0,0,NULL),
(209162,2091620,0,0,0,0,NULL);

DELETE FROM `waypoint_data` WHERE `id` IN (2091630,2091800,2091650,2091620);
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`)
VALUES
(2091630,1,6630.43,-1015.39,423.77,0,1000,1,0,100,0),
(2091630,2,6622.07,-1013.76,424.221,0,0,1,0,100,0),
(2091630,3,6614.1,-1012.62,426.875,0,0,1,0,100,0),
(2091630,4,6606.56,-1011.06,428.427,0,0,1,0,100,0),
(2091630,5,6598.27,-1009.9,429.236,0,0,1,0,100,0),
(2091630,6,6590.27,-1011.39,430.744,0,0,1,0,100,0),
(2091630,7,6582.52,-1014.3,432.688,0,0,1,0,100,0),
(2091630,8,6574.71,-1017.68,433.038,0,0,1,0,100,0),
(2091630,9,6565.33,-1020.03,433.574,0,0,1,0,100,0),
(2091630,10,6554.67,-1022.05,433.46,0,0,1,0,100,0),
(2091630,11,6543.89,-1024.07,433.205,0,0,1,0,100,0),
(2091630,12,6532.89,-1026.65,433.027,0,0,1,0,100,0),
(2091630,13,6522.08,-1030.37,433.347,0,0,1,0,100,0),
(2091630,14,6515.32,-1032.15,434.707,0,0,1,0,100,0),
(2091630,15,6507.42,-1032.8,435.709,0,0,1,0,100,0),
(2091630,16,6499.33,-1031.94,435.736,0,0,1,0,100,0),
(2091630,17,6492.16,-1029.87,434.726,0,0,1,0,100,0),
(2091630,18,6485.65,-1027.07,434.509,0,0,1,0,100,0),
(2091630,19,6477.88,-1022.57,434.729,0,0,1,0,100,0),
(2091630,20,6471.27,-1018.38,434.462,0,0,1,0,100,0),
(2091630,21,6463.92,-1012.45,433.676,0,0,1,0,100,0),
(2091630,22,6458.56,-1006.62,433.884,0,0,1,0,100,0),
(2091630,23,6455.38,-1000.79,435.29,0,1000,1,0,100,0),
(2091630,24,6457.27,-1003.75,434.494,0,0,1,0,100,0),
(2091630,25,6461.97,-1010.15,433.367,0,0,1,0,100,0),
(2091630,26,6468.53,-1016.09,434.238,0,0,1,0,100,0),
(2091630,27,6475.2,-1020.79,434.806,0,0,1,0,100,0),
(2091630,28,6482.75,-1025.2,434.578,0,0,1,0,100,0),
(2091630,29,6489.8,-1028.58,434.611,0,0,1,0,100,0),
(2091630,30,6496.33,-1030.74,435.823,0,0,1,0,100,0),
(2091630,31,6503.68,-1032.07,435.97,0,0,1,0,100,0),
(2091630,32,6511.37,-1032.07,435.43,0,0,1,0,100,0),
(2091630,33,6519.63,-1030.57,433.52,0,0,1,0,100,0),
(2091630,34,6528.75,-1027.72,433.005,0,0,1,0,100,0),
(2091630,35,6539.5,-1024.61,433.142,0,0,1,0,100,0),
(2091630,36,6549.8,-1022.64,433.309,0,0,1,0,100,0),
(2091630,37,6560.07,-1020.48,433.519,0,0,1,0,100,0),
(2091630,38,6570.28,-1018.65,433.627,0,0,1,0,100,0),
(2091630,39,6578.14,-1015.71,433.441,0,0,1,0,100,0),
(2091630,40,6586.27,-1012.46,431.826,0,0,1,0,100,0),
(2091630,41,6594.8,-1010.12,429.553,0,0,1,0,100,0),
(2091630,42,6603.13,-1010.47,428.777,0,0,1,0,100,0),
(2091630,43,6610.5,-1011.56,427.755,0,0,1,0,100,0),
(2091630,44,6617.97,-1012.87,425.756,0,0,1,0,100,0),
(2091630,45,6626.45,-1014.44,423.855,0,0,1,0,100,0),

(2091800,1,6677.18,-1246.83,396.086,0,1000,1,0,100,0),
(2091800,2,6676.67,-1238.8,397.454,0,0,1,0,100,0),
(2091800,3,6676.81,-1230.99,398.968,0,0,1,0,100,0),
(2091800,4,6675.18,-1223.14,398.868,0,0,1,0,100,0),
(2091800,5,6671.69,-1213.99,398.77,0,0,1,0,100,0),
(2091800,6,6668.7,-1203.33,398.77,0,0,1,0,100,0),
(2091800,7,6666.24,-1193.73,398.751,0,0,1,0,100,0),
(2091800,8,6666.56,-1183.28,398.76,0,0,1,0,100,0),
(2091800,9,6668.35,-1171.62,398.75,0,0,1,0,100,0),
(2091800,10,6669.51,-1160.6,398.306,0,0,1,0,100,0),
(2091800,11,6671.12,-1149.05,398.231,0,0,1,0,100,0),
(2091800,12,6672.74,-1138.79,397.17,0,0,1,0,100,0),
(2091800,13,6675.58,-1129.66,397.099,0,0,1,0,100,0),
(2091800,14,6679.43,-1120.14,397.099,0,0,1,0,100,0),
(2091800,15,6682.86,-1111.09,397.02,0,0,1,0,100,0),
(2091800,16,6685.89,-1104.26,396.743,0,1000,1,0,100,0),
(2091800,17,6684.44,-1107.33,396.897,0,0,1,0,100,0),
(2091800,18,6681.12,-1115.55,397.084,0,0,1,0,100,0),
(2091800,19,6677.49,-1125.03,397.097,0,0,1,0,100,0),
(2091800,20,6674.15,-1134.12,397.097,0,0,1,0,100,0),
(2091800,21,6671.93,-1144.38,398.04,0,0,1,0,100,0),
(2091800,22,6670.5,-1155.02,398.001,0,0,1,0,100,0),
(2091800,23,6669.08,-1166.01,398.567,0,0,1,0,100,0),
(2091800,24,6667.69,-1177.95,398.769,0,0,1,0,100,0),
(2091800,25,6666.16,-1189.03,398.761,0,0,1,0,100,0),
(2091800,26,6667.65,-1198.76,398.743,0,0,1,0,100,0),
(2091800,27,6670.35,-1209.03,398.773,0,0,1,0,100,0),
(2091800,28,6673.64,-1219.12,398.851,0,0,1,0,100,0),
(2091800,29,6676.67,-1227.94,399.019,0,0,1,0,100,0),
(2091800,30,6677.07,-1235.62,398.13,0,0,1,0,100,0),
(2091800,31,6677.02,-1243.56,396.397,0,0,1,0,100,0),

(2091650,1,6578.26,-1277.9,393.034,0,1000,1,0,100,0),
(2091650,2,6586.44,-1279.82,393.083,0,0,1,0,100,0),
(2091650,3,6595.6,-1279.21,393.154,0,0,1,0,100,0),
(2091650,4,6602.59,-1279.33,394.355,0,0,1,0,100,0),
(2091650,5,6605.95,-1280.55,394.474,0,0,1,0,100,0),
(2091650,6,6608.36,-1284.96,394.474,0,0,1,0,100,0),
(2091650,7,6614.06,-1284.95,394.474,0,0,1,0,100,0),
(2091650,8,6620.63,-1283.89,394.474,0,0,1,0,100,0),
(2091650,9,6628.49,-1288.4,394.902,0,0,1,0,100,0),
(2091650,10,6636.18,-1294.47,395.329,0,0,1,0,100,0),
(2091650,11,6645.2,-1299.54,396.085,0,0,1,0,100,0),
(2091650,12,6654.24,-1301.18,396.226,0,0,1,0,100,0),
(2091650,13,6664.92,-1302.26,396.218,0,0,1,0,100,0),
(2091650,14,6675.64,-1306.46,395.105,0,0,1,0,100,0),
(2091650,15,6686.01,-1312.05,395.674,0,0,1,0,100,0),
(2091650,16,6696.54,-1320.83,395.229,0,0,1,0,100,0),
(2091650,17,6701.56,-1331.16,395.373,0,0,1,0,100,0),
(2091650,18,6701.11,-1341.36,394.735,0,0,1,0,100,0),
(2091650,19,6699.32,-1351.83,394.723,0,0,1,0,100,0),
(2091650,20,6698.82,-1362.54,393.332,0,0,1,0,100,0),
(2091650,21,6698.43,-1372.11,391.398,0,0,1,0,100,0),
(2091650,22,6698.08,-1382.83,391.834,0,0,1,0,100,0),
(2091650,23,6698.7,-1395.51,391.744,0,0,1,0,100,0),
(2091650,24,6696.86,-1408.19,391.235,0,0,1,0,100,0),
(2091650,25,6695.76,-1419.31,389.774,0,0,1,0,100,0),
(2091650,26,6699.13,-1429.37,388.324,0,0,1,0,100,0),
(2091650,27,6702.81,-1440.93,384.793,0,0,1,0,100,0),
(2091650,28,6705.79,-1452.69,382.83,0,0,1,0,100,0),
(2091650,29,6708.66,-1465.56,379.526,0,0,1,0,100,0),
(2091650,30,6711.44,-1476.77,377.976,0,0,1,0,100,0),
(2091650,31,6714.61,-1488.59,376.005,0,0,1,0,100,0),
(2091650,32,6716.78,-1497.06,374.54,0,1000,1,0,100,0),
(2091650,33,6715.64,-1492.81,375.078,0,0,1,0,100,0),
(2091650,34,6712.82,-1482.93,377.398,0,0,1,0,100,0),
(2091650,35,6709.94,-1471.16,378.513,0,0,1,0,100,0),
(2091650,36,6707.06,-1458.3,381.516,0,0,1,0,100,0),
(2091650,37,6704.37,-1446.95,383.782,0,0,1,0,100,0),
(2091650,38,6700.5,-1433.98,386.707,0,0,1,0,100,0),
(2091650,39,6697.51,-1424.65,388.919,0,0,1,0,100,0),
(2091650,40,6696.44,-1413.71,390.502,0,0,1,0,100,0),
(2091650,41,6697.76,-1402.37,391.508,0,0,1,0,100,0),
(2091650,42,6698.46,-1389.68,391.988,0,0,1,0,100,0),
(2091650,43,6698.15,-1378.03,391.593,0,0,1,0,100,0),
(2091650,44,6698.62,-1367.65,392.737,0,0,1,0,100,0),
(2091650,45,6699.07,-1357.28,393.821,0,0,1,0,100,0),
(2091650,46,6700.17,-1345.68,394.523,0,0,1,0,100,0),
(2091650,47,6701.97,-1334.98,395.238,0,0,1,0,100,0),
(2091650,48,6699.26,-1325.95,395.088,0,0,1,0,100,0),
(2091650,49,6691.94,-1316.82,395.343,0,0,1,0,100,0),
(2091650,50,6680.76,-1309.42,395.373,0,0,1,0,100,0),
(2091650,51,6670.04,-1304.69,395.639,0,0,1,0,100,0),
(2091650,52,6659.18,-1301.74,396.25,0,0,1,0,100,0),
(2091650,53,6648.71,-1301.07,395.98,0,0,1,0,100,0),
(2091650,54,6640.4,-1297.22,395.799,0,0,1,0,100,0),
(2091650,55,6631.49,-1291.3,394.648,0,0,1,0,100,0),
(2091650,56,6624.4,-1286.21,394.675,0,0,1,0,100,0),
(2091650,57,6616.7,-1284.88,394.475,0,0,1,0,100,0),
(2091650,58,6611.02,-1285.46,394.475,0,0,1,0,100,0),
(2091650,59,6607.09,-1282.98,394.475,0,0,1,0,100,0),
(2091650,60,6603.69,-1279.6,394.475,0,0,1,0,100,0),
(2091650,61,6599.27,-1279.46,393.855,0,0,1,0,100,0),
(2091650,62,6591.01,-1279.84,393.026,0,0,1,0,100,0),
(2091650,63,6582.31,-1279.31,393.094,0,0,1,0,100,0),

(2091620,1,6696.47,-998.844,415.433,0,1000,1,0,100,0),
(2091620,2,6696.38,-1009.51,414.833,0,0,1,0,100,0),
(2091620,3,6696.29,-1020.35,414.814,0,0,1,0,100,0),
(2091620,4,6695.76,-1031.29,414.671,0,0,1,0,100,0),
(2091620,5,6695.56,-1037.94,413.492,0,0,1,0,100,0),
(2091620,6,6695.07,-1046.08,409.932,0,0,1,0,100,0),
(2091620,7,6691.14,-1052.88,407.74,0,0,1,0,100,0),
(2091620,8,6683.92,-1057.17,406.692,0,0,1,0,100,0),
(2091620,9,6676.16,-1061.22,406.116,0,0,1,0,100,0),
(2091620,10,6667.37,-1066.28,404.756,0,0,1,0,100,0),
(2091620,11,6659.36,-1071.06,403.259,0,0,1,0,100,0),
(2091620,12,6651.76,-1076.68,402.319,0,0,1,0,100,0),
(2091620,13,6643.15,-1081.6,403.919,0,0,1,0,100,0),
(2091620,14,6634.41,-1087.41,406.653,0,0,1,0,100,0),
(2091620,15,6627.55,-1093.56,408.932,0,0,1,0,100,0),
(2091620,16,6621.29,-1100.47,410.532,0,0,1,0,100,0),
(2091620,17,6616.89,-1106.21,411.095,0,0,1,0,100,0),
(2091620,18,6616.79,-1112.16,411.68,0,1000,1,0,100,0),
(2091620,19,6616.83,-1108.66,411.24,0,0,1,0,100,0),
(2091620,20,6619.2,-1102.84,410.888,0,0,1,0,100,0),
(2091620,21,6624.57,-1096.86,409.545,0,0,1,0,100,0),
(2091620,22,6630.83,-1090.45,407.931,0,0,1,0,100,0),
(2091620,23,6639.85,-1084.06,404.571,0,0,1,0,100,0),
(2091620,24,6648.4,-1078.84,402.597,0,0,1,0,100,0),
(2091620,25,6655.95,-1073.79,402.82,0,0,1,0,100,0),
(2091620,26,6663.41,-1068.59,404.161,0,0,1,0,100,0),
(2091620,27,6669.62,-1060.96,405.849,0,0,1,0,100,0),
(2091620,28,6675.63,-1053.22,407.141,0,0,1,0,100,0),
(2091620,29,6678.22,-1045.16,409.219,0,0,1,0,100,0),
(2091620,30,6681.44,-1037.37,411.508,0,0,1,0,100,0),
(2091620,31,6687.29,-1032.94,414.056,0,0,1,0,100,0),
(2091620,32,6691.28,-1026.64,414.768,0,0,1,0,100,0),
(2091620,33,6696.59,-1015.74,414.825,0,0,1,0,100,0),
(2091620,34,6696.36,-1003.84,414.835,0,0,1,0,100,0);


-- Spiders

-- Set respawn time to 5 minutes; set model ID 0 (take from template)
UPDATE `creature` SET `spawntimesecs` = 300, `modelid` = 0 WHERE `id` IN (29411,29412);

-- Icetip Crawlers have a reduced respawn time
UPDATE `creature` SET `spawntimesecs` = 30 WHERE `id` = 29461;

-- Relocate creatures and change movement
UPDATE `creature` SET `position_x` = 6553.6, `position_y` = -1000.54, `position_z` = 433.248, `orientation` = 5.26452, `MovementType` = 2 WHERE `guid` = 117735;
UPDATE `creature` SET `position_x` = 6586.39, `position_y` = -1039.64, `position_z` = 429.658, `orientation` = 0.963119, `MovementType` = 2 WHERE `guid` = 152064;
UPDATE `creature` SET `position_x` = 6682.47, `position_y` = -999.111, `position_z` = 414.846, `orientation` = 5.63318, `MovementType` = 2 WHERE `guid` = 117737;
UPDATE `creature` SET `position_x` = 6584.25, `position_y` = -1089.26, `position_z` = 410.336, `orientation` = 5.78408, `spawndist` = 2, `MovementType` = 1 WHERE `guid` = 117736;
UPDATE `creature` SET `spawndist` = 2, `MovementType` = 1 WHERE `guid` = 152057;
UPDATE `creature` SET `id` = 29411, `position_x` = 6635.26, `position_y` = -1110.19, `position_z` = 426.405, `orientation` = 2.71711 WHERE `guid` = 117781;
UPDATE `creature` SET `id` = 29411, `position_x` = 6675.71, `position_y` = -1132.09, `position_z` = 424.748, `orientation` = 3.24333, `spawndist` = 0, `MovementType` = 2 WHERE `guid` = 117783;
UPDATE `creature` SET `spawndist` = 5, `MovementType` = 1 WHERE `guid` = 152056;
UPDATE `creature` SET `position_x` = 6683.6, `position_y` = -1415.72, `position_z` = 390.707, `orientation` = 4.74043, `MovementType` = 2 WHERE `guid` = 152058;
UPDATE `creature` SET `position_x` = 6714.72, `position_y` = -1497.73, `position_z` = 374.571, `orientation` = 1.61848, `MovementType` = 2 WHERE `guid` = 152067;
UPDATE `creature` SET `spawndist` = 0, `MovementType` = 2 WHERE `guid` IN (152062,152068,98067,98068,98069);

-- Recycle some of the GUIDs of the Snowblind Diggers (there are too many of them) and use them for missing spiders
DELETE FROM `creature` WHERE `guid` IN (209164,209166,209168,209179,209183,209184,209185,209186);
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `spawndist`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`)
VALUES
(209164,29411,571,0,0,1,1,0,0,6675.5,-1188.03,398.765,2.13514,300,0,0,11379,0,0,0,0,0,'',0),
(209166,29411,571,0,0,1,1,0,0,6683.76,-1273,396.134,1.97412,300,5,0,11379,0,1,0,0,0,'',0),
(209168,29411,571,0,0,1,1,0,0,6773.32,-1507.57,364.754,3.44676,300,5,0,11379,0,1,0,0,0,'',0),
(209179,29411,571,0,0,1,1,0,0,6681.34,-1112.28,397.097,4.32809,300,0,0,11379,0,2,0,0,0,'',0),
-- new GUIDs
(209183,29411,571,0,0,1,1,0,0,6698.37,-1404.31,391.313,1.52815,300,0,0,11379,0,2,0,0,0,'',0),
(209184,29411,571,0,0,1,1,0,0,6592.36,-1307.19,396.52,1.6393,300,2,0,11379,0,1,0,0,0,'',0),   -- pool
(209185,29411,571,0,0,1,1,0,0,6586.55,-1240.34,400.105,4.05832,300,2,0,11379,0,1,0,0,0,'',0), -- pool
(209186,29411,571,0,0,1,1,0,0,6628.81,-1281.21,394.476,3.28862,300,2,0,11379,0,1,0,0,0,'',0); -- pool

DELETE FROM `pool_template` WHERE `entry` = 201117;
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`)
VALUES
(201117,1,'Crystalweb Weaver Spawn (1 out of 3) - Attack Snowblind Digger camp');

DELETE FROM `pool_creature` WHERE `pool_entry` = 201117;
INSERT INTO `pool_creature` (`guid`, `pool_entry`, `chance`, `description`)
VALUES
(209184,201117,0,'Crystalweb Weaver 1'),
(209185,201117,0,'Crystalweb Weaver 2'),
(209186,201117,0,'Crystalweb Weaver 3');

DELETE FROM `creature_addon` WHERE `guid` IN (117735,152064,117737,152068,117783,152062,209179,209183,152058,152067,98067,98068,98069);
INSERT INTO `creature_addon` (`guid`, `path_id`, `mount`, `bytes1`, `bytes2`, `emote`, `auras`)
VALUES
(117735,1177350,0,0,0,0,NULL),
(152064,1520640,0,0,0,0,NULL),
(117737,1177370,0,0,0,0,NULL),
(152068,1520680,0,0,0,0,NULL),
(117783,1177830,0,0,0,0,NULL),
(152062,1520620,0,0,0,0,NULL),
(209179,2091790,0,0,0,0,NULL),
(209183,2091830,0,0,0,0,NULL),
(152058,1520580,0,0,0,0,NULL),
(152067,1520670,0,0,0,0,NULL),
(98067,980670,0,0,0,0,NULL),
(98068,980680,0,0,0,0,NULL),
(98069,980690,0,0,0,0,NULL);

DELETE FROM `waypoint_data` WHERE `id` IN (1177350,1520640,1177370,1520680,1177830,1520620,2091790,2091830,1520580,1520670,980670,980680,980690);
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`)
VALUES
(1177350,1,6553.6,-1000.54,433.248,0,1000,0,0,100,0),
(1177350,2,6560.46,-1008.18,433.478,0,0,0,0,100,0),
(1177350,3,6566.69,-1012.83,433.6,0,0,0,0,100,0),
(1177350,4,6572.58,-1015.22,433.933,0,0,0,0,100,0),
(1177350,5,6576.39,-1014.74,433.955,0,0,0,0,100,0),
(1177350,6,6579.15,-1012.79,433.737,0,0,0,0,100,0),
(1177350,7,6581.9,-1010.31,433.441,0,0,0,0,100,0),
(1177350,8,6584.09,-1006.59,433.302,0,0,0,0,100,0),
(1177350,9,6585.49,-1002.27,433.482,0,0,0,0,100,0),
(1177350,10,6586.79,-997.431,433.205,0,0,0,0,100,0),
(1177350,11,6587.94,-992.781,432.41,0,1000,0,0,100,0),
(1177350,12,6587.39,-995.417,432.847,0,0,0,0,100,0),
(1177350,13,6585.94,-1000.35,433.611,0,0,0,0,100,0),
(1177350,14,6584.72,-1004.23,433.442,0,0,0,0,100,0),
(1177350,15,6582.49,-1008.45,433.524,0,0,0,0,100,0),
(1177350,16,6580.15,-1011.64,433.716,0,0,0,0,100,0),
(1177350,17,6577.65,-1013.73,433.898,0,0,0,0,100,0),
(1177350,18,6574.39,-1014.98,434.066,0,0,0,0,100,0),
(1177350,19,6569.17,-1013.41,433.79,0,0,0,0,100,0),
(1177350,20,6562.71,-1009.62,433.533,0,0,0,0,100,0),
(1177350,21,6556.98,-1003.97,433.364,0,0,0,0,100,0),

(1520640,1,6586.39,-1039.64,429.657,0,1000,0,0,100,0),
(1520640,2,6591.63,-1032.2,429.346,0,0,0,0,100,0),
(1520640,3,6598.7,-1024.61,428.691,0,0,0,0,100,0),
(1520640,4,6604.75,-1020.97,428.199,0,0,0,0,100,0),
(1520640,5,6610.62,-1020.11,427.833,0,0,0,0,100,0),
(1520640,6,6616.03,-1020.86,425.976,0,0,0,0,100,0),
(1520640,7,6620.13,-1023.87,423.885,0,0,0,0,100,0),
(1520640,8,6622.85,-1027.89,423.715,0,0,0,0,100,0),
(1520640,9,6624.63,-1032.33,423.089,0,0,0,0,100,0),
(1520640,10,6626.34,-1036.92,421.616,0,1000,0,0,100,0),
(1520640,11,6625.37,-1034.43,422.455,0,0,0,0,100,0),
(1520640,12,6623.45,-1029.55,423.543,0,0,0,0,100,0),
(1520640,13,6621.19,-1025.75,423.734,0,0,0,0,100,0),
(1520640,14,6617.56,-1021.95,425.084,0,0,0,0,100,0),
(1520640,15,6613.07,-1020.72,427.283,0,0,0,0,100,0),
(1520640,16,6607.02,-1021.07,428.044,0,0,0,0,100,0),
(1520640,17,6601.36,-1022.67,428.482,0,0,0,0,100,0),
(1520640,18,6594.71,-1028.87,428.999,0,0,0,0,100,0),
(1520640,19,6588.7,-1036.15,429.421,0,0,0,0,100,0),

(1177370,1,6682.47,-999.111,414.846,0,1000,0,0,100,0),
(1177370,2,6687.93,-1004.71,414.839,0,0,0,0,100,0),
(1177370,3,6692.57,-1011.95,414.831,0,0,0,0,100,0),
(1177370,4,6694.1,-1018.65,414.821,0,0,0,0,100,0),
(1177370,5,6694.64,-1026.68,414.805,0,0,0,0,100,0),
(1177370,6,6694.84,-1031.8,414.619,0,0,0,0,100,0),
(1177370,7,6695.1,-1037.16,413.706,0,0,0,0,100,0),
(1177370,8,6695.33,-1042.41,411.46,0,0,0,0,100,0),
(1177370,9,6693.89,-1048.98,408.653,0,0,0,0,100,0),
(1177370,10,6689.75,-1052.92,407.46,0,0,0,0,100,0),
(1177370,11,6683.89,-1056.72,406.768,0,0,0,0,100,0),
(1177370,12,6677.54,-1060.43,406.227,0,1000,0,0,100,0),
(1177370,13,6681.16,-1058.53,406.496,0,0,0,0,100,0),
(1177370,14,6687.02,-1054.7,407.151,0,0,0,0,100,0),
(1177370,15,6691.78,-1050.95,408.07,0,0,0,0,100,0),
(1177370,16,6694.81,-1046.1,409.862,0,0,0,0,100,0),
(1177370,17,6694.71,-1039.35,412.496,0,0,0,0,100,0),
(1177370,18,6694.51,-1034.33,414.571,0,0,0,0,100,0),
(1177370,19,6694.37,-1029.09,414.749,0,0,0,0,100,0),
(1177370,20,6694.2,-1022.67,414.808,0,0,0,0,100,0),
(1177370,21,6693.05,-1015.17,414.826,0,0,0,0,100,0),
(1177370,22,6690.06,-1007.84,414.833,0,0,0,0,100,0),
(1177370,23,6685.03,-1001.86,414.841,0,0,0,0,100,0),

(1520680,1,6615.51,-1116.22,411.475,0,1000,0,0,100,0),
(1520680,2,6615.32,-1109.34,411.254,0,0,0,0,100,0),
(1520680,3,6616.58,-1101.41,411.094,0,0,0,0,100,0),
(1520680,4,6617.15,-1093.49,411.316,0,0,0,0,100,0),
(1520680,5,6617.13,-1086.96,413.435,0,0,0,0,100,0),
(1520680,6,6617.77,-1082.23,414.446,0,0,0,0,100,0),
(1520680,7,6620.27,-1076.46,415.292,0,0,0,0,100,0),
(1520680,8,6624.31,-1068.98,414.793,0,0,0,0,100,0),
(1520680,9,6626.87,-1063.74,414.693,0,1000,0,0,100,0),
(1520680,10,6625.56,-1066.44,414.744,0,0,0,0,100,0),
(1520680,11,6622.16,-1072.95,415.043,0,0,0,0,100,0),
(1520680,12,6619,-1079.46,414.673,0,0,0,0,100,0),
(1520680,13,6617.34,-1084.8,414.241,0,0,0,0,100,0),
(1520680,14,6617.27,-1091.32,411.619,0,0,0,0,100,0),
(1520680,15,6616.9,-1097.96,411.1,0,0,0,0,100,0),
(1520680,16,6615.88,-1105.71,411.137,0,0,0,0,100,0),
(1520680,17,6615.29,-1112.68,411.578,0,0,0,0,100,0),

(1177830,1,6675.71,-1132.09,424.748,0,1000,0,0,100,0),
(1177830,2,6666.29,-1132.76,423.12,0,0,0,0,100,0),
(1177830,3,6659.17,-1132.76,421.637,0,0,0,0,100,0),
(1177830,4,6654.28,-1130.93,419.653,0,0,0,0,100,0),
(1177830,5,6649.27,-1127.94,417.865,0,0,0,0,100,0),
(1177830,6,6641.87,-1126.02,416.799,0,0,0,0,100,0),
(1177830,7,6636.29,-1126.37,416.983,0,0,0,0,100,0),
(1177830,8,6630.07,-1128.71,415.376,0,0,0,0,100,0),
(1177830,9,6625.91,-1128,414.151,0,0,0,0,100,0),
(1177830,10,6621.8,-1123.87,412.587,0,1000,0,0,100,0),
(1177830,11,6624.24,-1126.17,413.302,0,0,0,0,100,0),
(1177830,12,6628.26,-1128.29,414.804,0,0,0,0,100,0),
(1177830,13,6633.69,-1126.95,416.589,0,0,0,0,100,0),
(1177830,14,6639.26,-1125.65,417.22,0,0,0,0,100,0),
(1177830,15,6646.29,-1126.73,417.142,0,0,0,0,100,0),
(1177830,16,6652.29,-1129.6,419.223,0,0,0,0,100,0),
(1177830,17,6656.91,-1131.84,420.603,0,0,0,0,100,0),
(1177830,18,6662.82,-1132.7,422.418,0,0,0,0,100,0),
(1177830,19,6670.4,-1132.31,423.847,0,0,0,0,100,0),

(1520620,1,6706.14,-1073.63,391.625,0,1000,0,0,100,0),
(1520620,2,6701.08,-1079.13,391.625,0,0,0,0,100,0),
(1520620,3,6695.91,-1084.35,392.961,0,0,0,0,100,0),
(1520620,4,6693.07,-1088.72,393.862,0,0,0,0,100,0),
(1520620,5,6691.9,-1091.88,394.405,0,0,0,0,100,0),
(1520620,6,6692.45,-1094.87,394.945,0,0,0,0,100,0),
(1520620,7,6694.52,-1097.82,395.097,0,0,0,0,100,0),
(1520620,8,6698.76,-1099.74,395.324,0,0,0,0,100,0),
(1520620,9,6702.99,-1102.46,395.619,0,0,0,0,100,0),
(1520620,10,6706.99,-1105.66,395.85,0,0,0,0,100,0),
(1520620,11,6708.55,-1109.06,396.244,0,0,0,0,100,0),
(1520620,12,6709.34,-1113.31,397.322,0,0,0,0,100,0),
(1520620,13,6710.53,-1117.46,397.56,0,0,0,0,100,0),
(1520620,14,6711.54,-1121.65,398.178,0,1000,0,0,100,0),
(1520620,15,6710.76,-1119.58,397.906,0,0,0,0,100,0),
(1520620,16,6709.58,-1115.06,397.388,0,0,0,0,100,0),
(1520620,17,6708.85,-1110.82,396.443,0,0,0,0,100,0),
(1520620,18,6707.51,-1107.08,396.035,0,0,0,0,100,0),
(1520620,19,6704.47,-1104.03,395.71,0,0,0,0,100,0),
(1520620,20,6700.67,-1101.15,395.505,0,0,0,0,100,0),
(1520620,21,6696.23,-1099.07,395.3,0,0,0,0,100,0),
(1520620,22,6693.29,-1096.38,395.071,0,0,0,0,100,0),
(1520620,23,6692.04,-1092.9,394.607,0,0,0,0,100,0),
(1520620,24,6692.56,-1089.91,394.026,0,0,0,0,100,0),
(1520620,25,6694.76,-1086.34,393.25,0,0,0,0,100,0),
(1520620,26,6699.02,-1081.43,392.073,0,0,0,0,100,0),
(1520620,27,6703.75,-1076.42,391.625,0,0,0,0,100,0),

(2091790,1,6681.34,-1112.28,397.097,0,1000,0,0,100,0),
(2091790,2,6677.48,-1122.3,397.166,0,0,0,0,100,0),
(2091790,3,6672.33,-1134.93,397.099,0,0,0,0,100,0),
(2091790,4,6671.73,-1144.68,398.067,0,0,0,0,100,0),
(2091790,5,6669.9,-1153.92,398.025,0,0,0,0,100,0),
(2091790,6,6667.5,-1157.36,398.155,0,0,0,0,100,0),
(2091790,7,6663.99,-1158.24,398.257,0,0,0,0,100,0),
(2091790,8,6661.66,-1155.78,399.057,0,0,0,0,100,0),
(2091790,9,6661.7,-1151.7,399.905,0,0,0,0,100,0),
(2091790,10,6662.28,-1144.72,401.652,0,0,0,0,100,0),
(2091790,11,6662.65,-1138.67,403.589,0,0,0,0,100,0),
(2091790,12,6663.32,-1133.11,405.547,0,0,0,0,100,0),
(2091790,13,6663.89,-1129.42,406.8,0,1000,0,0,100,0),
(2091790,14,6663.61,-1131.27,406.172,0,0,0,0,100,0),
(2091790,15,6662.91,-1135.64,404.692,0,0,0,0,100,0),
(2091790,16,6662.32,-1141.44,402.776,0,0,0,0,100,0),
(2091790,17,6661.84,-1148.31,400.77,0,0,0,0,100,0),
(2091790,18,6661.64,-1153.91,399.335,0,0,0,0,100,0),
(2091790,19,6663.19,-1157.01,398.533,0,0,0,0,100,0),
(2091790,20,6666.26,-1157.7,398.19,0,0,0,0,100,0),
(2091790,21,6669.15,-1154.99,398.02,0,0,0,0,100,0),
(2091790,22,6670.88,-1149.19,398.217,0,0,0,0,100,0),
(2091790,23,6672.13,-1138.77,397.167,0,0,0,0,100,0),
(2091790,24,6675.11,-1128.02,397.099,0,0,0,0,100,0),
(2091790,25,6679.58,-1117,397.204,0,0,0,0,100,0),

(2091830,1,6698.37,-1404.31,391.313,0,1000,0,0,100,0),
(2091830,2,6699.33,-1395.04,391.746,0,0,0,0,100,0),
(2091830,3,6700.79,-1383.93,391.863,0,0,0,0,100,0),
(2091830,4,6701.14,-1373.68,391.042,0,0,0,0,100,0),
(2091830,5,6701.68,-1364.73,393.519,0,0,0,0,100,0),
(2091830,6,6703.62,-1353.7,395.763,0,0,0,0,100,0),
(2091830,7,6706.37,-1342.27,395.338,0,0,0,0,100,0),
(2091830,8,6711.25,-1335.78,395.905,0,0,0,0,100,0),
(2091830,9,6717.93,-1331.65,395.861,0,0,0,0,100,0),
(2091830,10,6724.99,-1331.84,395.861,0,0,0,0,100,0),
(2091830,11,6731.1,-1333.36,395.861,0,1000,0,0,100,0),
(2091830,12,6727.88,-1332.62,395.861,0,0,0,0,100,0),
(2091830,13,6720.61,-1331.57,395.861,0,0,0,0,100,0),
(2091830,14,6714.54,-1333.92,395.861,0,0,0,0,100,0),
(2091830,15,6708.91,-1339.01,395.861,0,0,0,0,100,0),
(2091830,16,6704.99,-1348.51,395.278,0,0,0,0,100,0),
(2091830,17,6703.16,-1359.2,394.793,0,0,0,0,100,0),
(2091830,18,6701.71,-1369.95,392.231,0,0,0,0,100,0),
(2091830,19,6701.34,-1379.63,391.623,0,0,0,0,100,0),
(2091830,20,6700.51,-1389.86,391.928,0,0,0,0,100,0),
(2091830,21,6699.05,-1400.26,391.543,0,0,0,0,100,0),

(1520580,1,6683.6,-1415.72,390.707,0,1000,0,0,100,0),
(1520580,2,6683.16,-1425.39,389.24,0,0,0,0,100,0),
(1520580,3,6682.99,-1435.52,387.97,0,0,0,0,100,0),
(1520580,4,6684.1,-1445.02,386.499,0,0,0,0,100,0),
(1520580,5,6685.45,-1454.25,384.245,0,0,0,0,100,0),
(1520580,6,6687.63,-1463.21,382.289,0,0,0,0,100,0),
(1520580,7,6691.15,-1473.6,379.471,0,0,0,0,100,0),
(1520580,8,6693.78,-1483.15,377.722,0,0,0,0,100,0),
(1520580,9,6695.24,-1493.88,376.36,0,0,0,0,100,0),
(1520580,10,6696.04,-1501.07,375.798,0,0,0,0,100,0),
(1520580,11,6697.41,-1510.64,373.832,0,1000,0,0,100,0),
(1520580,12,6696.82,-1506.02,374.7,0,0,0,0,100,0),
(1520580,13,6695.41,-1497.27,376.093,0,0,0,0,100,0),
(1520580,14,6694.53,-1488.32,376.891,0,0,0,0,100,0),
(1520580,15,6692.56,-1478.03,378.552,0,0,0,0,100,0),
(1520580,16,6689.01,-1467.91,381.129,0,0,0,0,100,0),
(1520580,17,6686.12,-1457.95,383.331,0,0,0,0,100,0),
(1520580,18,6684.59,-1449.34,385.459,0,0,0,0,100,0),
(1520580,19,6683.3,-1440.21,387.411,0,0,0,0,100,0),
(1520580,20,6683.18,-1430.31,388.617,0,0,0,0,100,0),
(1520580,21,6683.33,-1420.62,389.994,0,0,0,0,100,0),

(1520670,1,6714.72,-1497.73,374.571,0,1000,0,0,100,0),
(1520670,2,6714.8,-1486.42,376.673,0,0,0,0,100,0),
(1520670,3,6712.3,-1476.03,377.843,0,0,0,0,100,0),
(1520670,4,6708.79,-1465.4,379.557,0,0,0,0,100,0),
(1520670,5,6707.16,-1454.79,382.351,0,0,0,0,100,0),
(1520670,6,6704.44,-1444.07,384.139,0,0,0,0,100,0),
(1520670,7,6700.39,-1433.88,386.756,0,0,0,0,100,0),
(1520670,8,6697.17,-1424.87,388.919,0,1000,0,0,100,0),
(1520670,9,6698.52,-1428.83,388.407,0,0,0,0,100,0),
(1520670,10,6702.6,-1439.39,385.019,0,0,0,0,100,0),
(1520670,11,6705.91,-1449.46,383.228,0,0,0,0,100,0),
(1520670,12,6708.03,-1460.09,380.85,0,0,0,0,100,0),
(1520670,13,6710.73,-1471.28,378.357,0,0,0,0,100,0),
(1520670,14,6713.59,-1480.88,377.38,0,0,0,0,100,0),
(1520670,15,6715,-1493.02,375.048,0,0,0,0,100,0),

(980670,1,6856.67,-1576.58,353.392,0,0,1,0,100,0),
(980670,2,6865.9,-1579.75,352.31,0,0,1,0,100,0),
(980670,3,6876.68,-1576.78,351.509,0,0,1,0,100,0),
(980670,4,6876.18,-1586.39,350.515,0,0,1,0,100,0),
(980670,5,6871.17,-1597.81,350.101,0,0,1,0,100,0),
(980670,6,6875.34,-1588.63,350.57,0,0,1,0,100,0),
(980670,7,6865.64,-1580.02,352.237,0,0,1,0,100,0),
(980670,8,6862.62,-1571.44,354.459,0,0,1,0,100,0),
(980670,9,6859.87,-1560.25,355.799,0,0,1,0,100,0),
(980670,10,6854.77,-1547.48,354.838,0,0,1,0,100,0),
(980670,11,6846.72,-1532.09,354.8,0,0,1,0,100,0),
(980670,12,6836.98,-1535.57,354.236,0,0,1,0,100,0),
(980670,13,6825.48,-1540.13,353.706,0,0,1,0,100,0),
(980670,14,6839.48,-1541.98,354.379,0,0,1,0,100,0),
(980670,15,6849.71,-1543.71,354.62,0,0,1,0,100,0),
(980670,16,6861.86,-1547.45,355.322,0,0,1,0,100,0),
(980670,17,6871.91,-1550.03,355.969,0,0,1,0,100,0),
(980670,18,6867.52,-1537.02,355.027,0,0,1,0,100,0),
(980670,19,6854.84,-1525.97,355.622,0,0,1,0,100,0),
(980670,20,6864.8,-1520.88,356.448,0,0,1,0,100,0),
(980670,21,6872.74,-1528.56,356.077,0,0,1,0,100,0),
(980670,22,6883.2,-1529.04,357.023,0,0,1,0,100,0),
(980670,23,6894.19,-1525.06,358.033,0,0,1,0,100,0),
(980670,24,6886.52,-1532.48,357.438,0,0,1,0,100,0),
(980670,25,6880.65,-1542.24,356.615,0,0,1,0,100,0),
(980670,26,6861.27,-1538.75,354.948,0,0,1,0,100,0),
(980670,27,6850.85,-1533.78,355.01,0,0,1,0,100,0),
(980670,28,6854.76,-1543.03,354.735,0,0,1,0,100,0),
(980670,29,6860,-1555.74,355.964,0,0,1,0,100,0),
(980670,30,6851.09,-1560.57,355.422,0,0,1,0,100,0),
(980670,31,6840.95,-1567.02,353.959,0,0,1,0,100,0),
(980670,32,6849.03,-1572.55,353.562,0,0,1,0,100,0),
(980670,33,6849.05,-1582.22,352.279,0,0,1,0,100,0),

(980680,1,6798.8,-1575.41,354.284,0,0,1,0,100,0),
(980680,2,6790.76,-1574.96,355.648,0,0,1,0,100,0),
(980680,3,6795.24,-1564.7,354.881,0,0,1,0,100,0),
(980680,4,6799.34,-1559.74,354.143,0,0,1,0,100,0),
(980680,5,6806.52,-1561.46,353.528,0,0,1,0,100,0),
(980680,6,6805.12,-1552.94,354.334,0,0,1,0,100,0),
(980680,7,6809.04,-1546.28,354.874,0,0,1,0,100,0),
(980680,8,6817.55,-1559.21,353.559,0,0,1,0,100,0),
(980680,9,6818.37,-1545.27,353.926,0,0,1,0,100,0),
(980680,10,6821.52,-1557.42,353.763,0,0,1,0,100,0),
(980680,11,6822.7,-1565.5,353.484,0,0,1,0,100,0),
(980680,12,6829.57,-1570.32,353.08,0,0,1,0,100,0),
(980680,13,6834.59,-1572.79,352.903,0,0,1,0,100,0),
(980680,14,6820.95,-1570.42,353.093,0,0,1,0,100,0),
(980680,15,6828.87,-1585.12,352.117,0,0,1,0,100,0),
(980680,16,6818.74,-1590.86,352.139,0,0,1,0,100,0),
(980680,17,6816.03,-1596.81,352.185,0,0,1,0,100,0),
(980680,18,6816.68,-1605.53,352.182,0,0,1,0,100,0),
(980680,19,6811.02,-1609.39,351.719,0,0,1,0,100,0),
(980680,20,6804.13,-1608.21,352.531,0,0,1,0,100,0),
(980680,21,6809.44,-1614.67,351.275,0,0,1,0,100,0),
(980680,22,6810.19,-1621.03,350.67,0,0,1,0,100,0),
(980680,23,6806.37,-1626.93,349.945,0,0,1,0,100,0),
(980680,24,6799.56,-1630.91,348.916,0,0,1,0,100,0),
(980680,25,6790.9,-1632.74,350.188,0,0,1,0,100,0),
(980680,26,6791.53,-1637.35,349.782,0,0,1,0,100,0),
(980680,27,6797.93,-1639.03,348.206,0,0,1,0,100,0),
(980680,28,6803.05,-1631.66,348.645,0,0,1,0,100,0),
(980680,29,6808.53,-1623.54,350.241,0,0,1,0,100,0),
(980680,30,6815.43,-1621.56,350.811,0,0,1,0,100,0),
(980680,31,6810.12,-1611.66,351.51,0,0,1,0,100,0),
(980680,32,6816.66,-1605.18,352.246,0,0,1,0,100,0),
(980680,33,6821.9,-1600.91,352.117,0,0,1,0,100,0),
(980680,34,6820.92,-1591.16,352.058,0,0,1,0,100,0),
(980680,35,6823.45,-1582.67,352.102,0,0,1,0,100,0),
(980680,36,6817.89,-1569.32,353.029,0,0,1,0,100,0),
(980680,37,6813.44,-1575.43,353.005,0,0,1,0,100,0),
(980680,38,6806.33,-1575.09,353.392,0,0,1,0,100,0),

(980690,1,6793.76,-1524.01,359.611,0,0,1,0,100,0),
(980690,2,6784.57,-1518.4,362.223,0,0,1,0,100,0),
(980690,3,6791.52,-1512.72,360.638,0,0,1,0,100,0),
(980690,4,6798.97,-1509.37,359.107,0,0,1,0,100,0),
(980690,5,6804.17,-1501.5,358.81,0,0,1,0,100,0),
(980690,6,6802.04,-1492.32,359.427,0,0,1,0,100,0),
(980690,7,6799.61,-1484.16,360.262,0,0,1,0,100,0),
(980690,8,6798.06,-1478.17,361.102,0,0,1,0,100,0),
(980690,9,6814.37,-1486.88,358.499,0,0,1,0,100,0),
(980690,10,6817.83,-1472.83,359.391,0,0,1,0,100,0),
(980690,11,6815.61,-1466.56,360.007,0,0,1,0,100,0),
(980690,12,6812.43,-1459.8,361.486,0,0,1,0,100,0),
(980690,13,6817.66,-1468.87,359.792,0,0,1,0,100,0),
(980690,14,6822.57,-1477.22,358.668,0,0,1,0,100,0),
(980690,15,6824.64,-1486.7,357.826,0,0,1,0,100,0),
(980690,16,6831.7,-1479.31,358.961,0,0,1,0,100,0),
(980690,17,6835.53,-1477.15,359.409,0,0,1,0,100,0),
(980690,18,6841.78,-1475.26,360.369,0,0,1,0,100,0),
(980690,19,6838.42,-1486.52,358.105,0,0,1,0,100,0),
(980690,20,6848,-1495.39,357.008,0,0,1,0,100,0),
(980690,21,6838.56,-1495.7,356.873,0,0,1,0,100,0),
(980690,22,6829.91,-1496.9,356.979,0,0,1,0,100,0),
(980690,23,6833.98,-1506.42,356.48,0,0,1,0,100,0),
(980690,24,6838.81,-1515.74,356.157,0,0,1,0,100,0),
(980690,25,6828.82,-1519.92,356.267,0,0,1,0,100,0),
(980690,26,6821.57,-1523.16,356.42,0,0,1,0,100,0),
(980690,27,6814.63,-1526.19,356.694,0,0,1,0,100,0),
(980690,28,6805.15,-1524.91,357.434,0,0,1,0,100,0);


-- Injured Goblin Miners

UPDATE `creature_template` SET `AIName` = 'SmartAI', `ScriptName` = '' WHERE `entry` = 29434;
UPDATE `creature` SET `position_x` = 6630.9, `position_y` = -1250.24, `position_z` = 396.158, `orientation` = 2.59883 WHERE `guid` = 202337;
DELETE FROM `script_waypoint` WHERE `entry` = 29434;

DELETE FROM `creature` WHERE `guid` IN (209187,209188);
INSERT INTO `creature` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `modelid`, `equipment_id`, `position_x`, `position_y`, `position_z`, `orientation`, `spawntimesecs`, `spawndist`, `currentwaypoint`, `curhealth`, `curmana`, `MovementType`, `npcflag`, `unit_flags`, `dynamicflags`, `ScriptName`, `VerifiedBuild`)
VALUES
(209187,29434,571,0,0,1,1,0,1,6691.62,-1166.98,398.74,0.427999,25,0,0,9416,0,0,0,0,0,'',0),
(209188,29434,571,0,0,1,1,0,1,6646.12,-1111.91,427.331,6.108,25,0,0,9416,0,0,0,0,0,'',0);

DELETE FROM `conditions` WHERE `SourceTypeOrReferenceId` = 15 AND `SourceGroup` = 9859;
INSERT INTO `conditions` (`SourceTypeOrReferenceId`, `SourceGroup`, `SourceEntry`, `SourceId`, `ElseGroup`, `ConditionTypeOrReference`, `ConditionTarget`, `ConditionValue1`, `ConditionValue2`, `ConditionValue3`, `NegativeCondition`, `ErrorType`, `ErrorTextId`, `ScriptName`, `Comment`)
VALUES
(15,9859,0,0,0,9,0,12832,0,0,0,0,0,'','Injured Goblin Miner - Show gossip option only during quest ''Bitter Departure''');

DELETE FROM `smart_scripts` WHERE `entryorguid` IN (-202337,-209187,-209188) AND `source_type` = 0;
DELETE FROM `smart_scripts` WHERE `entryorguid` = 2943400 AND `source_type` = 9;
INSERT INTO `smart_scripts` (`entryorguid`,`source_type`,`id`,`link`,`event_type`,`event_phase_mask`,`event_chance`,`event_flags`,`event_param1`,`event_param2`,`event_param3`,`event_param4`,`action_type`,`action_param1`,`action_param2`,`action_param3`,`action_param4`,`action_param5`,`action_param6`,`target_type`,`target_param1`,`target_param2`,`target_param3`,`target_x`,`target_y`,`target_z`,`target_o`,`comment`) VALUES
(-202337,0,0,0,11,0,100,0,0,0,0,0,90,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Respawn - Set ''UNIT_STAND_STATE_DEAD'''),
(-202337,0,1,2,19,0,100,0,12832,0,0,0,91,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Accepted Quest ''Bitter Departure'' - Remove ''UNIT_STAND_STATE_DEAD'''),
(-202337,0,2,3,61,0,100,0,0,0,0,0,1,0,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Say Line 0'),
(-202337,0,3,0,61,0,100,0,0,0,0,0,66,0,0,0,0,0,0,7,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Set Orientation (Invoker)'),
(-202337,0,4,5,62,0,100,0,9859,0,0,0,48,1,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Gossip Select 0 - Set Active On'),
(-202337,0,5,6,61,0,100,0,0,0,0,0,91,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Remove ''UNIT_STAND_STATE_DEAD'''),
(-202337,0,6,7,61,0,100,0,0,0,0,0,64,1,0,0,0,0,0,7,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Store Target List (Invoker)'),
(-202337,0,7,8,61,0,100,0,0,0,0,0,83,3,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Remove NPC Flags ''Gossip'' & ''Quest Giver'''),
(-202337,0,8,9,61,0,100,0,0,0,0,0,2,250,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Set Faction 250'),
(-202337,0,9,0,61,0,100,0,0,0,0,0,53,1,2943400,0,0,0,2,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Start Waypoint Movement (React State ''Aggressive'')'),
(-202337,0,10,0,58,0,100,0,0,0,0,0,80,2943400,2,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Waypoint Ended - Call Timed Action List'),
(-202337,0,11,0,6,0,100,0,0,0,0,0,6,12832,0,0,0,0,0,12,1,0,0,0,0,0,0,'Injured Goblin Miner - On Just Died - Fail Quest ''Bitter Departure'' (Stored Target)'),

(-209187,0,0,0,11,0,100,0,0,0,0,0,90,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Respawn - Set ''UNIT_STAND_STATE_DEAD'''),
(-209187,0,1,2,19,0,100,0,12832,0,0,0,91,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Accepted Quest ''Bitter Departure'' - Remove ''UNIT_STAND_STATE_DEAD'''),
(-209187,0,2,3,61,0,100,0,0,0,0,0,1,0,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Say Line 0'),
(-209187,0,3,0,61,0,100,0,0,0,0,0,66,0,0,0,0,0,0,7,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Set Orientation (Invoker)'),
(-209187,0,4,5,62,0,100,0,9859,0,0,0,48,1,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Gossip Select 0 - Set Active On'),
(-209187,0,5,6,61,0,100,0,0,0,0,0,91,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Remove ''UNIT_STAND_STATE_DEAD'''),
(-209187,0,6,7,61,0,100,0,0,0,0,0,64,1,0,0,0,0,0,7,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Store Target List (Invoker)'),
(-209187,0,7,8,61,0,100,0,0,0,0,0,83,3,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Remove NPC Flags ''Gossip'' & ''Quest Giver'''),
(-209187,0,8,9,61,0,100,0,0,0,0,0,2,250,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Set Faction 250'),
(-209187,0,9,0,61,0,100,0,0,0,0,0,53,1,2943401,0,0,0,2,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Start Waypoint Movement (React State ''Aggressive'')'),
(-209187,0,10,0,58,0,100,0,0,0,0,0,80,2943400,2,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Waypoint Ended - Call Timed Action List'),
(-209187,0,11,0,6,0,100,0,0,0,0,0,6,12832,0,0,0,0,0,12,1,0,0,0,0,0,0,'Injured Goblin Miner - On Just Died - Fail Quest ''Bitter Departure'' (Stored Target)'),

(-209188,0,0,0,11,0,100,0,0,0,0,0,90,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Respawn - Set ''UNIT_STAND_STATE_DEAD'''),
(-209188,0,1,2,19,0,100,0,12832,0,0,0,91,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Accepted Quest ''Bitter Departure'' - Remove ''UNIT_STAND_STATE_DEAD'''),
(-209188,0,2,3,61,0,100,0,0,0,0,0,1,0,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Say Line 0'),
(-209188,0,3,0,61,0,100,0,0,0,0,0,66,0,0,0,0,0,0,7,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Set Orientation (Invoker)'),
(-209188,0,4,5,62,0,100,0,9859,0,0,0,48,1,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Gossip Select 0 - Set Active On'),
(-209188,0,5,6,61,0,100,0,0,0,0,0,91,7,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Remove ''UNIT_STAND_STATE_DEAD'''),
(-209188,0,6,7,61,0,100,0,0,0,0,0,64,1,0,0,0,0,0,7,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Store Target List (Invoker)'),
(-209188,0,7,8,61,0,100,0,0,0,0,0,83,3,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Remove NPC Flags ''Gossip'' & ''Quest Giver'''),
(-209188,0,8,9,61,0,100,0,0,0,0,0,2,250,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Set Faction 250'),
(-209188,0,9,0,61,0,100,0,0,0,0,0,53,1,2943402,0,0,0,2,1,0,0,0,0,0,0,0,'Injured Goblin Miner - Linked - Start Waypoint Movement (React State ''Aggressive'')'),
(-209188,0,10,0,58,0,100,0,0,0,0,0,80,2943400,2,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Waypoint Ended - Call Timed Action List'),
(-209188,0,11,0,6,0,100,0,0,0,0,0,6,12832,0,0,0,0,0,12,1,0,0,0,0,0,0,'Injured Goblin Miner - On Just Died - Fail Quest ''Bitter Departure'' (Stored Target)'),

(2943400,9,0,0,0,0,100,0,0,0,0,0,66,0,0,0,0,0,0,12,1,0,0,0,0,0,0,'Injured Goblin Miner - On Script - Set Orientation (Stored Target)'),
(2943400,9,1,0,0,0,100,0,1000,1000,0,0,1,1,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Script - Say Line 1'),
(2943400,9,2,0,0,0,100,0,0,0,0,0,15,12832,0,0,0,0,0,12,1,0,0,0,0,0,0,'Injured Goblin Miner - On Script - Quest Credit ''Bitter Departure'' (Stored Target)'),
(2943400,9,3,0,0,0,100,0,5000,5000,0,0,69,55,0,0,0,0,0,8,0,0,0,6404.84,-1030.53,425.215,0,'Injured Goblin Miner - On Script - Move to Position'),
(2943400,9,4,0,0,0,100,0,5000,5000,0,0,41,0,0,0,0,0,0,1,0,0,0,0,0,0,0,'Injured Goblin Miner - On Script - Force Despawn');

DELETE FROM `waypoints` WHERE `entry` IN (2943400,2943401,2943402);
INSERT INTO `waypoints` (`entry`, `pointid`, `position_x`, `position_y`, `position_z`, `point_comment`)
VALUES
(2943400,1,6630.9,-1250.24,396.158,'Injured Goblin Miner - Path 1'),
(2943400,2,6636.94,-1252.83,395.515,''),
(2943400,3,6642.93,-1255.12,396.739,''),
(2943400,4,6653.43,-1259.34,397.054,''),
(2943400,5,6661.06,-1260.11,396.752,''),
(2943400,6,6666.82,-1256.6,396.358,''),
(2943400,7,6670.32,-1249.39,395.916,''),
(2943400,8,6670.43,-1242.44,397.338,''),
(2943400,9,6667.77,-1235.48,398.454,''),
(2943400,10,6663.29,-1227.83,399.517,''),
(2943400,11,6661.88,-1223.62,399.605,''),
(2943400,12,6662.03,-1219.08,399.259,''),
(2943400,13,6660.49,-1215.93,399.497,''),
(2943400,14,6656.63,-1210.38,399.817,''),
(2943400,15,6654.45,-1201.55,399.526,''),
(2943400,16,6655.82,-1190.88,398.76,''),
(2943400,17,6659.34,-1179.03,398.76,''),
(2943400,18,6663.06,-1167.6,398.697,''),
(2943400,19,6666.78,-1155.69,398.097,''),
(2943400,20,6671.45,-1145.52,398.125,''),
(2943400,21,6673.88,-1133.91,397.099,''),
(2943400,22,6677.99,-1121.77,397.126,''),
(2943400,23,6682.46,-1110.49,397.033,''),
(2943400,24,6685.58,-1101.34,396.279,''),
(2943400,25,6683.75,-1090.87,395.828,''),
(2943400,26,6677.66,-1088.04,397.732,''),
(2943400,27,6670.44,-1085.06,399.645,''),
(2943400,28,6665.07,-1080.05,401.212,''),
(2943400,29,6662.98,-1074.12,402.998,''),
(2943400,30,6665.29,-1067.52,404.62,''),
(2943400,31,6671.53,-1059.54,406.142,''),
(2943400,32,6676.84,-1053.17,407.182,''),
(2943400,33,6678.34,-1043.88,409.474,''),
(2943400,34,6677.61,-1036.92,411.526,''),
(2943400,35,6671.8,-1031.37,413.122,''),
(2943400,36,6665.94,-1025.02,414.775,''),
(2943400,37,6659.51,-1016.44,414.823,''),
(2943400,38,6651.64,-1012.94,417.578,''),
(2943400,39,6640.62,-1013.38,421.786,''),
(2943400,40,6630.16,-1014.22,423.779,''),
(2943400,41,6620.38,-1014.93,424.316,''),
(2943400,42,6611.53,-1015.02,427.563,''),
(2943400,43,6601.79,-1015.9,428.737,''),
(2943400,44,6588.95,-1018.34,430.184,''),
(2943400,45,6577.59,-1019.43,432.311,''),
(2943400,46,6565.75,-1020.53,433.575,''),
(2943400,47,6554.54,-1023.29,433.452,''),
(2943400,48,6544.71,-1027.29,433.058,''),
(2943400,49,6534.61,-1029.6,432.752,''),
(2943400,50,6523.73,-1030.5,433.251,''),
(2943400,51,6512.65,-1030.56,435.558,''),
(2943400,52,6502.94,-1031.84,436.044,''),
(2943400,53,6493.76,-1029.74,435.614,''),
(2943400,54,6483.93,-1028.38,434.219,''),
(2943400,55,6473.55,-1027.1,434.46,''),
(2943400,56,6465.75,-1026.49,433.765,''),
(2943400,57,6451.81,-1025.43,431.502,''),

(2943401,1,6691.62,-1166.98,398.74,'Injured Goblin Miner - Path 2'),
(2943401,2,6681.27,-1161.11,398.374,''),
(2943401,3,6676.8,-1156.97,398.109,''),
(2943401,4,6674.58,-1150.37,398.097,''),
(2943401,5,6672.93,-1143.34,397.981,''),
(2943401,6,6673.64,-1134.95,397.098,''),
(2943401,7,6676.67,-1125.9,397.098,''),
(2943401,8,6679.77,-1118.34,397.098,''),
(2943401,9,6682.99,-1109.71,397.003,''),
(2943401,10,6685.18,-1101.85,396.432,''),
(2943401,11,6684.32,-1093.62,396.019,''),
(2943401,12,6678.83,-1088.07,397.717,''),
(2943401,13,6671.29,-1084.13,399.935,''),
(2943401,14,6665.29,-1079.17,401.521,''),
(2943401,15,6663.37,-1073.1,403.355,''),
(2943401,16,6666.26,-1066.08,404.724,''),
(2943401,17,6672.05,-1058.18,406.363,''),
(2943401,18,6676.35,-1051.17,407.57,''),
(2943401,19,6678.49,-1042.47,409.756,''),
(2943401,20,6676.52,-1035.37,412.154,''),
(2943401,21,6670.64,-1029.07,413.926,''),
(2943401,22,6662.8,-1023.21,414.812,''),
(2943401,23,6655.28,-1017.88,415.732,''),
(2943401,24,6648.4,-1014.24,418.825,''),
(2943401,25,6640.83,-1013.21,421.716,''),
(2943401,26,6633.5,-1013.48,423.774,''),
(2943401,27,6624.31,-1014.11,424.033,''),
(2943401,28,6614.99,-1014.54,426.688,''),
(2943401,29,6606.21,-1015.62,428.27,''),
(2943401,30,6596.4,-1017.1,429.275,''),
(2943401,31,6586.7,-1018.43,430.863,''),
(2943401,32,6576.33,-1021.14,432.45,''),
(2943401,33,6566.7,-1022.89,433.574,''),
(2943401,34,6557.01,-1024.36,433.472,''),
(2943401,35,6547.04,-1027.21,433.036,''),
(2943401,36,6537.87,-1029.5,432.485,''),
(2943401,37,6528.03,-1031.42,432.965,''),
(2943401,38,6517.97,-1030.28,433.66,''),
(2943401,39,6509.17,-1029.27,436.394,''),
(2943401,40,6500.43,-1029.16,436.934,''),
(2943401,41,6493.08,-1029.14,435.476,''),
(2943401,42,6485.17,-1028.62,434.268,''),
(2943401,43,6476.41,-1027.85,434.392,''),
(2943401,44,6467.45,-1027.22,433.938,''),
(2943401,45,6451.81,-1025.43,431.502,''),

(2943402,1,6646.12,-1111.91,427.333,'Injured Goblin Miner - Path 3'),
(2943402,2,6641.28,-1109.86,427.018,''),
(2943402,3,6639.6,-1107.49,427.446,''),
(2943402,4,6640.17,-1104.28,426.943,''),
(2943402,5,6643.59,-1102.81,426.271,''),
(2943402,6,6651.91,-1103.95,424.587,''),
(2943402,7,6660.17,-1107,422.845,''),
(2943402,8,6667.57,-1111.9,423.728,''),
(2943402,9,6672.04,-1118.99,424.471,''),
(2943402,10,6671.27,-1127.23,423.752,''),
(2943402,11,6664.88,-1132.07,422.862,''),
(2943402,12,6657.88,-1131.42,420.963,''),
(2943402,13,6649.67,-1129.19,418.211,''),
(2943402,14,6640.5,-1127.03,416.856,''),
(2943402,15,6631.06,-1127.09,415.75,''),
(2943402,16,6623.81,-1124.22,412.966,''),
(2943402,17,6619.03,-1117.02,411.954,''),
(2943402,18,6615.87,-1108.49,411.232,''),
(2943402,19,6616.47,-1099.42,411.141,''),
(2943402,20,6616.45,-1089.16,412.495,''),
(2943402,21,6617.36,-1081.09,414.671,''),
(2943402,22,6621.79,-1073.01,415.079,''),
(2943402,23,6626.65,-1064.5,414.711,''),
(2943402,24,6630.33,-1056.72,415.189,''),
(2943402,25,6632.11,-1047.6,416.516,''),
(2943402,26,6630.44,-1038.92,421.234,''),
(2943402,27,6625.94,-1030.35,423.343,''),
(2943402,28,6621.02,-1022.58,423.878,''),
(2943402,29,6614.45,-1015.99,426.85,''),
(2943402,30,6605.01,-1012.53,428.533,''),
(2943402,31,6595.49,-1010.98,429.473,''),
(2943402,32,6585.65,-1012.74,431.971,''),
(2943402,33,6575.47,-1016.45,433.811,''),
(2943402,34,6565.44,-1019.92,433.574,''),
(2943402,35,6556.13,-1022.97,433.47,''),
(2943402,36,6547.14,-1026.57,433.079,''),
(2943402,37,6538.49,-1029.73,432.356,''),
(2943402,38,6529.72,-1030.58,432.886,''),
(2943402,39,6519.4,-1029.54,433.607,''),
(2943402,40,6510.33,-1028.8,436.316,''),
(2943402,41,6500.42,-1028.45,437.175,''),
(2943402,42,6492.37,-1028.61,435.288,''),
(2943402,43,6485.28,-1028.13,434.34,''),
(2943402,44,6478.12,-1027.1,434.323,''),
(2943402,45,6469.27,-1026.56,434.135,''),
(2943402,46,6451.81,-1025.43,431.502,'');

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
