-- DB update 2021_05_07_03 -> 2021_05_08_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_05_07_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_05_07_03 2021_05_08_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1619824902615694400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1619824902615694400');

UPDATE `conditions` SET `comment` = 'Manual: Heavy Frostweave Bandage - Require Skill 129 Level 390 to drop' WHERE `ConditionTypeOrReference` = 7 AND `SourceGroup` IN (26550, 26553, 26554, 26555, 26668, 26669, 26687, 26693, 26861, 27633, 27635, 27871, 27960, 27969, 28022, 28023, 28026, 28035, 28036, 28080, 28081, 28101, 28108, 28123, 28124, 28158, 28188, 28255, 28257, 28268, 28373, 28388, 28402, 28403, 28412, 28414, 28417, 28418, 28465, 28494, 28495, 28504, 28538, 28565, 28575, 28578, 28579, 28586, 28587, 28600, 28602, 28641, 28802, 28861, 28916, 28923, 28988, 29123, 29129, 29133, 29211, 29235, 29236, 29237, 29266, 29304, 29305, 29306, 29312, 29313, 29314, 29315, 29316, 29323, 29329, 29331, 29338, 29370, 29404, 29407, 29409, 29413, 29449, 29450, 29451, 29554, 29586, 29622, 29623, 29646, 29652, 29654, 29656, 29697, 29699, 29717, 29719, 29720, 29792, 29793, 29819, 29820, 29822, 29826, 29832, 29834, 29843, 29880, 29932, 30037, 30202, 30204, 30205, 30250, 30333, 30453, 30510, 30529, 30530, 30532, 30540, 30660, 30666, 30667, 30668, 30680, 30681, 30682, 30687, 30689, 30695, 30701, 30748, 30774, 30788, 30807, 30810, 30856, 30860, 30863, 30865, 30892, 30893, 30894, 30920, 30921, 30951, 30998, 31134, 31140, 31150, 31152, 31231, 31258, 31349, 31350, 31360, 31362, 31368, 31370, 31402, 31403, 31456, 31463, 31464, 31465, 31469, 31506, 31507, 31508, 31509, 31510, 31511, 31512, 31533, 31536, 31537, 31538, 31610, 31611, 31612, 31674, 31718, 31738, 31746, 31754, 31779, 31847, 32191, 32255, 32259, 32276, 32278, 32289, 32353, 32507) AND `SourceEntry` = 39152;
UPDATE `conditions` SET `comment` = 'A Guide to Northern Cloth Scavenging - Require Skill 197 Level 325 to drop' WHERE `ConditionTypeOrReference` = 7 AND `SourceGroup` IN (25026, 25316, 26268, 26280, 26529, 26530, 26532, 26630, 26631, 26632, 26668, 26687, 26693, 26723, 26731, 26763, 26861, 27206, 27284, 27357, 27483, 28586, 28587, 28684, 28921, 28923, 29120, 29266, 29304, 29305, 29306, 29308, 29309, 29310, 29311, 29312, 29313, 29314, 29315, 29316, 29932, 30510, 30529, 30530, 30532, 30540, 30748, 30774, 30788, 30807, 30810, 31134, 31211, 31212, 31215, 31349, 31350, 31360, 31362, 31368, 31370, 31456, 31463, 31464, 31465, 31469, 31506, 31507, 31508, 31509, 31510, 31511, 31512, 31533, 31536, 31537, 31538, 31610, 31611, 31612, 31674, 32313) AND `SourceEntry` = 43876;

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
