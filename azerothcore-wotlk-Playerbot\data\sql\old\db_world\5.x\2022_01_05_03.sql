-- DB update 2022_01_05_02 -> 2022_01_05_03
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_05_02';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_05_02 2022_01_05_03 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641403331598101028'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641403331598101028');

-- <PERSON><PERSON> "Full type2 respawn for Cave Yeti's and Ferocious Yeti's"
DELETE FROM `creature` WHERE `guid` IN 
(16306,16328,16369,16389,16391,16393,16480,16503,16504,16505,16512,16521,16540,16548,16550,16555,16558,16311,16330,16346,16360,16367,16380,16388,16491,16497,16499,16515,16528,16533,16537,16542,16546,16552);
INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(16306, 2248, 0, 0, 0, 1, 1, 0, 0, -297.92392, -300.8097, 43.50025, 5.948596000671386718, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -297.92392 -300.8097 43.50025
(16328, 2248, 0, 0, 0, 1, 1, 0, 0, -235.83813, -327.89664, 59.58117, 6.157728195190429687, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -235.83813 -327.89664 59.58117
(16369, 2248, 0, 0, 0, 1, 1, 0, 0, -265.1622, -383.8915, 66.944664, 2.798336744308471679, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -265.1622 -383.8915 66.944664
(16389, 2248, 0, 0, 0, 1, 1, 0, 0, -250.4255, -421.26474, 69.73594, 4.044747352600097656, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -250.4255 -421.26474 69.73594
(16391, 2248, 0, 0, 0, 1, 1, 0, 0, -316.47, -382.14932, 64.873215, 4.383516311645507812, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -316.47 -382.14932 64.873215
(16393, 2248, 0, 0, 0, 1, 1, 0, 0, -281.67892, -415.94934, 67.8656, 3.141989946365356445, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -281.67892 -415.94934 67.8656
(16480, 2248, 0, 0, 0, 1, 1, 0, 0, -204.44112, -368.9501, 72.963486, 0.802851438522338867, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -204.44112 -368.9501 72.963486
(16503, 2248, 0, 0, 0, 1, 1, 0, 0, -173.13818, -329.95786, 52.983875, 2.98076486587524414, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -173.13818 -329.95786 52.983875
(16504, 2248, 0, 0, 0, 1, 1, 0, 0, -265.55658, -360.2198, 66.68646, 1.479270339012145996, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -265.55658 -360.2198 66.68646
(16505, 2248, 0, 0, 0, 1, 1, 0, 0, -184.32733, -339.42188, 53.319458, 5.557514190673828125, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -184.32733 -339.42188 53.319458
(16512, 2248, 0, 0, 0, 1, 1, 0, 0, -134.28261, -369.06696, 52.82077, 4.751373767852783203, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -134.28261 -369.06696 52.82077
(16521, 2248, 0, 0, 0, 1, 1, 0, 0, -226.97736, -357.35315, 48.509075, 5.50786590576171875, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -226.97736 -357.35315 48.509075
(16540, 2248, 0, 0, 0, 1, 1, 0, 0, -277.20895, -303.78354, 42.006165, 4.127336978912353515, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -277.20895 -303.78354 42.006165
(16548, 2248, 0, 0, 0, 1, 1, 0, 0, -307.32935, -277.53445, 45.868458, 4.479941844940185546, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -307.32935 -277.53445 45.868458
(16550, 2248, 0, 0, 0, 1, 1, 0, 0, -282.88504, -283.57785, 51.867973, 5.213503837585449218, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -282.88504 -283.57785 51.867973
(16555, 2248, 0, 0, 0, 1, 1, 0, 0, -284.29575, -448.09567, 64.274994, 3.63948988914489746, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -284.29575 -448.09567 64.274994
(16558, 2248, 0, 0, 0, 1, 1, 0, 0, -252.41022, -450.73666, 65.71086, 0.371946781873703002, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -252.41022 -450.73666 65.71086
(16552, 2248, 0, 0, 0, 1, 1, 0, 0, -315.9529, -415.5095, 62.598007, 1.447588801383972167, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -315.9529 -415.5095 62.598007	
(16311, 2249, 0, 0, 0, 1, 1, 0, 0, -261.57654, -339.48932, 64.745346, 4.419243335723876953, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -261.57654 -339.48932 64.745346
(16330, 2249, 0, 0, 0, 1, 1, 0, 0, -191.17932, -346.08893, 72.963486, 1.01655280590057373, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -191.17932 -346.08893 72.963486
(16346, 2249, 0, 0, 0, 1, 1, 0, 0, -253.6798, -323.69928, 44.741364, 5.363449573516845703, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -253.6798 -323.69928 44.741364
(16360, 2249, 0, 0, 0, 1, 1, 0, 0, -233.25084, -349.45862, 48.12525, 3.098873376846313476, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0), -- .go xyz -233.25084 -349.45862 48.12525
(16367, 2249, 0, 0, 0, 1, 1, 0, 0, -231.66393, -382.12653, 70.16933, 0.226892799139022827, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -231.66393 -382.12653 70.16933
(16380, 2249, 0, 0, 0, 1, 1, 0, 0, -177.80458, -377.7014, 51.625637, 2.283098459243774414, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -177.80458 -377.7014 51.625637
(16388, 2249, 0, 0, 0, 1, 1, 0, 0, -186.86464, -322.34235, 73.01433, 3.622419118881225585, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -186.86464 -322.34235 73.01433
(16491, 2249, 0, 0, 0, 1, 1, 0, 0, -211.52203, -339.07535, 57.005615, 2.368273735046386718, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -211.52203 -339.07535 57.005615
(16497, 2249, 0, 0, 0, 1, 1, 0, 0, -175.85954, -341.29297, 52.969337, 4.2758636474609375, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0), -- .go xyz -175.85954 -341.29297 52.969337
(16499, 2249, 0, 0, 0, 1, 1, 0, 0, -146.02986, -324.1747, 52.893974, 2.742749214172363281, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -146.02986 -324.1747 52.893974
(16515, 2249, 0, 0, 0, 1, 1, 0, 0, -133.04782, -353.9987, 52.852203, 2.338741064071655273, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -133.04782 -353.9987 52.852203
(16528, 2249, 0, 0, 0, 1, 1, 0, 0, -240.76129, -368.051, 48.232735, 6.083996295928955078, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -240.76129 -368.051 48.232735
(16533, 2249, 0, 0, 0, 1, 1, 0, 0, -244.32742, -299.69788, 53.306503, 1.712427139282226562, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0), -- .go xyz -244.32742 -299.69788 53.306503
(16537, 2249, 0, 0, 0, 1, 1, 0, 0, -312.87793, -304.6967, 43.82531, 6.082273006439208984, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -312.87793 -304.6967 43.82531
(16542, 2249, 0, 0, 0, 1, 1, 0, 0, -250.76631, -287.36203, 53.001698, 1.39348602294921875, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0), -- .go xyz -250.76631 -287.36203 53.001698
(16546, 2249, 0, 0, 0, 1, 1, 0, 0, -263.19812, -280.61224, 52.30829, 5.942534446716308593, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0); -- .go xyz -263.19812 -280.61224 52.30829

DELETE FROM `creature_addon` WHERE `guid` IN 
(16306,16328,16369,16389,16391,16393,16480,16503,16504,16505,16512,16521,16540,16548,16550,16555,16558,16311,16330,16346,16360,16367,16380,16388,16491,16497,16499,16515,16528,16533,16537,16542,16546,16552);
DELETE FROM `waypoint_data` WHERE `id` IN (164800,165520);

-- Pathing for Ferocious Yeti Entry: 2249
SET @NPC := 16497;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-176.07104,-341.7463,52.872036,0,0,0,0,100,0),
(@PATH,2,-169.51015,-323.0471,52.878174,0,0,0,0,100,0),
(@PATH,3,-148.36299,-324.12222,52.74871,0,0,0,0,100,0),
(@PATH,4,-136.87291,-334.44168,52.785267,0,0,0,0,100,0),
(@PATH,5,-135.52174,-354.87268,52.882423,0,0,0,0,100,0),
(@PATH,6,-141.0634,-366.0482,52.5152,0,0,0,0,100,0),
(@PATH,7,-158.92867,-375.29666,51.987953,0,0,0,0,100,0),
(@PATH,8,-182.36055,-378.16635,51.28596,0,0,0,0,100,0),
(@PATH,9,-208.20155,-366.30664,49.279476,0,0,0,0,100,0),
(@PATH,10,-223.51387,-369.43484,48.425747,0,0,0,0,100,0),
(@PATH,11,-232.35481,-370.80872,48.229324,0,0,0,0,100,0),
(@PATH,12,-235.35847,-360.67593,48.420048,0,0,0,0,100,0),
(@PATH,13,-245.57408,-375.20395,47.751385,0,0,0,0,100,0),
(@PATH,14,-235.35847,-360.67593,48.420048,0,0,0,0,100,0),
(@PATH,15,-232.35481,-370.80872,48.229324,0,0,0,0,100,0),
(@PATH,16,-223.51387,-369.43484,48.425747,0,0,0,0,100,0),
(@PATH,17,-208.20155,-366.30664,49.279476,0,0,0,0,100,0),
(@PATH,18,-182.36055,-378.16635,51.28596,0,0,0,0,100,0),
(@PATH,19,-158.92867,-375.29666,51.987953,0,0,0,0,100,0),
(@PATH,20,-141.0634,-366.0482,52.5152,0,0,0,0,100,0),
(@PATH,21,-135.52174,-354.87268,52.882423,0,0,0,0,100,0),
(@PATH,22,-136.87291,-334.44168,52.785267,0,0,0,0,100,0),
(@PATH,23,-148.36299,-324.12222,52.74871,0,0,0,0,100,0),
(@PATH,24,-169.51015,-323.0471,52.878174,0,0,0,0,100,0);

-- Pathing for Ferocious Yeti Entry: 2249
SET @NPC := 16360;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-236.80482,-349.3067,48.00849,0,0,0,0,100,0),
(@PATH,2,-228.86038,-371.95932,48.17719,0,0,0,0,100,0),
(@PATH,3,-209.40927,-363.79385,49.66804,0,0,0,0,100,0),
(@PATH,4,-207.30122,-344.808,54.412838,0,0,0,0,100,0),
(@PATH,5,-213.50728,-335.34402,57.859627,0,0,0,0,100,0),
(@PATH,6,-231.96729,-325.83267,58.319298,0,0,0,0,100,0),
(@PATH,7,-254.43498,-331.71008,62.735683,0,0,0,0,100,0),
(@PATH,8,-268.08243,-353.10352,66.26643,0,0,0,0,100,0),
(@PATH,9,-265.55386,-385.31458,66.9522,0,0,0,0,100,0),
(@PATH,10,-229.93903,-385.83084,70.145805,0,0,0,0,100,0),
(@PATH,11,-204.79166,-373.83795,72.88037,0,0,0,0,100,0),
(@PATH,12,-186.73639,-352.40695,73.02463,0,0,0,0,100,0),
(@PATH,13,-187.55948,-343.1726,72.89425,0,0,0,0,100,0),
(@PATH,14,-188.91678,-327.9449,72.89116,0,0,0,0,100,0),
(@PATH,15,-179.79608,-321.7788,73.08281,0,0,0,0,100,0),
(@PATH,16,-205.62408,-321.0629,72.9294,0,0,0,0,100,0),
(@PATH,17,-179.79608,-321.7788,73.08281,0,0,0,0,100,0),
(@PATH,18,-188.91678,-327.9449,72.89116,0,0,0,0,100,0),
(@PATH,19,-187.55948,-343.1726,72.89425,0,0,0,0,100,0),
(@PATH,20,-186.73639,-352.40695,73.02463,0,0,0,0,100,0),
(@PATH,21,-204.79166,-373.83795,72.88037,0,0,0,0,100,0),
(@PATH,22,-229.93903,-385.83084,70.145805,0,0,0,0,100,0),
(@PATH,23,-265.55386,-385.31458,66.9522,0,0,0,0,100,0),
(@PATH,24,-268.08243,-353.10352,66.26643,0,0,0,0,100,0),
(@PATH,25,-254.43498,-331.71008,62.735683,0,0,0,0,100,0),
(@PATH,26,-231.96729,-325.83267,58.319298,0,0,0,0,100,0),
(@PATH,27,-213.50728,-335.34402,57.859627,0,0,0,0,100,0),
(@PATH,28,-207.30122,-344.808,54.412838,0,0,0,0,100,0),
(@PATH,29,-209.40927,-363.79385,49.66804,0,0,0,0,100,0),
(@PATH,30,-228.86038,-371.95932,48.17719,0,0,0,0,100,0);

-- Pathing for Ferocious Yeti Entry: 2249
SET @NPC := 16533;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-244.51053,-298.4137,53.219738,0,0,0,0,100,0),
(@PATH,2,-265.10806,-280.22586,52.20138,0,0,0,0,100,0),
(@PATH,3,-285.18143,-268.797,50.044495,0,0,0,0,100,0),
(@PATH,4,-309.13687,-276.40756,45.9816,0,0,0,0,100,0),
(@PATH,5,-307.90262,-300.1556,43.7372,0,0,0,0,100,0),
(@PATH,6,-274.4386,-305.9394,42.2876,0,0,0,0,100,0),
(@PATH,7,-260.4003,-310.1499,42.975338,0,0,0,0,100,0),
(@PATH,8,-253.90193,-326.76505,44.79197,0,0,0,0,100,0),
(@PATH,9,-237.0129,-350.89545,48.11982,0,0,0,0,100,0),
(@PATH,10,-253.90193,-326.76505,44.79197,0,0,0,0,100,0),
(@PATH,11,-260.4003,-310.1499,42.975338,0,0,0,0,100,0),
(@PATH,12,-274.4386,-305.9394,42.2876,0,0,0,0,100,0),
(@PATH,13,-307.90262,-300.1556,43.7372,0,0,0,0,100,0),
(@PATH,14,-309.13687,-276.40756,45.9816,0,0,0,0,100,0),
(@PATH,15,-285.18143,-268.797,50.044495,0,0,0,0,100,0),
(@PATH,16,-265.10806,-280.22586,52.20138,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_05_03' WHERE sql_rev = '1641403331598101028';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
