-- DB update 2021_10_16_05 -> 2021_10_16_06
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_16_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_16_05 2021_10_16_06 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1634148788264237300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1634148788264237300');

UPDATE `quest_poi_points` SET `X`=3565, `Y`=-5381 WHERE `QuestID`=5246 AND `Idx1`=2 AND `Idx2`=0;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_16_06' WHERE sql_rev = '1634148788264237300';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
