-- DB update 2020_09_17_00 -> 2020_09_18_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_09_17_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_09_17_00 2020_09_18_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1598878994711082500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1598878994711082500');
/*
 * General: Build Update
 * Update by Knindza | <www.azerothcore.org>
 * Copyright (C) <www.shadowburn.net> & <www.lichbane.com>
*/

/* Content 3.1.x */ 
SET @Build := 9767;

UPDATE `gameobject_template` SET `VerifiedBuild` = @Build WHERE `entry` IN (194200, 194213, 194238, 194307, 194312, 194316, 194324, 194340, 194341, 194356, 194423, 194424, 194463, 194479, 194519, 194537, 194538, 194539, 194541, 194542, 194543, 194555, 194565, 194569, 194625, 194628, 194752, 194789, 194902, 195036, 195046);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
