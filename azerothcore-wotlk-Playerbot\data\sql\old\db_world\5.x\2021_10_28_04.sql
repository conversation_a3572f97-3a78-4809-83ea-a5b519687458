-- DB update 2021_10_28_03 -> 2021_10_28_04
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_28_03';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_28_03 2021_10_28_04 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1631371074636591511'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1631371074636591511');

-- -- <PERSON> the Friendly
SET @FRANKLIN_WPID := 460200;

INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`) VALUES
(@FRANKLIN_WPID,1,-8316.99,-1006.56,177.641,0,0,0,0,100,0),
(@FRANKLIN_WPID,2,-8305.2,-1011.45,172.119,0,0,0,0,100,0),
(@FRANKLIN_WPID,3,-8291.97,-1017.28,162.798,0,0,0,0,100,0),
(@FRANKLIN_WPID,4,-8278.362,-1025.672,153.739,0,0,0,0,100,0),
(@FRANKLIN_WPID,5,-8264.753,-1034.065,148.866,0,0,0,0,100,0),
(@FRANKLIN_WPID,6,-8251.152,-1042.453,146.348,0,0,0,0,100,0),
(@FRANKLIN_WPID,7,-8237.310,-1050.990,143.320,0,0,0,0,100,0),
(@FRANKLIN_WPID,8,-8223.46,-1059.57,142.632,0,0,0,0,100,0),
(@FRANKLIN_WPID,9,-8213.409,-1067.735,143.325,0,0,0,0,100,0),
(@FRANKLIN_WPID,10,-8203.453,-1075.824,146.251,0,0,0,0,100,0),
(@FRANKLIN_WPID,11,-8193.54,-1083.9,148.981,0,0,0,0,100,0),
(@FRANKLIN_WPID,12,-8183.38,-1086.576,150.362,0,0,0,0,100,0),
(@FRANKLIN_WPID,13,-8167.484,-1090.767,142.01,0,0,0,0,100,0),
(@FRANKLIN_WPID,14,-8151.57,-1095.01,135.284,0,0,0,0,100,0),
(@FRANKLIN_WPID,15,-8135.705,-1090.979,131.24,0,0,0,0,100,0),
(@FRANKLIN_WPID,16,-8119.748,-1086.925,130.316,0,0,0,0,100,0),
(@FRANKLIN_WPID,17,-8103.913,-1082.902,130.549,0,0,0,0,100,0),
(@FRANKLIN_WPID,18,-8088.07,-1078.876,129.343,0,0,0,0,100,0),
(@FRANKLIN_WPID,19,-8072.27,-1074.97,129.596,0,0,0,0,100,0),
(@FRANKLIN_WPID,20,-8056.285,-1065.265,129.378,0,0,0,0,100,0),
(@FRANKLIN_WPID,21,-8040.16,-1055.54,130.915,0,0,0,0,100,0),
(@FRANKLIN_WPID,22,-8030.153,-1041.759,130.299,0,0,0,0,100,0),
(@FRANKLIN_WPID,23,-8020.17,-1028.09,130.082,0,0,0,0,100,0),
(@FRANKLIN_WPID,24,-8011.435,-1011.860,129.200,0,0,0,0,100,0),
(@FRANKLIN_WPID,25,-8002.57,-995.689,128.183,0,0,0,0,100,0),
(@FRANKLIN_WPID,26,-7999.169,-979.507,127.616,0,0,0,0,100,0),
(@FRANKLIN_WPID,27,-7995.779,-963.324,127.869,0,0,0,0,100,0),
(@FRANKLIN_WPID,28,-7992.436,-947.095,128.584,0,0,0,0,100,0),
(@FRANKLIN_WPID,29,-7989.077,-930.786,129.41,0,0,0,0,100,0),
(@FRANKLIN_WPID,30,-7985.57,-914.513,129.488,0,0,0,0,100,0),
(@FRANKLIN_WPID,31,-7978.095,-899.513,129.657,0,0,0,0,100,0),
(@FRANKLIN_WPID,32,-7970.6,-884.63,129.096,0,0,0,0,100,0),
(@FRANKLIN_WPID,33,-7971.895,-872.093,129.856,0,0,0,0,100,0),
(@FRANKLIN_WPID,34,-7973.25,-859.507,130.788,0,0,0,0,100,0),
(@FRANKLIN_WPID,35,-7982.222,-843.672,131.085,0,0,0,0,100,0),
(@FRANKLIN_WPID,36,-7991.21,-827.825,131.034,0,0,0,0,100,0),
(@FRANKLIN_WPID,37,-8003.523,-817.578,131.838,0,0,0,0,100,0),
(@FRANKLIN_WPID,38,-8015.894,-807.285,131.929,0,0,0,0,100,0),
(@FRANKLIN_WPID,39,-8028.179,-797.063,131.616,0,0,0,0,100,0),
(@FRANKLIN_WPID,40,-8040.458,-786.846,131.146,0,0,0,0,100,0),
(@FRANKLIN_WPID,41,-8052.81,-776.616,131.194,0,0,0,0,100,0),
(@FRANKLIN_WPID,42,-8066.924,-771.172,131.246,0,0,0,0,100,0),
(@FRANKLIN_WPID,43,-8080.99,-765.609,132.48,0,0,0,0,100,0),
(@FRANKLIN_WPID,44,-8094.912,-763.642,133.229,0,0,0,0,100,0),
(@FRANKLIN_WPID,45,-8109.003,-761.652,133.502,0,0,0,0,100,0),
(@FRANKLIN_WPID,46,-8123.15,-759.628,133.418,0,0,0,0,100,0),
(@FRANKLIN_WPID,47,-8133.863,-766.926,131.14,0,0,0,0,100,0),
(@FRANKLIN_WPID,48,-8144.492,-774.167,128.998,0,0,0,0,100,0),
(@FRANKLIN_WPID,49,-8155.13,-781.265,129.619,0,0,0,0,100,0),
(@FRANKLIN_WPID,50,-8164.803,-791.834,129.902,0,0,0,0,100,0),
(@FRANKLIN_WPID,51,-8174.491,-802.420,129.545,0,0,0,0,100,0),
(@FRANKLIN_WPID,52,-8184.21,-813.102,129.499,0,0,0,0,100,0),
(@FRANKLIN_WPID,53,-8188.647,-829.914,129.508,0,0,0,0,100,0),
(@FRANKLIN_WPID,54,-8193.06,-846.649,131.526,0,0,0,0,100,0),
(@FRANKLIN_WPID,55,-8192.838,-859.842,131.906,0,0,0,0,100,0),
(@FRANKLIN_WPID,56,-8192.618,-873.027,132.604,0,0,0,0,100,0),
(@FRANKLIN_WPID,57,-8192.45,-886.107,132.995,0,0,0,0,100,0),
(@FRANKLIN_WPID,58,-8196.933,-902.065,133.216,0,0,0,0,100,0),
(@FRANKLIN_WPID,59,-8201.440,-918.107,133.240,0,0,0,0,100,0),
(@FRANKLIN_WPID,60,-8205.974,-934.253,133.378,0,0,0,0,100,0),
(@FRANKLIN_WPID,61,-8210.51,-950.259,133.334,0,0,0,0,100,0),
(@FRANKLIN_WPID,62,-8221.936,-958.579,133.523,0,0,0,0,100,0),
(@FRANKLIN_WPID,63,-8233.356,-966.895,135.174,0,0,0,0,100,0),
(@FRANKLIN_WPID,64,-8244.73,-975.171,135.976,0,0,0,0,100,0),
(@FRANKLIN_WPID,65,-8249.922,-982.952,139.239,0,0,0,0,100,0),
(@FRANKLIN_WPID,66,-8255.06,-990.76,142.55,0,0,0,0,100,0),
(@FRANKLIN_WPID,67,-8254.280,-1008.357,145.246,0,0,0,0,100,0),
(@FRANKLIN_WPID,68,-8256.648,-1019.407,147.487,0,0,0,0,100,0),
(@FRANKLIN_WPID,69,-8259.07,-1030.48,148.679,0,0,0,0,100,0),
(@FRANKLIN_WPID,70,-8269.588,-1025.644,151.828,0,0,0,0,100,0),
(@FRANKLIN_WPID,71,-8280.13,-1020.75,155.286,0,0,0,0,100,0),
(@FRANKLIN_WPID,72,-8288.718,-1015.341,161.123,0,0,0,0,100,0),
(@FRANKLIN_WPID,73,-8297.302,-1009.934,170.018,0,0,0,0,100,0),
(@FRANKLIN_WPID,74,-8305.89,-1004.62,173.304,0,0,0,0,100,0),
(@FRANKLIN_WPID,75,-8317.888,-994.580,176.718,0,0,0,0,100,0),
(@FRANKLIN_WPID,76,-8329.91,-984.533,181.675,0,0,0,0,100,0),
(@FRANKLIN_WPID,77,-8338.235,-975.403,184.932,0,0,0,0,100,0),
(@FRANKLIN_WPID,78,-8346.570,-966.264,187.763,0,0,0,0,100,0),
(@FRANKLIN_WPID,79,-8354.9,-957.027,190.596,0,0,0,0,100,0),
(@FRANKLIN_WPID,80,-8362.666,-967.062,188.836,0,0,0,0,100,0),
(@FRANKLIN_WPID,81,-8370.491,-977.171,187.538,0,0,0,0,100,0),
(@FRANKLIN_WPID,82,-8378.37,-987.249,187.333,0,0,0,0,100,0),
(@FRANKLIN_WPID,83,-8362.346,-995.818,186.122,0,0,0,0,100,0),
(@FRANKLIN_WPID,84,-8346.49,-1004.44,186.726,0,0,0,0,100,0),
(@FRANKLIN_WPID,85,-8331.710,-1005.412,184.642,0,0,0,0,100,0);

SET @FRANKLIN_ENTRY := 14529;
SET @FRANKLIN_GUID := 4602;

-- Set MovementType and path_id
UPDATE `creature` SET `MovementType` = 2 WHERE (`id` = @FRANKLIN_ENTRY) AND (`guid` = @FRANKLIN_GUID);
UPDATE `creature_addon` SET `path_id` = @FRANKLIN_WPID WHERE (`guid` = @FRANKLIN_GUID);

-- Allow player interaction, remove the quest item from the humanoid form, set Movement Type and change the Script Name
UPDATE `creature_template`
SET `npcflag` = 1,
    `unit_flags2` = 18432,
    `MovementType` = 2,
    `lootid` = 0,
    `ScriptName` = 'npc_franklin'
WHERE (`entry` = @FRANKLIN_ENTRY);

-- Delete the unused loot_template
DELETE FROM `creature_loot_template` WHERE `Entry` = @FRANKLIN_ENTRY;

-- Add creature text for gossip option
INSERT INTO `creature_text` (`CreatureID`, `GroupID`, `ID`, `Text`, `Type`, `Language`, `Probability`, `Emote`, `Duration`, `Sound`, `BroadcastTextId`, `comment`) VALUES
(@FRANKLIN_ENTRY, 0, 0, "So you are Klinfran the Crazed? Sad, I was expecting an actual challenge. Do you dare face me in your true form?", 0, 0, 0, 0, 0, 0, 9754, "Franklin the Friendly");

-- Correct Franklin evil entry(Klinfran the Crazed) speed_walk
UPDATE `creature_template` SET `speed_walk` = 1 WHERE (`entry` = 14534);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_28_04' WHERE sql_rev = '1631371074636591511';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
