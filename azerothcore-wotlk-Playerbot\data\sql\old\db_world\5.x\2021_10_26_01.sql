-- DB update 2021_10_26_00 -> 2021_10_26_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_26_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_26_00 2021_10_26_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1635012097182969400'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1635012097182969400');

UPDATE `creature_template` SET `unit_flags`=`unit_flags`&~0x00000200 WHERE `entry` IN (727,1496,1652,1738,1743,1745,1746);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_26_01' WHERE sql_rev = '1635012097182969400';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
