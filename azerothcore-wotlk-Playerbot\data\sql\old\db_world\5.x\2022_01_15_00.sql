-- DB update 2022_01_14_05 -> 2022_01_15_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_14_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_14_05 2022_01_15_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1642128832605724154'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1642128832605724154');

DELETE FROM `creature` WHERE `guid` IN (23741,23743,23745);
DELETE FROM `creature_addon` WHERE `guid` IN (23741,23743,23745);
DELETE FROM `waypoint_data` WHERE `id` IN (237410,237430,237450);

DELETE FROM `creature` WHERE `guid` IN (24181,24182,24183,24184,24185,24186);
DELETE FROM `creature_addon` WHERE `guid` IN (24181,24182,24183,24184,24185,24186);
INSERT INTO `creature` (`guid`,`id1`,`id2`,`id3`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(24181,6498,6499,6500,1,0,0,1,1,0,-6782.5547,-2216.1597,-271.96085,2.897246599197387695,300,0,0,1,0,2,0,0,0,'',0),
(24182,6498,6499,6500,1,0,0,1,1,0,-6830.725,-1875.4896,-271.39758,2.897246599197387695,300,0,0,1,0,2,0,0,0,'',0),
(24183,6498,6499,6500,1,0,0,1,1,0,-7823.6685,-1614.9938,-268.27194,2.897246599197387695,300,0,0,1,0,2,0,0,0,'',0),
(24184,6498,6499,6500,1,0,0,1,1,0,-7780.711,-1447.6191,-270.79898,2.897246599197387695,300,0,0,1,0,2,0,0,0,'',0),
(24185,6498,6499,6500,1,0,0,1,1,0,-6864.7144,-619.1283,-271.304,2.897246599197387695,300,0,0,1,0,2,0,0,0,'',0),
(24186,6498,6499,6500,1,0,0,1,1,0,-6513.4663,-749.08813,-270.07254,2.897246599197387695,300,0,0,1,0,2,0,0,0,'',0);

SET @POOL := 388;
DELETE FROM `pool_template` WHERE `entry` IN (@POOL);
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(@POOL, 1, 'Un''Goro Develsaur East Pool');
DELETE FROM `pool_creature` WHERE `guid` IN (24181, 24182);
INSERT INTO `pool_creature` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(24181, @POOL, 0, 'Un''Goro Develsaur East Pool 1 of 2'),
(24182, @POOL, 0, 'Un''Goro Develsaur East Pool 2 of 2');

SET @POOL := 389;
DELETE FROM `pool_template` WHERE `entry` IN (@POOL);
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(@POOL, 1, 'Un''Goro Develsaur Center Pool');
DELETE FROM `pool_creature` WHERE `guid` IN (24183, 24184);
INSERT INTO `pool_creature` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(24183, @POOL, 0, 'Un''Goro Develsaur Center Pool 1 of 2'),
(24184, @POOL, 0, 'Un''Goro Develsaur Center Pool 2 of 2');

SET @POOL := 390;
DELETE FROM `pool_template` WHERE `entry` IN (@POOL);
INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES
(@POOL, 1, 'Un''Goro Develsaur West Pool');
DELETE FROM `pool_creature` WHERE `guid` IN (24185, 24186);
INSERT INTO `pool_creature` (`guid`, `pool_entry`, `chance`, `description`) VALUES
(24185, @POOL, 0, 'Un''Goro Develsaur West Pool 1 of 2'),
(24186, @POOL, 0, 'Un''Goro Develsaur West Pool 2 of 2');

-- East Pool

-- Pathing for Devilsaur Entry: 6498 Ironhide Devilsaur Entry: 6499 Tyrant Devilsaur Entry: 6500
SET @NPC := 24181;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-6782.5547,-2216.1597,-271.96085,0,0,0,0,100,0),
(@PATH,2,-6822.6445,-2215.738,-272.0484,0,0,0,0,100,0),
(@PATH,3,-6847.1206,-2220.1985,-272.09723,0,0,0,0,100,0),
(@PATH,4,-6877.78,-2212.7825,-271.84723,0,0,0,0,100,0),
(@PATH,5,-6917.131,-2191.336,-271.45462,0,0,0,0,100,0),
(@PATH,6,-6922.839,-2212.5857,-271.84723,0,0,0,0,100,0),
(@PATH,7,-6952.1636,-2221.521,-273.0862,0,0,0,0,100,0),
(@PATH,8,-6980.0537,-2250.1948,-273.5414,0,0,0,0,100,0),
(@PATH,9,-7015.7397,-2253.7551,-271.25418,0,0,0,0,100,0),
(@PATH,10,-7048.962,-2249.7817,-270.9821,0,0,0,0,100,0),
(@PATH,11,-7081.293,-2254.4758,-270.40656,0,0,0,0,100,0),
(@PATH,12,-7113.1396,-2225.671,-271.47107,0,0,0,0,100,0),
(@PATH,13,-7144.082,-2205.0642,-276.23523,0,0,0,0,100,0),
(@PATH,14,-7181.443,-2201.8933,-272.3534,0,0,0,0,100,0),
(@PATH,15,-7190.5713,-2175.6992,-270.30408,0,0,0,0,100,0),
(@PATH,16,-7192.834,-2146.2236,-270.87247,0,0,0,0,100,0),
(@PATH,17,-7210.8193,-2118.6519,-272.1057,0,0,0,0,100,0),
(@PATH,18,-7229.116,-2106.4587,-272.14233,0,0,0,0,100,0),
(@PATH,19,-7251.3574,-2095.0518,-272.59586,0,0,0,0,100,0),
(@PATH,20,-7280.811,-2085.6316,-271.4495,0,0,0,0,100,0),
(@PATH,21,-7313.0415,-2071.2021,-269.54462,0,0,0,0,100,0),
(@PATH,22,-7335.3066,-2062.2012,-272.2747,0,0,0,0,100,0),
(@PATH,23,-7359.135,-2049.837,-272.40897,0,0,0,0,100,0),
(@PATH,24,-7381.5566,-2046.3516,-272.05817,0,0,0,0,100,0),
(@PATH,25,-7389.7935,-2024.901,-271.69653,0,0,0,0,100,0),
(@PATH,26,-7392.7144,-2006.9766,-271.2212,0,0,0,0,100,0),
(@PATH,27,-7396.009,-1981.8356,-270.7506,0,0,0,0,100,0),
(@PATH,28,-7404.401,-1951.7893,-271.9282,0,0,0,0,100,0),
(@PATH,29,-7419.2925,-1921.909,-270.01773,0,0,0,0,100,0),
(@PATH,30,-7430.9585,-1890.1814,-272.33807,0,0,0,0,100,0),
(@PATH,31,-7456.5444,-1864.9586,-271.62433,0,0,0,0,100,0),
(@PATH,32,-7448.3906,-1831.527,-272.09723,0,0,0,0,100,0),
(@PATH,33,-7429.1553,-1811.6262,-270.106,0,0,0,0,100,0),
(@PATH,34,-7423.4453,-1777.7034,-272.09976,0,0,0,0,100,0),
(@PATH,35,-7429.1553,-1811.6262,-270.106,0,0,0,0,100,0),
(@PATH,36,-7448.3906,-1831.527,-272.09723,0,0,0,0,100,0),
(@PATH,37,-7456.5444,-1864.9586,-271.62433,0,0,0,0,100,0),
(@PATH,38,-7430.9585,-1890.1814,-272.33807,0,0,0,0,100,0),
(@PATH,39,-7419.2925,-1921.909,-270.01773,0,0,0,0,100,0),
(@PATH,40,-7404.401,-1951.7893,-271.9282,0,0,0,0,100,0),
(@PATH,41,-7396.009,-1981.8356,-270.7506,0,0,0,0,100,0),
(@PATH,42,-7392.7144,-2006.9766,-271.2212,0,0,0,0,100,0),
(@PATH,43,-7389.7935,-2024.901,-271.69653,0,0,0,0,100,0),
(@PATH,44,-7381.5566,-2046.3516,-272.05817,0,0,0,0,100,0),
(@PATH,45,-7359.135,-2049.837,-272.40897,0,0,0,0,100,0),
(@PATH,46,-7335.2866,-2062.1914,-272.2278,0,0,0,0,100,0),
(@PATH,47,-7313.0415,-2071.2021,-269.54462,0,0,0,0,100,0),
(@PATH,48,-7280.811,-2085.6316,-271.4495,0,0,0,0,100,0),
(@PATH,49,-7251.3574,-2095.0518,-272.59586,0,0,0,0,100,0),
(@PATH,50,-7229.116,-2106.4587,-272.14233,0,0,0,0,100,0),
(@PATH,51,-7210.8193,-2118.6519,-272.1057,0,0,0,0,100,0),
(@PATH,52,-7192.834,-2146.2236,-270.87247,0,0,0,0,100,0),
(@PATH,53,-7190.5713,-2175.6992,-270.30408,0,0,0,0,100,0),
(@PATH,54,-7181.443,-2201.8933,-272.3534,0,0,0,0,100,0),
(@PATH,55,-7144.082,-2205.0642,-276.23523,0,0,0,0,100,0),
(@PATH,56,-7113.1396,-2225.671,-271.47107,0,0,0,0,100,0),
(@PATH,57,-7081.293,-2254.4758,-270.40656,0,0,0,0,100,0),
(@PATH,58,-7048.962,-2249.7817,-270.9821,0,0,0,0,100,0),
(@PATH,59,-7015.7397,-2253.7551,-271.25418,0,0,0,0,100,0),
(@PATH,60,-6980.0537,-2250.1948,-273.5414,0,0,0,0,100,0),
(@PATH,61,-6952.1636,-2221.521,-273.0862,0,0,0,0,100,0),
(@PATH,62,-6922.839,-2212.5857,-271.84723,0,0,0,0,100,0),
(@PATH,63,-6917.131,-2191.336,-271.45462,0,0,0,0,100,0),
(@PATH,64,-6877.78,-2212.7825,-271.84723,0,0,0,0,100,0),
(@PATH,65,-6847.1206,-2220.1985,-272.09723,0,0,0,0,100,0),
(@PATH,66,-6822.6445,-2215.738,-272.0484,0,0,0,0,100,0);

-- Pathing for Devilsaur Entry: 6498 Ironhide Devilsaur Entry: 6499 Tyrant Devilsaur Entry: 6500
SET @NPC := 24182;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-6830.725,-1875.4896,-271.39758,0,0,0,0,100,0),
(@PATH,2,-6816.659,-1859.1771,-270.5357,0,0,0,0,100,0),
(@PATH,3,-6784.8037,-1854.7101,-272.09723,0,0,0,0,100,0),
(@PATH,4,-6750.0957,-1889.4584,-272.18686,0,0,0,0,100,0),
(@PATH,5,-6741.579,-1927.4087,-271.8244,0,0,0,0,100,0),
(@PATH,6,-6741.2837,-1956.0122,-272.08203,0,0,0,0,100,0),
(@PATH,7,-6759.5234,-1988.3965,-269.73355,0,0,0,0,100,0),
(@PATH,8,-6737.058,-2017.6836,-272.09723,0,0,0,0,100,0),
(@PATH,9,-6726.1675,-2053.654,-272.09723,0,0,0,0,100,0),
(@PATH,10,-6689.4814,-2078.7253,-271.993,0,0,0,0,100,0),
(@PATH,11,-6658.7705,-2080.2634,-271.993,0,0,0,0,100,0),
(@PATH,12,-6622.141,-2060.4756,-270.36252,0,0,0,0,100,0),
(@PATH,13,-6591.321,-2044.9631,-271.993,0,0,0,0,100,0),
(@PATH,14,-6583.6343,-2012.885,-270.54575,0,0,0,0,100,0),
(@PATH,15,-6581.5586,-1981.3889,-271.63852,0,0,0,0,100,0),
(@PATH,16,-6587.061,-1960.6146,-271.85968,0,0,0,0,100,0),
(@PATH,17,-6617.8545,-1941.0282,-272.14835,0,0,0,0,100,0),
(@PATH,18,-6627.069,-1913.767,-271.85208,0,0,0,0,100,0),
(@PATH,19,-6618.782,-1885.7466,-272.42914,0,0,0,0,100,0),
(@PATH,20,-6614.9927,-1855.2661,-272.09723,0,0,0,0,100,0),
(@PATH,21,-6611.3677,-1819.9827,-271.68268,0,0,0,0,100,0),
(@PATH,22,-6609.3125,-1783.5842,-272.09723,0,0,0,0,100,0),
(@PATH,23,-6628.4204,-1751.0371,-272.09723,0,0,0,0,100,0),
(@PATH,24,-6644.029,-1715.733,-272.0597,0,0,0,0,100,0),
(@PATH,25,-6657.172,-1672.8267,-272.09723,0,0,0,0,100,0),
(@PATH,26,-6665.9165,-1636.5437,-272.09723,0,0,0,0,100,0),
(@PATH,27,-6642.9307,-1614.7375,-271.55328,0,0,0,0,100,0),
(@PATH,28,-6620.0225,-1615.3267,-271.93933,0,0,0,0,100,0),
(@PATH,29,-6581.4883,-1593.3329,-272.09723,0,0,0,0,100,0),
(@PATH,30,-6555.971,-1586.0521,-272.09723,0,0,0,0,100,0),
(@PATH,31,-6581.4883,-1593.3329,-272.09723,0,0,0,0,100,0),
(@PATH,32,-6620.0225,-1615.3267,-271.93933,0,0,0,0,100,0),
(@PATH,33,-6642.9307,-1614.7375,-271.55328,0,0,0,0,100,0),
(@PATH,34,-6665.9165,-1636.5437,-272.09723,0,0,0,0,100,0),
(@PATH,35,-6657.172,-1672.8267,-272.09723,0,0,0,0,100,0),
(@PATH,36,-6644.029,-1715.733,-272.0597,0,0,0,0,100,0),
(@PATH,37,-6628.4204,-1751.0371,-272.09723,0,0,0,0,100,0),
(@PATH,38,-6609.3125,-1783.5842,-272.09723,0,0,0,0,100,0),
(@PATH,39,-6611.3677,-1819.9827,-271.68268,0,0,0,0,100,0),
(@PATH,40,-6614.9927,-1855.2661,-272.09723,0,0,0,0,100,0),
(@PATH,41,-6618.782,-1885.7466,-272.42914,0,0,0,0,100,0),
(@PATH,42,-6627.069,-1913.767,-271.85208,0,0,0,0,100,0),
(@PATH,43,-6617.8545,-1941.0282,-272.14835,0,0,0,0,100,0),
(@PATH,44,-6587.061,-1960.6146,-271.85968,0,0,0,0,100,0),
(@PATH,45,-6581.5586,-1981.3889,-271.63852,0,0,0,0,100,0),
(@PATH,46,-6583.6343,-2012.885,-270.54575,0,0,0,0,100,0),
(@PATH,47,-6591.321,-2044.9631,-271.993,0,0,0,0,100,0),
(@PATH,48,-6622.141,-2060.4756,-270.36252,0,0,0,0,100,0),
(@PATH,49,-6658.7705,-2080.2634,-271.993,0,0,0,0,100,0),
(@PATH,50,-6689.4814,-2078.7253,-271.993,0,0,0,0,100,0),
(@PATH,51,-6726.1675,-2053.654,-272.09723,0,0,0,0,100,0),
(@PATH,52,-6737.058,-2017.6836,-272.09723,0,0,0,0,100,0),
(@PATH,53,-6759.5234,-1988.3965,-269.73355,0,0,0,0,100,0),
(@PATH,54,-6741.2837,-1956.0122,-272.08203,0,0,0,0,100,0),
(@PATH,55,-6741.579,-1927.4087,-271.8244,0,0,0,0,100,0),
(@PATH,56,-6750.0957,-1889.4584,-272.18686,0,0,0,0,100,0),
(@PATH,57,-6784.8037,-1854.7101,-272.09723,0,0,0,0,100,0),
(@PATH,58,-6816.659,-1859.1771,-270.5357,0,0,0,0,100,0);

-- Center Pool

-- Pathing for Devilsaur Entry: 6498 Ironhide Devilsaur Entry: 6499 Tyrant Devilsaur Entry: 6500
SET @NPC := 24183;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-7823.6685,-1614.9938,-268.27194,0,0,0,0,100,0),
(@PATH,2,-7788.6973,-1608.7888,-271.3161,0,0,0,0,100,0),
(@PATH,3,-7756.185,-1594.472,-269.77042,0,0,0,0,100,0),
(@PATH,4,-7736.2188,-1602.9099,-271.63385,0,0,0,0,100,0),
(@PATH,5,-7727.5903,-1637.0361,-272.09723,0,0,0,0,100,0),
(@PATH,6,-7702.585,-1643.7139,-272.09723,0,0,0,0,100,0),
(@PATH,7,-7667.405,-1636.9862,-272.09723,0,0,0,0,100,0),
(@PATH,8,-7650.8228,-1613.3208,-271.97223,0,0,0,0,100,0),
(@PATH,9,-7617.3774,-1604.8785,-272.09723,0,0,0,0,100,0),
(@PATH,10,-7582.8994,-1603.9391,-271.98273,0,0,0,0,100,0),
(@PATH,11,-7554.349,-1603.8027,-271.9733,0,0,0,0,100,0),
(@PATH,12,-7542.848,-1588.219,-271.8529,0,0,0,0,100,0),
(@PATH,13,-7518.5713,-1584.4501,-272.6941,0,0,0,0,100,0),
(@PATH,14,-7489.7144,-1586.3778,-275.07336,0,0,0,0,100,0),
(@PATH,15,-7473.316,-1605.1969,-276.27362,0,0,0,0,100,0),
(@PATH,16,-7448.233,-1611.8818,-276.68106,0,0,0,0,100,0),
(@PATH,17,-7421.018,-1611.8055,-275.9141,0,0,0,0,100,0),
(@PATH,18,-7404.0537,-1594.6815,-273.52127,0,0,0,0,100,0),
(@PATH,19,-7369.202,-1587.396,-276.3828,0,0,0,0,100,0),
(@PATH,20,-7340.2837,-1583.002,-272.64053,0,0,0,0,100,0),
(@PATH,21,-7313.5713,-1570.7089,-271.52505,0,0,0,0,100,0),
(@PATH,22,-7295.442,-1561.8331,-272.18155,0,0,0,0,100,0),
(@PATH,23,-7246.901,-1564.6493,-273.2084,0,0,0,0,100,0),
(@PATH,24,-7224.023,-1582.2583,-269.36847,0,0,0,0,100,0),
(@PATH,25,-7201.0234,-1582.752,-261.46954,0,0,0,0,100,0),
(@PATH,26,-7164.602,-1585.1335,-271.2383,0,0,0,0,100,0),
(@PATH,27,-7135.2993,-1574.0183,-272.01688,0,0,0,0,100,0),
(@PATH,28,-7116.2944,-1580.9711,-271.4659,0,0,0,0,100,0),
(@PATH,29,-7090.492,-1590.7963,-272.09723,0,0,0,0,100,0),
(@PATH,30,-7077.8467,-1619.2809,-272.09723,0,0,0,0,100,0),
(@PATH,31,-7052.0767,-1624.292,-272.09723,0,0,0,0,100,0),
(@PATH,32,-7019.862,-1621.3812,-272.1178,0,0,0,0,100,0),
(@PATH,33,-6977.7266,-1621.2892,-273.28308,0,0,0,0,100,0),
(@PATH,34,-6950.4316,-1608.5426,-271.4152,0,0,0,0,100,0),
(@PATH,35,-6934.276,-1590.8773,-271.7101,0,0,0,0,100,0),
(@PATH,36,-6907.7646,-1578.9176,-272.1077,0,0,0,0,100,0),
(@PATH,37,-6877.012,-1573.6382,-271.77618,0,0,0,0,100,0),
(@PATH,38,-6853.1445,-1555.5033,-267.4567,0,0,0,0,100,0),
(@PATH,39,-6819.5347,-1552.8822,-272.50928,0,0,0,0,100,0),
(@PATH,40,-6785.0103,-1550.5763,-271.4793,0,0,0,0,100,0),
(@PATH,41,-6819.5347,-1552.8822,-272.50928,0,0,0,0,100,0),
(@PATH,42,-6853.1445,-1555.5033,-267.4567,0,0,0,0,100,0),
(@PATH,43,-6877.012,-1573.6382,-271.77618,0,0,0,0,100,0),
(@PATH,44,-6907.7646,-1578.9176,-272.1077,0,0,0,0,100,0),
(@PATH,45,-6934.276,-1590.8773,-271.7101,0,0,0,0,100,0),
(@PATH,46,-6950.4316,-1608.5426,-271.4152,0,0,0,0,100,0),
(@PATH,47,-6977.7266,-1621.2892,-273.28308,0,0,0,0,100,0),
(@PATH,48,-7019.862,-1621.3812,-272.1178,0,0,0,0,100,0),
(@PATH,49,-7052.0767,-1624.292,-272.09723,0,0,0,0,100,0),
(@PATH,50,-7077.8467,-1619.2809,-272.09723,0,0,0,0,100,0),
(@PATH,51,-7090.492,-1590.7963,-272.09723,0,0,0,0,100,0),
(@PATH,52,-7116.2944,-1580.9711,-271.4659,0,0,0,0,100,0),
(@PATH,53,-7135.2993,-1574.0183,-272.01688,0,0,0,0,100,0),
(@PATH,54,-7164.602,-1585.1335,-271.2383,0,0,0,0,100,0),
(@PATH,55,-7200.9233,-1582.7693,-261.523,0,0,0,0,100,0),
(@PATH,56,-7223.924,-1582.2754,-269.34454,0,0,0,0,100,0),
(@PATH,57,-7246.901,-1564.6493,-273.2084,0,0,0,0,100,0),
(@PATH,58,-7295.442,-1561.8331,-272.18155,0,0,0,0,100,0),
(@PATH,59,-7313.5713,-1570.7089,-271.52505,0,0,0,0,100,0),
(@PATH,60,-7340.2837,-1583.002,-272.64053,0,0,0,0,100,0),
(@PATH,61,-7369.202,-1587.396,-276.3828,0,0,0,0,100,0),
(@PATH,62,-7404.0537,-1594.6815,-273.52127,0,0,0,0,100,0),
(@PATH,63,-7420.9277,-1611.7441,-275.8538,0,0,0,0,100,0),
(@PATH,64,-7448.233,-1611.8818,-276.68106,0,0,0,0,100,0),
(@PATH,65,-7473.316,-1605.1969,-276.27362,0,0,0,0,100,0),
(@PATH,66,-7489.7144,-1586.3778,-275.07336,0,0,0,0,100,0),
(@PATH,67,-7518.5713,-1584.4501,-272.6941,0,0,0,0,100,0),
(@PATH,68,-7542.848,-1588.219,-271.8529,0,0,0,0,100,0),
(@PATH,69,-7554.349,-1603.8027,-271.9733,0,0,0,0,100,0),
(@PATH,70,-7582.8994,-1603.9391,-271.98273,0,0,0,0,100,0),
(@PATH,71,-7617.3774,-1604.8785,-272.09723,0,0,0,0,100,0),
(@PATH,72,-7650.8228,-1613.3208,-271.97223,0,0,0,0,100,0),
(@PATH,73,-7667.405,-1636.9862,-272.09723,0,0,0,0,100,0),
(@PATH,74,-7702.585,-1643.7139,-272.09723,0,0,0,0,100,0),
(@PATH,75,-7727.5903,-1637.0361,-272.09723,0,0,0,0,100,0),
(@PATH,76,-7736.2188,-1602.9099,-271.63385,0,0,0,0,100,0),
(@PATH,77,-7756.185,-1594.472,-269.77042,0,0,0,0,100,0),
(@PATH,78,-7788.6973,-1608.7888,-271.3161,0,0,0,0,100,0);

-- Pathing for Devilsaur Entry: 6498 Ironhide Devilsaur Entry: 6499 Tyrant Devilsaur Entry: 6500
SET @NPC := 24184;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-7780.711,-1447.6191,-270.79898,0,0,0,0,100,0),
(@PATH,2,-7755.7603,-1446.4471,-271.97113,0,0,0,0,100,0),
(@PATH,3,-7722.4453,-1422.0408,-270.96017,0,0,0,0,100,0),
(@PATH,4,-7685.863,-1406.7393,-270.27335,0,0,0,0,100,0),
(@PATH,5,-7649.5522,-1410.9366,-266.54605,0,0,0,0,100,0),
(@PATH,6,-7621.239,-1411.9152,-266.98206,0,0,0,0,100,0),
(@PATH,7,-7582.098,-1418.0159,-269.53207,0,0,0,0,100,0),
(@PATH,8,-7554.5376,-1451.9064,-270.02164,0,0,0,0,100,0),
(@PATH,9,-7521.4185,-1478.77,-271.22546,0,0,0,0,100,0),
(@PATH,10,-7491.7915,-1485.8961,-271.04626,0,0,0,0,100,0),
(@PATH,11,-7451.0728,-1475.1328,-271.7145,0,0,0,0,100,0),
(@PATH,12,-7422.434,-1448.4263,-270.1582,0,0,0,0,100,0),
(@PATH,13,-7420.229,-1419.5692,-270.41214,0,0,0,0,100,0),
(@PATH,14,-7438.738,-1380.0265,-267.74368,0,0,0,0,100,0),
(@PATH,15,-7449.5454,-1348.4596,-269.10068,0,0,0,0,100,0),
(@PATH,16,-7463.901,-1321.9386,-271.52612,0,0,0,0,100,0),
(@PATH,17,-7492.5703,-1281.239,-271.78326,0,0,0,0,100,0),
(@PATH,18,-7504.8257,-1246.4401,-269.47784,0,0,0,0,100,0),
(@PATH,19,-7517.237,-1216.5406,-271.81128,0,0,0,0,100,0),
(@PATH,20,-7551.1104,-1196.7008,-271.2597,0,0,0,0,100,0),
(@PATH,21,-7590.022,-1171.3826,-272.15796,0,0,0,0,100,0),
(@PATH,22,-7618.889,-1170.614,-271.88495,0,0,0,0,100,0),
(@PATH,23,-7646.8735,-1178.0884,-269.33325,0,0,0,0,100,0),
(@PATH,24,-7677.654,-1191.656,-270.64304,0,0,0,0,100,0),
(@PATH,25,-7710.768,-1184.6135,-270.07062,0,0,0,0,100,0),
(@PATH,26,-7738.1025,-1187.704,-272.0796,0,0,0,0,100,0),
(@PATH,27,-7778.335,-1193.2941,-268.37213,0,0,0,0,100,0),
(@PATH,28,-7792.684,-1163.4635,-262.36884,0,0,0,0,100,0),
(@PATH,29,-7823.4287,-1142.0603,-258.818,0,0,0,0,100,0),
(@PATH,30,-7853.635,-1120.7422,-264.96274,0,0,0,0,100,0),
(@PATH,31,-7878.114,-1122.9609,-268.8905,0,0,0,0,100,0),
(@PATH,32,-7907.4517,-1115.1016,-274.92004,0,0,0,0,100,0),
(@PATH,33,-7935.369,-1088.0607,-273.00418,0,0,0,0,100,0),
(@PATH,34,-7907.4517,-1115.1016,-274.92004,0,0,0,0,100,0),
(@PATH,35,-7878.114,-1122.9609,-268.8905,0,0,0,0,100,0),
(@PATH,36,-7853.678,-1120.7142,-264.83774,0,0,0,0,100,0),
(@PATH,37,-7823.4287,-1142.0603,-258.818,0,0,0,0,100,0),
(@PATH,38,-7792.684,-1163.4635,-262.36884,0,0,0,0,100,0),
(@PATH,39,-7778.335,-1193.2941,-268.37213,0,0,0,0,100,0),
(@PATH,40,-7738.1025,-1187.704,-272.0796,0,0,0,0,100,0),
(@PATH,41,-7710.768,-1184.6135,-270.07062,0,0,0,0,100,0),
(@PATH,42,-7677.654,-1191.656,-270.64304,0,0,0,0,100,0),
(@PATH,43,-7646.8735,-1178.0884,-269.33325,0,0,0,0,100,0),
(@PATH,44,-7618.889,-1170.614,-271.88495,0,0,0,0,100,0),
(@PATH,45,-7590.022,-1171.3826,-272.15796,0,0,0,0,100,0),
(@PATH,46,-7551.1104,-1196.7008,-271.2597,0,0,0,0,100,0),
(@PATH,47,-7517.237,-1216.5406,-271.81128,0,0,0,0,100,0),
(@PATH,48,-7504.8257,-1246.4401,-269.47784,0,0,0,0,100,0),
(@PATH,49,-7492.5703,-1281.239,-271.78326,0,0,0,0,100,0),
(@PATH,50,-7463.901,-1321.9386,-271.52612,0,0,0,0,100,0),
(@PATH,51,-7449.5454,-1348.4596,-269.10068,0,0,0,0,100,0),
(@PATH,52,-7438.738,-1380.0265,-267.74368,0,0,0,0,100,0),
(@PATH,53,-7420.229,-1419.5692,-270.41214,0,0,0,0,100,0),
(@PATH,54,-7422.434,-1448.4263,-270.1582,0,0,0,0,100,0),
(@PATH,55,-7451.0728,-1475.1328,-271.7145,0,0,0,0,100,0),
(@PATH,56,-7491.7915,-1485.8961,-271.04626,0,0,0,0,100,0),
(@PATH,57,-7521.4185,-1478.77,-271.22546,0,0,0,0,100,0),
(@PATH,58,-7554.5376,-1451.9064,-270.02164,0,0,0,0,100,0),
(@PATH,59,-7582.098,-1418.0159,-269.53207,0,0,0,0,100,0),
(@PATH,60,-7621.239,-1411.9152,-266.98206,0,0,0,0,100,0),
(@PATH,61,-7649.5522,-1410.9366,-266.54605,0,0,0,0,100,0),
(@PATH,62,-7685.863,-1406.7393,-270.27335,0,0,0,0,100,0),
(@PATH,63,-7722.4453,-1422.0408,-270.96017,0,0,0,0,100,0),
(@PATH,64,-7755.7603,-1446.4471,-271.97113,0,0,0,0,100,0);

-- West Pool

-- Pathing for Devilsaur Entry: 6498 Ironhide Devilsaur Entry: 6499 Tyrant Devilsaur Entry: 6500
SET @NPC := 24185;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-6864.7144,-619.1283,-271.304,0,0,0,0,100,0),
(@PATH,2,-6881.9663,-654.2593,-270.69498,0,0,0,0,100,0),
(@PATH,3,-6888.868,-689.1165,-271.96564,0,0,0,0,100,0),
(@PATH,4,-6910.723,-718.0936,-271.6525,0,0,0,0,100,0),
(@PATH,5,-6940.0044,-749.9374,-271.53912,0,0,0,0,100,0),
(@PATH,6,-6957.604,-776.2957,-272.09723,0,0,0,0,100,0),
(@PATH,7,-6981.0728,-809.32715,-271.80487,0,0,0,0,100,0),
(@PATH,8,-7009.3594,-833.7102,-271.34723,0,0,0,0,100,0),
(@PATH,9,-7034.7764,-858.32697,-271.02106,0,0,0,0,100,0),
(@PATH,10,-7058.56,-880.8818,-271.39365,0,0,0,0,100,0),
(@PATH,11,-7086.558,-887.9312,-272.06186,0,0,0,0,100,0),
(@PATH,12,-7111.1777,-891.0868,-271.24332,0,0,0,0,100,0),
(@PATH,13,-7132.3853,-916.3673,-271.99625,0,0,0,0,100,0),
(@PATH,14,-7166.531,-913.61176,-271.42252,0,0,0,0,100,0),
(@PATH,15,-7193.4966,-913.24066,-270.9859,0,0,0,0,100,0),
(@PATH,16,-7226.394,-915.18665,-270.9859,0,0,0,0,100,0),
(@PATH,17,-7257.7583,-919.55536,-272.00308,0,0,0,0,100,0),
(@PATH,18,-7288.3345,-938.0317,-271.06564,0,0,0,0,100,0),
(@PATH,19,-7312.4263,-935.9516,-271.8075,0,0,0,0,100,0),
(@PATH,20,-7350.686,-907.9636,-271.7268,0,0,0,0,100,0),
(@PATH,21,-7377.6953,-907.4906,-271.97223,0,0,0,0,100,0),
(@PATH,22,-7416.8696,-919.70593,-269.02335,0,0,0,0,100,0),
(@PATH,23,-7459.6104,-929.6074,-272.24057,0,0,0,0,100,0),
(@PATH,24,-7492.9497,-942.21765,-271.67062,0,0,0,0,100,0),
(@PATH,25,-7525.2163,-975.85236,-270.39566,0,0,0,0,100,0),
(@PATH,26,-7540.869,-1009.0169,-269.0665,0,0,0,0,100,0),
(@PATH,27,-7525.2163,-975.85236,-270.39566,0,0,0,0,100,0),
(@PATH,28,-7492.9497,-942.21765,-271.67062,0,0,0,0,100,0),
(@PATH,29,-7459.6104,-929.6074,-272.24057,0,0,0,0,100,0),
(@PATH,30,-7416.8696,-919.70593,-269.02335,0,0,0,0,100,0),
(@PATH,31,-7377.711,-907.5332,-271.97223,0,0,0,0,100,0),
(@PATH,32,-7350.686,-907.9636,-271.7268,0,0,0,0,100,0),
(@PATH,33,-7312.4263,-935.9516,-271.8075,0,0,0,0,100,0),
(@PATH,34,-7288.3345,-938.0317,-271.06564,0,0,0,0,100,0),
(@PATH,35,-7257.7583,-919.55536,-272.00308,0,0,0,0,100,0),
(@PATH,36,-7226.394,-915.18665,-270.9859,0,0,0,0,100,0),
(@PATH,37,-7193.4966,-913.24066,-270.9859,0,0,0,0,100,0),
(@PATH,38,-7166.531,-913.61176,-271.42252,0,0,0,0,100,0),
(@PATH,39,-7132.3853,-916.3673,-271.99625,0,0,0,0,100,0),
(@PATH,40,-7111.1777,-891.0868,-271.24332,0,0,0,0,100,0),
(@PATH,41,-7086.558,-887.9312,-272.06186,0,0,0,0,100,0),
(@PATH,42,-7058.56,-880.8818,-271.39365,0,0,0,0,100,0),
(@PATH,43,-7034.7764,-858.32697,-271.02106,0,0,0,0,100,0),
(@PATH,44,-7009.3594,-833.7102,-271.34723,0,0,0,0,100,0),
(@PATH,45,-6981.0728,-809.32715,-271.80487,0,0,0,0,100,0),
(@PATH,46,-6957.604,-776.2957,-272.09723,0,0,0,0,100,0),
(@PATH,47,-6940.0044,-749.9374,-271.53912,0,0,0,0,100,0),
(@PATH,48,-6910.723,-718.0936,-271.6525,0,0,0,0,100,0),
(@PATH,49,-6888.868,-689.1165,-271.96564,0,0,0,0,100,0),
(@PATH,50,-6881.9663,-654.2593,-270.69498,0,0,0,0,100,0);

-- Pathing for Devilsaur Entry: 6498 Ironhide Devilsaur Entry: 6499 Tyrant Devilsaur Entry: 6500
SET @NPC := 24186;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-6513.4663,-749.08813,-270.07254,0,0,0,0,100,0),
(@PATH,2,-6552.7686,-751.06256,-275.10147,0,0,0,0,100,0),
(@PATH,3,-6588.3037,-751.25586,-273.9518,0,0,0,0,100,0),
(@PATH,4,-6622.4575,-749.4856,-274.829,0,0,0,0,100,0),
(@PATH,5,-6649.6367,-747.92126,-272.11533,0,0,0,0,100,0),
(@PATH,6,-6663.7734,-772.0286,-269.80362,0,0,0,0,100,0),
(@PATH,7,-6689.9062,-783.4278,-270.83243,0,0,0,0,100,0),
(@PATH,8,-6724.6626,-788.21027,-271.67133,0,0,0,0,100,0),
(@PATH,9,-6755.685,-792.0954,-270.93686,0,0,0,0,100,0),
(@PATH,10,-6779.4277,-812.86993,-272.09723,0,0,0,0,100,0),
(@PATH,11,-6818.7095,-818.29926,-271.3062,0,0,0,0,100,0),
(@PATH,12,-6846.479,-813.2197,-271.71805,0,0,0,0,100,0),
(@PATH,13,-6877.01,-787.4286,-270.88806,0,0,0,0,100,0),
(@PATH,14,-6890.8237,-746.28143,-271.27734,0,0,0,0,100,0),
(@PATH,15,-6909.9556,-722.16797,-271.6525,0,0,0,0,100,0),
(@PATH,16,-6941.4336,-693.0585,-271.50842,0,0,0,0,100,0),
(@PATH,17,-6980.1187,-684.9656,-270.01654,0,0,0,0,100,0),
(@PATH,18,-7015.244,-679.3659,-272.1358,0,0,0,0,100,0),
(@PATH,19,-7051.738,-674.37317,-270.46112,0,0,0,0,100,0),
(@PATH,20,-7083.509,-646.9298,-271.26624,0,0,0,0,100,0),
(@PATH,21,-7085.586,-615.77704,-270.84412,0,0,0,0,100,0),
(@PATH,22,-7115.3145,-612.83826,-270.06946,0,0,0,0,100,0),
(@PATH,23,-7146.102,-612.7906,-270.7179,0,0,0,0,100,0),
(@PATH,24,-7180.032,-607.22766,-270.69537,0,0,0,0,100,0),
(@PATH,25,-7204.038,-605.3099,-270.28717,0,0,0,0,100,0),
(@PATH,26,-7225.2837,-609.30707,-269.53204,0,0,0,0,100,0),
(@PATH,27,-7247.8345,-596.6033,-269.1193,0,0,0,0,100,0),
(@PATH,28,-7282.6284,-581.6567,-270.84683,0,0,0,0,100,0),
(@PATH,29,-7247.8345,-596.6033,-269.1193,0,0,0,0,100,0),
(@PATH,30,-7225.2837,-609.30707,-269.53204,0,0,0,0,100,0),
(@PATH,31,-7204.038,-605.3099,-270.28717,0,0,0,0,100,0),
(@PATH,32,-7180.032,-607.22766,-270.69537,0,0,0,0,100,0),
(@PATH,33,-7146.102,-612.7906,-270.7179,0,0,0,0,100,0),
(@PATH,34,-7115.3145,-612.83826,-270.06946,0,0,0,0,100,0),
(@PATH,35,-7085.586,-615.77704,-270.84412,0,0,0,0,100,0),
(@PATH,36,-7083.509,-646.9298,-271.26624,0,0,0,0,100,0),
(@PATH,37,-7051.738,-674.37317,-270.46112,0,0,0,0,100,0),
(@PATH,38,-7015.244,-679.3659,-272.1358,0,0,0,0,100,0),
(@PATH,39,-6980.1187,-684.9656,-270.01654,0,0,0,0,100,0),
(@PATH,40,-6941.4336,-693.0585,-271.50842,0,0,0,0,100,0),
(@PATH,41,-6909.9556,-722.16797,-271.6525,0,0,0,0,100,0),
(@PATH,42,-6890.8237,-746.28143,-271.27734,0,0,0,0,100,0),
(@PATH,43,-6877.01,-787.4286,-270.88806,0,0,0,0,100,0),
(@PATH,44,-6846.479,-813.2197,-271.71805,0,0,0,0,100,0),
(@PATH,45,-6818.7095,-818.29926,-271.3062,0,0,0,0,100,0),
(@PATH,46,-6779.4277,-812.86993,-272.09723,0,0,0,0,100,0),
(@PATH,47,-6755.685,-792.0954,-270.93686,0,0,0,0,100,0),
(@PATH,48,-6724.6626,-788.21027,-271.67133,0,0,0,0,100,0),
(@PATH,49,-6689.9062,-783.4278,-270.83243,0,0,0,0,100,0),
(@PATH,50,-6663.7734,-772.0286,-269.80362,0,0,0,0,100,0),
(@PATH,51,-6649.6367,-747.92126,-272.11533,0,0,0,0,100,0),
(@PATH,52,-6622.4575,-749.4856,-274.829,0,0,0,0,100,0),
(@PATH,53,-6588.3037,-751.25586,-273.9518,0,0,0,0,100,0),
(@PATH,54,-6552.7686,-751.06256,-275.10147,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_15_00' WHERE sql_rev = '1642128832605724154';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
