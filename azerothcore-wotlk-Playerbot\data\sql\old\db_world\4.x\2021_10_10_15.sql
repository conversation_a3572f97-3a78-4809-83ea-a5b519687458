-- DB update 2021_10_10_14 -> 2021_10_10_15
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_10_14';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_10_14 2021_10_10_15 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1633513829949812900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1633513829949812900');

DELETE FROM `creature_loot_template` WHERE `item`=8838 AND `entry` NOT IN (2302, 4321, 6510, 6509, 6511, 6512, 3890, 12224, 3841, 15184, 8384, 3310, 
31426, 352, 12636, 5490, 10583, 1573, 14753, 20010, 20011, 20012, 20013, 20014, 7139, 13219, 
16112, 29176, 13841, 8981, 16133, 16135, 28177, 29181, 16131, 1812, 14527, 15481, 6517, 6527, 
6519, 6518, 7846, 1851, 5481, 5485, 1855, 20349, 15070, 7100, 7101, 11465, 11462, 13022);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_10_15' WHERE sql_rev = '1633513829949812900';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
