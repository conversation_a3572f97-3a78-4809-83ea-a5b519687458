-- DB update 2021_12_23_06 -> 2021_12_23_07
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_23_06';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_23_06 2021_12_23_07 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639932916698986865'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639932916698986865');

-- <PERSON><PERSON> Spire

-- <PERSON> <PERSON>
UPDATE `creature` SET `wander_distance`=3, `MovementType`=1 WHERE `guid` IN (82535,82533);
DELETE FROM `creature` WHERE `guid` IN (82508,82526,82525,82543,82541,82542,82532,82524,82546,82507,82509,82523,82530,82534,82721);
INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(82508, 16314, 530, 0, 0, 1, 1, 0, 0, 6983.7007, -5851.492, 44.219906, 2.168352842330932617, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82526, 16314, 530, 0, 0, 1, 1, 0, 0, 7024.639, -5750.212, 102.1215, 5.016381263732910156, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82525, 16314, 530, 0, 0, 1, 1, 0, 0, 7020.252, -5734.569, 105.11516, 3.686086177825927734, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82543, 16314, 530, 0, 0, 1, 1, 0, 0, 7011.3403, -5674.5645, 102.55021, 0.041728798300027847, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82541, 16314, 530, 0, 0, 1, 1, 0, 0, 6999.9272, -5686.927, 102.61046, 4.277652740478515625, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82542, 16314, 530, 0, 0, 1, 1, 0, 0, 7026.8984, -5682.8594, 102.624756, 5.363351821899414062, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82532, 16314, 530, 0, 0, 1, 1, 0, 0, 7068.2124, -5719.7227, 84.33799, 2.009582281112670898, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82524, 16314, 530, 0, 0, 1, 1, 0, 0, 6971.39, -5737.2197, 84.33299, 2.712093353271484375, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82546, 16314, 530, 0, 0, 1, 1, 0, 0, 7025.488, -5847.6167, 49.222034, 0.558374702930450439, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82507, 16314, 530, 0, 0, 1, 1, 0, 0, 7056.1978, -5790.0493, 49.678337, 6.015683650970458984, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82509, 16314, 530, 0, 0, 1, 1, 0, 0, 7004.2363, -5815.4536, 64.85308, 0.970486223697662353, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82523, 16314, 530, 0, 0, 1, 1, 0, 0, 7023.6147, -5780.621, 84.95819, 0.942538857460021972, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82530, 16314, 530, 0, 0, 1, 1, 0, 0, 7018.4077, -5655.7944, 80.863686, 4.746488571166992187, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82534, 16314, 530, 0, 0, 1, 1, 0, 0, 7007.4146, -5646.7217, 80.82516, 4.034592628479003906, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82721, 16314, 530, 0, 0, 1, 1, 0, 0, 7001.0156, -5658.835, 80.86369, 4.616763114929199218, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0);

-- Deatholme Acolyte
UPDATE `creature` SET `wander_distance`=3, `MovementType`=1 WHERE `guid`=82539;
DELETE FROM `creature` WHERE `guid` IN (82545,82537,82538,82514,82529,82536,82540,82528,82547,82531,82544,82527,82522,82722);
INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(82545, 16315, 530, 0, 0, 1, 1, 0, 0, 7019.394, -5656.379, 80.86369, 5.50263214111328125, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(82537, 16315, 530, 0, 0, 1, 1, 0, 0, 7005.123, -5687.3057, 102.73737, 5.445427417755126953, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(82538, 16315, 530, 0, 0, 1, 1, 0, 0, 6998.022, -5687.269, 102.671326, 3.302978992462158203, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(82514, 16315, 530, 0, 0, 1, 1, 0, 0, 7020.5127, -5738.437, 105.06328, 0.675088286399841308, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(82529, 16315, 530, 0, 0, 1, 1, 0, 0, 7017.0967, -5712.483, 106.690636, 0.518938064575195312, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82536, 16315, 530, 0, 0, 1, 1, 0, 0, 6968.924, -5700.384, 92.17129, 4.382868766784667968, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82540, 16315, 530, 0, 0, 1, 1, 0, 0, 7057.357, -5684.993, 93.18661, 2.646962404251098632, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82528, 16315, 530, 0, 0, 1, 1, 0, 0, 7059.1665, -5732.9517, 84.30424, 4.299655914306640625, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82547, 16315, 530, 0, 0, 1, 1, 0, 0, 7040.532, -5668.764, 82.169365, 2.28472900390625, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82531, 16315, 530, 0, 0, 1, 1, 0, 0, 7053.874, -5716.201, 84.314766, 0.234128057956695556, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82544, 16315, 530, 0, 0, 1, 1, 0, 0, 6981.289, -5682.581, 82.57476, 2.01177978515625, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82527, 16315, 530, 0, 0, 1, 1, 0, 0, 6983.612, -5726.4375, 84.31618, 3.875350236892700195, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82522, 16315, 530, 0, 0, 1, 1, 0, 0, 6986.226, -5746.8867, 84.32931, 2.400921344757080078, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(82722, 16315, 530, 0, 0, 1, 1, 0, 0, 7051.5356, -5811.5474, 46.646347, 2.067746639251708984, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0);

-- Pathing for Deatholme Acolyte Entry: 16315
SET @NPC := 82545;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,7019.9004,-5656.8804,80.780396,0,0,0,0,100,0),
(@PATH,2,7026.0024,-5658.157,81.05498,0,0,0,0,100,0),
(@PATH,3,7034.332,-5662.384,81.58098,0,0,0,0,100,0),
(@PATH,4,7040.5137,-5668.5884,82.08218,0,0,0,0,100,0),
(@PATH,5,7047.8506,-5682.1416,83.382835,0,0,0,0,100,0),
(@PATH,6,7053.4814,-5695.8267,84.24979,0,0,0,0,100,0),
(@PATH,7,7054.388,-5710.0835,84.32376,0,0,0,0,100,0),
(@PATH,8,7057.6626,-5714.525,84.231926,0,0,0,0,100,0),
(@PATH,9,7062.471,-5713.8022,84.26095,0,0,0,0,100,0),
(@PATH,10,7064.8384,-5704.6904,85.973885,0,0,0,0,100,0),
(@PATH,11,7063.4204,-5693.1353,90.06106,0,0,0,0,100,0),
(@PATH,12,7056.7983,-5684.974,93.249886,0,0,0,0,100,0),
(@PATH,13,7046.176,-5681.1313,98.17687,0,0,0,0,100,0),
(@PATH,14,7036.658,-5680.9824,101.35006,0,0,0,0,100,0),
(@PATH,15,7028.906,-5683.0977,102.596794,0,0,0,0,100,0),
(@PATH,16,7036.658,-5680.9824,101.35006,0,0,0,0,100,0),
(@PATH,17,7046.176,-5681.1313,98.17687,0,0,0,0,100,0),
(@PATH,18,7056.7983,-5684.974,93.249886,0,0,0,0,100,0),
(@PATH,19,7063.4204,-5693.1353,90.06106,0,0,0,0,100,0),
(@PATH,20,7064.8384,-5704.6904,85.973885,0,0,0,0,100,0),
(@PATH,21,7062.471,-5713.8022,84.26095,0,0,0,0,100,0),
(@PATH,22,7057.6626,-5714.525,84.231926,0,0,0,0,100,0),
(@PATH,23,7054.388,-5710.0835,84.32376,0,0,0,0,100,0),
(@PATH,24,7053.4814,-5695.8267,84.24979,0,0,0,0,100,0),
(@PATH,25,7047.8506,-5682.1416,83.382835,0,0,0,0,100,0),
(@PATH,26,7040.5137,-5668.5884,82.08218,0,0,0,0,100,0),
(@PATH,27,7034.332,-5662.384,81.58098,0,0,0,0,100,0),
(@PATH,28,7026.0024,-5658.157,81.05498,0,0,0,0,100,0);

-- Pathing for Deatholme Acolyte Entry: 16315
SET @NPC := 82537;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,7005.509,-5689.2285,102.36206,0,0,0,0,100,0),
(@PATH,2,7008.458,-5692.7563,102.44194,0,0,0,0,100,0),
(@PATH,3,7012.471,-5694.4805,102.39561,0,0,0,0,100,0),
(@PATH,4,7017.362,-5693.4585,102.40703,0,0,0,0,100,0),
(@PATH,5,7020.9595,-5689.228,102.37142,0,0,0,0,100,0),
(@PATH,6,7021.114,-5682.309,102.35921,0,0,0,0,100,0),
(@PATH,7,7018.1533,-5678.813,102.36813,0,0,0,0,100,0),
(@PATH,8,7012.334,-5677.6343,102.36711,0,0,0,0,100,0),
(@PATH,9,7007.7095,-5679.725,102.36724,0,0,0,0,100,0),
(@PATH,10,7004.7573,-5684.2275,102.37097,0,0,0,0,100,0);

-- Pathing for Deatholme Acolyte Entry: 16315
SET @NPC := 82538;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,6998.1616,-5687.537,102.5845,0,0,0,0,100,0),
(@PATH,2,6987.718,-5688.9478,100.59123,0,0,0,0,100,0),
(@PATH,3,6975.971,-5694.044,95.72439,0,0,0,0,100,0),
(@PATH,4,6968.481,-5702.578,91.3148,0,0,0,0,100,0),
(@PATH,5,6967.3354,-5713.7583,87.91317,0,0,0,0,100,0),
(@PATH,6,6971.3066,-5723.894,84.36391,0,0,0,0,100,0),
(@PATH,7,6977.486,-5726.349,84.29573,0,0,0,0,100,0),
(@PATH,8,6981.325,-5722.795,84.23877,0,0,0,0,100,0),
(@PATH,9,6979.14,-5713.551,84.424385,0,0,0,0,100,0),
(@PATH,10,6978.81,-5693.6313,83.435234,0,0,0,0,100,0),
(@PATH,11,6981.4985,-5678.178,82.222755,0,0,0,0,100,0),
(@PATH,12,6986.7153,-5669.108,81.55677,0,0,0,0,100,0),
(@PATH,13,6992.815,-5663.3853,81.08229,0,0,0,0,100,0),
(@PATH,14,6997.6,-5659.572,80.76387,0,0,0,0,100,0),
(@PATH,15,6992.815,-5663.3853,81.08229,0,0,0,0,100,0),
(@PATH,16,6986.7153,-5669.108,81.55677,0,0,0,0,100,0),
(@PATH,17,6981.4985,-5678.178,82.222755,0,0,0,0,100,0),
(@PATH,18,6978.81,-5693.6313,83.435234,0,0,0,0,100,0),
(@PATH,19,6979.14,-5713.551,84.424385,0,0,0,0,100,0),
(@PATH,20,6981.325,-5722.795,84.23877,0,0,0,0,100,0),
(@PATH,21,6977.486,-5726.349,84.29573,0,0,0,0,100,0),
(@PATH,22,6971.3066,-5723.894,84.36391,0,0,0,0,100,0),
(@PATH,23,6967.3354,-5713.7583,87.91317,0,0,0,0,100,0),
(@PATH,24,6968.481,-5702.578,91.3148,0,0,0,0,100,0),
(@PATH,25,6975.971,-5694.044,95.72439,0,0,0,0,100,0),
(@PATH,26,6987.63,-5688.986,100.55549,0,0,0,0,100,0);

-- Pathing for Deatholme Acolyte Entry: 16315
SET @NPC := 82514;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,7020.8555,-5738.1626,105.2407,0,0,0,0,100,0),
(@PATH,2,7023.218,-5752.3257,100.925026,0,0,0,0,100,0),
(@PATH,3,7025.0693,-5766.5073,91.8989,0,0,0,0,100,0),
(@PATH,4,7024.269,-5783.517,83.72383,0,0,0,0,100,0),
(@PATH,5,7019.844,-5795.6807,77.70369,0,0,0,0,100,0),
(@PATH,6,7009.5645,-5810.03,68.75157,0,0,0,0,100,0),
(@PATH,7,6996.7256,-5823.7837,59.22976,0,0,0,0,100,0),
(@PATH,8,6985.5815,-5841.487,49.218353,0,0,0,0,100,0),
(@PATH,9,6980.203,-5862.2065,38.689667,0,0,0,0,100,0),
(@PATH,10,6980.615,-5901.375,27.756826,0,0,0,0,100,0),
(@PATH,11,6980.203,-5862.2065,38.689667,0,0,0,0,100,0),
(@PATH,12,6985.5815,-5841.487,49.218353,0,0,0,0,100,0),
(@PATH,13,6996.7256,-5823.7837,59.22976,0,0,0,0,100,0),
(@PATH,14,7009.5645,-5810.03,68.75157,0,0,0,0,100,0),
(@PATH,15,7019.844,-5795.6807,77.70369,0,0,0,0,100,0),
(@PATH,16,7024.269,-5783.517,83.72383,0,0,0,0,100,0),
(@PATH,17,7025.0664,-5766.551,92.00779,0,0,0,0,100,0),
(@PATH,18,7023.218,-5752.3257,100.925026,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_23_07' WHERE sql_rev = '1639932916698986865';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
