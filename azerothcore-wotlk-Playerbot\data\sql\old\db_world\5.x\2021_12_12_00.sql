-- DB update 2021_12_11_08 -> 2021_12_12_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_11_08';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_11_08 2021_12_12_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1638820849350039500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1638820849350039500');

DELETE FROM `spell_group` WHERE `spell_id` IN (16878,24752,39233,23947,23948);
INSERT INTO `spell_group` VALUES
(1023,16878,0),
(1023,24752,0),
(1023,39233,0),
(1024,23947,0), 
(1024,23948,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_12_00' WHERE sql_rev = '1638820849350039500';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
