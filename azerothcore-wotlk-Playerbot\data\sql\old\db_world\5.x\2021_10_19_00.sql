-- DB update 2021_10_18_01 -> 2021_10_19_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_10_18_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_10_18_01 2021_10_19_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1634394523910362900'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1634394523910362900');

 -- Forsaken Thug
UPDATE `creature_template_addon` SET `mount` = 0, `bytes2` = 4097 WHERE `entry` = 3734;
UPDATE `creature_addon` SET `mount` = 0 WHERE `guid` IN (32905, 32906, 32907, 32908, 32909, 32910);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_10_19_00' WHERE sql_rev = '1634394523910362900';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
