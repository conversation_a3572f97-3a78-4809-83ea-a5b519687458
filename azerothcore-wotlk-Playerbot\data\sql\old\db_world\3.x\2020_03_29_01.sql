-- DB update 2020_03_29_00 -> 2020_03_29_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_03_29_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_03_29_00 2020_03_29_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1582663580844407800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1582663580844407800');

-- Set movement type to stay in one place
UPDATE `creature` SET `MovementType` = 0 WHERE `guid` IN (46414, 46416);

-- Deactivate paths for creature 46414 and 46416
UPDATE `creature_addon` SET `path_id` = 0 WHERE `guid` IN (46414, 46416);

-- Take over waypoints from creature 46414 to creature 46394
DELETE FROM `waypoint_data` WHERE `id` = 463940;
INSERT INTO `waypoint_data` (`id`, `point`, `position_x`, `position_y`, `position_z`, `orientation`, `delay`, `move_type`, `action`, `action_chance`, `wpguid`)
VALUES
(463940,1,9841.91,965.874,1307.26,0,0,0,0,100,0),
(463940,2,9847.21,980.266,1305.29,0,0,0,0,100,0),
(463940,3,9846.65,1006.46,1305.44,0,0,0,0,100,0),
(463940,4,9846.94,1037.57,1304.88,0,0,0,0,100,0),
(463940,5,9858.5,1069.92,1307.49,0,0,0,0,100,0),
(463940,6,9879.15,1092.46,1307.99,0,0,0,0,100,0),
(463940,7,9885.72,1118.77,1307.86,0,0,0,0,100,0),
(463940,8,9912.28,1140.93,1307.96,0,0,0,0,100,0),
(463940,9,9915.84,1181.2,1307.91,0,0,0,0,100,0),
(463940,10,9909.05,1205.88,1308.46,0,0,0,0,100,0),
(463940,11,9926.72,1238.11,1307.83,0,0,0,0,100,0),
(463940,12,9951.81,1245.91,1307.83,0,0,0,0,100,0),
(463940,13,9992.02,1246.51,1307.86,0,0,0,0,100,0),
(463940,14,10020.8,1284.3,1307.55,0,0,0,0,100,0),
(463940,15,9994.73,1247.44,1307.86,0,0,0,0,100,0),
(463940,16,9949.29,1243.79,1307.73,0,0,0,0,100,0),
(463940,17,9923.5,1239.4,1307.85,0,0,0,0,100,0),
(463940,18,9907.7,1206.18,1308.46,0,0,0,0,100,0),
(463940,19,9916.67,1166.02,1307.9,0,0,0,0,100,0),
(463940,20,9907.02,1133.67,1307.95,0,0,0,0,100,0),
(463940,21,9881.11,1108.69,1307.99,0,0,0,0,100,0),
(463940,22,9868.7,1077.61,1307.99,0,0,0,0,100,0),
(463940,23,9848.29,1050.17,1305.27,0,0,0,0,100,0),
(463940,24,9845.39,1007.59,1305.43,0,0,0,0,100,0);

-- Delete waypoints for creature 46414 and 46416
DELETE FROM `waypoint_data` WHERE `id` IN (464140, 464160);

-- Create the formation
DELETE FROM `creature_formations` WHERE `leaderGUID` = 46394;
INSERT INTO `creature_formations` (`leaderGUID`, `memberGUID`, `dist`, `angle`, `groupAI`, `point_1`, `point_2`) VALUES
(46394,46394,0,0,515,0,0),
(46394,46414,3,135,515,0,0),
(46394,46416,3,225,515,0,0);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
