-- DB update 2022_01_17_01 -> 2022_01_18_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_17_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_17_01 2022_01_18_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1642209403314565365'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1642209403314565365');

-- <PERSON><PERSON> Run
DELETE FROM `creature` WHERE `guid` IN (40193,40194,40195,40196,40240,40241,40242,40284,40288,40289,40290,40291,40296,40301,40302,40365,40671,40677,40678,40680,40712,40713,40714,40715,40718,40468,40230,40283,40286,40287,40297,40298,40299,40300,40338,40668,40674,40706,40481,40708);
DELETE FROM `creature_addon` WHERE `guid` IN (40193,40194,40195,40196,40240,40241,40242,40284,40288,40289,40290,40291,40296,40301,40302,40365,40671,40677,40678,40680,40712,40713,40714,40715,40718,40468,40230,40283,40286,40287,40297,40298,40299,40300,40338,40668,40674,40706,40481,40708);
INSERT INTO `creature` (`guid`,`id1`,`id2`,`id3`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(40193, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6597.9893, -937.82446, 473.77335, 0.209439516067504882, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40194, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6581.721, -1013.5572, 463.79858, 1.172463178634643554, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40195, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6595.478, -964.7803, 472.8459, 3.744056224822998046, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40196, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6606.426, -912.1112, 473.53186, 2.259292364120483398, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40240, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6500.4424, -850.4374, 472.95465, 4.504848957061767578, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40241, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6489.674, -784.32434, 473.6464, 0.226892799139022827, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40242, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6553.232, -841.71313, 473.59003, 0.154711350798606872, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40284, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6535.845, -834.43854, 474.60547, 4.304278373718261718, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40288, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6569.074, -806.16907, 475.7942, 0.657858967781066894, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40289, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6479.3735, -793.56195, 473.90875, 4.374718189239501953, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40290, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6581.0884, -804.00696, 474.8405, 3.724053382873535156, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40291, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6483.8745, -817.1497, 473.94272, 1.133222579956054687, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40296, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6516.1167, -814.5349, 474.74985, 2.449142217636108398, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40301, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6448.5664, -800.8992, 474.3046, 5.257455348968505859, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40302, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6432.1587, -759.84686, 473.15967, 3.224145412445068359, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(40365, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6414.7876, -782.52045, 471.68338, 4.049969673156738281, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40671, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6417.261, -818.1496, 470.33954, 3.518303632736206054, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40677, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6383.858, -749.6224, 469.33542, 4.047421932220458984, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40678, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6400.7476, -712.16486, 474.86307, 1.413716673851013183, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40680, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6373.806, -699.54706, 476.85983, 2.024573326110839843, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40712, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6377.0596, -797.05804, 457.78555, 3.528153657913208007, 300, 15, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40713, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6410.2524, -725.2984, 473.8544, 4.321902275085449218, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40714, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6350.9453, -734.53973, 471.33276, 5.67474365234375, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40715, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6351.046, -717.2646, 472.2365, 5.79449319839477539, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40718, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6287.962, -598.7009, 466.59583, 2.396016359329223632, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40468, 7107, 7108, 0, 1, 0, 0, 1, 1, 0, 6279.444, -614.3792, 472.5725, 2.256314516067504882, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40230, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6580.9106, -846.0111, 474.0431, 2.908204555511474609, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40283, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6546.847, -842.53314, 473.8505, 3.269372463226318359, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40286, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6479.9087, -786.9245, 474.54443, 3.862565040588378906, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40287, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6577.2773, -798.1775, 475.03653, 1.438873529434204101, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40297, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6589.328, -856.6679, 474.59833, 3.874630928039550781, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40298, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6558.4224, -823.91797, 475.64575, 5.380662918090820312, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40299, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6409.626, -718.0106, 475.00592, 3.664785385131835937, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40300, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6342.289, -727.19995, 470.21567, 6.003914833068847656, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40338, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6302.0747, -757.5039, 468.49915, 5.619960308074951171, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40668, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6327.2744, -623.5396, 476.49002, 1.379523515701293945, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40674, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6297.897, -617.827, 471.6025, 2.321287870407104492, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40706, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6290.3145, -635.4162, 483.368, 5.811946392059326171, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40481, 7111, 0, 0, 1, 0, 0, 1, 1, 0, 6281.056, -651.52264, 489.869, 4.186584949493408203, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40708, 10648, 0, 0, 1, 0, 0, 1, 1, 0, 6279.093, -603.6922, 467.83997, 1.442377448081970214, 300, 2, 0, 1, 0, 1, 0, 0, 0, '', 0);

-- Pathing for Jadefire Betrayer Entry: 7108
SET @NPC := 40302;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,6431.575,-759.89514,473.14578,0,0,0,0,100,0),
(@PATH,2,6415.6743,-757.93774,473.14578,0,0,0,0,100,0),
(@PATH,3,6399.2617,-760.9202,470.7167,0,0,0,0,100,0),
(@PATH,4,6406.117,-791.04846,468.84244,0,0,0,0,100,0),
(@PATH,5,6424.424,-803.3945,471.96426,0,0,0,0,100,0),
(@PATH,6,6436.6743,-790.0301,473.39835,0,0,0,0,100,0),
(@PATH,7,6466.341,-799.6447,474.1249,0,0,0,0,100,0),
(@PATH,8,6478.2183,-818.8252,474.33282,0,0,0,0,100,0),
(@PATH,9,6496.9536,-828.95374,473.3863,0,0,0,0,100,0),
(@PATH,10,6520.4375,-828.62994,473.9445,0,0,0,0,100,0),
(@PATH,11,6543.6343,-830.434,474.0695,0,0,0,0,100,0),
(@PATH,12,6552.5806,-842.2002,473.697,0,0,0,0,100,0),
(@PATH,13,6571.129,-847.305,473.45517,0,0,0,0,100,0),
(@PATH,14,6583.8125,-830.9744,474.79904,0,0,0,0,100,0),
(@PATH,15,6573.4272,-806.39954,475.57468,0,0,0,0,100,0),
(@PATH,16,6558.5117,-795.7527,475.39703,0,0,0,0,100,0),
(@PATH,17,6529.44,-795.3073,473.6696,0,0,0,0,100,0),
(@PATH,18,6513.4146,-788.9368,474.66885,0,0,0,0,100,0),
(@PATH,19,6490.856,-802.297,474.06464,0,0,0,0,100,0),
(@PATH,20,6469.944,-795.0282,474.02267,0,0,0,0,100,0),
(@PATH,21,6451.1313,-779.17725,474.14835,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_18_00' WHERE sql_rev = '1642209403314565365';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
