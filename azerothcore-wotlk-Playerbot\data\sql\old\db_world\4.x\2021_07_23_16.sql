-- DB update 2021_07_23_15 -> 2021_07_23_16
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_07_23_15';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_07_23_15 2021_07_23_16 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1626462644022857380'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1626462644022857380');

UPDATE `creature_template` SET `spell_school_immune_mask` = 0 WHERE `entry` = 89; 

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_07_23_16' WHERE sql_rev = '1626462644022857380';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
