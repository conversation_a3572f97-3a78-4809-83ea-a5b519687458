-- DB update 2021_08_25_01 -> 2021_08_25_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_25_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_25_01 2021_08_25_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1629322815778584087'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1629322815778584087');

UPDATE `gameobject` SET `position_x` = 4217.04, `position_y` = 906.04, `position_z` = -8.08 WHERE `GUID` = 48120;
UPDATE `gameobject` SET `position_x` = 4222.18, `position_y` = 914.03, `position_z` = -7.44 WHERE `GUID` = 63507;


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_25_02' WHERE sql_rev = '1629322815778584087';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
