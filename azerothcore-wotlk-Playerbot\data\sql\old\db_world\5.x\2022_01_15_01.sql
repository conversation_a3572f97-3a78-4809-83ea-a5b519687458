-- DB update 2022_01_15_00 -> 2022_01_15_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_15_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_15_00 2022_01_15_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1642204504731558189'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1642204504731558189');

-- Felpaw Village, Felwood
DELETE FROM `creature` WHERE `guid` IN (39690,39691,40303,40304,40305,40306,40307,40308,40314,40386,40388,40389,40390,40391,40393,40394,40397,40398,40401,40403,40408,40409,40413,40414,40415,40417,40421,40424,40430,40464,40472,40604,40605,40608,40611,40612,40613,40615,40621,40623,40625,40627,40629,40631,39733,40134,40153,40482,40483,40427,40473);
DELETE FROM `creature_addon` WHERE `guid` IN (39690,39691,40303,40304,40305,40306,40307,40308,40314,40386,40388,40389,40390,40391,40393,40394,40397,40398,40401,40403,40408,40409,40413,40414,40415,40417,40421,40424,40430,40464,40472,40604,40605,40608,40611,40612,40613,40615,40621,40623,40625,40627,40629,40631,39733,40134,40153,40482,40483,40427,40473);
INSERT INTO `creature` (`guid`,`id1`,`id2`,`id3`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(39690, 7156, 0, 0, 1, 0, 0, 1, 1, 0, 6752.151, -1961.7408, 551.0034, 2.897246599197387695, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0),
(39691, 7156, 0, 0, 1, 0, 0, 1, 1, 0, 6826.352, -1967.1415, 551.7001, 0.087266460061073303, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40303, 7157, 0, 0, 1, 0, 0, 1, 1, 0, 6836.794, -1954.4425, 550.3044, 4.97418832778930664, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(40304, 7157, 0, 0, 1, 0, 0, 1, 1, 0, 6747.612, -1965.1079, 551.0034, 1.466076612472534179, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0),
(40305, 7158, 0, 0, 1, 0, 0, 1, 1, 0, 6860.563, -1964.6693, 556.07733, 1.868465900421142578, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40306, 7156, 7157, 0, 1, 0, 0, 1, 1, 0, 6906.3325, -1812.831, 571.74634, 3.124139308929443359, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40307, 7156, 7157, 0, 1, 0, 0, 1, 1, 0, 6907.568, -1824.5265, 569.79987, 2.652148962020874023, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40308, 7156, 7157, 0, 1, 0, 0, 1, 1, 0, 6732.4287, -1885.0846, 549.65784, 2.639858484268188476, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(40314, 7156, 7157, 0, 1, 0, 0, 1, 1, 0, 6836.325, -1875.1724, 550.6761, 5.632167816162109375, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40386, 7157, 7158, 0, 1, 0, 0, 1, 1, 0, 6911.731, -1813.9487, 571.83374, 3.90445256233215332, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0),
(40388, 7157, 7158, 0, 1, 0, 0, 1, 1, 0, 6920.1743, -1834.089, 571.92725, 1.535889744758605957, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40389, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6717.204, -1983.8384, 559.4351, 4.812276840209960937, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40390, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6873.1665, -2038.6376, 581.6539, 3.997686624526977539, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40391, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6707.918, -1899.5065, 549.25116, 2.629336118698120117, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40393, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6650.4067, -1949.7196, 549.8623, 1.452553272247314453, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40394, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6733.7173, -1955.1018, 549.71326, 3.353776931762695312, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40397, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6702.9175, -1961.2802, 553.9784, 0.38945859670639038, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40398, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6689.4556, -2008.2662, 560.1293, 6.061192035675048828, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40401, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6852.629, -1939.76, 551.0034, 3.651977300643920898, 300, 10, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40403, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6816.777, -2014.8325, 568.51807, 5.54440164566040039, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40408, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6852.9307, -1980.0747, 551.7032, 0.850379586219787597, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40409, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6912.4556, -1800.7333, 573.76605, 3.690003395080566406, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40413, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6884.3813, -1815.441, 569.1466, 1.413716673851013183, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40414, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6671.831, -1937.1498, 549.9153, 5.767045497894287109, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(40415, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6783.9683, -2017.4205, 567.54736, 4.150726318359375, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40417, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6854.4717, -1891.2478, 551.0034, 0.333302795886993408, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40421, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6848.996, -1814.8412, 564.1588, 2.317746877670288085, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40424, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6889.5596, -1947.1342, 572.1914, 5.422736167907714843, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40430, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6901.011, -2020.2257, 583.0537, 4.345870018005371093, 300, 6, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40464, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6840.962, -2011.3381, 572.1896, 2.666453838348388671, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0),
(40472, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6850.334, -1898.8638, 550.99396, 0.419144600629806518, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40604, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6748.8423, -1888.2213, 549.94415, 5.060978889465332031, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40605, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6908.5566, -1872.0408, 568.9377, 1.676499843597412109, 300, 9, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40608, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6756.5103, -1986.3594, 551.00354, 1.836662650108337402, 300, 4, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40611, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6811.798, -1862.0892, 551.0034, 3.028398513793945312, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40612, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6785.5903, -1867.4802, 551.0258, 5.412777900695800781, 300, 5, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40613, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6816.9775, -1818.9469, 562.42096, 5.254460334777832031, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40615, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6799.929, -1833.5918, 558.3517, 0.822560429573059082, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40621, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6683.4727, -1983.9648, 553.7465, 5.527495384216308593, 300, 7, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40623, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6633.604, -1994.87, 551.8855, 1.831908464431762695, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40625, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6659.386, -2017.5638, 552.1679, 5.303110122680664062, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40627, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6893.018, -1907.9801, 568.61505, 3.519721269607543945, 300, 9, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40629, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6789.6323, -1987.0955, 563.5386, 3.399090290069580078, 300, 9, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40631, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6813.097, -1962.2092, 551.4164, 1.928655982017517089, 300, 9, 0, 1, 0, 1, 0, 0, 0, '', 0),
(39733, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6849.124, -1881.0748, 551.00366, 3.333578824996948242, 300, 1, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40134, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6679.4087, -1953.9244, 551.26965, 3.954087495803833007, 300, 3, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40153, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6782.4507, -1952.2059, 551.1027, 0.982004404067993164, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40482, 7156, 7157, 7158, 1, 0, 0, 1, 1, 0, 6854.5806, -1923.5446, 551.0034, 2.179383754730224609, 300, 8, 0, 1, 0, 1, 0, 0, 0, '', 0),
(40427, 9462, 0, 0, 1, 0, 0, 1, 1, 0, 6836.5737, -1966.5795, 551.4811, 1.553343057632446289, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0),
(40473, 10916, 0, 0, 1, 0, 0, 1, 1, 0, 6912.8394, -1821.6619, 570.90906, 2.827433347702026367, 300, 0, 0, 1, 0, 0, 0, 0, 0, '', 0);

-- Pathing for Deadwood Avenger Entry: 7157
SET @NPC := 40303;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,6836.2563,-1958.3312,551.14636,0,50000,0,0,100,0),
(@PATH,2,6832.138,-1946.8033,548.6861,0,40000,0,0,100,0);

-- Pathing for Deadwood Shaman Entry: 7158
SET @NPC := 40464;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=6839.8213,`position_y`=-2010.7513,`position_z`=571.88824 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,6839.8213,-2010.7513,571.88824,0,0,0,0,100,0),
(@PATH,2,6825.454,-2003.0621,567.79156,0,0,0,0,100,0),
(@PATH,3,6808.6484,-1998.2537,565.77673,0,0,0,0,100,0),
(@PATH,4,6797.5586,-1997.8064,565.4205,0,0,0,0,100,0),
(@PATH,5,6785.587,-2006.0873,566.36304,0,0,0,0,100,0),
(@PATH,6,6779.187,-2018.8337,567.20764,0,0,0,0,100,0),
(@PATH,7,6787.61,-2023.3473,569.01,0,0,0,0,100,0),
(@PATH,8,6810.3037,-2020.6549,569.0567,0,0,0,0,100,0),
(@PATH,9,6826.6704,-2013.8262,569.9943,0,0,0,0,100,0),
(@PATH,10,6844.8022,-2016.8806,574.1186,0,0,0,0,100,0),
(@PATH,11,6849.61,-2027.3008,577.63873,0,0,0,0,100,0),
(@PATH,12,6860.901,-2037.0543,581.5809,0,0,0,0,100,0),
(@PATH,13,6872.202,-2040.5083,582.4156,0,0,0,0,100,0),
(@PATH,14,6885.85,-2040.0408,581.4642,0,0,0,0,100,0),
(@PATH,15,6893.2324,-2036.9629,582.4974,0,0,0,0,100,0),
(@PATH,16,6906.8237,-2029.5887,585.2558,0,0,0,0,100,0),
(@PATH,17,6912.1626,-2023.863,586.6153,0,0,0,0,100,0),
(@PATH,18,6908.6235,-2015.8668,585.2845,0,0,0,0,100,0),
(@PATH,19,6901.057,-2009.9861,583.15265,0,0,0,0,100,0),
(@PATH,20,6891.577,-2012.1641,579.04706,0,0,0,0,100,0),
(@PATH,21,6884.7573,-2017.9587,578.0085,0,0,0,0,100,0),
(@PATH,22,6875.134,-2024.5499,577.02216,0,0,0,0,100,0),
(@PATH,23,6870.844,-2026.7975,577.436,0,0,0,0,100,0),
(@PATH,24,6859.9976,-2023.9427,577.399,0,0,0,0,100,0),
(@PATH,25,6851.0894,-2018.5762,575.4251,0,0,0,0,100,0);

-- Pathing for Deadwood Shaman Entry: 7158
SET @NPC := 40414;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=6674.144,`position_y`=-1938.4623,`position_z`=550.1063 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,6674.144,-1938.4623,550.1063,0,0,0,0,100,0),
(@PATH,2,6680.9253,-1945.6392,550.8187,0,0,0,0,100,0),
(@PATH,3,6693.1694,-1952.7042,551.0707,0,0,0,0,100,0),
(@PATH,4,6705.156,-1953.2203,552.2102,0,0,0,0,100,0),
(@PATH,5,6720.814,-1957.4888,552.2864,0,0,0,0,100,0),
(@PATH,6,6725.9185,-1964.8463,552.3895,0,0,0,0,100,0),
(@PATH,7,6725.5947,-1978.221,559.3325,0,0,0,0,100,0),
(@PATH,8,6722.6387,-1987.695,560.3678,0,0,0,0,100,0),
(@PATH,9,6716.1553,-1990.7474,560.49414,0,0,0,0,100,0),
(@PATH,10,6707.6743,-1986.0039,559.0332,0,0,0,0,100,0),
(@PATH,11,6697.0845,-1982.1294,556.9809,0,0,0,0,100,0),
(@PATH,12,6689.924,-1972.5391,552.7983,0,0,0,0,100,0),
(@PATH,13,6681.2456,-1970.9508,551.23065,0,0,0,0,100,0),
(@PATH,14,6661.076,-1962.1512,550.74835,0,0,0,0,100,0),
(@PATH,15,6648.992,-1958.2153,550.6717,0,0,0,0,100,0),
(@PATH,16,6643.303,-1953.4427,550.5903,0,0,0,0,100,0),
(@PATH,17,6641.8594,-1941.9397,549.2579,0,0,0,0,100,0),
(@PATH,18,6649.981,-1935.5818,547.8481,0,0,0,0,100,0),
(@PATH,19,6663.953,-1934.707,549.12335,0,0,0,0,100,0);

-- Pathing for Deadwood Den Watcher Entry: 7156
SET @NPC := 40308;
SET @PATH := @NPC * 10;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,6742.783,-1884.2014,550.0086,0,0,0,0,100,0),
(@PATH,2,6755.027,-1882.4729,550.80035,0,0,0,0,100,0),
(@PATH,3,6762.4033,-1880.247,550.92535,0,0,0,0,100,0),
(@PATH,4,6772.671,-1875.1853,550.8313,0,0,0,0,100,0),
(@PATH,5,6785.2915,-1858.1077,552.3018,0,0,0,0,100,0),
(@PATH,6,6799.3403,-1845.1311,554.64874,0,0,0,0,100,0),
(@PATH,7,6813.109,-1824.484,560.7498,0,0,0,0,100,0),
(@PATH,8,6828.3228,-1820.8368,561.93854,0,0,0,0,100,0),
(@PATH,9,6845.292,-1826.416,561.6869,0,0,0,0,100,0),
(@PATH,10,6867.53,-1825.0946,563.73517,0,0,0,0,100,0),
(@PATH,11,6884.5103,-1824.497,566.3377,0,0,0,0,100,0),
(@PATH,12,6898.2207,-1826.3928,568.0859,0,0,0,0,100,0),
(@PATH,13,6880.141,-1823.8627,565.93854,0,0,0,0,100,0),
(@PATH,14,6862.512,-1819.7505,564.43787,0,0,0,0,100,0),
(@PATH,15,6853.1475,-1807.4088,565.7855,0,0,0,0,100,0),
(@PATH,16,6829.3037,-1811.7084,564.3947,0,0,0,0,100,0),
(@PATH,17,6809.335,-1826.6364,560.1082,0,0,0,0,100,0),
(@PATH,18,6803.229,-1842.7974,554.82904,0,0,0,0,100,0),
(@PATH,19,6802.725,-1864.1155,551.0451,0,0,0,0,100,0),
(@PATH,20,6793.358,-1871.483,550.9563,0,0,0,0,100,0),
(@PATH,21,6780.5254,-1878.0662,550.2063,0,0,0,0,100,0),
(@PATH,22,6761.8857,-1883.431,550.92535,0,0,0,0,100,0),
(@PATH,23,6750.664,-1881.2048,550.42535,0,0,0,0,100,0),
(@PATH,24,6722.1313,-1889.0719,549.3373,0,0,0,0,100,0),
(@PATH,25,6713.2954,-1894.2188,549.21985,0,0,0,0,100,0),
(@PATH,26,6708.041,-1900.7806,549.07025,0,0,0,0,100,0),
(@PATH,27,6710.7495,-1892.5039,549.4479,0,0,0,0,100,0),
(@PATH,28,6709.9814,-1882.2263,550.2838,0,0,0,0,100,0),
(@PATH,29,6734.5825,-1882.2701,549.99054,0,0,0,0,100,0),
(@PATH,30,6750.664,-1881.2048,550.42535,0,0,0,0,100,0),
(@PATH,31,6761.8857,-1883.431,550.92535,0,0,0,0,100,0),
(@PATH,32,6780.5254,-1878.0662,550.2063,0,0,0,0,100,0),
(@PATH,33,6793.358,-1871.483,550.9563,0,0,0,0,100,0),
(@PATH,34,6802.725,-1864.1155,551.0451,0,0,0,0,100,0),
(@PATH,35,6803.229,-1842.7974,554.82904,0,0,0,0,100,0),
(@PATH,36,6809.335,-1826.6364,560.1082,0,0,0,0,100,0),
(@PATH,37,6829.3037,-1811.7084,564.3947,0,0,0,0,100,0),
(@PATH,38,6853.1475,-1807.4088,565.7855,0,0,0,0,100,0),
(@PATH,39,6862.512,-1819.7505,564.43787,0,0,0,0,100,0),
(@PATH,40,6880.141,-1823.8627,565.93854,0,0,0,0,100,0),
(@PATH,41,6898.2207,-1826.3928,568.0859,0,0,0,0,100,0),
(@PATH,42,6884.5103,-1824.497,566.3377,0,0,0,0,100,0),
(@PATH,43,6867.53,-1825.0946,563.73517,0,0,0,0,100,0),
(@PATH,44,6845.292,-1826.416,561.6869,0,0,0,0,100,0),
(@PATH,45,6828.3228,-1820.8368,561.93854,0,0,0,0,100,0),
(@PATH,46,6813.109,-1824.484,560.7498,0,0,0,0,100,0),
(@PATH,47,6799.3403,-1845.1311,554.64874,0,0,0,0,100,0),
(@PATH,48,6785.2915,-1858.1077,552.3018,0,0,0,0,100,0),
(@PATH,49,6772.671,-1875.1853,550.8313,0,0,0,0,100,0),
(@PATH,50,6762.4033,-1880.247,550.92535,0,0,0,0,100,0),
(@PATH,51,6755.027,-1882.4729,550.80035,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_15_01' WHERE sql_rev = '1642204504731558189';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
