-- DB update 2021_12_22_01 -> 2021_12_23_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_12_22_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_12_22_01 2021_12_23_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1637556174175999600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1637556174175999600');
-- Pathing for Sunstrider Guardian Entry: 15371 GUID 55176 
-- 0x2044EC42400F02C00000AC000381B3A0 .go xyz 10378.912 -6399.252 49.71716
SET @NPC := 55176;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=10378.912,`position_y`=-6399.252,`position_z`=49.71716 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`, `visibilityDistanceType`, `auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,10378.912,-6399.252,49.71716,0,0,0,0,100,0),
(@PATH,2,10383.641,-6413.364,49.718002,0,0,0,0,100,0),
(@PATH,3,10380.581,-6423.8926,47.823597,0,0,0,0,100,0),
(@PATH,4,10374.763,-6431.1235,45.994095,0,0,0,0,100,0),
(@PATH,5,10360.774,-6435.0903,42.68185,0,0,0,0,100,0),
(@PATH,6,10346.349,-6430.6836,39.25982,0,0,0,0,100,0),
(@PATH,7,10337.379,-6419.7407,38.532295,0,0,0,0,100,0),
(@PATH,8,10359.71,-6411.252,38.53222,0,0,0,0,100,0),
(@PATH,9,10351.961,-6386.578,38.527874,0,0,0,0,100,0),
(@PATH,10,10346.69,-6365.3022,34.769882,0,0,0,0,100,0),
(@PATH,11,10349.388,-6344.54,31.205553,0,0,0,0,100,0),
(@PATH,12,10366.51,-6347.947,32.08055,0,0,0,0,100,0),
(@PATH,13,10382.246,-6341.5537,34.067444,0,0,0,0,100,0),
(@PATH,14,10408.614,-6344.924,36.926704,0,0,0,0,100,0),
(@PATH,15,10382.403,-6341.804,34.086304,0,0,0,0,100,0),
(@PATH,16,10363.819,-6347.5557,31.820726,0,0,0,0,100,0),
(@PATH,17,10347.966,-6345.291,31.328112,0,0,0,0,100,0),
(@PATH,18,10351.415,-6317.104,29.917639,0,0,0,0,100,0),
(@PATH,19,10344.479,-6293.903,24.547476,0,0,0,0,100,0),
(@PATH,20,10346.602,-6264.276,24.630213,0,0,0,0,100,0),
(@PATH,21,10346.754,-6233.914,26.920923,0,0,0,0,100,0),
(@PATH,22,10333.727,-6217.84,27.230778,0,0,0,0,100,0),
(@PATH,23,10300.165,-6204.8047,26.07338,0,0,0,0,100,0),
(@PATH,24,10282.401,-6196.8975,24.918736,0,0,0,0,100,0),
(@PATH,25,10300.165,-6204.8047,26.07338,0,0,0,0,100,0),
(@PATH,26,10333.727,-6217.84,27.230778,0,0,0,0,100,0),
(@PATH,27,10346.754,-6233.914,26.920923,0,0,0,0,100,0),
(@PATH,28,10346.602,-6264.276,24.630213,0,0,0,0,100,0),
(@PATH,29,10344.479,-6293.903,24.547476,0,0,0,0,100,0),
(@PATH,30,10351.415,-6317.104,29.917639,0,0,0,0,100,0),
(@PATH,31,10347.966,-6345.291,31.328112,0,0,0,0,100,0),
(@PATH,32,10363.819,-6347.5557,31.820726,0,0,0,0,100,0),
(@PATH,33,10382.403,-6341.804,34.086304,0,0,0,0,100,0),
(@PATH,34,10408.614,-6344.924,36.926704,0,0,0,0,100,0),
(@PATH,35,10382.246,-6341.5537,34.067444,0,0,0,0,100,0),
(@PATH,36,10366.51,-6347.947,32.08055,0,0,0,0,100,0),
(@PATH,37,10349.388,-6344.54,31.205553,0,0,0,0,100,0),
(@PATH,38,10346.69,-6365.3022,34.769882,0,0,0,0,100,0),
(@PATH,39,10351.95,-6386.545,38.527786,0,0,0,0,100,0),
(@PATH,40,10359.826,-6411.208,38.5322,0,0,0,0,100,0),
(@PATH,41,10337.379,-6419.7407,38.532295,0,0,0,0,100,0),
(@PATH,42,10346.349,-6430.6836,39.25982,0,0,0,0,100,0),
(@PATH,43,10360.774,-6435.0903,42.68185,0,0,0,0,100,0),
(@PATH,44,10374.763,-6431.1235,45.994095,0,0,0,0,100,0),
(@PATH,45,10380.581,-6423.8926,47.823597,0,0,0,0,100,0),
(@PATH,46,10383.641,-6413.364,49.718002,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_12_23_00' WHERE sql_rev = '1637556174175999600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
