-- DB update 2020_09_05_00 -> 2020_09_05_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2020_09_05_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2020_09_05_00 2020_09_05_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1598881127506300800'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1598881127506300800');
/*
 * General: Build Update
 * Update by Knindza | <www.azerothcore.org>
 * Copyright (C) <www.shadowburn.net> & <www.lichbane.com>
*/

/* Content 3.3.5 */ 
SET @Build := 12340;

UPDATE `creature_template` SET `VerifiedBuild` = @Build WHERE `entry` IN (31366, 31799, 31800, 31803, 34031, 34116, 34154, 35034, 35528, 36910, 36923, 42078, 38545, 39746, 39747, 39751, 39794, 39814, 39863, 39899, 40029, 40041, 40042, 40043, 40044, 40055, 40081, 40083, 40091, 40100, 40135, 40142, 40417, 40421, 40423, 40429, 40626, 40627, 40628, 40870, 39047, 39103, 39130, 39131, 39132, 39253, 39263, 39283, 39466, 39623, 39624, 39639, 39711, 39712, 39805, 39815, 39820, 39823, 39852, 39864, 39920, 39922, 39934, 39940, 39944, 39945, 40006, 40143, 40144, 40145, 40151, 40176, 40188, 40196, 40217, 40218, 40222, 40256, 40257, 40260, 40264, 40301, 40305, 40312, 40356, 40361, 40363, 40373, 40374, 40387, 40388, 40416, 40418, 40422, 40424, 40468, 40469, 40470, 40471, 40472, 40481, 40673, 40674, 40675, 40681, 40682, 40683, 40684, 43280, 43281, 43282);

--
-- END UPDATING QUERIES
--
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
