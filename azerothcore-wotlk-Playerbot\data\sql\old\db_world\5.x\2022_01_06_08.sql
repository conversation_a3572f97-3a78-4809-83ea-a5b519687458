-- DB update 2022_01_06_07 -> 2022_01_06_08
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_06_07';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_06_07 2022_01_06_08 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1641439653882850490'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1641439653882850490');

-- random movement for Stormwind Guard
UPDATE `creature` SET `wander_distance`=1,`MovementType`=1 WHERE `guid` IN (80443,80446,80447,80457,80458,80460,80461,80464,80465);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 81544;
SET @PATH := @NPC * 10;
DELETE FROM `creature` WHERE `guid`=@NPC;
INSERT INTO `creature` (`guid`,`id`,`map`,`zoneId`,`areaId`,`spawnMask`,`phaseMask`,`modelid`,`equipment_id`,`position_x`,`position_y`,`position_z`,`orientation`,`spawntimesecs`,`wander_distance`,`currentwaypoint`,`curhealth`,`curmana`,`MovementType`,`npcflag`,`unit_flags`,`dynamicflags`,`ScriptName`,`VerifiedBuild`) VALUES
(@NPC, 1423, 0, 0, 0, 1, 1, 0, 0, -9619.242, -1036.4723, 39.873875, 4.865544, 300, 0, 0, 1, 0, 2, 0, 0, 0, '', 0);
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9619.242,-1036.4723,39.873875,0,10000,0,0,100,0),
(@PATH,2,-9621.585,-1009.8045,41.460102,0,0,0,0,100,0),
(@PATH,3,-9622.9795,-993.51605,42.519413,0,0,0,0,100,0),
(@PATH,4,-9616.812,-971.4866,43.643192,0,0,0,0,100,0),
(@PATH,5,-9605.6875,-952.92535,43.846966,0,0,0,0,100,0),
(@PATH,6,-9589.601,-935.541,43.71594,0,0,0,0,100,0),
(@PATH,7,-9581.84,-917.2405,43.888416,0,0,0,0,100,0),
(@PATH,8,-9582.131,-892.79517,43.528683,0,0,0,0,100,0),
(@PATH,9,-9587.052,-870.24786,43.778683,0,0,0,0,100,0),
(@PATH,10,-9603.133,-848.3824,43.8069,0,0,0,0,100,0),
(@PATH,11,-9635.435,-813.6964,43.84379,0,0,0,0,100,0),
(@PATH,12,-9646.747,-797.55945,43.59379,0,0,0,0,100,0),
(@PATH,13,-9651.212,-783.6508,44.32621,0,0,0,0,100,0),
(@PATH,14,-9651.457,-762.5749,44.479652,0,0,0,0,100,0),
(@PATH,15,-9656.33,-740.673,44.619545,0,0,0,0,100,0),
(@PATH,16,-9656.645,-723.0509,44.664135,0,0,0,0,100,0),
(@PATH,17,-9649.04,-701.8206,46.00813,0,0,0,0,100,0),
(@PATH,18,-9643.346,-663.4222,49.364964,0,0,0,0,100,0),
(@PATH,19,-9628.837,-641.6657,51.14138,0,0,0,0,100,0),
(@PATH,20,-9622.42,-603.77734,52.849976,0,0,0,0,100,0),
(@PATH,21,-9619.694,-574.7324,54.198833,0,0,0,0,100,0),
(@PATH,22,-9617.313,-551.60187,54.49004,0,0,0,0,100,0),
(@PATH,23,-9611.551,-532.0573,54.778297,0,0,0,0,100,0),
(@PATH,24,-9602.272,-517.88434,56.768776,0,0,0,0,100,0),
(@PATH,25,-9593.436,-504.34235,57.526066,0,0,0,0,100,0),
(@PATH,26,-9589.494,-487.52823,57.76851,0,0,0,0,100,0),
(@PATH,27,-9590.743,-469.7306,57.816605,0,0,0,0,100,0),
(@PATH,28,-9600.497,-453.02713,57.780994,0,0,0,0,100,0),
(@PATH,29,-9608.497,-436.10513,57.36327,0,0,0,0,100,0),
(@PATH,30,-9614.505,-416.28842,57.664856,0,0,0,0,100,0),
(@PATH,31,-9617.868,-395.89767,57.955738,0,0,0,0,100,0),
(@PATH,32,-9619.874,-371.51694,57.580738,0,0,0,0,100,0),
(@PATH,33,-9620.904,-344.16104,57.28194,0,0,0,0,100,0),
(@PATH,34,-9620.884,-320.54407,57.52703,0,0,0,0,100,0),
(@PATH,35,-9618.642,-297.62848,57.18179,0,0,0,0,100,0),
(@PATH,36,-9612.653,-276.41525,57.762844,0,0,0,0,100,0),
(@PATH,37,-9608.691,-255.34322,57.321648,0,0,0,0,100,0),
(@PATH,38,-9598.686,-228.6033,57.3935,0,0,0,0,100,0),
(@PATH,39,-9588.345,-202.67969,57.812447,0,0,0,0,100,0),
(@PATH,40,-9578.535,-171.4949,57.50352,0,0,0,0,100,0),
(@PATH,41,-9567.966,-150.99425,57.1911,0,0,0,0,100,0),
(@PATH,42,-9553.459,-136.26813,57.280342,0,0,0,0,100,0),
(@PATH,43,-9549.182,-112.03646,57.552357,0,0,0,0,100,0),
(@PATH,44,-9548.437,-83.411896,57.64455,0,0,0,0,100,0),
(@PATH,45,-9546.561,-64.21647,57.190815,0,0,0,0,100,0),
(@PATH,46,-9542.052,-43.974174,56.690815,0,0,0,0,100,0),
(@PATH,47,-9531.666,-17.718859,56.00526,0,0,0,0,100,0),
(@PATH,48,-9518.505,4.58865,56.11089,0,0,0,0,100,0),
(@PATH,49,-9505.715,31.629665,56.48589,0,0,0,0,100,0),
(@PATH,50,-9495.036,54.661133,55.813526,0,0,0,0,100,0),
(@PATH,51,-9492.798,64.35981,56.027515,0,10000,0,0,100,0),
(@PATH,52,-9497.884,48.81619,55.947926,0,0,0,0,100,0),
(@PATH,53,-9503.121,36.35319,56.422554,0,0,0,0,100,0),
(@PATH,54,-9509.772,23.945095,56.53191,0,0,0,0,100,0),
(@PATH,55,-9517.83,6.747939,56.11089,0,0,0,0,100,0),
(@PATH,56,-9529.744,-15.72743,55.887096,0,0,0,0,100,0),
(@PATH,57,-9541.357,-44.91298,56.76772,0,0,0,0,100,0),
(@PATH,58,-9547.372,-71.95128,57.51955,0,0,0,0,100,0),
(@PATH,59,-9549.507,-109.44618,57.506702,0,0,0,0,100,0),
(@PATH,60,-9556.684,-136.55284,57.559395,0,0,0,0,100,0),
(@PATH,61,-9567.811,-151.01823,57.172302,0,0,0,0,100,0),
(@PATH,62,-9578.885,-170.2882,57.37852,0,0,0,0,100,0),
(@PATH,63,-9586.776,-195.13954,57.62852,0,0,0,0,100,0),
(@PATH,64,-9598.216,-224.65039,57.303413,0,0,0,0,100,0),
(@PATH,65,-9608.607,-258.07812,57.150505,0,0,0,0,100,0),
(@PATH,66,-9614.821,-289.09702,57.59048,0,0,0,0,100,0),
(@PATH,67,-9618.106,-312.99155,57.583427,0,0,0,0,100,0),
(@PATH,68,-9620.264,-340.5638,57.524616,0,0,0,0,100,0),
(@PATH,69,-9619.762,-370.95486,57.580738,0,0,0,0,100,0),
(@PATH,70,-9618.118,-393.6722,57.830738,0,0,0,0,100,0),
(@PATH,71,-9610.834,-427.73633,57.522766,0,0,0,0,100,0),
(@PATH,72,-9602.774,-445.21603,57.624012,0,0,0,0,100,0),
(@PATH,73,-9596.105,-461.79807,57.748386,0,0,0,0,100,0),
(@PATH,74,-9591.105,-476.25824,57.76851,0,0,0,0,100,0),
(@PATH,75,-9591.471,-488.46744,57.840042,0,0,0,0,100,0),
(@PATH,76,-9593.778,-501.4398,57.38178,0,0,0,0,100,0),
(@PATH,77,-9598.513,-512.4593,57.474308,0,0,0,0,100,0),
(@PATH,78,-9609.811,-533.6465,54.86504,0,0,0,0,100,0),
(@PATH,79,-9615.971,-550.82,54.49004,0,0,0,0,100,0),
(@PATH,80,-9618.966,-571.0148,54.374615,0,0,0,0,100,0),
(@PATH,81,-9620.506,-590.84235,53.78599,0,0,0,0,100,0),
(@PATH,82,-9623.553,-620.666,51.959106,0,0,0,0,100,0),
(@PATH,83,-9628.995,-638.22687,51.179222,0,0,0,0,100,0),
(@PATH,84,-9636.7,-653.1119,49.694065,0,0,0,0,100,0),
(@PATH,85,-9641.278,-663.94415,49.396946,0,0,0,0,100,0),
(@PATH,86,-9645.316,-681.18304,48.008854,0,0,0,0,100,0),
(@PATH,87,-9650.676,-701.9413,45.993725,0,0,0,0,100,0),
(@PATH,88,-9653.724,-722.3216,44.672924,0,0,0,0,100,0),
(@PATH,89,-9654.573,-742.36804,44.551186,0,0,0,0,100,0),
(@PATH,90,-9652.612,-766.4833,44.302162,0,0,0,0,100,0),
(@PATH,91,-9648.009,-792.97485,43.624306,0,0,0,0,100,0),
(@PATH,92,-9638.621,-808.64453,43.71879,0,0,0,0,100,0),
(@PATH,93,-9626.769,-822.28125,43.88491,0,0,0,0,100,0),
(@PATH,94,-9613.571,-836.70856,43.623062,0,0,0,0,100,0),
(@PATH,95,-9600.535,-851.83704,43.6819,0,0,0,0,100,0),
(@PATH,96,-9589.787,-865.6964,43.83097,0,0,0,0,100,0),
(@PATH,97,-9585.493,-875.5536,43.903683,0,0,0,0,100,0),
(@PATH,98,-9584.774,-898.893,43.903683,0,0,0,0,100,0),
(@PATH,99,-9586.0625,-915.4184,43.903553,0,0,0,0,100,0),
(@PATH,100,-9592.028,-931.4844,43.744373,0,0,0,0,100,0),
(@PATH,101,-9598.08,-942.4412,43.923214,0,0,0,0,100,0),
(@PATH,102,-9607.78,-959.0113,43.49467,0,0,0,0,100,0),
(@PATH,103,-9615.448,-974.1688,43.851933,0,0,0,0,100,0),
(@PATH,104,-9620.877,-993.4258,42.643192,0,0,0,0,100,0),
(@PATH,105,-9620.597,-1003.663,42.256245,0,0,0,0,100,0),
(@PATH,106,-9620.338,-1014.1092,41.06777,0,0,0,0,100,0),
(@PATH,107,-9619.945,-1024.6925,40.49306,0,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 80263;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9438.457,`position_y`=54.57856,`position_z`=56.019997 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9438.457,54.57856,56.019997,0,10000,0,0,100,0),
(@PATH,2,-9431.209,46.559135,56.693523,0,0,0,0,100,0),
(@PATH,3,-9421.634,36.470814,57.318523,0,0,0,0,100,0),
(@PATH,4,-9411.369,24.516819,58.24777,0,0,0,0,100,0),
(@PATH,5,-9401.32,13.384766,59.679165,0,0,0,0,100,0),
(@PATH,6,-9390.167,-1.145291,60.821873,0,0,0,0,100,0),
(@PATH,7,-9378.083,-13.014106,61.88828,0,0,0,0,100,0),
(@PATH,8,-9365.3545,-27.729383,63.365242,0,0,0,0,100,0),
(@PATH,9,-9351.892,-40.30295,64.81542,0,0,0,0,100,0),
(@PATH,10,-9337.786,-50.155273,65.67455,0,0,0,0,100,0),
(@PATH,11,-9329.192,-52.84429,66.13094,0,0,0,0,100,0),
(@PATH,12,-9314.485,-56.48058,66.70492,0,0,0,0,100,0),
(@PATH,13,-9302.469,-59.147026,66.93221,0,0,0,0,100,0),
(@PATH,14,-9290.404,-63.35688,67.5221,0,0,0,0,100,0),
(@PATH,15,-9281.472,-69.32259,68.097786,0,0,0,0,100,0),
(@PATH,16,-9270.33,-77.17632,68.6869,0,0,0,0,100,0),
(@PATH,17,-9258.7295,-86.89681,69.7136,0,0,0,0,100,0),
(@PATH,18,-9245.781,-95.48481,70.52708,0,0,0,0,100,0),
(@PATH,19,-9235.747,-101.56695,70.77646,0,0,0,0,100,0),
(@PATH,20,-9228.748,-104.13086,70.99169,0,0,0,0,100,0),
(@PATH,21,-9219.661,-106.70421,71.2539,0,0,0,0,100,0),
(@PATH,22,-9208.192,-109.80003,71.17455,0,0,0,0,100,0),
(@PATH,23,-9197.428,-112.18891,71.09542,0,0,0,0,100,0),
(@PATH,24,-9185.816,-112.74067,70.99557,0,0,0,0,100,0),
(@PATH,25,-9174.032,-111.84169,71.549034,0,0,0,0,100,0),
(@PATH,26,-9159.315,-106.84603,73.04197,0,0,0,0,100,0),
(@PATH,27,-9149.284,-100.66135,74.23655,0,0,0,0,100,0),
(@PATH,28,-9135.363,-91.4846,76.243866,0,0,0,0,100,0),
(@PATH,29,-9119.705,-80.96517,79.70452,0,0,0,0,100,0),
(@PATH,30,-9109.234,-73.007484,82.217705,0,0,0,0,100,0),
(@PATH,31,-9097.453,-63.842014,84.35332,0,0,0,0,100,0),
(@PATH,32,-9090.091,-58.360134,85.420944,0,0,0,0,100,0),
(@PATH,33,-9084.711,-52.9579,86.38237,0,0,0,0,100,0),
(@PATH,34,-9079.419,-48.8967,87.354294,0,0,0,0,100,0),
(@PATH,35,-9070.88,-43.417645,88.0645,0,10000,0,0,100,0),
(@PATH,36,-9083.599,-53.350803,86.42607,0,0,0,0,100,0),
(@PATH,37,-9098.672,-65.61827,84.054,0,0,0,0,100,0),
(@PATH,38,-9109.419,-75.7207,82.09222,0,0,0,0,100,0),
(@PATH,39,-9125.424,-85.16591,78.16302,0,0,0,0,100,0),
(@PATH,40,-9142.284,-95.87967,75.094696,0,0,0,0,100,0),
(@PATH,41,-9163.195,-108.53538,72.567116,0,0,0,0,100,0),
(@PATH,42,-9176.171,-111.98329,71.24508,0,0,0,0,100,0),
(@PATH,43,-9195.773,-112.79459,71.12057,0,0,0,0,100,0),
(@PATH,44,-9208.957,-109.31749,71.1289,0,0,0,0,100,0),
(@PATH,45,-9225.822,-104.71723,71.1289,0,0,0,0,100,0),
(@PATH,46,-9238.654,-99.04698,70.70847,0,0,0,0,100,0),
(@PATH,47,-9249.91,-91.48221,70.406715,0,0,0,0,100,0),
(@PATH,48,-9268.238,-77.892685,68.6869,0,0,0,0,100,0),
(@PATH,49,-9280.204,-67.65853,68.24964,0,0,0,0,100,0),
(@PATH,50,-9297.876,-60.612846,67.34437,0,0,0,0,100,0),
(@PATH,51,-9322.913,-53.600803,66.44393,0,0,0,0,100,0),
(@PATH,52,-9335.962,-49.374783,65.8247,0,0,0,0,100,0),
(@PATH,53,-9351.4795,-40.113823,64.81542,0,0,0,0,100,0),
(@PATH,54,-9362.026,-29.914822,63.764656,0,0,0,0,100,0),
(@PATH,55,-9376.168,-14.784288,62.244236,0,0,0,0,100,0),
(@PATH,56,-9397.745,6.895725,60.043953,0,0,0,0,100,0),
(@PATH,57,-9409.909,21.162977,58.827236,0,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 80488;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9800.407,`position_y`=706.3314,`position_z`=68.20654 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9800.407,706.3314,68.20654,0,0,0,0,100,0),
(@PATH,2,-9800.407,706.3314,68.20654,4.520402908325195312,60000,0,0,100,0),
(@PATH,3,-9789.82,713.63,68.20654,0,0,0,0,100,0),
(@PATH,4,-9789.82,713.63,68.20654,6.143558979034423828,60000,0,0,100,0),
(@PATH,5,-9796.794,723.85974,68.20654,0,0,0,0,100,0),
(@PATH,6,-9796.794,723.85974,68.20654,1.326450228691101074,60000,0,0,100,0),
(@PATH,7,-9807.694,717.24396,68.206024,0,0,0,0,100,0),
(@PATH,8,-9807.694,717.24396,68.206024,2.897246599197387695,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 80484;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9798.452,`position_y`=695.19556,`position_z`=33.112034 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9798.452,695.19556,33.112034,0,2000,0,0,100,0),
(@PATH,2,-9787.982,698.6899,33.022564,0,0,0,0,100,0),
(@PATH,3,-9780.442,707.81964,33.10679,0,0,0,0,100,0),
(@PATH,4,-9779.388,719.58044,33.023594,0,0,0,0,100,0),
(@PATH,5,-9784.109,729.6285,33.103867,0,0,0,0,100,0),
(@PATH,6,-9790.603,734.1717,33.017864,0,0,0,0,100,0),
(@PATH,7,-9800.767,735.68085,32.96836,0,0,0,0,100,0),
(@PATH,8,-9810.418,732.05927,33.015137,0,0,0,0,100,0),
(@PATH,9,-9817.442,723.67883,33.11065,0,0,0,0,100,0),
(@PATH,10,-9818.047,711.8033,33.041603,0,0,0,0,100,0),
(@PATH,11,-9812.931,700.99554,33.123142,0,2000,0,0,100,0),
(@PATH,12,-9818.047,711.8033,33.041603,0,0,0,0,100,0),
(@PATH,13,-9817.442,723.67883,33.11065,0,0,0,0,100,0),
(@PATH,14,-9810.418,732.05927,33.015137,0,0,0,0,100,0),
(@PATH,15,-9800.767,735.68085,32.96836,0,0,0,0,100,0),
(@PATH,16,-9790.603,734.1717,33.017864,0,0,0,0,100,0),
(@PATH,17,-9784.109,729.6285,33.103867,0,0,0,0,100,0),
(@PATH,18,-9779.388,719.58044,33.023594,0,0,0,0,100,0),
(@PATH,19,-9780.442,707.81964,33.10679,0,0,0,0,100,0),
(@PATH,20,-9787.982,698.6899,33.022564,0,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 80463;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9622.002,`position_y`=658.1578,`position_z`=38.652153 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9622.002,658.1578,38.652153,0,0,0,0,100,0),
(@PATH,2,-9617.351,668.33185,38.65217,0,0,0,0,100,0),
(@PATH,3,-9611.132,683.5247,38.652126,0,0,0,0,100,0),
(@PATH,4,-9611.558,688.25586,38.65211,0,0,0,0,100,0),
(@PATH,5,-9612.169,690.6416,38.65211,0,0,0,0,100,0),
(@PATH,6,-9616.687,691.81586,38.65212,0,0,0,0,100,0),
(@PATH,7,-9619.487,691.1839,38.652122,0,0,0,0,100,0),
(@PATH,8,-9621.871,687.6222,38.65214,0,0,0,0,100,0),
(@PATH,9,-9628.441,674.22687,37.152905,0,0,0,0,100,0),
(@PATH,10,-9631.934,666.4326,37.816895,0,0,0,0,100,0),
(@PATH,11,-9635.862,656.57086,38.652134,0,0,0,0,100,0),
(@PATH,12,-9635.827,652.6503,38.652115,0,0,0,0,100,0),
(@PATH,13,-9634.247,650.34314,38.65211,0,0,0,0,100,0),
(@PATH,14,-9631.206,648.6608,38.65211,0,0,0,0,100,0),
(@PATH,15,-9626.505,647.6031,38.65211,0,0,0,0,100,0),
(@PATH,16,-9624.614,651.0484,38.65212,0,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 80455;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9623.048,`position_y`=652.2398,`position_z`=47.473915 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9623.048,652.2398,47.473915,0,0,0,0,100,0),
(@PATH,2,-9621.247,655.22974,47.47388,0,0,0,0,100,0),
(@PATH,3,-9619.213,661.5179,47.473907,0,0,0,0,100,0),
(@PATH,4,-9617.516,665.6706,47.473904,0,0,0,0,100,0),
(@PATH,5,-9615.05,670.0313,47.473904,0,0,0,0,100,0),
(@PATH,6,-9612.664,676.0534,47.473896,0,0,0,0,100,0),
(@PATH,7,-9609.549,682.40216,47.473896,0,0,0,0,100,0),
(@PATH,8,-9612.343,686.01117,47.473896,0,0,0,0,100,0),
(@PATH,9,-9616.939,688.31665,49.721916,0,0,0,0,100,0),
(@PATH,10,-9622.063,690.267,52.57558,0,0,0,0,100,0),
(@PATH,11,-9625.925,689.81964,52.57559,0,0,0,0,100,0),
(@PATH,12,-9629.106,686.2172,52.575573,0,0,0,0,100,0),
(@PATH,13,-9631.966,678.9166,52.57557,0,0,0,0,100,0),
(@PATH,14,-9633.849,674.3489,52.57557,0,0,0,0,100,0),
(@PATH,15,-9635.843,670.5042,52.57557,0,0,0,0,100,0),
(@PATH,16,-9637.912,665.65936,52.575573,0,0,0,0,100,0),
(@PATH,17,-9638.895,660.9452,52.575584,0,0,0,0,100,0),
(@PATH,18,-9638.579,657.653,52.57558,0,0,0,0,100,0),
(@PATH,19,-9636.524,656.40936,52.575565,0,0,0,0,100,0),
(@PATH,20,-9631.175,653.5473,49.28209,0,0,0,0,100,0),
(@PATH,21,-9625.827,652.3152,47.47392,0,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 80444;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9613.598,`position_y`=641.953,`position_z`=62.67805 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9613.598,641.953,62.67805,0,0,0,0,100,0),
(@PATH,2,-9623.298,646.38385,62.732975,0,0,0,0,100,0),
(@PATH,3,-9633.624,651.1018,62.73352,0,0,0,0,100,0),
(@PATH,4,-9640.683,653.89465,62.73388,0,0,0,0,100,0),
(@PATH,5,-9649.079,657.3284,62.678032,0,0,0,0,100,0),
(@PATH,6,-9640.683,653.89465,62.73388,0,0,0,0,100,0),
(@PATH,7,-9633.624,651.1018,62.73352,0,0,0,0,100,0),
(@PATH,8,-9623.298,646.38385,62.732975,0,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 80462;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9631.311,`position_y`=697.4772,`position_z`=62.678036 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9631.311,697.4772,62.678036,0,0,0,0,100,0),
(@PATH,2,-9621.152,693.2395,62.733246,0,0,0,0,100,0),
(@PATH,3,-9614.743,690.13043,62.73286,0,0,0,0,100,0),
(@PATH,4,-9606.849,687.18427,62.73253,0,0,0,0,100,0),
(@PATH,5,-9596.611,682.7405,62.67806,0,0,0,0,100,0),
(@PATH,6,-9606.849,687.18427,62.73253,0,0,0,0,100,0),
(@PATH,7,-9614.743,690.13043,62.73286,0,0,0,0,100,0),
(@PATH,8,-9621.152,693.2395,62.733246,0,0,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 81365;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9771.3,`position_y`=-1406.9572,`position_z`=97.88677 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9771.3,-1406.9572,97.88677,0,0,0,0,100,0),
(@PATH,2,-9771.3,-1406.9572,97.88677,1.169370651245117187,60000,0,0,100,0),
(@PATH,3,-9776.593,-1394.8129,97.88729,0,0,0,0,100,0),
(@PATH,4,-9776.593,-1394.8129,97.88729,2.740167140960693359,60000,0,0,100,0),
(@PATH,5,-9788.505,-1399.9374,97.88729,0,0,0,0,100,0),
(@PATH,6,-9788.505,-1399.9374,97.88729,4.345870018005371093,60000,0,0,100,0),
(@PATH,7,-9783.521,-1412.0233,97.88729,0,0,0,0,100,0),
(@PATH,8,-9783.521,-1412.0233,97.88729,5.846853256225585937,60000,0,0,100,0);

-- Pathing for Stormwind Guard Entry: 1423
SET @NPC := 81362;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9776.632,`position_y`=-1383.5325,`position_z`=62.792778 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9776.632,-1383.5325,62.792778,0,2000,0,0,100,0),
(@PATH,2,-9787.192,-1384.7284,62.70096,0,0,0,0,100,0),
(@PATH,3,-9796.349,-1393.6975,62.78296,0,0,0,0,100,0),
(@PATH,4,-9799.684,-1403.431,62.7103,0,0,0,0,100,0),
(@PATH,5,-9797.448,-1413.294,62.797836,0,0,0,0,100,0),
(@PATH,6,-9791.156,-1420.47,62.699223,0,0,0,0,100,0),
(@PATH,7,-9780.899,-1422.8915,62.66864,0,0,0,0,100,0),
(@PATH,8,-9771.842,-1421.9237,62.697872,0,0,0,0,100,0),
(@PATH,9,-9762.633,-1412.1235,62.792976,0,0,0,0,100,0),
(@PATH,10,-9760.813,-1402.7131,62.72537,0,0,0,0,100,0),
(@PATH,11,-9763.664,-1392.0123,62.800457,0,2000,0,0,100,0),
(@PATH,12,-9760.813,-1402.7131,62.72537,0,0,0,0,100,0),
(@PATH,13,-9762.633,-1412.1235,62.792976,0,0,0,0,100,0),
(@PATH,14,-9771.842,-1421.9237,62.697872,0,0,0,0,100,0),
(@PATH,15,-9780.899,-1422.8915,62.66864,0,0,0,0,100,0),
(@PATH,16,-9791.156,-1420.47,62.699223,0,0,0,0,100,0),
(@PATH,17,-9797.448,-1413.294,62.797836,0,0,0,0,100,0),
(@PATH,18,-9799.684,-1403.431,62.7103,0,0,0,0,100,0),
(@PATH,19,-9796.349,-1393.6975,62.78296,0,0,0,0,100,0),
(@PATH,20,-9787.192,-1384.7284,62.70096,0,0,0,0,100,0);

-- Pathing for Northshire Guard Entry: 1642
SET @NPC := 79948;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-8998.911,`position_y`=-85.83616,`position_z`=85.95305,`orientation`=2.646825 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-8998.911,-85.83616,85.95305,0,180000,0,0,100,0),
(@PATH,2,-8985.992,-98.67936,85.521164,0,0,0,0,100,0),
(@PATH,3,-8985.992,-98.67936,85.521164,0,0,0,0,100,0),
(@PATH,4,-8973.213,-112.15788,84.36455,0,0,0,0,100,0),
(@PATH,5,-8942.789,-112.88097,82.87124,0,0,0,0,100,0),
(@PATH,6,-8928.891,-115.53168,82.53855,0,0,0,0,100,0),
(@PATH,7,-8915.746,-131.6454,80.8845,0,0,0,0,100,0),
(@PATH,8,-8914.443,-134.26715,80.55233,0,180000,0,0,100,0),
(@PATH,9,-8921.773,-122.04546,81.911354,0,0,0,0,100,0),
(@PATH,10,-8933.163,-114.32422,82.58787,0,0,0,0,100,0),
(@PATH,11,-8949.463,-112.98318,83.12124,0,0,0,0,100,0),
(@PATH,12,-8966.525,-114.03516,83.87441,0,0,0,0,100,0),
(@PATH,13,-8978.185,-108.68858,84.97563,0,0,0,0,100,0),
(@PATH,14,-8991.045,-93.846245,85.7592,0,0,0,0,100,0);

-- Pathing for Northshire Guard Entry: 1642
SET @NPC := 79927;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9007.525,`position_y`=-81.2003,`position_z`=86.618355 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9007.525,-81.2003,86.618355,0,2000,0,0,100,0),
(@PATH,2,-9021.59,-96.39242,86.98774,0,0,0,0,100,0),
(@PATH,3,-9035.88,-102.08578,87.77118,0,0,0,0,100,0),
(@PATH,4,-9046.873,-96.740944,88.15223,0,0,0,0,100,0),
(@PATH,5,-9052.141,-86.49409,87.95789,0,0,0,0,100,0),
(@PATH,6,-9047.826,-67.8278,88.177376,0,0,0,0,100,0),
(@PATH,7,-9046.549,-51.212944,88.337,0,0,0,0,100,0),
(@PATH,8,-9046.152,-44.838867,88.420006,0,2000,0,0,100,0),
(@PATH,9,-9047.811,-66.246475,88.170006,0,0,0,0,100,0),
(@PATH,10,-9051.52,-86.36279,88.03236,0,0,0,0,100,0),
(@PATH,11,-9047.505,-95.975746,88.15223,0,0,0,0,100,0),
(@PATH,12,-9037.521,-101.62913,87.84076,0,0,0,0,100,0),
(@PATH,13,-9024.465,-99.40381,87.33271,0,0,0,0,100,0),
(@PATH,14,-9013.879,-90.97689,86.58271,0,0,0,0,100,0);

-- Pathing for Northshire Guard Entry: 1642
SET @NPC := 79932;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9006.875,`position_y`=-78.34467,`position_z`=86.59687 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9006.875,-78.34467,86.59687,0,2000,0,0,100,0),
(@PATH,2,-9009.569,-58.098797,87.24557,0,0,0,0,100,0),
(@PATH,3,-9009.41,-31.61084,88.27456,0,0,0,0,100,0),
(@PATH,4,-9014.083,-3.766493,88.84121,0,0,0,0,100,0),
(@PATH,5,-9024.77,4.272787,88.38243,0,0,0,0,100,0),
(@PATH,6,-9035.086,2.457791,88.32811,0,0,0,0,100,0),
(@PATH,7,-9040.156,-7.506565,88.295006,0,0,0,0,100,0),
(@PATH,8,-9041.208,-21.520563,88.295006,0,0,0,0,100,0),
(@PATH,9,-9043.388,-35.013294,88.295006,0,0,0,0,100,0),
(@PATH,10,-9045.403,-42.90777,88.420006,0,2000,0,0,100,0),
(@PATH,11,-9043.856,-30.389051,88.33578,0,0,0,0,100,0),
(@PATH,12,-9040.349,-11.365994,88.295006,0,0,0,0,100,0),
(@PATH,13,-9037.478,-0.492893,88.420006,0,0,0,0,100,0),
(@PATH,14,-9031.2,4.595215,88.20775,0,0,0,0,100,0),
(@PATH,15,-9020.281,3.15446,88.58275,0,0,0,0,100,0),
(@PATH,16,-9012.075,-14.76454,88.46621,0,0,0,0,100,0),
(@PATH,17,-9010.279,-34.814236,88.12057,0,0,0,0,100,0),
(@PATH,18,-9009.692,-53.72705,87.37057,0,0,0,0,100,0),
(@PATH,19,-9007.624,-70.43734,86.811714,0,0,0,0,100,0);

-- Pathing for Kira Songshine Entry: 3937
SET @NPC := 80283;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9125.449,`position_y`=389.9619,`position_z`=91.57055 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9125.449,389.9619,91.57055,0,0,0,0,100,0),
(@PATH,2,-9144.014,376.64615,90.755264,0,0,0,0,100,0),
(@PATH,3,-9150.359,370.64236,90.630264,0,0,0,0,100,0),
(@PATH,4,-9159.769,358.97116,89.092285,0,0,0,0,100,0),
(@PATH,5,-9169.689,347.30524,86.06768,0,0,0,0,100,0),
(@PATH,6,-9176.367,336.1582,83.30425,0,0,0,0,100,0),
(@PATH,7,-9179.039,324.03336,81.122444,0,0,0,0,100,0),
(@PATH,8,-9181.582,310.68277,79.00208,0,0,0,0,100,0),
(@PATH,9,-9182.38,299.1464,77.95396,0,0,0,0,100,0),
(@PATH,10,-9183.653,286.01465,76.427345,0,0,0,0,100,0),
(@PATH,11,-9184.186,259.5755,74.103455,0,0,0,0,100,0),
(@PATH,12,-9187.476,239.79866,72.50726,0,0,0,0,100,0),
(@PATH,13,-9193.939,228.36848,71.82954,0,0,0,0,100,0),
(@PATH,14,-9217.061,204.2547,69.10202,0,0,0,0,100,0),
(@PATH,15,-9229.925,191.6477,68.03234,0,0,0,0,100,0),
(@PATH,16,-9239.118,181.45674,67.84704,0,0,0,0,100,0),
(@PATH,17,-9252.867,160.50575,67.88796,0,0,0,0,100,0),
(@PATH,18,-9270.517,152.484,67.12913,0,0,0,0,100,0),
(@PATH,19,-9281.984,150.66148,66.75413,0,0,0,0,100,0),
(@PATH,20,-9303.524,142.94772,65.81363,0,0,0,0,100,0),
(@PATH,21,-9326.493,133.65489,64.281525,0,0,0,0,100,0),
(@PATH,22,-9338.177,126.13138,63.79213,0,0,0,0,100,0),
(@PATH,23,-9357.066,114.86759,62.030045,0,0,0,0,100,0),
(@PATH,24,-9376.01,105.78207,60.5753,0,0,0,0,100,0),
(@PATH,25,-9396.483,100.0127,58.823223,0,0,0,0,100,0),
(@PATH,26,-9415.7295,90.13653,57.428368,0,0,0,0,100,0),
(@PATH,27,-9430.02,79.53779,56.531395,0,0,0,0,100,0),
(@PATH,28,-9441.903,69.07547,56.12108,0,0,0,0,100,0),
(@PATH,29,-9459.128,61.957737,55.894997,0,0,0,0,100,0),
(@PATH,30,-9471.797,63.62576,56.152515,0,0,0,0,100,0),
(@PATH,31,-9492.84,58.88786,55.855885,0,0,0,0,100,0),
(@PATH,32,-9502.176,40.734756,56.346992,0,0,0,0,100,0),
(@PATH,33,-9513.744,15.730035,56.48589,0,0,0,0,100,0),
(@PATH,34,-9519.953,3.746257,56.11089,0,0,0,0,100,0),
(@PATH,35,-9526.677,-8.577745,55.88026,0,0,0,0,100,0),
(@PATH,36,-9535.507,-27.89106,55.83387,0,0,0,0,100,0),
(@PATH,37,-9544.865,-53.436146,56.815815,0,0,0,0,100,0),
(@PATH,38,-9550.579,-84.43788,57.65163,0,0,0,0,100,0),
(@PATH,39,-9550.719,-113.81445,57.658558,0,0,0,0,100,0),
(@PATH,40,-9549.074,-80.8469,57.51955,0,0,0,0,100,0),
(@PATH,41,-9543.954,-52.830025,56.815815,0,0,0,0,100,0),
(@PATH,42,-9537.006,-30.561958,55.88392,0,0,0,0,100,0),
(@PATH,43,-9529.335,-14.989149,55.83778,0,0,0,0,100,0),
(@PATH,44,-9522.49,-4.730361,55.993786,0,0,0,0,100,0),
(@PATH,45,-9517.441,9.093316,56.23589,0,0,0,0,100,0),
(@PATH,46,-9508.014,28.546875,56.48589,0,0,0,0,100,0),
(@PATH,47,-9496.207,50.32487,55.884937,0,0,0,0,100,0),
(@PATH,48,-9493.627,67.18815,56.246754,0,0,0,0,100,0),
(@PATH,49,-9486.809,89.65723,56.21941,0,0,0,0,100,0),
(@PATH,50,-9483.946,102.30203,56.50797,0,0,0,0,100,0),
(@PATH,51,-9480.245,126.30482,56.63297,0,0,0,0,100,0),
(@PATH,52,-9482.919,145.26506,56.433086,0,0,0,0,100,0),
(@PATH,53,-9485.429,167.39752,56.054165,0,0,0,0,100,0),
(@PATH,54,-9484.816,180.07121,55.429165,0,0,0,0,100,0),
(@PATH,55,-9493.019,196.93376,54.554165,0,0,0,0,100,0),
(@PATH,56,-9508.471,218.06671,52.964153,0,0,0,0,100,0),
(@PATH,57,-9533.1045,242.1964,50.86973,0,0,0,0,100,0),
(@PATH,58,-9519.216,232.3553,51.839153,0,0,0,0,100,0),
(@PATH,59,-9515.605,227.52412,52.223797,0,0,0,0,100,0),
(@PATH,60,-9503.669,216.6386,52.90214,0,0,0,0,100,0),
(@PATH,61,-9498.017,202.37566,54.15273,0,0,0,0,100,0),
(@PATH,62,-9486.037,183.3565,55.226772,0,0,0,0,100,0),
(@PATH,63,-9483.614,166.14851,56.05247,0,0,0,0,100,0),
(@PATH,64,-9481.673,150.38591,56.443584,0,0,0,0,100,0),
(@PATH,65,-9481.871,138.39304,56.67747,0,0,0,0,100,0),
(@PATH,66,-9483.6455,118.8074,56.608067,0,0,0,0,100,0),
(@PATH,67,-9486.335,100.28494,56.50797,0,0,0,0,100,0),
(@PATH,68,-9487.605,81.91924,56.073902,0,0,0,0,100,0),
(@PATH,69,-9485.286,71.61192,56.401783,0,0,0,0,100,0),
(@PATH,70,-9479.711,67.17296,56.322315,0,0,0,0,100,0),
(@PATH,71,-9467.877,61.330677,56.105396,0,0,0,0,100,0),
(@PATH,72,-9454.594,66.6288,56.20298,0,0,0,0,100,0),
(@PATH,73,-9415.596,86.5546,56.99038,0,0,0,0,100,0),
(@PATH,74,-9402.721,93.8989,58.410545,0,0,0,0,100,0),
(@PATH,75,-9383.628,105.08906,60.036236,0,0,0,0,100,0),
(@PATH,76,-9362.615,111.66406,61.468887,0,0,0,0,100,0),
(@PATH,77,-9337.428,124.46851,63.717667,0,0,0,0,100,0),
(@PATH,78,-9316.489,136.07143,64.95474,0,0,0,0,100,0),
(@PATH,79,-9298.161,143.41545,65.91209,0,0,0,0,100,0),
(@PATH,80,-9273.096,151.20242,67.00413,0,0,0,0,100,0),
(@PATH,81,-9256.78,159.32216,67.66848,0,0,0,0,100,0),
(@PATH,82,-9242.877,174.16461,67.97204,0,0,0,0,100,0),
(@PATH,83,-9232.261,186.13951,67.72204,0,0,0,0,100,0),
(@PATH,84,-9217.805,203.66469,69.03122,0,0,0,0,100,0),
(@PATH,85,-9201.397,220.05016,71.15402,0,0,0,0,100,0),
(@PATH,86,-9188.232,236.89157,72.442444,0,0,0,0,100,0),
(@PATH,87,-9182.984,255.77832,73.853455,0,0,0,0,100,0),
(@PATH,88,-9182.252,275.11658,75.442116,0,0,0,0,100,0),
(@PATH,89,-9180.925,301.32944,78.33094,0,0,0,0,100,0),
(@PATH,90,-9178.171,323.03793,80.88319,0,0,0,0,100,0),
(@PATH,91,-9173.903,335.75302,83.669975,0,0,0,0,100,0),
(@PATH,92,-9168.686,347.0231,86.27471,0,0,0,0,100,0),
(@PATH,93,-9156.068,363.6808,89.99182,0,0,0,0,100,0),
(@PATH,94,-9142.421,375.96457,90.755264,0,0,0,0,100,0);

-- Pathing for Stormwind City Guard Entry: 68
SET @NPC := 120789;
SET @PATH := @NPC * 10;
UPDATE `creature` SET `wander_distance`=0,`MovementType`=2,`position_x`=-9165.44,`position_y`=351.63565,`position_z`=87.38135 WHERE `guid`=@NPC;
DELETE FROM `creature_addon` WHERE `guid`=@NPC;
INSERT INTO `creature_addon` (`guid`,`path_id`,`mount`,`bytes1`,`bytes2`,`emote`,`visibilityDistanceType`,`auras`) VALUES (@NPC,@PATH,0,0,1,0,0, '');
DELETE FROM `waypoint_data` WHERE `id`=@PATH;
INSERT INTO `waypoint_data` (`id`,`point`,`position_x`,`position_y`,`position_z`,`orientation`,`delay`,`move_type`,`action`,`action_chance`,`wpguid`) VALUES
(@PATH,1,-9165.44,351.63565,87.38135,0,0,0,0,100,0),
(@PATH,2,-9144.325,374.03607,90.755264,0,0,0,0,100,0),
(@PATH,3,-9125.303,391.61426,91.78637,0,0,0,0,100,0),
(@PATH,4,-9091.403,414.3217,92.169464,0,0,0,0,100,0),
(@PATH,5,-9073.301,427.17996,93.05584,0,0,0,0,100,0),
(@PATH,6,-9091.403,414.3217,92.169464,0,0,0,0,100,0),
(@PATH,7,-9125.303,391.61426,91.78637,0,0,0,0,100,0),
(@PATH,8,-9144.325,374.03607,90.755264,0,0,0,0,100,0);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_06_08' WHERE sql_rev = '1641439653882850490';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
