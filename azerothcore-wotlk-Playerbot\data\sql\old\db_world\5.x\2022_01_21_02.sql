-- DB update 2022_01_21_01 -> 2022_01_21_02
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_21_01';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_21_01 2022_01_21_02 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1642739081837973500'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1642739081837973500');

UPDATE `creature_template_movement` SET `Ground` = 1 WHERE `CreatureId` = 4844;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_21_02' WHERE sql_rev = '1642739081837973500';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
