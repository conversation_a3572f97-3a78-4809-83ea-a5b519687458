-- DB update 2021_08_06_12 -> 2021_08_07_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_08_06_12';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_08_06_12 2021_08_07_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1627894869122476600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1627894869122476600');

-- Make the Egg of Onyxia (20359) dissapear after atacking for the quest Brood of Onyxia (1172)
UPDATE `gameobject_template` SET `Data3` = 1 WHERE (`entry` = 20359);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_08_07_00' WHERE sql_rev = '1627894869122476600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
