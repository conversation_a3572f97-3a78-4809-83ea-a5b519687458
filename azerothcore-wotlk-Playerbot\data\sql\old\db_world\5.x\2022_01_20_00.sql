-- DB update 2022_01_19_05 -> 2022_01_20_00
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2022_01_19_05';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2022_01_19_05 2022_01_20_00 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1639261348058596300'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1639261348058596300');

UPDATE `creature_template` SET `npcflag`=`npcflag`|1 WHERE `entry` IN (13439,22722,31823,37484,32766,22597,13437,32101,37480,31960,31965,31923,30792,30796,30801,30797,30799,22938,22640,22575,22735,22541,22567,22527,32048,21602,3881,40413,38040,1735,1747,7231,38043,3079,3139,37223,3628,37671,33648,4794,5119,5190,5191,5192,35068,5627,5637,5835,5875,35035,35093,35770,34987,7770,7772,7937,36955,9540,9598,10162,10676,11627,12018,13257,34983,34985,15708,16033,16261,16443,16480,23103,16610,17718,17764,17827,18530,19471,19858,19923,20165,20494,20500,20977,21112,22823,21685,21859,21950,22433,22919,23101,23139,23140,23143,23144,23145,23149,23191,23291,23316,23376,23895,24037,37221,24788,28210,24956,25078,25093,37675,26664,25697,25754,25841,28621,25924,26218,26547,26666,27215,27216,29145,33642,30677,28042,28044,28099,28209,28283,28284,29095,29139,29143,29319,29327,29396,29778,29804,33956,30007,30295,30401,30631,30708,32301,30944,35766,31044,37589,31115,31135,31154,31261,37369,31776,33957,34973,34978,36517,37554,37887,38039,38041,38042,38293,38295);
UPDATE `creature_template` SET `npcflag`=`npcflag`&~1 WHERE `entry` IN (37272,1574,37283,1355,1365,1382,1475,1575,1700,37308,1749,38590,2114,2132,2264,2389,2390,38589,2425,2458,2459,2460,2461,2801,2861,2880,2881,2996,3026,3028,3175,3184,3185,3318,3496,3555,3604,3692,3702,3965,37479,4204,4208,33210,4209,4267,4319,4549,4550,5941,33549,33676,33679,4898,4980,5052,5060,5099,35004,5502,34382,33682,5690,34712,34786,33547,5938,35005,6287,6288,6290,6292,6295,6297,6387,35002,6548,6669,6726,7087,7088,7089,7799,7867,7869,7871,7918,7946,7957,8119,8124,8128,8144,8306,37322,8356,8357,37398,8587,8610,10296,8999,9077,11997,9273,9620,9956,10370,10668,10684,10941,10993,39654,11138,11696,11702,11750,11900,11946,12025,12030,12032,12097,12578,15309,12657,12740,12807,12961,12996,13140,13154,13177,13179,13181,13236,13284,13320,13429,13430,34254,13448,33550,13718,13917,14242,14348,14436,14444,14522,14720,14736,14848,34072,15070,15080,15165,34999,15383,15431,15432,15434,15437,15440,15445,15446,15448,15450,15451,15452,15453,15455,15456,15457,15539,15701,15709,15731,15733,15734,15735,15761,15765,15767,15768,35001,16285,16295,16433,16434,16617,16643,16668,16671,16687,16702,16724,16728,16736,16740,16741,16743,16744,16745,16746,16786,16859,17076,17290,17296,17380,17633,17637,17894,18020,18024,22817,18601,20416,18747,18752,18753,18771,18781,18821,18898,18953,18990,18991,19013,19015,19020,19034,19140,19246,19265,19266,19314,19318,19338,19774,22059,19705,19777,19855,19856,19857,19859,19861,21970,20362,20374,20381,20384,20385,20388,20435,20556,23079,20674,20724,50004,22721,22568,23002,21349,21359,21471,21475,21732,21733,21734,21810,21971,22073,22234,22308,22396,22557,22574,22598,22613,22615,22616,22641,22648,22678,22760,24519,22931,22935,23009,23010,23064,23065,23768,23437,23861,23712,23904,23971,23973,24491,26327,26328,24780,24868,25043,26421,25176,25177,25179,37371,25274,37482,25384,25392,25639,26012,26073,26075,26076,26089,31954,26307,26324,26325,26326,26329,26330,26331,26332,26352,26499,26760,26809,27046,27305,29141,27721,27722,27923,28070,28347,33438,33666,31523,33545,29757,32204,30175,29533,29534,29568,29569,29570,32050,29694,29736,30046,30099,31522,34567,30322,32209,30610,30611,35000,31210,31952,31366,31416,31819,31825,31828,31833,31834,31955,31986,31989,32003,32077,32095,32523,32616,32617,32618,32619,32620,32621,32622,32623,32624,32625,32684,33544,33548,33631,33636,33680,34119,34127,34568,34713,34714,34816,35008,35146,35365,37270,37273,37305,37416,37888);

UPDATE `creature_template` SET `gossip_menu_id`=5147 WHERE `entry`IN(13439,22722,31823);
UPDATE `creature_template` SET `gossip_menu_id`=5146 WHERE `entry`IN(13437,22597,32766);
UPDATE `creature_template` SET `gossip_menu_id`=5142 WHERE `entry`IN(22527,31923);
UPDATE `creature_template` SET `gossip_menu_id`=5021 WHERE `entry`IN(22567,32048);
UPDATE `creature_template` SET `gossip_menu_id`=5141 WHERE `entry`IN(22541,31965);
UPDATE `creature_template` SET `gossip_menu_id`=5081 WHERE `entry`IN(22735,31960);
UPDATE `creature_template` SET `gossip_menu_id`=5281 WHERE `entry`IN(22575,32101);
UPDATE `creature_template` SET `gossip_menu_id`=5441 WHERE `entry`=22640;
UPDATE `creature_template` SET `gossip_menu_id`=7520 WHERE `entry`=22938;
UPDATE `creature_template` SET `gossip_menu_id`=8310 WHERE `entry`=21602;
UPDATE `creature_template` SET `gossip_menu_id`=8723 WHERE `entry`=30799;
UPDATE `creature_template` SET `gossip_menu_id`=8716 WHERE `entry`=30797;
UPDATE `creature_template` SET `gossip_menu_id`=8722 WHERE `entry`=30801;
UPDATE `creature_template` SET `gossip_menu_id`=8721 WHERE `entry`=30796;
UPDATE `creature_template` SET `gossip_menu_id`=8724 WHERE `entry`=30792;

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2022_01_20_00' WHERE sql_rev = '1639261348058596300';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
