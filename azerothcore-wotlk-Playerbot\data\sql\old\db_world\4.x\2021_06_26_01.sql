-- DB update 2021_06_26_00 -> 2021_06_26_01
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_06_26_00';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_06_26_00 2021_06_26_01 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1624104272585530600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1624104272585530600');

-- Correct position for Magister <PERSON><PERSON><PERSON><PERSON>
UPDATE `creature` SET `position_x` = 9026.383789, `position_y` = -7457.562988, `position_z` = 103.274475, `orientation` = 4.869936 WHERE (`id` = 15951) AND (`guid` IN (56398));

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_06_26_01' WHERE sql_rev = '1624104272585530600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
