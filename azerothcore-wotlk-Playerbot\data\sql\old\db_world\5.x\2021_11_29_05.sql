-- DB update 2021_11_29_04 -> 2021_11_29_05
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_11_29_04';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_11_29_04 2021_11_29_05 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1637592933273396000'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1637592933273396000');

-- Query used to get Gameobjects based on pool_gameobject's pool_entry
-- You can find the pool_gameobject.pool_entry by searching "LIKE '%copper%'"
-- SELECT gameobject.guid, gameobject.id, gameobject.position_x, gameobject.position_y, gameobject.position_z, gameobject.orientation, gameobject.rotation0, gameobject.rotation1, gameobject.rotation2, gameobject.rotation3, gameobject.spawntimesecsmin, gameobject.animprogress, gameobject.state FROM gameobject JOIN  pool_gameobject ON gameobject.guid = pool_gameobject.guid WHERE pool_gameobject.pool_entry = 1019;

SET @FREE_POOL_TEMPLATE = 11660;
SET @FREE_GAMEOBJECT = 93000;

DELETE FROM `pool_template` WHERE `entry` BETWEEN @FREE_POOL_TEMPLATE AND @FREE_POOL_TEMPLATE+17;

INSERT INTO `pool_template` (`entry`, `max_limit`, `description`) VALUES 
(@FREE_POOL_TEMPLATE , 10, 'Copper Veins in Duskwood 30 objects total'),
(@FREE_POOL_TEMPLATE+1, 8, 'Copper Veins in Westfall 57 objects total'),
(@FREE_POOL_TEMPLATE+2, 28, 'Copper Veins in Darkshore 84 objects total'),
(@FREE_POOL_TEMPLATE+3, 27, 'Copper Veins in Loch Modan 82 objects total'),
(@FREE_POOL_TEMPLATE+4, 11, 'Copper Veins in Wetlands 35 objects total'),
(@FREE_POOL_TEMPLATE+5, 6, 'Copper Veins in Hillsbrad 20 objects total'),
(@FREE_POOL_TEMPLATE+6, 30, 'Copper Veins in Silverpine Forest 92 objects total'),
(@FREE_POOL_TEMPLATE+7, 40, 'Copper Veins in Barrens 147 objects total'),
(@FREE_POOL_TEMPLATE+8, 16, 'Copper Veins in Thousand Needles 48 objects total'),
(@FREE_POOL_TEMPLATE+9, 21, 'Copper Veins in Mulgore 64 objects total'),
(@FREE_POOL_TEMPLATE+10, 30, 'Copper Veins in Durotar 127 objects total'),
(@FREE_POOL_TEMPLATE+11, 23, 'Copper Veins in Ashenvale 69 objects total'),
(@FREE_POOL_TEMPLATE+12, 2, 'Copper Veins in Desolace 7 objects total'),
(@FREE_POOL_TEMPLATE+13, 26, 'Copper Veins in Stonetalon 79 objects total'),
(@FREE_POOL_TEMPLATE+14, 17, 'Copper Veins in Redridge 52 objects total'),
(@FREE_POOL_TEMPLATE+15, 15, 'Copper Veins in Tirisfal 45 objects total'),
(@FREE_POOL_TEMPLATE+16, 28, 'Copper Veins in Elwynn Forest 86 objects total'),
(@FREE_POOL_TEMPLATE+17, 25, 'Copper Veins in Dun Morogh 76 objects total');

-- Cleaning up gameobjects for inserting. Starting at ID 93000 for all the Copper Veins
DELETE FROM `gameobject` WHERE `id` IN (1731, 3763, 103713, 2055, 181248);

INSERT INTO `gameobject` (`guid`, `id`, `map`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`) VALUES 
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11045.7, -299.893, 16.2588, 3.75246, 0, 0, -0.953716, 0.300708, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10174.9, -700.101, 44.6107, 2.96704, 0, 0, 0.996194, 0.087165, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10435.3, -1011.91, 48.5019, 3.73501, 0, 0, -0.956305, 0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11125.1, -774.882, 59.6363, 1.48353, 0, 0, 0.67559, 0.737278, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10973.7, 73.9823, 39.4804, 5.67232, 0, 0, -0.300705, 0.953717, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11102, -256, 34.241, -1, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10519.2, 677.127, 15.6123, 2.58308, 0, 0, 0.961261, 0.27564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10513.9, -74.5481, 45.2932, 3.07178, 0, 0, 0.999391, 0.034899, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10481.8, 41.4525, 42.5627, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11100, -195.819, 28.6323, -1.41372, 0, 0, 0.649448, -0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10714.9, -1436.62, 63.2028, -1.90241, 0, 0, 0.814116, -0.580703, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11125, -253.782, 45.8497, -0.541052, 0, 0, 0.267238, -0.96363, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11158.4, -925.164, 85.8275, 1.53589, 0, 0, 0.694658, 0.71934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11137.5, -904.159, 66.684, 2.1293, 0, 0, 0.87462, 0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11160.1, 290.516, 41.1961, 2.00713, 0, 0, 0.843391, 0.5373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10673.2, 691.022, 43.2237, 0.733038, 0, 0, 0.358368, 0.93358, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10638.4, -1390.39, 60.8947, 2.05949, 0, 0, 0.857167, 0.515038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11050, -1033.27, 72.4054, 0.244346, 0, 0, 0.121869, 0.992546, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10343.2, 773.869, 31.3173, 0.122173, 0, 0, 0.061049, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10359, -790.396, 61.1401, 2.32129, 0, 0, 0.91706, 0.398749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11014.4, -1079.73, 51.1815, -2.96706, 0, 0, 0.996195, -0.087156, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10341.1, 83.216, 36.8964, -1.53589, 0, 0, 0.694658, -0.71934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10726.6, -965.106, 70.1185, 1.67552, 0, 0, 0.743145, 0.669131, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11039.1, -420.595, 36.0661, -0.087267, 0, 0, 0.04362, -0.999048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10467.1, -948.977, 49.8306, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10557.2, -760.492, 60.5832, 5.23599, 0, 0, -0.5, 0.866025, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10133.8, 793.8, 19.4021, 0.837758, 0, 0, 0.406737, 0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11114.4, -1016.67, 80.772, 0, 0, 0, 0, 0, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10124.3, -341.976, 53.3324, 4.04917, 0, 0, -0.898793, 0.438373, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11194.9, 682.466, 36.5628, 2.84489, 0, 0, 0.989016, 0.147809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11118.3, -578.917, 46.1832, 5.48033, 0, 0, -0.390731, 0.920505, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11016.6, -1180.58, 46.4404, 2.93214, 0, 0, 0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11062.5, -729.195, 59.6659, 4.62512, 0, 0, -0.737277, 0.675591, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10966.5, -183.178, 17.1387, 2.56563, 0, 0, 0.958819, 0.284016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10856.7, -1283.99, 62.4551, 0.296705, 0, 0, 0.147809, 0.989016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10088, -450.424, 65.3588, 2.28638, 0, 0, 0.909961, 0.414694, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10699.3, -187.865, 39.9624, 3.4383, 0, 0, -0.989016, 0.147811, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10782.3, 1400.45, 23.0415, 1.71042, 0, 0, 0.754709, 0.656059, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10008.9, 878.734, 38.924, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9687.82, 915.132, 36.3152, -2.56563, 0, 0, 0.95882, -0.284015, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9603, 970.304, 39.404, -3, 0, 0, -0.97237, 0.233445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10507, 1976.45, 9.944, 1.018, 0, 0, 0.487484, 0.873132, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11134, 1440.32, 60.008, -2, 0, 0, -0.848048, 0.529919, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10677.6, 863.392, 42.8536, -2.93215, 0, 0, 0.994522, -0.104529, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10462, 1951.66, 9.521, 4.751, 0, 0, 0.693435, -0.720519, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9756.33, 1008.48, 35.1334, 5.20108, 0, 0, -0.515037, 0.857168, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11019, 960.273, 38.1155, 0.908, 0, 0, 0.438371, 0.898794, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9912.36, 1115.99, 37.6016, 5.74214, 0, 0, -0.267238, 0.963631, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10327, 1437.16, 41.671, 1.553, 0, 0, 0.700909, 0.71325, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10795.9, 1266.72, 34.3962, 1.32645, 0, 0, 0.615661, 0.788011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9938, 1438.88, 39.449, 4.51, 0, 0, 0.775039, -0.631913, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10518, 1953.73, 5.183, 1.485, 0, 0, 0.676104, 0.736807, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10530, 1940.12, 3.946, 5.535, 0, 0, 0.36566, -0.930748, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10498, 1985.95, 4.486, 2.477, 0, 0, 0.945272, 0.326285, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10320, 1884.29, 38.209, 3.721, 0, 0, 0.95836, -0.285563, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9917.85, 884.944, 33.5319, 3.73501, 0, 0, -0.956305, 0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9830.03, 1401.71, 48.2201, 3.52557, 0, 0, -0.981627, 0.190812, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9849.11, 1442.37, 40.4727, 2.63545, 0, 0, 0.968148, 0.25038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9741.26, 1284.89, 41.5235, -2.11185, 0, 0, 0.870356, -0.492423, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10089.5, 1729.51, 39.0119, 1.97222, 0, 0, 0.833886, 0.551937, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10029.4, 2017.66, -15.1234, 3.01942, 0, 0, 0.998135, 0.061049, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9888.8, 1745.42, 18.9283, 1.67552, 0, 0, 0.743145, 0.669131, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10314.5, 1278.49, 45.8038, 2.25148, 0, 0, 0.902585, 0.430511, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9736.57, 1076.61, 17.0875, -0.872665, 0, 0, 0.422618, -0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10476.2, 1968.14, 9.30176, 1.97222, 0, 0, 0.833886, 0.551937, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10479, 1994.38, 12.0118, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10206.2, 1805.9, 38.0356, -0.2618, 0, 0, 0.130526, -0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9892.99, 1446.51, 46.1272, -0.767945, 0, 0, 0.374607, -0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9905.59, 1435.75, 40.0603, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9966.55, 1790.64, 20.2446, -1.8326, 0, 0, 0.793353, -0.608761, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10008.5, 1865.07, 20.2473, 2.74017, 0, 0, 0.979925, 0.199368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10205.7, 1951.59, 21.2334, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10476.6, 1951.56, 11.4407, 0.139626, 0, 0, 0.069756, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10595.6, 1990.49, -4.30636, 1.64061, 0, 0, 0.731354, 0.681998, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10291.3, 1942.4, 35.3724, -1.13446, 0, 0, 0.5373, -0.843391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10904, 2098.75, 13.2407, 0.523599, 0, 0, 0.258819, 0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11194.2, 1694.54, 45.6586, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11175.1, 1620.2, 29.506, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10466.7, 1001.84, 49.0825, -2.58309, 0, 0, 0.961262, -0.275637, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9871.07, 1421.18, 45.4027, 0.733038, 0, 0, 0.358368, 0.93358, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11120.5, 1514.43, 24.9607, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11187.9, 1549.99, 20.5152, -0.471239, 0, 0, 0.233445, -0.97237, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11051.7, 1347.31, 43.2865, 2.82743, 0, 0, 0.987688, 0.156434, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11080.3, 1519.16, 31.9193, 5.95157, 0, 0, -0.165047, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11150.4, 1547.24, 22.634, 0.942477, 0, 0, 0.45399, 0.891007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10468.5, 1341.91, 44.6808, 0, 0, 0, 0, 0, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9917.51, 1586.95, 43.3489, 2.21657, 0, 0, 0.894934, 0.446198, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10679.6, 2004.03, 17.5325, -1.74533, 0, 0, 0.766044, -0.642788, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11087.8, 2077.56, 5.49718, 2.3911, 0, 0, 0.930418, 0.366501, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11193.2, 1441.63, 89.3392, -3.12414, 0, 0, 0.999962, -0.008727, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9939.12, 1472.84, 41.5435, 3.87463, 0, 0, -0.93358, 0.358368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10738.4, 2188.22, 16.2193, -0.785398, 0, 0, 0.382683, -0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10557.3, 1950.03, -1.81455, 2.23402, 0, 0, 0.898794, 0.438371, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10411.3, 1948.61, 12.6105, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11400, 1979.35, 5.21146, 2.32129, 0, 0, 0.91706, 0.39875, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9815.68, 1416.98, 38.3897, 1.3439, 0, 0, 0.622514, 0.782609, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10446.2, 1932.18, 13.1237, 3.05433, 0, 0, 0.999048, 0.0436193, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10492.3, 1913.15, 41.5969, 4.60767, 0, 0, -0.743144, 0.669132, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11298, 1636.16, 61.5966, 0.209439, 0, 0, 0.104528, 0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11257.2, 1054.57, 109.002, 6.16101, 0, 0, -0.0610485, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11151.8, 1017.99, 39.6759, 2.26892, 0, 0, 0.906307, 0.422619, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -10238, 1316.13, 43.1496, 4.81711, 0, 0, -0.66913, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11008.9, 835.871, 36.7729, 1.02974, 0, 0, 0.492423, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11320, 1603.41, 37.1515, 3.14159, 0, 0, -1, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11324, 1599.41, 37.1515, 3.14159, 0, 0, -1, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11446.9, 1718.49, 13.7341, 2.30383, 0, 0, 0.913545, 0.406738, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11557.6, 1578.82, -14.9868, 6.26573, 0, 0, -0.00872612, 0.999962, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11320.5, 1560.14, 28.3017, 0.139625, 0, 0, 0.0697555, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11216.8, 1537.08, 36.0322, 6.14356, 0, 0, -0.0697555, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11201.5, 1520.96, 21.3151, 5.14872, 0, 0, -0.537299, 0.843392, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11113.2, 1484.9, 25.8984, 3.28124, 0, 0, -0.997563, 0.0697661, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11158.7, 1562.7, 23.7935, 4.45059, 0, 0, -0.793353, 0.608762, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11231.4, 1625.16, 28.2586, 1.20428, 0, 0, 0.566406, 0.824126, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11175.3, 1637.78, 27.8243, 4.83456, 0, 0, -0.66262, 0.748956, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -11306.8, 1566.3, 37.7273, 1.67551, 0, 0, 0.743144, 0.669132, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6061.28, 89.874, 44.249, 0, 0, 0, -0.233445, 0.97237, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6636.36, -84.0922, 30.0194, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5664.99, 351.459, 17.8281, 0.698131, 0, 0, 0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6896.85, -630, 85.768, 1.425, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5214.69, 316.013, 43.3236, 0.750491, 0, 0, 0.366501, 0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5129.34, 146.26, 48.5483, 5.89921, 0, 0, -0.190808, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4573.88, 559.401, 1.293, 1.069, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4594.8, 576.503, 1.253, 2.757, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4602.73, 597.9, 1.681, 2.529, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6314.4, 83.0318, 44.3543, 1.74533, 0, 0, 0.766044, 0.642789, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4562.35, 820.458, 3.31582, 2.70526, 0, 0, 0.976296, 0.21644, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6608.19, 234.431, 44.8424, 4.99164, 0, 0, -0.601814, 0.798636, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6354.29, 119.984, 22.1034, 1.58825, 0, 0, 0.71325, 0.70091, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6798.9, 231.377, 25.7076, 5.60251, 0, 0, -0.333807, 0.942641, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7149.56, -284.307, 36.5178, 5.21854, 0, 0, -0.507538, 0.861629, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7052.75, 297.974, 0.323, -1, 0, 0, -0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7470.96, -1033.38, 16.8551, 3.31614, 0, 0, -0.996194, 0.087165, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6867.76, -610, 54.861, 1.522, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4896.26, 44.194, 60.931, 1.641, 0, 0, 0.731354, 0.681998, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6898.14, -589, 30.224, -3, 0, 0, -0.978148, 0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6825.55, -664, 86.337, -1, 0, 0, -0.67559, 0.737277, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6573.71, -219, 48.386, 2.95, 0, 0, 0.995396, 0.095846, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6917.62, -597, 45.348, 2.135, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4560.8, 630.978, 28.017, 3.521, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5161.25, 31.9684, 44.8402, 2.25148, 0, 0, 0.902585, 0.430511, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7277.69, -842, 37.668, 1.169, 0, 0, 0.551937, 0.833886, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6657.28, 89.4858, 34.8699, 0.122173, 0, 0, 0.0610485, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6725.17, 90.662, 29.634, 4.67, 0, 0, 0.722008, -0.691884, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7148.24, -934.898, 75.9276, 0.977384, 0, 0, 0.469472, 0.882948, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7637.88, -1134, 72.186, 5.297, 0, 0, 0.473286, -0.880909, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5788.55, 180.098, 41.5897, -0.034907, 0, 0, 0.017452, -0.999848, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4560.07, 587.813, 1.126, 5.591, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7605.1, -1025, 40.266, -1, 0, 0, -0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5242.39, 40.943, 61.728, 0, 0, 0, -0.199368, 0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6715.34, -360.81, 47.1552, -1.39626, 0, 0, 0.642788, -0.766044, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6176.48, 463.119, 24.8209, 5.07891, 0, 0, -0.566406, 0.824126, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6874.35, -583, 42.533, 1.781, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6835.45, -291, 40.423, 1.92, 0, 0, 0.819152, 0.573577, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7325.42, -523, 8.434, 1.553, 0, 0, 0.700909, 0.71325, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6832.27, -610, 51.397, 0.144, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6888.3, 396.644, 15.7299, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6770.85, 0.051432, 26.9423, 5.74214, 0, 0, -0.267238, 0.963631, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5122.07, 354.769, 11.8237, -2.9147, 0, 0, 0.993572, -0.113203, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4451.96, 413.918, 67.525, -1, 0, 0, -0.469471, 0.882948, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6135.04, 17.2649, 44.6068, 2.16421, 0, 0, 0.882948, 0.469472, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4420.88, 826.078, 15.3331, -1.18682, 0, 0, 0.559193, -0.829037, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4535.69, 902.658, 7.67582, 2.18166, 0, 0, 0.887011, 0.461749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4854.44, 535.212, 10.5588, -0.802851, 0, 0, 0.390731, -0.920505, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4787.54, 766.542, 8.5393, 1.20428, 0, 0, 0.566406, 0.824126, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4855.59, 722.665, 5.86654, -0.122173, 0, 0, 0.061049, -0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4348.59, 966.763, 15.5835, -2.89725, 0, 0, 0.992546, -0.121869, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4580.22, 709.536, 25.268, -0.122173, 0, 0, 0.061049, -0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4470.7, 743.511, -1.83624, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4422.54, 774.582, 14.917, 1.309, 0, 0, 0.608761, 0.793353, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4606.25, -16.0594, 72.8562, 0.541052, 0, 0, 0.267238, 0.96363, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4608.91, 88.0124, 68.9622, -2.14675, 0, 0, 0.878817, -0.477159, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4374.9, 568.298, 58.7022, 1.41372, 0, 0, 0.649448, 0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4553.72, 134.689, 61.4659, -1.06465, 0, 0, 0.507538, -0.861629, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4792.63, 33.5088, 63.3617, 1.74533, 0, 0, 0.766044, 0.642788, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4776.8, 379.226, 46.0786, 2.44346, 0, 0, 0.939693, 0.34202, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6355.19, -94.2816, 28.0781, -3.01942, 0, 0, 0.998135, -0.061048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6140.56, -124.413, 68.1804, -2.47837, 0, 0, 0.945519, -0.325568, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6140.81, -77.5832, 37.688, -1.46608, 0, 0, 0.669131, -0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5996.1, -116.554, 64.3843, 2.25148, 0, 0, 0.902585, 0.430511, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6004.19, -47.5299, 23.18, -2.94961, 0, 0, 0.995396, -0.095846, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7172.98, 299.393, -28.7123, 3.03687, 0, 0, 0.99863, 0.052336, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7148.67, 45.7817, 15.9786, -0.698132, 0, 0, 0.34202, -0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7358.84, 117.991, 12.6753, 3.35105, 0, 0, -0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7427.97, -258, 0.78858, 1.69297, 0, 0, 0.748956, 0.66262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6827.71, -727.338, 61.7001, -2.02458, 0, 0, 0.848048, -0.529919, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6780.64, -702.896, 73.9283, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7116.58, -332.782, 36.5861, 2.16421, 0, 0, 0.882948, 0.469472, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7087.58, -685.904, 65.6651, 2.07694, 0, 0, 0.861629, 0.507538, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7123.89, -787.781, 73.2812, 4.34587, 0, 0, -0.824126, 0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7223.21, -1027.89, 71.9734, 2.51327, 0, 0, 0.951057, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7905.12, -1060.73, 37.5217, -0.034907, 0, 0, 0.017452, -0.999848, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7925.2, -1176.94, 57.2328, 2.84488, 0, 0, 0.989016, 0.147811, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7959.42, -1006.33, 38.6745, -0.226893, 0, 0, 0.113203, -0.993572, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7749.07, -955.581, 32.851, 2.19912, 0, 0, 0.891007, 0.453991, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7688.81, -814.313, 8.07843, -0.698132, 0, 0, 0.34202, -0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7475.87, -686.872, 4.20838, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7518.72, -495.795, -0.740971, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7342.31, -1098.23, 47.9114, 0.558504, 0, 0, 0.275637, 0.961262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7513.76, -580.116, 0.446005, 0.401425, 0, 0, 0.199367, 0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5906.93, 616.413, 1.08114, 4.69494, 0, 0, -0.71325, 0.70091, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4662.22, 766.788, 30.5175, 3.29869, 0, 0, -0.996917, 0.0784664, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5153.76, 458.863, 26.0669, 1.43117, 0, 0, 0.656058, 0.75471, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6335.66, -169.052, 47.5812, 6.0912, 0, 0, -0.0958452, 0.995396, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6866.71, -659.674, 83.6733, 5.3058, 0, 0, -0.469471, 0.882948, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6872.02, -628.109, 84.8797, 3.194, 0, 0, -0.999657, 0.0262017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7517.69, -725.736, 3.49451, 2.00713, 0, 0, 0.843391, 0.5373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7346.2, -35.5666, 11.517, 2.68781, 0, 0, 0.97437, 0.224951, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 7245.04, 151.198, 5.61268, 3.50812, 0, 0, -0.983254, 0.182238, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4612.88, 634.874, 6.40536, 0.750491, 0, 0, 0.366501, 0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 5200.01, 116.131, 61.4818, 0.59341, 0, 0, 0.292371, 0.956305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6585.01, 291.717, 39.6147, 3.15906, 0, 0, -0.999962, 0.00873464, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6033.26, 155.371, 22.8641, 0.314158, 0, 0, 0.156434, 0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6704.14, -426.594, 74.9637, 2.11185, 0, 0, 0.870356, 0.492424, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6884.74, -458.588, 41.7223, 4.50295, 0, 0, -0.777145, 0.629321, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5547.15, -2933.8, 374.021, 3.10665, 0, 0, 0.999847, 0.0174693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5722, -3714, 312.778, -3, 0, 0, -0.953717, 0.300706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4946, -2901, 352.869, 4.391, 0, 0, 0.811015, -0.585025, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5606, -4105, 390.494, 3.054, 0, 0, 0.999048, 0.04362, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5773, -2917, 365.51, 0.731, 0, 0, 0.35732, 0.933982, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5711.03, -3176.5, 321.307, 5.77704, 0, 0, -0.25038, 0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4939, -2632, 331.85, 0.873, 0, 0, 0.422618, 0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5359, -3894, 341.694, 0.507, 0, 0, 0.250971, 0.967995, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5846, -3040, 330.966, 1.399, 0, 0, 0.643664, 0.765308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5091.79, -3324.24, 280.257, 3.78737, 0, 0, -0.948323, 0.317306, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4838.35, -3140.2, 318.547, 4.92183, 0, 0, -0.62932, 0.777146, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4985.08, -3873.26, 317.478, 2.65289, 0, 0, 0.970295, 0.241925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5896, -2848, 383.134, 1.871, 0, 0, 0.804971, 0.593315, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5804, -3134, 342.026, 3.506, 0, 0, 0.983404, -0.181432, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5849, -3600, 351.124, 5.819, 0, 0, 0.230151, -0.973155, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5538, -3116, 342.941, 3.977, 0, 0, 0.914032, -0.405643, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5899.41, -2972.41, 371.663, 4.57276, 0, 0, -0.754709, 0.656059, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5464.75, -3190.14, 335.181, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5109, -3122, 313.269, -2, 0, 0, -0.939693, 0.34202, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4943, -2974, 321.613, 0, 0, 0, -0.069756, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4986.73, -2962.82, 316.091, 1.88496, 0, 0, 0.809017, 0.587785, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5534.53, -2711.2, 369.837, 0.261799, 0, 0, 0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5357.71, -2718.82, 363.565, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4926.78, -3765, 322.941, 0.506145, 0, 0, 0.25038, 0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5306.12, -2698.85, 352.962, -2.51327, 0, 0, 0.951057, -0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4673.84, -3000.53, 320.473, -1.39626, 0, 0, 0.642788, -0.766044, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4898.06, -3039.79, 319.946, -2.1293, 0, 0, 0.87462, -0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5366.79, -3137.5, 303.529, -2.40855, 0, 0, 0.93358, -0.358368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5783.95, -3212.13, 309.845, 0.645772, 0, 0, 0.317305, 0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5376, -4026.73, 345.587, 2.23402, 0, 0, 0.898794, 0.438371, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5717.69, -3627.2, 315.512, -2.86234, 0, 0, 0.990268, -0.139173, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5626.25, -3151.73, 323.532, 1.39626, 0, 0, 0.642788, 0.766044, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4867.67, -2974.29, 318.302, 0.925024, 0, 0, 0.446198, 0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4965.51, -2996.1, 317.392, -2.6529, 0, 0, 0.970296, -0.241922, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5593.5, -3256.05, 289.35, -1.13446, 0, 0, 0.5373, -0.843391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5071.19, -2724.19, 320.265, -0.733038, 0, 0, 0.358368, -0.93358, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5960.8, -2781.47, 392.505, 0.122173, 0, 0, 0.061049, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5528.69, -2989.79, 373.278, -0.942478, 0, 0, 0.453991, -0.891006, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5906.61, -3848.87, 350.884, -2.77507, 0, 0, 0.983255, -0.182235, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5860.61, -3754.48, 334.931, -2.93215, 0, 0, 0.994522, -0.104529, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5869.6, -4071.64, 398.154, -1.16937, 0, 0, 0.551937, -0.833886, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5407.56, -3421.5, 285.105, 1.88495, 0, 0, 0.809016, 0.587786, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5522.71, -4161.74, 383.012, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4826.3, -3957.94, 336.999, 0.575959, 0, 0, 0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5477.28, -4141.59, 392.58, 1.85005, 0, 0, 0.798636, 0.601815, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5361.94, -3235.94, 286.772, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5179.52, -3290.78, 276.421, 3.38594, 0, 0, -0.992546, 0.12187, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5284.12, -3306.02, 254.845, 1.13446, 0, 0, 0.5373, 0.843391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4895.64, -4037.91, 325.167, 1.43117, 0, 0, 0.656059, 0.75471, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5090.62, -4145.23, 336.802, 0.959931, 0, 0, 0.461749, 0.887011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5692.69, -3123.95, 316.19, 4.18879, 0, 0, -0.866025, 0.500001, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5624.58, -2926.26, 409.998, -3.07178, 0, 0, 0.999391, -0.034899, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5744.19, -3532.99, 306.714, -0.541052, 0, 0, 0.267238, -0.96363, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5792.43, -3586.6, 337.733, 1.5708, 0, 0, 0.707107, 0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5189.03, -4137.99, 344.148, -1.48353, 0, 0, 0.67559, -0.737277, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4748.16, -3588.43, 308.719, 1.44862, 0, 0, 0.66262, 0.748956, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4728.94, -3142.43, 302.941, 3.10665, 0, 0, 0.999847, 0.0174693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4651.34, -3121.1, 301.035, -0.401426, 0, 0, 0.199368, -0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4834.88, -2986.44, 323.039, 2.33874, 0, 0, 0.920505, 0.390731, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4923.44, -2971.1, 317.558, -2.07694, 0, 0, 0.861629, -0.507538, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4968.39, -2911.64, 338.077, 1.53589, 0, 0, 0.694658, 0.71934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5362.56, -3552.67, 280.771, -1.64061, 0, 0, 0.731354, -0.681998, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5306.52, -3564.57, 287.213, -2.37365, 0, 0, 0.927184, -0.374607, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5748.61, -3936.8, 331.427, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5538.97, -4016.46, 382.358, -1.06465, 0, 0, 0.507538, -0.861629, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5791.16, -2947.83, 376.026, -2.98451, 0, 0, 0.996917, -0.078459, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6001.23, -2859, 394.728, 3.05433, 0, 0, 0.999048, 0.043619, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5517.81, -2731.01, 366.158, 0.750491, 0, 0, 0.366501, 0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4971.56, -3146.3, 321.716, 1.27409, 0, 0, 0.594823, 0.803857, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4913.92, -3381, 302.121, 0.541052, 0, 0, 0.267238, 0.96363, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5642.75, -3256.99, 312.215, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5142.94, -3360.46, 284.629, -1.29154, 0, 0, 0.601815, -0.798635, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5498.42, -3316.85, 286.99, -2.1293, 0, 0, 0.87462, -0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5423.67, -2709.96, 368.752, 2.51327, 0, 0, 0.951056, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5815.49, -3477.51, 313.627, 0.244346, 0, 0, 0.121869, 0.992546, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5582.72, -3545.79, 290.129, 5.044, 0, 0, -0.580703, 0.814116, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4863.07, -3003.43, 317.635, 6.16101, 0, 0, -0.0610485, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4789.87, -3009.63, 307.927, 0.994837, 0, 0, 0.477158, 0.878817, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4835.49, -2962.1, 321.342, 1.93731, 0, 0, 0.824125, 0.566408, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4661.44, -2844.7, 326.785, 3.82227, 0, 0, -0.942641, 0.333808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4907.81, -2994.97, 318.189, 2.67035, 0, 0, 0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5268.53, -2691.13, 351.273, 2.04204, 0, 0, 0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5162.66, -2647.23, 354.12, 5.41052, 0, 0, -0.422618, 0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5116.83, -3976.2, 319.844, 4.17134, 0, 0, -0.870356, 0.492424, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5919.37, -4147.32, 403.105, 4.15388, 0, 0, -0.874619, 0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5930.48, -3610.93, 355.29, 0.383971, 0, 0, 0.190808, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6054.89, -2957.95, 401.839, 5.09636, 0, 0, -0.559193, 0.829038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6161.33, -3029.14, 392.284, 5.96903, 0, 0, -0.156434, 0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6111.96, -2923.7, 401.062, 1.69297, 0, 0, 0.748956, 0.66262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6141, -2972.89, 400.16, 2.80997, 0, 0, 0.986285, 0.16505, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6093.14, -3050.13, 400.793, 5.70723, 0, 0, -0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5474.6, -3864.09, 331.252, 1.29154, 0, 0, 0.601814, 0.798636, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4733.4, -3428.12, 287.19, 0.90757, 0, 0, 0.438371, 0.898794, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5302.34, -3377.92, 286.355, 5.20108, 0, 0, -0.515037, 0.857168, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5815.2, -3610.52, 356.486, 3.24635, 0, 0, -0.998629, 0.0523532, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4035, -2924, 9.719, 5.223, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -2877, -1713, 8.906, 3.341, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3049, -1892, 7.422, 0.56, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4337, -2650, 101.786, 3.08, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3066.62, -1932.85, 4.93504, 2.33874, 0, 0, 0.920505, 0.390732, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4361, -2925, 34.976, 1.875, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4140, -2456, 185.147, 1.03, 0, 0, 0.492423, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4060, -2416, 121.647, 5.448, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3153, -1198, 7.912, 2.401, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -2761, -1638, 8.36136, 2.571, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4034, -2472, 151.041, 4.721, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3123, -1427, 8.889, 0.32, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3715.4, -2879.98, 5.16273, 1.93731, 0, 0, 0.824125, 0.566408, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -2739, -1694, 7.936, 0.58, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3195, -1538, 7.937, 0.892, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3008, -1530, 6.995, 4.785, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3833.94, -2866.97, 3.53475, 3.24635, 0, 0, -0.998629, 0.0523532, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4100, -2385, 117.557, 2.81, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4080.01, -2911.19, 4.63452, 0.977383, 0, 0, 0.469471, 0.882948, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -2982, -1308, 7.72, 1.31, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3881, -2862, 6.556, 3.882, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3021, -1509, 7.241, 4.472, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4028.35, -2945.35, 6.08753, 1.06465, 0, 0, 0.507538, 0.861629, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3118.42, -1245, 2.63742, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3644.19, -1439.96, 11.7144, 0.034907, 0, 0, 0.017452, 0.999848, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -2757.12, -1669.96, -0.252495, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4159.18, -2369.03, 212.793, -2.18166, 0, 0, 0.887011, -0.461749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4153.12, -3038.73, 2.50557, 3.90954, 0, 0, -0.927183, 0.374608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3024.83, -1356.18, 0.228389, -2.16421, 0, 0, 0.882948, -0.469472, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3800.21, -1383.16, 39.8308, 1.90241, 0, 0, 0.814116, 0.580703, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4144.73, -2474.29, 185.1, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3338.99, -1702.82, 13.6847, 2.58309, 0, 0, 0.961262, 0.275637, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3438.17, -1643.46, 21.7194, 3.38594, 0, 0, -0.992546, 0.12187, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3531.56, -1456.55, 27.5651, -0.314159, 0, 0, 0.156434, -0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -2923.04, -1608.39, 0.933756, 0.925024, 0, 0, 0.446198, 0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4271.32, -3020.73, 10.5367, 4.18879, 0, 0, -0.866025, 0.500001, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3037.39, -1525.7, 0.639823, 0.90757, 0, 0, 0.438371, 0.898794, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4218.17, -2474.73, 282.472, 2.93214, 0, 0, 0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4341.22, -2511.06, 256.408, 0.471238, 0, 0, 0.233445, 0.97237, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3132.9, -1450.79, -0.29859, 0.436332, 0, 0, 0.216439, 0.976296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3220.77, -1333.22, 1.52318, 3.12412, 0, 0, 0.999962, 0.00873464, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -2951.17, -1806.75, 3.60814, 4.76475, 0, 0, -0.688354, 0.725375, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -3174.07, -1533.11, 2.15818, 4.03171, 0, 0, -0.902585, 0.430512, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -445.19, -1529.4, 71.0053, -1.8675, 0, 0, 0.803857, -0.594823, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -502.591, -828.237, 53.3549, 3.03684, 0, 0, 0.998629, 0.0523532, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -714, 488.594, 82.605, 0.367, 0, 0, 0.182235, 0.983255, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -664.995, 489.126, 88.248, 6.14356, 0, 0, -0.0697555, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -1100.65, -830.58, 17.9532, -0.471239, 0, 0, 0.233445, -0.97237, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -809.145, 21.4447, 36.635, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -745.166, 577.436, 103.427, 0.925024, 0, 0, 0.446198, 0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -278.271, 228.777, 102.131, -2.23402, 0, 0, 0.898794, -0.438371, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -386.992, 260.187, 94.6462, 1.93731, 0, 0, 0.824126, 0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -801.256, 519.086, 101.712, 1.88496, 0, 0, 0.809017, 0.587785, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -743.788, -245.083, 40.2169, 2.49582, 0, 0, 0.948324, 0.317305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -315.451, -842.828, 61.7272, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -893.763, 309.781, 39.3592, -0.122173, 0, 0, 0.061049, -0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -644.457, -1361.17, 68.5742, 2.23402, 0, 0, 0.898794, 0.438371, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -620.953, -849.445, 39.3938, -3.05433, 0, 0, 0.999048, -0.043619, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -1458.58, -1085.96, 7.96406, 5.14872, 0, 0, -0.537299, 0.843392, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -1048.94, -13.9949, 17.3759, 0.331611, 0, 0, 0.165047, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -1151.24, 112.54, 8.40114, 5.88176, 0, 0, -0.199367, 0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -435.648, 303.569, 99.0216, 1.0472, 0, 0, 0.5, 0.866025, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -472.812, 368.856, 106.055, 2.67035, 0, 0, 0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -291.258, 293.587, 118.587, 0.767944, 0, 0, 0.374606, 0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -383.365, -182.706, 61.7857, 5.23599, 0, 0, -0.5, 0.866025, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -357.374, -456.945, 57.1996, 1.6057, 0, 0, 0.719339, 0.694659, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -1004.35, -339.056, 13.7319, 0.226893, 0, 0, 0.113203, 0.993572, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -745.488, -98.0213, 57.8578, 1.81514, 0, 0, 0.788011, 0.615662, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -526.157, 480.685, 86.8847, 4.20625, 0, 0, -0.861628, 0.507539, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -473.576, 1463.86, 27.1123, 0.261799, 0, 0, 0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -342.521, 1673.51, 25.3213, 5.81195, 0, 0, -0.233445, 0.97237, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 388.705, 1038.2, 107.54, 0.925024, 0, 0, 0.446198, 0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 129.166, 1097.66, 87.4431, 0.680678, 0, 0, 0.333807, 0.942641, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -643.304, 789.86, 128.134, 0.855211, 0, 0, 0.414693, 0.909961, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -577.718, 1709.77, 22.0632, -1.13446, 0, 0, 0.5373, -0.843391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 805.129, 1164.21, 52.6026, 0.855211, 0, 0, 0.414693, 0.909961, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 895.387, 1604.67, 44.542, 5.46, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 848.075, 368.491, 22.9007, 3.22886, 0, 0, -0.999048, 0.0436193, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1144.8, 1857.25, 27.916, 2.087, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1196.7, 1185.35, 49.815, 2.492, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -142.785, 1280.16, 54.9085, 3.7001, 0, 0, -0.961261, 0.27564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 96.613, 1030.55, 124.76, 3.651, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1300.18, 1395.15, 61.724, 1.515, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -581, 1097.44, 90.111, 1.239, 0, 0, 0.580703, 0.814115, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 383.233, 1054.9, 106.53, 2.618, 0, 0, 0.965926, 0.258819, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -447, 1648.44, 13.009, 2.867, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 912.864, 623.534, 53.645, 0.69, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -123, 1353.89, 94.635, 2.726, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 622.258, 1211.16, 86.023, 5.952, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 180.635, 1062.78, 92.608, 4.751, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -349, 992.667, 126.913, 2.678, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 887.186, 1642.41, 34.843, 5.909, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 298.83, 1183.47, 90.898, 4.294, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 970.928, 1637.48, 44.789, 1.986, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1046.06, 1433.04, 42.641, 0.469, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1235.25, 1540.25, 47.696, 5.078, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 831.482, 604.969, 34.86, 1.969, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -710, 1204.41, 90.622, 0.044, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1234.75, 2052.94, 10.635, 0.541, 0, 0, 0.267238, 0.96363, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -262, 1496.29, 47.785, 0.859, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 900.318, 1704.4, 29.177, -1, 0, 0, -0.5373, 0.843391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 266, 1074.96, 96.534, 5.809, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -249, 1431.17, 40.295, 0, 0, 0, -0.21644, 0.976296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 639.734, 1222.12, 85.3606, 4.7822, 0, 0, -0.681998, 0.731354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 825.814, 1157.32, 36.97, 0.281, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1364.66, 1138.66, 81.29, 3.329, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 229.32, 1514.23, 153.349, 2.077, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 762.282, 1271.56, 69.5199, 3.50812, 0, 0, -0.983254, 0.182238, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1011.34, 1184.54, 56.9709, 5.16618, 0, 0, -0.529919, 0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 511.705, 1353.7, 88.8817, 1.8675, 0, 0, 0.803857, 0.594823, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 646.375, 1415.15, 83.733, 1.228, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 710.791, 1538.67, 69.1527, 1.46608, 0, 0, 0.66913, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 978.403, 1369.48, 46.028, 6.095, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 271.289, 1564.88, 149.205, 1.426, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -319, 990.09, 124.668, 2.398, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 512.472, 1052.08, 107.543, 0.244, 0, 0, 0.121869, 0.992546, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 914.508, 758.553, 56.683, 1.557, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -635, 1599.98, 16.259, 1.866, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 378.417, 1341.24, 92.854, 2.775, 0, 0, 0.983255, 0.182235, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -234, 1297.04, 46.842, 1.451, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 723.573, 1125.98, 64.2494, 4.46804, 0, 0, -0.788011, 0.615662, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 742.61, 1032.95, 56.357, 1.41372, 0, 0, 0.649447, 0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 476.727, 1412.82, 116.182, 3.369, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1352.98, 1243.32, 47.671, 1.5708, 0, 0, 0.707107, 0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1339.78, 1960.68, 13.496, -1, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 227.436, 1485.74, 143.122, 2.797, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 906.962, 1070.09, 44.2785, 0.314159, 0, 0, 0.156434, 0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1023.28, 1784.72, 20.375, 2.37365, 0, 0, 0.927184, 0.374607, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1093.2, 2060.97, 0.456879, 1.23918, 0, 0, 0.580703, 0.814116, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1147.87, 1983.14, 22.8906, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1120.89, 1749.98, 21.8891, -0.785398, 0, 0, 0.382683, -0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1277.66, 1963.54, 16.3876, 1.32645, 0, 0, 0.615662, 0.788011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1222.31, 1578.68, 36.4169, 4.71239, 0, 0, 0.707107, -0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 305.858, 1558.15, 134.453, 1.79769, 0, 0, 0.782608, 0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1098.54, 229.932, 34.8875, 1.18682, 0, 0, 0.559193, 0.829038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -313.933, 936.846, 131.932, 2.14675, 0, 0, 0.878817, 0.477159, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1415.12, 1940.35, 10.5989, 0.645772, 0, 0, 0.317305, 0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 908.045, 1309.13, 48.4337, 5.2709, 0, 0, -0.484809, 0.87462, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 613.494, 1429.4, 102.028, -1.22173, 0, 0, 0.573576, -0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 923.438, 1590.01, 36.9635, 1.72788, 0, 0, 0.760406, 0.649448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1317.5, 1982.83, 15.0881, 3.01942, 0, 0, 0.998135, 0.061049, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1383.2, 1977.71, 15.059, -0.925024, 0, 0, 0.446198, -0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1438.32, 1980.49, 13.6133, -2.6529, 0, 0, 0.970296, -0.241922, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1132.6, 1902.35, 34.3818, -1.98968, 0, 0, 0.838671, -0.544639, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 784.642, 1691.71, 33.4731, -0.855212, 0, 0, 0.414693, -0.909961, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 315.29, 1409.67, 136.68, 2.51327, 0, 0, 0.951057, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1196.38, 1447.38, 44.417, 3.194, 0, 0, -0.999657, 0.0262017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 815.299, 1506.07, 47.6115, 5.35816, 0, 0, -0.446198, 0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1035.11, 1633.56, 28.2433, 0.261798, 0, 0, 0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1086.69, 1964.56, 8.29545, 4.66003, 0, 0, -0.725374, 0.688355, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1324.64, 1969.28, 14.6851, 0.331611, 0, 0, 0.165047, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1361.38, 1971.22, 13.9368, 2.82743, 0, 0, 0.987688, 0.156436, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1292.67, 1379.22, 53.7565, 2.51327, 0, 0, 0.951056, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1051.6, 742.094, 59.4798, 1.64061, 0, 0, 0.731353, 0.681999, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1193.68, 1263.56, 49.0877, 1.74533, 0, 0, 0.766044, 0.642789, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 396.622, 1014.3, 110.456, 1.78023, 0, 0, 0.777145, 0.629321, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 310.01, 1083.07, 105.552, 0.296705, 0, 0, 0.147809, 0.989016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -476.366, 1585.88, 17.7124, 6.12611, 0, 0, -0.0784588, 0.996917, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -522.009, 1252.89, 68.4464, 3.82227, 0, 0, -0.942641, 0.333808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 416.263, 1036.77, 106.968, 2.04204, 0, 0, 0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -631.387, -2378.14, 135.454, -1.11701, 0, 0, 0.529919, -0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -601.688, -2027.34, 145.97, 1.37881, 0, 0, 0.636078, 0.771625, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -770.842, -1545.78, 148.305, -0.436333, 0, 0, 0.21644, -0.976296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1594.93, -3509.24, 135.792, -1.3439, 0, 0, 0.622515, -0.782608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3307.13, -2266.09, 93.2679, 1.83259, 0, 0, 0.793353, 0.608762, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3815.96, -2400.9, 105.543, -1.16937, 0, 0, 0.551937, -0.833886, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 405.622, -2478.65, 95.5127, 0.698132, 0, 0, 0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 81.6544, -2316.57, 107.679, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 319.669, -1761.75, 103.019, 1.36136, 0, 0, 0.62932, 0.777146, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 405.053, -1977.74, 103.468, -2.1293, 0, 0, 0.87462, -0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 381.943, -2117.46, 141.642, -0.733038, 0, 0, 0.358368, -0.93358, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -418.612, -2186.97, 143.571, 5.70723, 0, 0, -0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1335.15, -3165.44, 97.3283, 0.907571, 0, 0, 0.438371, 0.898794, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -514.753, -3016.06, 100.401, -2.93215, 0, 0, 0.994522, -0.104529, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1376.74, -3570.06, 97.8136, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1556.57, -2080.35, 87.9973, -0.174533, 0, 0, 0.087156, -0.996195, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 730.945, -2801.62, 102.139, 3.01942, 0, 0, 0.998135, 0.061049, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3307.45, -1786.93, 102.86, 0.663225, 0, 0, 0.325568, 0.945519, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3193.65, -1641.74, 91.8558, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1723.62, -3260.31, 96.8265, 0.418879, 0, 0, 0.207912, 0.978148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1273.68, -3803, 31.5641, 5.70723, 0, 0, -0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1356.53, -3763.91, 58.7635, -2.84489, 0, 0, 0.989016, -0.147809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3738.37, -1542.1, 109.619, 0.593412, 0, 0, 0.292372, 0.956305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2215.77, -1631.75, 105.657, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 620.868, -3887.82, 29.8135, 1.23918, 0, 0, 0.580703, 0.814116, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 790.85, -3870.65, 21.5309, 3.94445, 0, 0, -0.920505, 0.390732, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -111.785, -4017.52, 67.2261, -1.41372, 0, 0, 0.649448, -0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 879.885, -3867.51, 34.1144, 2.82743, 0, 0, 0.987688, 0.156434, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1299.94, -4054.1, 39.2247, -0.20944, 0, 0, 0.104528, -0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 980.647, -4085.28, -5.86554, 0.383972, 0, 0, 0.190809, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 765.985, -3999.39, 24.1948, -2.75762, 0, 0, 0.981627, -0.190809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -861.641, -3449.88, 93.6537, 0.855211, 0, 0, 0.414693, 0.909961, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1112.9, -3589.96, 109.39, 0.942478, 0, 0, 0.45399, 0.891007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -484.619, -3073.22, 158.357, 0.15708, 0, 0, 0.078459, 0.996917, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 958.728, -3689.37, 37.103, 0.122173, 0, 0, 0.061049, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3942.16, -2003.7, 108.563, -1.62316, 0, 0, 0.725374, -0.688354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -10.5625, -3934.51, 56.4291, -1.85005, 0, 0, 0.798635, -0.601815, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 503.216, -3923.44, 23.0152, 3.47321, 0, 0, -0.986285, 0.16505, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 557.418, -3846.62, 30.4998, 2.35619, 0, 0, 0.92388, 0.382683, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1391.02, -3936.81, 20.6872, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -338.932, -2986.05, 100.969, 2.25147, 0, 0, 0.902585, 0.430512, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -952.934, -1583.11, 94.7977, 2.86234, 0, 0, 0.990268, 0.139173, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 887.469, -3138.51, 118.462, 5.49779, 0, 0, -0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1386.23, -1625.25, 120.082, -3.10669, 0, 0, 0.999848, -0.017452, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1013.44, -1702.45, 102.384, -0.715585, 0, 0, 0.350207, -0.936672, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1236.18, -3317.72, 101.758, -0.506145, 0, 0, 0.25038, -0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -176.022, -3579.29, 47.931, 0.20944, 0, 0, 0.104528, 0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1261.57, -2591.49, 102.075, 0.488692, 0, 0, 0.241922, 0.970296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1060.9, -2632.01, 101.019, 0.698132, 0, 0, 0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1190.27, -3345.26, 99.7463, -2.70526, 0, 0, 0.976296, -0.21644, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1844.02, -3356.48, 78.6834, 2.46091, 0, 0, 0.942641, 0.333807, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2041.03, -3471.56, 99.7707, 1.8675, 0, 0, 0.803857, 0.594823, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1348.71, -2963.96, 98.7622, -2.79253, 0, 0, 0.984808, -0.173648, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 591.822, -3281.41, 187.322, -2.07694, 0, 0, 0.861629, -0.507538, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 840.727, -3667.57, 32.224, 2.30383, 0, 0, 0.913545, 0.406737, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 450.51, -3281.45, 98.8256, -2.11185, 0, 0, 0.870356, -0.492423, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -621.845, -2219.93, 215.434, -0.10472, 0, 0, 0.052336, -0.99863, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1268.61, -1925.52, 89.2233, 2.09439, 0, 0, 0.866025, 0.5, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -970.368, -3106.75, 114.669, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -202.882, -3173.86, 178.712, 1.79769, 0, 0, 0.782608, 0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -342.159, -3227.42, 187.558, 0.575959, 0, 0, 0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1980.27, -3299.42, 118.611, 2.44346, 0, 0, 0.939693, 0.34202, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -590.947, -3261.27, 100.938, -0.523599, 0, 0, 0.258819, -0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 728.463, -3063.25, 93.5535, 0.331613, 0, 0, 0.165048, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 660.177, -3168.27, 183.398, -2.75762, 0, 0, 0.981627, -0.190809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 788.479, -3309.57, 230.934, 1.67552, 0, 0, 0.743145, 0.669131, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2063.11, -2160.76, 117.148, 2.98451, 0, 0, 0.996917, 0.078459, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -367.45, -3333.21, 94.927, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2575.44, -2387.76, 104.146, 5.8294, 0, 0, -0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3034.66, -1600.33, 109.809, 1.29154, 0, 0, 0.601815, 0.798636, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 46.2267, -1724.99, 113.622, 5.0091, 0, 0, -0.594823, 0.803857, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 835.805, -4055, -9, 3.073, 0, 0, 0.999408, 0.034417, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3710, -1559, 92.09, 2.772, 0, 0, 0.982941, 0.183923, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1136, -3019, 104.508, -1, 0, 0, -0.350207, 0.936672, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -727.292, -2078.22, 147.917, 2.594, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 212.813, -2059, 123.777, 0.855, 0, 0, 0.414693, 0.909961, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 251.159, -3871.59, 39.2736, 0.331611, 0, 0, 0.165047, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -493, -2338, 130.473, 4.645, 0, 0, 0.730471, -0.682943, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1387.82, -2392.19, 130.658, 2.3911, 0, 0, 0.930417, 0.366502, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -737.676, -3627.47, 95.0698, 4.76475, 0, 0, -0.688354, 0.725375, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2726.32, -2024.73, 113.357, 2.87979, 0, 0, 0.991445, 0.130528, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -101.218, -2886.92, 91.6667, 4.20625, 0, 0, -0.861628, 0.507539, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2595.26, -2242.73, 109.43, 1.43117, 0, 0, 0.656058, 0.75471, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -576, -2394, 119.873, 2.031, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -2056.05, -2825.92, 95.1547, 4.27606, 0, 0, -0.843391, 0.5373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 791.334, -4036, -6, 3.273, 0, 0, 0.997842, -0.065666, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1069.42, -2112.45, 56.0496, 1.5708, 0, 0, 0.707107, 0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1789.58, -1929.02, 122.134, 4.67748, 0, 0, -0.719339, 0.694659, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 103713, 1, -4093.85, -2122.36, 55.9365, 1.74533, 0, 0, 0.766044, 0.642789, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1066.33, -2085.51, 57.133, 2.44346, 0, 0, 0.939692, 0.342021, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -704, -2119, 170.004, 2.635, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1300, -3262, 97.866, -3, 0, 0, -0.989016, 0.147809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 814.53, -3039.36, 98.4465, 4.7473, 0, 0, -0.694658, 0.71934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1110, -2139, 80.593, 5.06, 0, 0, 0.57422, -0.818701, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1297, -3029, 72.685, -1, 0, 0, -0.66262, 0.748955, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1823.53, -1875.67, 97.7162, 6.24828, 0, 0, -0.0174522, 0.999848, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 207.442, -3865, 45.509, 4.674, 0, 0, 0.720517, -0.693438, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2637, -1761, 105.349, -3, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 713.16, -4010.18, 9.61455, 5.58505, 0, 0, -0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1345.16, -2232.31, 98.6866, 4.24115, 0, 0, -0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -423.354, -3601.65, 97.3639, 6.13159, 0, 0, 0.0757235, -0.997129, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -75.023, -3419.94, 98.4463, 3.79425, 0, 0, 0.947226, -0.320567, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 18.0536, -3379.66, 100.804, 0.173563, 0, 0, 0.0866726, 0.996237, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -294.109, -3269.11, 169.451, 3.76676, 0, 0, 0.951542, -0.307517, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -127.005, -3231.17, 117.079, 4.87417, 0, 0, 0.647658, -0.761931, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -105.957, -3021.48, 94.3172, 5.22132, 0, 0, 0.506339, -0.862335, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 424.006, -2508.96, 99.3446, 0.198702, 0, 0, 0.0991876, 0.995069, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 286.965, -2491.21, 96.9229, 0.727275, 0, 0, 0.355676, 0.934609, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 151.739, -2540.53, 103.42, 1.22679, 0, 0, 0.575646, 0.817699, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -11.7106, -2513.13, 106.123, 3.00964, 0, 0, 0.997824, 0.0659279, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 84.5532, -2268.01, 106.783, 0.543493, 0, 0, 0.268414, 0.963304, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 79.3855, -2183.33, 99.2929, 5.02576, 0, 0, 0.588104, -0.808785, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 163.719, -1906.58, 96.5737, 4.81135, 0, 0, 0.671268, -0.741215, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 52.4541, -1773.29, 104.064, 1.17653, 0, 0, 0.554917, 0.831905, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 80.1229, -1813.46, 100.563, 0.566275, 0, 0, 0.27937, 0.960184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 107.599, -1835.43, 102.923, 1.27706, 0, 0, 0.596016, 0.802972, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1553.23, -1500.86, 143.728, -2.72271, 0, 0, 0.978148, -0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1708.53, -3470.65, 96.6088, 4.90438, 0, 0, -0.636078, 0.771625, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3415, -2304, 114.538, 1.03, 0, 0, 0.492424, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1088.5, -2223.12, 67.59, 4.08407, 0, 0, -0.891007, 0.453991, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1530.36, -3934.37, 11.9037, 3.50812, 0, 0, -0.983254, 0.182238, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1019.65, -2055.23, 63.3198, 4.88692, 0, 0, -0.642787, 0.766045, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2750.86, -1867.28, 93.5215, 2.47837, 0, 0, 0.945518, 0.325568, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, 94.9738, -1945.36, 79.6132, 4.03171, 0, 0, -0.902585, 0.430512, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -426, -3247, 158.574, 0.96, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 389.877, -4049.1, 38.8331, 6.0912, 0, 0, -0.0958452, 0.995396, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1900.59, -1747.52, 92.6143, 4.34587, 0, 0, -0.824126, 0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1958.45, -2686.12, 94.873, 3.99681, 0, 0, -0.909961, 0.414694, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3172.37, -1849.55, 99.9321, 3.56047, 0, 0, -0.978148, 0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -592, -2116, 186.799, 1.653, 0, 0, 0.735462, 0.677566, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -88.5203, -1416.21, 100.186, 2.98451, 0, 0, 0.996917, 0.078459, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -271.54, -1471.81, 104.117, 0.907571, 0, 0, 0.438371, 0.898794, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 806.486, -1391, 92.108, 4.69, 0, 0, 0.714839, -0.699289, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3576.84, -2404.6, 99.1772, 4.93928, 0, 0, -0.622514, 0.782609, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1874.66, -3107.28, 106.662, 0.523598, 0, 0, 0.258819, 0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4146.65, -2297.33, 105.493, 0.296705, 0, 0, 0.147809, 0.989016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3439.51, -2399.7, 97.3195, 2.04204, 0, 0, 0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1303.93, -3200.05, 96.2165, 4.43314, 0, 0, -0.798635, 0.601815, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 630.384, -3078.95, 101.372, 1.53589, 0, 0, 0.694658, 0.71934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 682.998, -3525.74, 100.477, 5.32326, 0, 0, -0.461748, 0.887011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 865.913, -3474.46, 96.8132, 1.50098, 0, 0, 0.681998, 0.731354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 272.095, -2466.54, 102.545, 1.11701, 0, 0, 0.529919, 0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 155.229, -2527.16, 108.985, 2.72271, 0, 0, 0.978148, 0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 388.236, -2245.49, 196.68, 0.087266, 0, 0, 0.0436192, 0.999048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 182.288, -1926.24, 100.09, 3.80482, 0, 0, -0.945518, 0.325568, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 428.281, -1788.9, 115.552, 4.08407, 0, 0, -0.891007, 0.453991, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -344.821, -1543.51, 101.365, 3.42085, 0, 0, -0.990268, 0.139175, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -25.0396, -2447.36, 126.414, 1.69297, 0, 0, 0.748956, 0.66262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 639.388, -3394.1, 168.933, 6.14356, 0, 0, -0.0697555, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 868.723, -3359.76, 175.461, 3.50812, 0, 0, -0.983254, 0.182238, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 183.748, -1734.99, 97.5922, 5.95157, 0, 0, -0.165047, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 973.186, -3225.1, 101.245, 0.401425, 0, 0, 0.199367, 0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -501.907, -1354.49, 92.7284, 2.00713, 0, 0, 0.843391, 0.5373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1470.98, -3785.21, 35.7294, 2.53072, 0, 0, 0.953716, 0.300708, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1175.11, -2532.84, 123.906, 3.01941, 0, 0, 0.998135, 0.0610518, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -724.941, -2151.03, 138.44, 2.63544, 0, 0, 0.968147, 0.250381, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2494.99, -2542.07, 96.1752, 3.99681, 0, 0, -0.909961, 0.414694, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -511.093, -3651.16, 101.68, 5.81195, 0, 0, -0.233445, 0.97237, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -370.268, -3674.13, 46.1044, 4.2237, 0, 0, -0.857167, 0.515038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -520.767, -3323.54, 94.2695, 4.5204, 0, 0, -0.771625, 0.636078, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1548.2, -3675.97, 101.596, 6.23083, 0, 0, -0.0261765, 0.999657, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -100.367, -1315.26, 97.9216, 5.88176, 0, 0, -0.199367, 0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1566.68, -2692.2, 90.0199, 3.82227, 0, 0, -0.942641, 0.333808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1593.09, -1622.81, 116.249, 4.67748, 0, 0, -0.719339, 0.694659, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1447.37, -2373.73, 112.527, 3.42085, 0, 0, -0.990268, 0.139175, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1706.42, -3703.33, 33.6647, 2.35619, 0, 0, 0.92388, 0.382683, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 420.65, -3565.02, 56.9573, 2.93214, 0, 0, 0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2912.52, -1925.08, 92.8999, 0.0349062, 0, 0, 0.0174522, 0.999848, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2635.87, -2400.89, 95.7149, 0.558504, 0, 0, 0.275637, 0.961262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -720.831, -2347.2, 137.162, 2.65289, 0, 0, 0.970295, 0.241925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 814.091, -3138.75, 174.867, 3.29869, 0, 0, -0.996917, 0.0784664, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1180.05, -2749.38, 102.568, 5.34071, 0, 0, -0.45399, 0.891007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 255.976, -2246.03, 220.337, 0.663223, 0, 0, 0.325567, 0.945519, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1086.62, -1575, 166.013, 3.56047, 0, 0, -0.978148, 0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -566.826, -1441.67, 97.7237, 3.90954, 0, 0, -0.927183, 0.374608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 935.746, -2715.83, 103.601, 5.21854, 0, 0, -0.507538, 0.861629, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 618.11, -3663.26, 43.0938, 3.97936, 0, 0, -0.913545, 0.406738, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -648.439, -1595.35, 114.387, 4.31097, 0, 0, -0.833885, 0.551938, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1987.63, -2931.69, 94.5732, 5.98648, 0, 0, -0.147809, 0.989016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 503.881, -3469.48, 104.275, 4.5204, 0, 0, -0.771625, 0.636078, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1459.06, -3604.05, 93.9616, 2.96704, 0, 0, 0.996194, 0.087165, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1919.06, -2721.38, 91.8313, 4.15388, 0, 0, -0.874619, 0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -2080.55, -2800.56, 97.923, 0.837758, 0, 0, 0.406736, 0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1994.19, -2817.67, 95.5165, 2.87979, 0, 0, 0.991445, 0.130528, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1970.99, -2801.34, 91.8729, 3.12412, 0, 0, 0.999962, 0.00873464, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -2241.22, -2552.48, 92.1614, 4.10152, 0, 0, -0.887011, 0.461749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, 52.788, -1923.76, 80.1537, 0.90757, 0, 0, 0.438371, 0.898794, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, 299.283, -1414.12, 93.4663, 3.90954, 0, 0, -0.927183, 0.374608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, 669.423, -1542.82, 106.891, 5.60251, 0, 0, -0.333807, 0.942641, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1844.55, -1955.4, 94.9395, 6.14356, 0, 0, -0.0697555, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -3956.45, -1863.94, 97.3947, 4.46804, 0, 0, -0.788011, 0.615662, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -141.477, -3034.68, 91.7, 5.25344, 0, 0, -0.492423, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1397.27, -1521.42, 117.744, 3.73501, 0, 0, -0.956305, 0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1462.52, -1464.5, 102.557, 0.750491, 0, 0, 0.366501, 0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -3672.36, -1631.22, 95.3799, 2.33874, 0, 0, 0.920505, 0.390732, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -3720.6, -1553.41, 96.1831, 4.4855, 0, 0, -0.782608, 0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -3925.95, -1621.06, 91.8752, 0.279252, 0, 0, 0.139173, 0.990268, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -3991.88, -1883.89, 97.9145, 5.41052, 0, 0, -0.422618, 0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1874.54, -2720.51, 95.3035, 1.06465, 0, 0, 0.507538, 0.861629, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -2067.9, -1745.81, 105.385, 5.42798, 0, 0, -0.414693, 0.909962, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -229.327, -2982.14, 92.0946, 3.75246, 0, 0, -0.953716, 0.300708, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -116.853, -3193.63, 93.3306, 3.10665, 0, 0, 0.999847, 0.0174693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -223.339, -3348.09, 91.6667, 0.331611, 0, 0, 0.165047, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -261.772, -3343.72, 99.7645, 1.81514, 0, 0, 0.788011, 0.615662, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, 697.511, -1628.27, 92.4118, 2.42601, 0, 0, 0.936672, 0.350207, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, 336.791, -1647.45, 105.217, 2.40855, 0, 0, 0.93358, 0.358368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -3980.38, -1706.46, 94.8775, 2.42601, 0, 0, 0.936672, 0.350207, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -3701.07, -1621.85, 96.2469, 3.90954, 0, 0, -0.927183, 0.374608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -2102.36, -1711.07, 93.8962, 1.50098, 0, 0, 0.681998, 0.731354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -1252.58, -2998.96, 76.7928, 1.81514, 0, 0, 0.788011, 0.615662, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 3763, 1, -2287.26, -2533.56, 95.5277, 0.541051, 0, 0, 0.267238, 0.963631, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 103713, 1, -4076.08, -2193.39, 53.0868, 4.71239, 0, 0, -0.707107, 0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 103713, 1, -4189.49, -2169.27, 56.5968, 4.15388, 0, 0, -0.874619, 0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 103713, 1, -4203.78, -2233.75, 55.3837, 0.209439, 0, 0, 0.104528, 0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 103713, 1, -4207.56, -2290.02, 57.1232, 3.38594, 0, 0, -0.992546, 0.12187, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5090.03, -1587.31, -25.4564, 2.80998, 0, 0, 0.986286, 0.165048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5365.58, -3033.83, -37.2912, 2.9147, 0, 0, 0.993572, 0.113203, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5396.93, -1709.76, -38.7407, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4647.86, -1294.27, -40.6488, -0.558505, 0, 0, 0.275637, -0.961262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5638.07, -2211.79, -56.7211, -1.11701, 0, 0, 0.529919, -0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5720.23, -3154.99, -28.4684, 5.68977, 0, 0, -0.292372, 0.956305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4873.66, -1104.55, -55.7532, 2.40855, 0, 0, 0.93358, 0.358368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4784.1, -1731.71, -37.375, 3.03687, 0, 0, 0.99863, 0.052336, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4494.19, -1153.58, -56.3431, -2.72271, 0, 0, 0.978148, -0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4384.31, -929.796, -53.7144, -2.26893, 0, 0, 0.906308, -0.422618, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5576.58, -3465.93, -46.912, -0.715585, 0, 0, 0.350207, -0.936672, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5126, -2436, -52, 4.652, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5403, -2922, -50, 4.191, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4762, -1768, -33, 2.236, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4546, -1195, -48, 3.132, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5306, -1632, -38, 1.264, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4663, -1376, -32, 3.686, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4979, -1856.3, -38.4405, 2.63544, 0, 0, 0.968147, 0.250381, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5173, -2426, -50, -3, 0, 0, -0.961262, 0.275638, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5154, -2351, -43, 3.886, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5255, -1554, -37, 6.202, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4981, -1205, -37, 3.21, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4911, -1282, -29.3489, 1.323, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5599, -3014, -46.7644, 0.809, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5675, -3390, -34, 3.022, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4980.47, -1215.82, -45.5943, 6.16101, 0, 0, -0.0610485, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5630, -2083, -50, 4.229, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5656, -2561, -38, 0.713, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5113, -1689, -34, 5.623, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5682.08, -3400.04, -39.1371, 2.02458, 0, 0, 0.848047, 0.529921, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4845.82, -1691.96, -33.0507, 5.02655, 0, 0, -0.587785, 0.809017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5208.44, -1578.95, -38.6578, 5.51524, 0, 0, -0.374606, 0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4917, -1993, -22, 3.779, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5053, -2034, -51, 2.644, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4481, -932, -50, 3.672, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5073, -1271, -30, 4.463, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4624, -1722, -29, -1, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5079, -2066, -51, 3.397, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4629, -1709, -27, 5.148, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4937, -1566, -26, 5.799, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4977, -1838, -38, 5.275, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4571.58, -1204.01, -45.4149, 2.86233, 0, 0, 0.990268, 0.139175, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4651.72, -1381.44, -42.4803, 3.17653, 0, 0, -0.999847, 0.0174693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4454.48, -849.572, -51.5708, 2.86233, 0, 0, 0.990268, 0.139175, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5183.99, -2413.04, -41.9981, 3.7001, 0, 0, -0.961261, 0.27564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5038.51, -2016.74, -46.3377, 3.47321, 0, 0, -0.986285, 0.16505, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4761.79, -1062.75, -54.6378, 2.93214, 0, 0, 0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4678.42, -1576.62, -28.2, 5.77704, 0, 0, -0.25038, 0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4620.59, -1715.86, -22.4819, 5.75959, 0, 0, -0.258819, 0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4870.76, -1858.29, -49.852, 3.76991, 0, 0, -0.951056, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5553.85, -2075.2, -58.7253, 1.16937, 0, 0, 0.551936, 0.833886, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -5140.26, -1305.46, -45.5541, 1.85005, 0, 0, 0.798635, 0.601815, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4868.77, -1196.56, -46.683, 5.46288, 0, 0, -0.398748, 0.91706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -4913.51, -1285.78, -32.4988, 0.628317, 0, 0, 0.309016, 0.951057, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1413.5, -1023.59, 142.286, 0.715585, 0, 0, 0.350207, 0.936672, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1541.22, -991.637, 154.309, -1.8675, 0, 0, 0.803857, -0.594823, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1385.24, -1081.32, 142.678, 2.67035, 0, 0, 0.97237, 0.233445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1382.94, -1171.94, 163.176, -0.942478, 0, 0, 0.453991, -0.891006, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1486.76, -1196.38, 129.623, -1.46608, 0, 0, 0.669131, -0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2881.33, 355.126, 127.839, 0.10472, 0, 0, 0.052336, 0.99863, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3006.23, 277.98, 111.43, -2.28638, 0, 0, 0.909961, -0.414693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2658.6, 257.63, 96.5652, 2.26893, 0, 0, 0.906308, 0.422618, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2658.35, -1470.27, 80.4637, -2.53073, 0, 0, 0.953717, -0.300706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -500.561, -67.4469, 72.3764, -2.40855, 0, 0, 0.93358, -0.358368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2834.42, -829.513, 28.5204, 0.977384, 0, 0, 0.469472, 0.882948, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1250.65, 488.017, 39.6058, 0.994838, 0, 0, 0.477159, 0.878817, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2395.6, 455.132, 74.6857, 2.02458, 0, 0, 0.848048, 0.529919, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2126.26, 342.359, 137.839, 0.20944, 0, 0, 0.104528, 0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2378.36, 391.934, 66.0987, 2.74017, 0, 0, 0.979925, 0.199368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1499.49, 375.201, 67.0526, 0.087266, 0, 0, 0.043619, 0.999048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1716.2, -1252.96, 114.492, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1598.59, -1179.21, 143.803, 1.48353, 0, 0, 0.67559, 0.737277, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1558.8, -1308.79, 135.994, -0.20944, 0, 0, 0.104528, -0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1554.69, -1083.31, 105.707, -1.64061, 0, 0, 0.731354, -0.681998, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2160.77, 246.023, 85.1964, 2.02458, 0, 0, 0.848048, 0.529919, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -441.273, -634.282, 68.3678, 1.55334, 0, 0, 0.700909, 0.713251, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -503.832, -529.76, 72.2556, 0.575959, 0, 0, 0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1886.84, 329.933, 110.194, 1.90241, 0, 0, 0.814116, 0.580703, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2412.37, 503.889, 64.3131, 4.71239, 0, 0, 0.707107, -0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2299.02, 385.609, 58.3092, 2.67035, 0, 0, 0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2415.56, 352.934, 65.7179, -2.77507, 0, 0, 0.983255, -0.182235, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1745.35, -912.952, 88.8484, -0.575959, 0, 0, 0.284015, -0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1301.52, -1039.44, 61.9712, 3.4383, 0, 0, -0.989016, 0.147811, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1271.72, -1073.83, 57.5627, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1648.13, -1087.61, 127.155, 2.46091, 0, 0, 0.942641, 0.333807, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1810.58, -1214.9, 108.548, -0.488692, 0, 0, 0.241922, -0.970296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1348.19, -948.489, 25.8795, -2.61799, 0, 0, 0.965926, -0.258819, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1583, 276.251, 22.789, -2, 0, 0, -0.902585, 0.430511, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1595.03, -873.919, 36.7227, 2.72271, 0, 0, 0.978148, 0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2673, 343.263, 137.52, -1, 0, 0, -0.446198, 0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1665, 355.18, 109.988, 2.531, 0, 0, 0.953717, 0.300706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2808, -792, 18.084, 1.917, 0, 0, 0.818442, 0.574589, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1844.6, -1004.5, 84.088, 2.74017, 0, 0, 0.979925, 0.199368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -713.564, -762.928, 44.0935, 4.7822, 0, 0, -0.681998, 0.731354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2762.56, -633.859, 21.2799, 0.541051, 0, 0, 0.267238, 0.963631, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2806, -144, 22.177, 4.521, 0, 0, 0.771557, -0.636161, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -815.609, -868.47, 26.8822, -2.84489, 0, 0, 0.989016, -0.147809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2766.59, -204.171, 22.2618, 1.41372, 0, 0, 0.649447, 0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -615.527, -775.294, 56.9064, 1.41372, 0, 0, 0.649448, 0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2587, -1468, 107.091, 1.564, 0, 0, 0.70483, 0.709376, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2436.57, -1486.68, 38.845, 0.698131, 0, 0, 0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2290.48, 262.298, 84.6144, -0.872665, 0, 0, 0.422618, -0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2820.62, -438.43, 24.2419, -0.977384, 0, 0, 0.469472, -0.882948, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2089, -1113, 38.546, 5.599, 0, 0, 0.33526, -0.942126, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -516, 78.965, 61.133, 1.449, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2036.96, 299.074, 126.994, 4.31097, 0, 0, -0.833885, 0.551938, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1118.57, -484.427, -39.7389, 5.86431, 0, 0, -0.207912, 0.978148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1385, 326.601, 33.147, 2.688, 0, 0, 0.97437, 0.224951, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -961, -183, 22.172, 0.454, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2508.05, -1484.73, 67.8085, -1.76278, 0, 0, 0.771625, -0.636078, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -758.024, 151.076, 19.3412, -1.72788, 0, 0, 0.760406, -0.649448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -840, 175.613, -2, -1, 0, 0, -0.398749, 0.91706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2716.08, -1351.25, 54.7605, 0.10472, 0, 0, 0.052336, 0.99863, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2169.64, -1179.78, 39.1276, 3.50812, 0, 0, -0.983254, 0.182238, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1997, 422.483, 133.59, 1.257, 0, 0, 0.587785, 0.809017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -3003, 269.918, 104.591, 2.859, 0, 0, 0.990066, 0.140601, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2000.81, -1117.73, 83.8292, -0.453786, 0, 0, 0.224951, -0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2321.38, 376.604, 68.9614, 4.7822, 0, 0, -0.681998, 0.731354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2266.06, 324.555, 114.628, 2.44346, 0, 0, 0.939692, 0.342021, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2745.81, -1099.56, 34.6196, 3.59538, 0, 0, -0.97437, 0.224951, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -948.007, -1154.67, 93.4486, 4.03171, 0, 0, -0.902585, 0.430512, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2351.13, 366.964, 64.8289, 6.26573, 0, 0, -0.00872612, 0.999962, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2316.38, 421.633, 49.3617, 3.15906, 0, 0, -0.999962, 0.00873464, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -917.607, -1080.39, 77.0898, 1.16937, 0, 0, 0.551936, 0.833886, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -973.593, -1207.51, 104.673, 2.18166, 0, 0, 0.887011, 0.461749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2305.67, 463.42, 47.8758, 4.7473, 0, 0, -0.694658, 0.71934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2832.31, -719.423, 36.6097, 3.28124, 0, 0, -0.997563, 0.0697661, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2706.93, -1273.01, 38.5069, 4.53786, 0, 0, -0.766044, 0.642789, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2764.18, -1390.74, 63.256, 5.79449, 0, 0, -0.241921, 0.970296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1656.59, -857.758, 42.3553, 2.02458, 0, 0, 0.848047, 0.529921, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1902.6, 411.649, 134.422, 1.83259, 0, 0, 0.793353, 0.608762, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1723.8, -1171.47, 114.257, 5.84685, 0, 0, -0.216439, 0.976296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1734.81, -1271.49, 115.614, 0.994837, 0, 0, 0.477158, 0.878817, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1495.55, -951.971, 144.255, 2.74016, 0, 0, 0.979924, 0.19937, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1439.97, -977.927, 145.568, 3.82227, 0, 0, -0.942641, 0.333808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1158.87, -1189.76, 67.4503, 3.61284, 0, 0, -0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -2102.07, -1112.8, 33.3685, 3.94445, 0, 0, -0.920505, 0.390732, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1037.59, -1101.76, 45.8684, 4.24115, 0, 0, -0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -887.394, -970.533, 31.9752, 3.26377, 0, 0, -0.998135, 0.0610518, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -738.586, -825.43, 52.3338, 6.19592, 0, 0, -0.0436192, 0.999048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -686.463, -243.221, -4.68434, 4.55531, 0, 0, -0.760405, 0.649449, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1466.25, 320.241, 56.9725, 2.26892, 0, 0, 0.906307, 0.422619, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -864.57, -1157.19, 118.865, 4.90438, 0, 0, -0.636078, 0.771625, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -959.687, -189.309, 25.0984, 0.453785, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 924.899, -3979.97, 26.1475, 3.97936, 0, 0, -0.913545, 0.406738, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 24.325, -3989, 49.571, 3.016, 0, 0, 0.998045, 0.062506, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 919.19, -4031, -13, 4.714, 0, 0, 0.706456, -0.707757, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 987.733, -4279.1, 20.6213, -2.02458, 0, 0, 0.848048, -0.529919, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -666.219, -4858.36, 39.6105, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -369.761, -5142.03, 25.3783, -0.174533, 0, 0, 0.087156, -0.996195, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 733.338, -4104.1, -9.99905, 1.27409, 0, 0, 0.594822, 0.803857, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 833.363, -4095.24, -12.8436, -1.09956, 0, 0, 0.522499, -0.85264, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -145.402, -4681.56, 32.4146, 1.3439, 0, 0, 0.622515, 0.782608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 612.375, -4124.88, 25.6376, -1.18682, 0, 0, 0.559193, -0.829037, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1176.75, -4150.77, 21.4219, 2.96706, 0, 0, 0.996195, 0.087156, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -940.237, -4518.29, 36.6469, -0.349066, 0, 0, 0.173648, -0.984808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -945.979, -4608.4, 25.5947, 0.296706, 0, 0, 0.147809, 0.989016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1178.94, -4858.16, 24.753, 0.645772, 0, 0, 0.317305, 0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 829.248, -4702.52, 12.2974, -2.21657, 0, 0, 0.894934, -0.446198, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 877.604, -4602.6, 14.8535, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1057.03, -4889.49, 25.6804, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1166.65, -4762.67, 22.9298, -0.2618, 0, 0, 0.130526, -0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1067.8, -4590.99, 27.041, -2.74017, 0, 0, 0.979925, -0.199368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1056.58, -4808.49, 21.7389, 4.24115, 0, 0, -0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 518.5, -4760.18, 29.9781, 0.785397, 0, 0, 0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 647.757, -4755.22, 22.2529, -1.69297, 0, 0, 0.748956, -0.66262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 496.516, -4427.27, 47.1461, 1.48353, 0, 0, 0.67559, 0.737277, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 793.832, -4771.2, 38.5628, -2.33874, 0, 0, 0.920505, -0.390731, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -416.801, -4633.31, 50.701, -1.20428, 0, 0, 0.566406, -0.824126, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 58.4049, -4381.18, 74.8717, 0.383972, 0, 0, 0.190809, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 546.318, -4874.02, 37.2483, -1.69297, 0, 0, 0.748956, -0.66262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 359.069, -5093.58, 12.6732, -0.506145, 0, 0, 0.25038, -0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 110.429, -4593.55, 69.8251, 2.75762, 0, 0, 0.981627, 0.190809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 701.043, -4386.61, 27.4552, 2.53073, 0, 0, 0.953717, 0.300706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1181.44, -5106.4, 8.4284, -2.96706, 0, 0, 0.996195, -0.087156, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 121.802, -4321.91, 60.3641, 1.44862, 0, 0, 0.66262, 0.748956, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 31.9014, -4346.94, 76.9537, -1.32645, 0, 0, 0.615661, -0.788011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 932.9, -4749.66, 21.0103, -1.62316, 0, 0, 0.725374, -0.688354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1316.97, -4828.23, 24.0255, -0.575959, 0, 0, 0.284015, -0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -281.428, -4741.94, 39.3108, -2.70526, 0, 0, 0.976296, -0.21644, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -111.012, -4599, 54.0679, -0.680679, 0, 0, 0.333807, -0.942641, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -710.721, -4951.73, 29.1461, 0.349066, 0, 0, 0.173648, 0.984808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -642, -5100, 21.157, 0.379, 0, 0, 0.18853, 0.982067, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -300, -4851, 37.896, 3.004, 0, 0, 0.997643, 0.068625, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 719.195, -4127, 1.455, 2.869, 0, 0, 0.990746, 0.135728, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6.673, -4578, 54.316, 0.314, 0, 0, 0.156434, 0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 971.139, -4718, 29.5891, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 590.341, -4278, 15.473, 1.777, 0, 0, 0.776019, 0.63071, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 833.341, -4095, 25.37, -1, 0, 0, -0.522499, 0.85264, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -708, -4690, 35.181, 6.181, 0, 0, 0.051245, -0.998686, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 82.093, -4522, 70.473, 2.467, 0, 0, 0.943659, 0.330919, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 951.524, -4226, -6, 2.417, 0, 0, 0.935001, 0.354646, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 418.199, -4938.16, 37.4955, 5.39307, 0, 0, -0.43051, 0.902586, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 543.154, -4580, 49.236, -2, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 802.221, -4834.63, 37.9759, 3.49067, 0, 0, -0.984807, 0.173652, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1133.49, -4584.18, 28.9594, 2.79252, 0, 0, 0.984807, 0.173652, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 958.429, -4298, -6, 4.213, 0, 0, 0.859792, -0.510644, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -817, -4629, 44.872, 3.991, 0, 0, 0.911245, -0.411865, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -552, -4832, 36.636, 4.135, 0, 0, 0.879262, -0.476338, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -325, -4856, 40.775, 1.292, 0, 0, 0.6018, 0.798647, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 188.034, -4793, 14.51, 3.147, 0, 0, 0.999997, -0.002613, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 193.885, -4771, 14.324, 0.164, 0, 0, 0.081855, 0.996644, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 358.909, -4596, 60.359, 4.485, 0, 0, 0.782814, -0.622256, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 846.558, -4165, -9, -1, 0, 0, -0.477159, 0.878817, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 712.542, -4512.38, 19.8831, 4.57276, 0, 0, -0.754709, 0.656059, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -548.849, -4909.94, 45.4167, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1177.25, -4769, 18.966, 0, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -317.643, -4847.5, 40.5401, 5.8294, 0, 0, -0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 833.743, -4211, -1, 5.17, 0, 0, 0.528468, -0.848953, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 945.301, -4123, -12, 3.856, 0, 0, 0.936927, -0.349526, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 900.322, -4608, 18.3222, 2.264, 0, 0, 0.905198, 0.424991, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -149.711, -5155.12, 22.047, 0.279, 0, 0, 0.139173, 0.990268, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -943, -5110, -9, 4.681, 0, 0, 0.71815, -0.695888, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1000.78, -4682, 33.097, 0.107, 0, 0, 0.053395, 0.998573, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1042, -4566, 45.508, 1.606, 0, 0, 0.71934, 0.694658, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -605, -4948, 48.027, 4.451, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1241.87, -4940, 14.829, -3, 0, 0, -0.97237, 0.233445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 356.217, -4973, 27.051, 6.106, 0, 0, 0.088544, -0.996072, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 413.661, -4258.74, 32.9778, 4.88692, 0, 0, -0.642787, 0.766045, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -999, -4500, 28.246, 0.698, 0, 0, 0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 751.688, -4683.93, 30.1805, 1.72787, 0, 0, 0.760405, 0.649449, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1117.3, -4850, 18.723, 2.619, 0, 0, 0.966114, 0.258117, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -420, -4898, 59.501, 4.375, 0, 0, 0.81589, -0.578208, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1141.85, -4684, 17.7031, 5.94, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 66.278, -4533, 66.611, 2.471, 0, 0, 0.94432, 0.329028, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 871.858, -4230, -11, 3.379, 0, 0, 0.992971, -0.118355, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1119.91, -4494, 20.438, 1.565, 0, 0, 0.704938, 0.709268, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 922.413, -4280, -5, 3.482, 0, 0, 0.985543, -0.169428, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 646.033, -4629, 11.574, 4.39, 0, 0, 0.811522, -0.584322, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -876.697, -4736.07, 29.935, 1.93731, 0, 0, 0.824125, 0.566408, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -297, -4870, 35.73, 1.833, 0, 0, 0.793363, 0.608748, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 37.761, -4547, 83.901, 2.107, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -9, -4221, 94.547, 2.251, 0, 0, 0.902585, 0.430511, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1199.76, -4642, 19.916, 3.019, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1127.29, -4691.03, 19.824, 3.99681, 0, 0, -0.909961, 0.414694, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 193.139, -4449, 32.768, 1.497, 0, 0, 0.680605, 0.73265, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 463.742, -4203, 26.989, 5.652, 0, 0, 0.31042, -0.9506, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1266.67, -3991.47, 23.3678, 6.00393, 0, 0, -0.139173, 0.990268, 900, 255, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 798.026, -4046.3, -1.21654, 3.03684, 0, 0, 0.998629, 0.0523532, 900, 255, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1242.78, -4948.08, 16.0424, 3.61284, 0, 0, -0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1200.46, -4646.21, 23.5411, 3.01941, 0, 0, 0.998135, 0.0610518, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1430.12, -4664.15, 46.1002, 5.86431, 0, 0, -0.207912, 0.978148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 955.784, -4045.23, -11.4768, 3.927, 0, 0, -0.923879, 0.382686, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 894.402, -4080.42, 26.5511, 0.366518, 0, 0, 0.182235, 0.983255, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1213.05, -4591.98, 23.571, 2.05949, 0, 0, 0.857167, 0.515038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1071.53, -3948.96, 24.9357, 3.52557, 0, 0, -0.981627, 0.190812, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 535.034, -4936.4, 37.0527, 5.46288, 0, 0, -0.398748, 0.91706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 901.996, -4036.53, -11.3043, 0.645772, 0, 0, 0.317305, 0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 87.6491, -4941.56, 19.3904, 5.8294, 0, 0, -0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -341.065, -5100.17, 28.9194, 1.88495, 0, 0, 0.809016, 0.587786, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -834.234, -4838.99, 21.76, 3.73501, 0, 0, -0.956305, 0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -996.959, -4816.75, 13.0821, 0.366518, 0, 0, 0.182235, 0.983255, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 72.8817, -4528.48, 61.0341, 3.28124, 0, 0, -0.997563, 0.0697661, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 134.77, -4134.01, 54.8472, 2.23402, 0, 0, 0.898793, 0.438373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 35.3273, -4101.95, 63.2919, 4.01426, 0, 0, -0.906307, 0.422619, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1460.43, -4691.54, -3.10957, 2.25147, 0, 0, 0.902585, 0.430512, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1518.04, -4753.08, 14.9666, 2.82743, 0, 0, 0.987688, 0.156436, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1465.41, -4865.19, 13.0559, 1.76278, 0, 0, 0.771625, 0.636078, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1489.51, -4668.87, 3.7799, 2.9845, 0, 0, 0.996917, 0.0784664, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1467.48, -4778.02, 8.51495, 0.244346, 0, 0, 0.121869, 0.992546, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1509.96, -4862.17, 8.72121, 1.85005, 0, 0, 0.798635, 0.601815, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -717.853, -4696.23, 37.8685, 2.42601, 0, 0, 0.936672, 0.350207, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 593.889, -4185.87, 19.861, 0.383971, 0, 0, 0.190808, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1417.42, -4711.25, -0.304713, 0.541051, 0, 0, 0.267238, 0.963631, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1519.99, -4687.98, 9.02939, 0.925024, 0, 0, 0.446198, 0.894934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1488.58, -4816.56, 9.27292, 1.0821, 0, 0, 0.515037, 0.857168, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1517.21, -4965.98, 11.4182, 5.44543, 0, 0, -0.406736, 0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1459.55, -4745.43, -0.996877, 4.27606, 0, 0, -0.843391, 0.5373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1513.36, -4712.86, 12.2831, 5.86431, 0, 0, -0.207912, 0.978148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1041.65, -4731.42, 17.7684, 5.2709, 0, 0, -0.484809, 0.87462, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1464.22, -4891.54, 14.0545, 0.296705, 0, 0, 0.147809, 0.989016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1458.08, -4807.5, 11.8591, 4.24115, 0, 0, -0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1562.55, -4769.49, 15.478, 6.00393, 0, 0, -0.139173, 0.990268, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1114.85, -4224.99, 28.0452, 0.541051, 0, 0, 0.267238, 0.963631, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -398.531, -4747.13, 38.928, 5.09636, 0, 0, -0.559193, 0.829038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1509.23, -4802.94, 8.99069, 5.75959, 0, 0, -0.258819, 0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1416.13, -4773.18, 4.92753, 4.10152, 0, 0, -0.887011, 0.461749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2421.4, 386.887, 109.635, 2.61799, 0, 0, 0.965926, 0.258819, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2605.49, 83.8325, 99.1653, -1.0821, 0, 0, 0.515038, -0.857167, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2728.67, -879.718, 160.605, -2.35619, 0, 0, 0.92388, -0.382683, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2480.6, -324.883, 115.091, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2585.54, -1074.56, 131.414, 2.35619, 0, 0, 0.92388, 0.382683, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2451.43, -997.505, 146.244, 1.01229, 0, 0, 0.48481, 0.87462, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2404.24, -705.668, 154.528, 0.663225, 0, 0, 0.325568, 0.945519, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2411.54, -856.929, 147.678, 6.21337, 0, 0, -0.0348988, 0.999391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2368.37, -502.473, 116.287, 5.06146, 0, 0, -0.573576, 0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2727.19, -805.128, 151.909, 2.15043, 0, 0, 0.879692, 0.475543, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3120.77, -354.15, 137.784, -0.645772, 0, 0, 0.317305, -0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1491.57, -2026.44, 117.112, -1.41372, 0, 0, 0.649448, -0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2948.33, -697.115, 189.46, -0.069813, 0, 0, 0.034899, -0.999391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3144.35, -610.987, 170.903, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3944.9, 241.402, 23.5239, 2.87979, 0, 0, 0.991445, 0.130526, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1795.4, -1623.78, 95.8916, 2.09439, 0, 0, 0.866025, 0.5, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3753.26, 618.903, 14.7077, -2.42601, 0, 0, 0.936672, -0.350207, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1385.21, -3485.35, 95.085, 1.27409, 0, 0, 0.594822, 0.803857, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2527.21, 122.553, 98.3699, 2.20776, 0, 0, 0.89296, 0.450136, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2420.83, -137.195, 97.6321, 5.83002, 0, 0, 0.224651, -0.974439, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2372.5, -232.726, 101.306, 2.72848, 0, 0, 0.978743, 0.205091, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2761.35, -3058.57, 164.029, 5.79449, 0, 0, -0.241921, 0.970296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2471.51, -334.146, 103.695, 0.581204, 0, 0, 0.286529, 0.958072, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2381.85, -494.339, 111.571, 5.06818, 0, 0, 0.570817, -0.821077, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 4031.87, 116.379, 10.8511, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2622.01, -966.571, 133.405, 1.54882, 0, 0, 0.699293, 0.714835, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2840.93, -2999.24, 171.869, 0.625194, 0, 0, 0.307531, 0.951538, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2743.54, -2850.19, 152.638, 1.05638, 0, 0, 0.50397, 0.863721, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1900.28, -2425.91, 88.1028, 1.5661, 0, 0, 0.705445, 0.708765, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1439.69, -1940.5, 106.255, 1.71925, 0, 0, 0.757597, 0.652722, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2586.05, -1105.28, 131.529, 5.20799, 0, 0, 0.512076, -0.85894, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2730.42, -1262.76, 181.695, -1.79769, 0, 0, 0.782608, -0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2546.06, -1335.72, 161.544, 1.55334, 0, 0, 0.700909, 0.713251, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2885.72, -790.483, 167.812, 5.21741, 0, 0, 0.508022, -0.861344, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2973.86, -691.858, 173.951, 1.55039, 0, 0, 0.699854, 0.714286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3116.76, -326.704, 131.087, 2.99787, 0, 0, 0.997419, 0.071797, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3196.77, -124.978, 107.313, 0.822322, 0, 0, 0.399674, 0.916657, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3024.65, -233.572, 132.981, 4.8145, 0, 0, 0.670099, -0.742271, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3166.67, 142.232, 10.8116, 3.55393, 0, 0, 0.978823, -0.204711, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3413.55, -12.685, 8.58239, 4.70611, 0, 0, 0.709324, -0.704883, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3680.38, -171.198, 8.77558, 4.91188, 0, 0, 0.633177, -0.774007, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3836.78, -228.029, 9.44121, 4.12256, 0, 0, 0.882106, -0.471051, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3982.12, 209.46, 10.8324, 0.869379, 0, 0, 0.421129, 0.907001, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2991.59, 467.605, 17.3735, 2.77397, 0, 0, 0.983154, 0.182779, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3414.91, 880.762, 21.9763, 4.46493, 0, 0, 0.788967, -0.614435, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3767.14, 637.685, 9.60146, 5.38385, 0, 0, 0.434666, -0.900592, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2985.02, 467.96, 21.9406, 1.13446, 0, 0, 0.537299, 0.843392, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2264.69, -2416.11, 120.281, 3.59538, 0, 0, -0.97437, 0.224951, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2970.24, -2967.93, 213.904, 4.20625, 0, 0, -0.861628, 0.507539, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3199.7, -121.156, 110.164, 4.62512, 0, 0, -0.737277, 0.675591, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2372.44, -259.678, 103.681, 3.75246, 0, 0, -0.953716, 0.300708, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3457.16, -345.805, 136.905, 5.13127, 0, 0, -0.544639, 0.838671, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 3209.08, 104.121, 25.2081, 2.19912, 0, 0, 0.891007, 0.453991, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -278.599, 967.17, 94.7685, -2.80998, 0, 0, 0.986286, -0.165048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 212.447, 1588.1, 174.453, -2.53073, 0, 0, 0.953717, -0.300706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 170.665, 1985.24, 179.044, -0.05236, 0, 0, 0.026177, -0.999657, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -201.552, 1178.94, 97.313, -1.50098, 0, 0, 0.681998, -0.731354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -114.647, 1298.89, 93.6003, 0.122173, 0, 0, 0.0610485, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 26.8517, 1824.61, 127.604, 5.07891, 0, 0, -0.566406, 0.824126, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -1164.81, 609.84, 83.9524, 2.9147, 0, 0, 0.993572, 0.113203, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -191.015, 1279.91, 96.0695, -0.383972, 0, 0, 0.190809, -0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -322.086, 1097.67, 94.8923, 0.558504, 0, 0, 0.275637, 0.961262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -546.85, 934.192, 96.0841, 3.76991, 0, 0, -0.951056, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -151.837, 1466.23, 102.198, 5.55015, 0, 0, -0.358368, 0.933581, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 56.7158, 1562.73, 123.846, 0.0174525, 0, 0, 0.00872612, 0.999962, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1578.98, 70.4523, -2.32776, -0.314159, 0, 0, 0.156434, -0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1462.14, -88.5425, 36.7921, -0.383972, 0, 0, 0.190809, -0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1237.52, 148.234, 12.0444, -2.37365, 0, 0, 0.927184, -0.374607, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1329.93, 911.696, 189.129, 2.84489, 0, 0, 0.989016, 0.147809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1182.49, -112.109, 6.31108, -2.37365, 0, 0, 0.927184, -0.374607, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1364.09, 615.343, 215.96, 0.069813, 0, 0, 0.034899, 0.999391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1199.96, -500.415, 10.4833, -1.88496, 0, 0, 0.809017, -0.587785, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2282.73, 1445.62, 281.699, -2.94961, 0, 0, 0.995396, -0.095846, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 198.019, -889.892, 20.2088, -3.12414, 0, 0, 0.999962, -0.008727, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1357.7, 1544.48, 156.776, 4.15388, 0, 0, -0.874619, 0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1570.61, 512.606, 180.156, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 175.005, -458.132, 34.7948, 1.71042, 0, 0, 0.75471, 0.656059, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 508.831, 275.832, 69.153, 5.665, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1000.95, 1509.58, 19.5043, 5.281, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 36.035, -197, 27.172, 0.288, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2059.95, 1124.83, 270.65, 1.197, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1225.42, -103, -2, 5.961, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 6.07, -34, 49.14, 4.863, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 829.24, 1452.84, -5, 0, 0, 0, -0.113203, 0.993572, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1752.61, 689.183, 161.426, 3.006, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1601.27, 102.149, 21.912, 0.677, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1153.52, -268, -9, 4.029, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1245.91, 315.511, 27.965, 3.092, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 924.105, 1489.77, 14.2401, 0.575957, 0, 0, 0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2287.31, 1126.64, 316.395, 2.828, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1268.36, 1499.91, 92.6485, 1.276, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1267.43, 759.141, 193.445, 0.767, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1233.14, -275, 2.302, 4.548, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1447.74, 214.272, 16.954, 0.903, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2354.04, 1204.05, 338.593, 2.534, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1299.81, 1362.38, 159.663, -1.48353, 0, 0, 0.67559, -0.737277, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 243.391, 359.352, 69.831, 3.28124, 0, 0, -0.997563, 0.0697661, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -225, -547, 41.729, 3.401, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 67.037, -217, 20.389, 2.451, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1465.76, -104, 33.935, 0.157, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 696.49, 313.62, 72.7516, 3.726, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 48.07, -281, 22.871, 4.64, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1563.38, -309, 64.214, 6.082, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1226.43, 1375.26, 100.911, 4.113, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 919.639, 47.729, 75.064, 2.97, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 770.491, 336.971, 75.4982, 0.322, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -85, -354, 29.077, 1.785, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2092.82, 1024.63, 237.424, 1.41372, 0, 0, 0.649448, 0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1572.45, 1415.66, 194.04, 0.401425, 0, 0, 0.199367, 0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1555.19, 1329.2, 170.396, 0.516, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1780.35, 999.612, 158.326, 0.816, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1864.1, 849.441, 166.562, 2.72271, 0, 0, 0.978148, 0.207912, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 979.869, -500.204, 18.4638, 3.54302, 0, 0, -0.979924, 0.19937, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 808.304, 157.392, 53.6267, 3.76991, 0, 0, -0.951056, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -197.107, -537.874, 20.8429, 0.733038, 0, 0, 0.358368, 0.933581, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 577.224, 427.996, 64.4729, 4.50295, 0, 0, -0.777145, 0.629321, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2445.04, 1097.22, 338.232, 0.698131, 0, 0, 0.34202, 0.939693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1794.04, 1073.96, 175.753, 2.63544, 0, 0, 0.968147, 0.250381, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 985.175, 430.589, 82.1604, 1.29154, 0, 0, 0.601814, 0.798636, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1493.05, -277.715, 26.7762, 0.453785, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -8.1365, 15.9418, 53.3904, 3.35105, 0, 0, -0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, -102.327, -575.467, -22.7587, 0.575957, 0, 0, 0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 29.4685, -512.114, -15.0884, 1.25664, 0, 0, 0.587785, 0.809017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 1872.95, 740.133, 162.041, 2.04204, 0, 0, 0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 951.668, 335.818, 33.3591, 5.89921, 0, 0, -0.190808, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 2067.76, 1119.08, 268.521, 4.31097, 0, 0, -0.833885, 0.551938, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 764.682, 1364.49, -2.69456, 5.77704, 0, 0, -0.25038, 0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 1, 67.2039, -513.643, 39.1255, 5.49779, 0, 0, -0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8804, -1930, 125.48, 3.048, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9767.09, -2316.49, 70.4724, 2.26893, 0, 0, 0.906308, 0.422618, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9007, -3213, 108.538, 0.591, 0, 0, 0.29099, 0.956726, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9052.53, -2680.67, 132.473, 1.8675, 0, 0, 0.803857, 0.594823, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9186, -2912, 112.139, 2.217, 0, 0, 0.894934, 0.446198, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9116.97, -2785.5, 104.123, -1.11701, 0, 0, 0.529919, -0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8835, -1927, 125.082, 2.03, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9137.28, -3187.06, 107.541, 4.53786, 0, 0, -0.766044, 0.642789, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9256, -3097, 100.701, 1.972, 0, 0, 0.833886, 0.551937, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9637.25, -2812.56, 60.8158, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9414.11, -1916.16, 97.5622, -2.54818, 0, 0, 0.956305, -0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9736, -2441, 70.37, -2, 0, 0, -0.782608, 0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9623.88, -2158.55, 69.8939, 3.35105, 0, 0, -0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9370, -1893, 82.194, 2.313, 0, 0, 0.915428, 0.402483, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9440.52, -2880.75, 76.799, -0.628319, 0, 0, 0.309017, -0.951056, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8961, -2664, 152.968, 4.575, 0, 0, 0.754015, -0.656857, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9152, -2106, 104.396, 6.094, 0, 0, 0.094207, -0.995553, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9141, -3080, 123.024, 3.054, 0, 0, 0.999048, 0.04362, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9741, -2102, 62.484, 2.845, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9078, -3300, 102.242, 4.673, 0, 0, 0.720786, -0.693158, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9484.96, -1976.89, 94.0196, 0.890117, 0, 0, 0.43051, 0.902586, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9045.17, -2379.82, 135.04, 0.117, 0, 0, 0.058555, 0.998284, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9200.89, -2441.78, 60.0945, 0.538, 0, 0, 0.266006, 0.963971, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9663, -2031, 59.886, 4.497, 0, 0, 0.778983, -0.627045, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8958, -1990, 137.616, 5.848, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9233, -3283, 104.756, -2, 0, 0, -0.793353, 0.608761, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9119, -2165, 124.142, 0.089, 0, 0, 0.044716, 0.999, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9430, -1973, 81.649, 5.14, 0, 0, 0.541043, -0.840995, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9285.47, -3419.89, 105.121, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8789, -2046, 127.599, 2.199, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8840, -2071, 128.304, 3.142, 0, 0, 0, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8839.28, -2528.36, 142.069, 2.98451, 0, 0, 0.996917, 0.078459, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9591.81, -2403.29, 64.4106, 3.59538, 0, 0, -0.97437, 0.224951, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9343, -2926, 109.874, 1.588, 0, 0, 0.713251, 0.700909, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8981.17, -2296.79, 136.786, -2.11185, 0, 0, 0.870356, -0.492423, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9675.05, -3188.28, 62.2984, 2.3911, 0, 0, 0.930418, 0.366501, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9697.58, -1754.86, 62.2564, 3.61284, 0, 0, -0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9804.08, -2248.52, 73.8627, -3.03687, 0, 0, 0.99863, -0.052336, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9004.29, -2477.13, 147.16, -2.75762, 0, 0, 0.981627, -0.190809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9737.6, -2925.08, 67.047, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9440.46, -3395.88, 89.0594, -1.67552, 0, 0, 0.743145, -0.669131, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8688.33, -2170.14, 156.791, 2.21657, 0, 0, 0.894934, 0.446198, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9565.56, -3240.22, 50.891, 2.3911, 0, 0, 0.930418, 0.366501, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8750.37, -2258.32, 155.634, 0.453786, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8810.75, -2136.72, 150.143, 0.453786, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9654.49, -2022.86, 57.1197, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9562.95, -1836.39, 74.0232, -1.0821, 0, 0, 0.515038, -0.857167, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9372.14, -1915.85, 67.3032, -0.541052, 0, 0, 0.267238, -0.96363, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9373.97, -2729.39, 57.8007, 2.56563, 0, 0, 0.95882, 0.284015, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8705.2, -2598.11, 140.532, 2.77507, 0, 0, 0.983255, 0.182236, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9412.47, -2484.25, 30.4685, 3.7001, 0, 0, -0.961261, 0.27564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9519.65, -2103.17, 94.1483, 3.7001, 0, 0, -0.961261, 0.27564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9590.31, -2733.05, 57.7435, 0.209439, 0, 0, 0.104528, 0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9638.75, -3028.55, 60.2563, 2.30383, 0, 0, 0.913545, 0.406738, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9821.69, -3219.74, 64.2859, 2.32129, 0, 0, 0.91706, 0.39875, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9086.08, -3267.75, 102.72, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9093.26, -3355.02, 102.589, 4.71239, 0, 0, -0.707107, 0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8718.69, -2251.12, 157.443, 5.09636, 0, 0, -0.559193, 0.829038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8702.09, -2153.17, 156.936, 1.62316, 0, 0, 0.725374, 0.688355, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 2055, 0, -8891.01, -1986.15, 136.204, 0.401425, 0, 0, 0.199367, 0.979925, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 2055, 0, -8776.79, -2043.35, 128.049, 3.50812, 0, 0, -0.983254, 0.182238, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2400.78, 569.456, 42.212, 2.74, 0, 0, 0.979925, 0.199368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1730.53, 993.143, 56.7983, 1.46608, 0, 0, 0.66913, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2219.76, 607.936, 29.159, -2, 0, 0, -0.766044, 0.642788, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2781.51, -833.162, 154.953, 2.23402, 0, 0, 0.898793, 0.438373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2478.94, 56.65, 12.432, 1.292, 0, 0, 0.601815, 0.798635, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2133.66, -315.844, 57.8635, 2.21656, 0, 0, 0.894934, 0.446199, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2225.5, -332.543, 73.1434, 6.24828, 0, 0, -0.0174522, 0.999848, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2245.48, 1332.42, 38.1899, 3.35105, 0, 0, -0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2805.09, 741.591, 138.367, -1, 0, 0, -0.300706, 0.953717, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2208.11, 553.019, 34.0019, 0.523598, 0, 0, 0.258819, 0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2659.03, 802.917, 114.841, 0.314, 0, 0, 0.156434, 0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2716.62, -540.282, 106.932, 0.139625, 0, 0, 0.0697555, 0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2613.72, -503.312, 91.9222, 3.05433, 0, 0, 0.999048, 0.0436193, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2485.92, 581.391, 34.6939, 4.20625, 0, 0, -0.861628, 0.507539, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2656.42, 1194.57, 74.715, -2, 0, 0, -0.793353, 0.608761, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1790.83, 884.56, 33.848, 2.304, 0, 0, 0.913545, 0.406737, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2115.94, -662.906, 79.4342, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2589.23, -673.345, 78.9289, 2.42601, 0, 0, 0.936672, 0.350207, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2686.04, -717.874, 129.438, 1.64061, 0, 0, 0.731354, 0.681998, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2228.82, 1288.9, 49.7022, 1.02974, 0, 0, 0.492424, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1841.24, 1133.81, 33.6349, -3.12414, 0, 0, 0.999962, -0.008727, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2524.39, -584.124, 83.0835, -0.890118, 0, 0, 0.430511, -0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2134.76, -457.173, 76.0097, -0.645772, 0, 0, 0.317305, -0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2718.82, 1259.24, 46.958, 2.21657, 0, 0, 0.894934, 0.446198, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2114.57, 1213.39, 48.3358, 2.84489, 0, 0, 0.989016, 0.147809, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2861.2, -498.192, 102.199, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2745.86, -385.011, 85.5166, 0.261799, 0, 0, 0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2968.22, -349.844, 27.8641, 0.715585, 0, 0, 0.350207, 0.936672, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2850.04, -461.927, 82.1176, -1.8326, 0, 0, 0.793353, -0.608761, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2453.47, 835.25, 85.8056, -2.58309, 0, 0, 0.961262, -0.275637, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2736.66, 1411.2, 2.68338, -0.750491, 0, 0, 0.366501, -0.930418, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2709.98, 1336.98, 42.0939, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2018.84, -628.099, 66.4502, 0.453786, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1732.7, 897.179, 60.023, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2551.51, -120.405, 25.5001, 2.67035, 0, 0, 0.97237, 0.233445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2245.63, 1519.28, 53.4071, -2.58309, 0, 0, 0.961262, -0.275637, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2700.2, -846.233, 84.8905, 0.279253, 0, 0, 0.139173, 0.990268, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2721.52, -931.349, 81.0418, -1.06465, 0, 0, 0.507538, -0.861629, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2252.92, -636.266, 81.7184, 0.959931, 0, 0, 0.461749, 0.887011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2985.67, -721.287, 161.564, 1.98968, 0, 0, 0.838671, 0.544639, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2373.29, 804.254, 47.0329, -1.72788, 0, 0, 0.760406, -0.649448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2575.05, 641.717, 31.4842, 0.977384, 0, 0, 0.469472, 0.882948, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2617.09, 564.733, 22.2648, 0.331613, 0, 0, 0.165048, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2927.08, 447.921, 37.3951, -2.60054, 0, 0, 0.96363, -0.267238, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2956.82, 412.312, 39.4448, -2.82743, 0, 0, 0.987688, -0.156434, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2440.95, 266.685, 29.7319, 3.73501, 0, 0, -0.956305, 0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2620.32, 1454.64, -8.52259, 4.71239, 0, 0, -0.707107, 0.707107, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1572.09, -687.518, 57.2998, 1.37881, 0, 0, 0.636078, 0.771625, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1727.84, 808.579, 68.0469, 4.72984, 0, 0, -0.700909, 0.713251, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1619.52, -560.97, 55.0243, 0.453785, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1633.95, -687.568, 47.3942, 3.26377, 0, 0, -0.998135, 0.0610518, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1779.62, -287.058, 42.0114, 3.01941, 0, 0, 0.998135, 0.0610518, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1781.53, -748.291, 63.1567, 2.00713, 0, 0, 0.843391, 0.5373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 3019.68, 162.956, -7.08108, 4.43314, 0, 0, -0.798635, 0.601815, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1702.92, 768.162, 69.0627, 2.3911, 0, 0, 0.930417, 0.366502, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2756.54, 1079.45, 110.497, 3.52557, 0, 0, -0.981627, 0.190812, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2101.47, -319.793, 52.2458, 0.0523589, 0, 0, 0.0261765, 0.999657, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2196.57, -500.417, 86.2787, 5.79449, 0, 0, -0.241921, 0.970296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1889.28, -681.401, 50.7889, 5.68977, 0, 0, -0.292372, 0.956305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2510.7, -376.824, 85.8907, 5.70723, 0, 0, -0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2764.11, -473.161, 100.833, 5.63741, 0, 0, -0.317305, 0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1845.71, 778.281, 31.2142, 2.32129, 0, 0, 0.91706, 0.39875, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2673.29, -772.678, 85.4531, 5.86431, 0, 0, -0.207912, 0.978148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2892.56, -712.691, 143.279, 1.97222, 0, 0, 0.833885, 0.551938, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2473.59, -476.253, 79.8804, 2.56563, 0, 0, 0.958819, 0.284016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1814.78, -141.659, 49.7266, 3.05433, 0, 0, 0.999048, 0.0436193, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2221.54, 616.308, 27.6591, 4.53786, 0, 0, -0.766044, 0.642789, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2489.71, 1485.08, 8.52363, 5.49779, 0, 0, -0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1904.54, -803.336, 71.4108, 1.02974, 0, 0, 0.492423, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2084.35, -290.538, 48.3615, 2.05949, 0, 0, 0.857167, 0.515038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2454.74, -1084.52, 103.86, 4.69494, 0, 0, -0.71325, 0.70091, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2489.14, -1011.01, 80.4089, 3.29869, 0, 0, -0.996917, 0.0784664, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2983.78, -474.167, 83.9406, 0.523598, 0, 0, 0.258819, 0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2448.6, 672.04, 36.6997, 1.22173, 0, 0, 0.573576, 0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2698.05, 576.933, 18.7021, 0.733038, 0, 0, 0.358368, 0.933581, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2783.56, 504.424, 27.0137, 2.63544, 0, 0, 0.968147, 0.250381, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2585.97, -307.915, 72.41, 1.46608, 0, 0, 0.66913, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2896.65, -399.971, 84.3644, 5.5676, 0, 0, -0.350207, 0.936672, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 3059.6, 460.269, 10.4857, 5.51524, 0, 0, -0.374606, 0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2416.76, 1470.06, 39.4758, 0.890117, 0, 0, 0.43051, 0.902586, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2421.14, 1762.29, 36.7975, 3.61284, 0, 0, -0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2646.08, 1350.02, 9.43725, 4.60767, 0, 0, -0.743144, 0.669132, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 3023.28, 790.686, 87.5232, 0.436332, 0, 0, 0.216439, 0.976296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2896.04, 541.865, 93.0305, 4.01426, 0, 0, -0.906307, 0.422619, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2505.24, 745.556, 120.932, 3.14159, 0, 0, -1, 0, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2270.86, 649.895, 25.2218, 1.97222, 0, 0, 0.833885, 0.551938, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2475.6, 482.201, 44.1396, 5.68977, 0, 0, -0.292372, 0.956305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1744.57, 1164.62, 75.0946, 4.4855, 0, 0, -0.782608, 0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1908.19, 1077.63, 31.1444, 0.209439, 0, 0, 0.104528, 0.994522, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2371.46, -634.19, 68.5649, 5.65487, 0, 0, -0.309016, 0.951057, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2889.69, 1104.54, 115.668, 4.17134, 0, 0, -0.870356, 0.492424, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2410.51, 846.121, 62.4501, 1.0821, 0, 0, 0.515037, 0.857168, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 1897.42, -749.906, 64.4441, 1.39626, 0, 0, 0.642787, 0.766045, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2109.25, -590.871, 65.8331, 3.01941, 0, 0, 0.998135, 0.0610518, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, 2202.98, -65.3898, 30.8705, 3.52557, 0, 0, -0.981627, 0.190812, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9404.72, -274.812, 62.4757, 1.93731, 0, 0, 0.824126, 0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9115.54, 240.614, 106.374, -0.349066, 0, 0, 0.173648, -0.984808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9074.68, 25.9893, 97.5041, 0.645772, 0, 0, 0.317305, 0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9153.33, 110.741, 76.7171, -0.506145, 0, 0, 0.25038, -0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9401.4, 422.391, 37.7848, 0.122173, 0, 0, 0.061049, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9333.82, 494.622, 65.8085, -2.98451, 0, 0, 0.996917, -0.078459, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9576.02, 430.352, 40.8878, -1.95477, 0, 0, 0.829038, -0.559193, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9617.02, 344.501, 48.3057, 3.05433, 0, 0, 0.999048, 0.043619, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9481.64, 649.059, 66.8165, -0.314159, 0, 0, 0.156434, -0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9978.82, 521.455, 32.8909, 0.453786, 0, 0, 0.224951, 0.97437, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9751.66, 184.723, 55.7324, 3.10669, 0, 0, 0.999848, 0.017452, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9853.56, 109.835, 34.562, -0.139626, 0, 0, 0.069756, -0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9862.2, 155.543, 7.98152, -0.593412, 0, 0, 0.292372, -0.956305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9757.65, 78.5534, 14.1574, -0.837758, 0, 0, 0.406737, -0.913545, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9654.41, 17.4469, 49.5562, -0.628319, 0, 0, 0.309017, -0.951056, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9808.41, -243.801, 40.8597, -2.1293, 0, 0, 0.87462, -0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9707.75, -267.908, 52.1275, 2.44346, 0, 0, 0.939693, 0.34202, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9820.85, -403.445, 46.8509, -1.25664, 0, 0, 0.587785, -0.809017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9729.23, -537.534, 45.7004, -2.87979, 0, 0, 0.991445, -0.130526, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9443.36, -981.152, 56.8838, 1.0472, 0, 0, 0.5, 0.866025, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9191.63, -598.046, 60.6417, -3.00197, 0, 0, 0.997564, -0.069756, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9072.23, -580.832, 63.2114, -1.90241, 0, 0, 0.814116, -0.580703, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9035.68, -577.905, 57.1753, -1.23918, 0, 0, 0.580703, -0.814116, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9409.22, -627.169, 70.6956, 0.890118, 0, 0, 0.430511, 0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8954.08, -1005.58, 53.8543, -2.07694, 0, 0, 0.861629, -0.507538, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9217.26, -1052.66, 77.0704, 2.49582, 0, 0, 0.948324, 0.317305, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9803.75, -1108, 31.2782, -3.01942, 0, 0, 0.998135, -0.061048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9085.04, -1192.78, 56.2631, 1.88496, 0, 0, 0.809017, 0.587785, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9136.85, -1119.04, 82.9461, -1.48353, 0, 0, 0.67559, -0.737277, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8799.84, -1210.71, 101.44, 1.69297, 0, 0, 0.748956, 0.66262, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9923.1, -1080.64, 22.2222, -2.42601, 0, 0, 0.936672, -0.350207, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9822.3, -1427.23, 43.4424, -2.68781, 0, 0, 0.97437, -0.224951, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9770.87, -1437.77, 54.8067, 1.44862, 0, 0, 0.66262, 0.748956, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9332.39, -1480.93, 84.6816, -2.3911, 0, 0, 0.930418, -0.366501, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9374.96, -1566.89, 88.3435, -0.733038, 0, 0, 0.358368, -0.93358, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9090.28, -550.917, 61.9709, 2.02458, 0, 0, 0.848048, 0.529919, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9501.33, -246.828, 46.4828, 0.767945, 0, 0, 0.374607, 0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9572.22, -789.044, 46.5476, 2.93215, 0, 0, 0.994522, 0.104528, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9090.22, -594.915, 59.1844, -0.191986, 0, 0, 0.095846, -0.995396, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9154.89, -982.684, 79.2237, -1.62316, 0, 0, 0.725374, -0.688354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9042.1, -896.944, 57.1838, -0.139626, 0, 0, 0.069756, -0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8926.02, -1270.43, 98.4984, -0.575959, 0, 0, 0.284015, -0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9892.89, 695.553, 33.7647, -2.07694, 0, 0, 0.861629, -0.507538, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9138.78, -591.058, 58.8044, 1.90241, 0, 0, 0.814116, 0.580703, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9031.15, -568.044, 55.8196, 2.54818, 0, 0, 0.956305, 0.292372, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9723.71, 118.831, 26.5206, -1.50098, 0, 0, 0.681998, -0.731354, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9802.81, 88.5173, 4.70444, -2.74017, 0, 0, 0.979925, -0.199368, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9620.11, -46.3336, 47.3641, 2.04204, 0, 0, 0.85264, 0.522499, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9802.83, 128.656, 7.04257, 2.93215, 0, 0, 0.994522, 0.104528, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9828.76, 184.747, 13.2738, 2.30383, 0, 0, 0.913545, 0.406737, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9628.99, 102.231, 48.9472, 2.32129, 0, 0, 0.91706, 0.398749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9775.94, 145.396, 25.5595, 1.46608, 0, 0, 0.669131, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9816, 148.449, 5.88154, -0.610865, 0, 0, 0.300706, -0.953717, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9183.71, -614.584, 61.8164, -0.610865, 0, 0, 0.300706, -0.953717, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9100.51, -586.156, 58.8091, 0.488692, 0, 0, 0.241922, 0.970296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9030.71, -617.63, 56.8576, -0.820305, 0, 0, 0.398749, -0.91706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9166.6, -572.001, 58.69, 2.33874, 0, 0, 0.920505, 0.390731, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9254.93, -452.93, 82.6758, 2.47837, 0, 0, 0.945519, 0.325568, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9787.18, 123.07, 25.8958, 0.069813, 0, 0, 0.034899, 0.999391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9840.77, 121.837, 6.28642, 2.82743, 0, 0, 0.987688, 0.156434, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9799.79, 107.987, 6.13216, 2.53073, 0, 0, 0.953717, 0.300706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9605.88, 517.683, 42.3721, -1.6057, 0, 0, 0.71934, -0.694658, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9585.05, 211.663, 50.7004, 0.261799, 0, 0, 0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9652.22, -212.85, 50.9247, -0.872665, 0, 0, 0.422618, -0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9685.88, -1195.69, 50.4208, 1.02974, 0, 0, 0.492423, 0.870356, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9248.86, 440.654, 88.0219, 3.00197, 0, 0, 0.997564, 0.069757, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9448.71, -551.097, 66.5675, -2.68781, 0, 0, 0.97437, -0.224951, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9763.46, -528.461, 39.4358, 0.907571, 0, 0, 0.438371, 0.898794, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9736.79, 87.749, 12.8907, 3.64774, 0, 0, -0.968147, 0.250381, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9723.19, 93.0938, 48.3514, 2.51327, 0, 0, 0.951057, 0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9758.44, 140.714, 21.5286, 2.63544, 0, 0, 0.968147, 0.250381, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9742.96, 139.8, 20.6754, 1.3439, 0, 0, 0.622515, 0.782608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9817.66, 205.771, 16.6964, -0.872665, 0, 0, 0.422618, -0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9945.23, 204.505, 27.2168, -0.942478, 0, 0, 0.453991, -0.891006, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9605.38, -741.081, 52.7849, 1.65806, 0, 0, 0.737277, 0.67559, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9647.58, -115.74, 48.5227, -2.51327, 0, 0, 0.951057, -0.309017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9415.71, -561.253, 69.0323, 2.05949, 0, 0, 0.857167, 0.515038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9188.92, 488.25, 113.085, -1.32645, 0, 0, 0.615661, -0.788011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9557.9, 573.378, 53.6437, -2.58309, 0, 0, 0.961262, -0.275637, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9116.41, 194.16, 112.556, -0.523599, 0, 0, 0.258819, -0.965926, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9856.54, 242.269, 24.5645, 5.58505, 0, 0, -0.34202, 0.939693, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9921.27, -285.241, 35.7804, 1.53589, 0, 0, 0.694658, 0.71934, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9873.02, -252.528, 37.8087, 0, 0, 0, 0, 0, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9841.58, -340.996, 46.2621, 2.25147, 0, 0, 0.902585, 0.430512, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9901.18, -204.867, 38.77, 0.994837, 0, 0, 0.477158, 0.878817, 900, 0, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9101.61, 76.0012, 93.6697, 1.39626, 0, 0, 0.642787, 0.766045, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9471.69, 560.812, 58.0184, 0.383971, 0, 0, 0.190808, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9737.26, 813.852, 33.3772, 6.16101, 0, 0, -0.0610485, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9897.81, -1149.99, 24.353, 4.34587, 0, 0, -0.824126, 0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9153.03, -599.311, 60.2455, 3.21142, 0, 0, -0.999391, 0.0349061, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9058.96, -621.586, 54.7672, 4.15388, 0, 0, -0.874619, 0.48481, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9248.18, -734.17, 69.6834, 2.67035, 0, 0, 0.972369, 0.233448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9403.04, -1049.86, 61.608, 1.51844, 0, 0, 0.688354, 0.725375, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9792.23, -1253.16, 36.0011, 4.03171, 0, 0, -0.902585, 0.430512, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9324.58, -1100.02, 66.707, 2.84488, 0, 0, 0.989016, 0.147811, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9294.92, -270.276, 81.7051, 0.122173, 0, 0, 0.0610485, 0.998135, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9532.9, -861.734, 50.7958, 2.37364, 0, 0, 0.927183, 0.374608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8801.88, -883.678, 82.6097, 3.35105, 0, 0, -0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -8843.56, -713.05, 82.6709, 5.09636, 0, 0, -0.559193, 0.829038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9151.22, -623.095, 78.4092, 5.77704, 0, 0, -0.25038, 0.968148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9025.47, -595.008, 56.6837, 0.767944, 0, 0, 0.374606, 0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9106.09, -560.421, 61.66, 0.331611, 0, 0, 0.165047, 0.986286, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9958.34, -218.238, 28.9477, 3.54302, 0, 0, -0.979924, 0.19937, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9806.22, -1365.82, 54.8668, 0.418879, 0, 0, 0.207912, 0.978148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9541.09, 652.099, 49.707, 0.226893, 0, 0, 0.113203, 0.993572, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9816.79, 178.992, 23.198, 4.88692, 0, 0, -0.642787, 0.766045, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9835.27, 205.609, 14.5273, 2.58308, 0, 0, 0.961261, 0.27564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9775.81, -420.04, 49.1723, 3.47321, 0, 0, -0.986285, 0.16505, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9030.82, -723.776, 111.017, 1.27409, 0, 0, 0.594822, 0.803857, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9222.35, -918.638, 60.2758, 2.53072, 0, 0, 0.953716, 0.300708, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -9672.27, 817.424, 32.0737, 5.96903, 0, 0, -0.156434, 0.987688, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5811.24, -426.122, 370.75, 0.279253, 0, 0, 0.139173, 0.990268, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5148.34, -324.104, 402.391, 2.98451, 0, 0, 0.996917, 0.078459, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4962.52, -169.262, 386.505, -0.15708, 0, 0, 0.078459, -0.996917, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4827.8, -231.656, 406.364, 0.366519, 0, 0, 0.182236, 0.983255, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5018.81, 466.14, 419.118, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5557.61, 649.328, 398.718, 0.855211, 0, 0, 0.414693, 0.909961, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5579.25, 745.874, 391.875, 1.39626, 0, 0, 0.642788, 0.766044, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5671.96, 758.951, 390.155, 0.767945, 0, 0, 0.374607, 0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5815.37, 224.161, 393.461, 2.18166, 0, 0, 0.887011, 0.461749, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5629.82, -64.7745, 420.671, -1.39626, 0, 0, 0.642788, -0.766044, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5557.18, 188.45, 416.896, -1.11701, 0, 0, 0.529919, -0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5660.26, 178.374, 427.587, 0.645772, 0, 0, 0.317305, 0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5235.36, 102.745, 392.428, 0.349066, 0, 0, 0.173648, 0.984808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5229.89, 31.2378, 363.083, 0.802851, 0, 0, 0.390731, 0.920505, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5120.81, -112.382, 399.669, -0.959931, 0, 0, 0.461749, -0.887011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5085.58, -272.124, 441.336, -3.07178, 0, 0, 0.999391, -0.034899, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5515.74, -358.843, 361.206, -2.00713, 0, 0, 0.843391, -0.5373, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5452.19, -281.138, 358.577, -2.47837, 0, 0, 0.945519, -0.325568, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5499.31, -206.126, 354.253, 0.785398, 0, 0, 0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5771, -653.592, 403.583, 6.02139, 0, 0, -0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5334.67, -659.969, 394.836, 5.41052, 0, 0, -0.422618, 0.906308, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5600.42, -744.175, 434.065, 1.79769, 0, 0, 0.782608, 0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5789.91, -781.136, 401.291, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5723.14, -977.155, 400.596, -0.663225, 0, 0, 0.325568, -0.945519, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5255.03, -1049.99, 399.236, 1.48353, 0, 0, 0.67559, 0.737277, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5422.98, -1276.98, 447.711, -1.78024, 0, 0, 0.777146, -0.62932, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5733.16, -1091.58, 387.806, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5717.33, -1426.85, 432.837, 1.11701, 0, 0, 0.529919, 0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5525.45, -1499.6, 409.249, -1.55334, 0, 0, 0.700909, -0.71325, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5738.28, -1613.1, 368.367, -1.22173, 0, 0, 0.573576, -0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5757.61, -1588.93, 362.943, 2.94961, 0, 0, 0.995396, 0.095846, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5868.58, -1568.41, 368.365, -0.645772, 0, 0, 0.317305, -0.948324, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5633.04, -1751.15, 358.066, 5.61996, 0, 0, -0.325567, 0.945519, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5640.9, -1706.94, 362.448, 2.58308, 0, 0, 0.961261, 0.27564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5546.32, -1687.21, 343.854, 1.32645, 0, 0, 0.615662, 0.788011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5616.14, -1752.76, 413.743, -2.21657, 0, 0, 0.894934, -0.446198, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5788.15, -1763.12, 407.646, 2.44346, 0, 0, 0.939693, 0.34202, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5950.65, -1827.33, 459.444, -1.93731, 0, 0, 0.824126, -0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5932.28, -1705.11, 425.12, -0.139626, 0, 0, 0.069756, -0.997564, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5633, -2081.8, 403.557, -2.21657, 0, 0, 0.894934, -0.446198, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5560.81, -2216.06, 436.481, -1.3439, 0, 0, 0.622515, -0.782608, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4981.55, -2143.23, 415.25, -1.18682, 0, 0, 0.559193, -0.829037, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5135.02, -2059.21, 436.066, 0.05236, 0, 0, 0.026177, 0.999657, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6015.35, -577.502, 414.565, 1.6057, 0, 0, 0.719339, 0.694659, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5958.88, -661.093, 404.042, 2.07694, 0, 0, 0.861629, 0.507538, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5564.15, -1394.78, 409.548, 1.11701, 0, 0, 0.529919, 0.848048, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5629.48, -962.107, 406.375, 1.74533, 0, 0, 0.766044, 0.642788, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5818.93, -1329.16, 394.533, 3.00197, 0, 0, 0.997564, 0.069757, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5649.31, -52.1474, 418.394, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5476.12, -111.775, 422.548, 2.67035, 0, 0, 0.97237, 0.233445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5463.95, -181.433, 424.59, 3.00197, 0, 0, 0.997564, 0.069757, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5403.73, -119.305, 369.452, -1.22173, 0, 0, 0.573576, -0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5359.79, -196.572, 451.925, 0.575959, 0, 0, 0.284015, 0.95882, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5431.58, -308.889, 358.476, -3.07178, 0, 0, 0.999391, -0.034899, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5564.46, -307.319, 365.538, 5.28835, 0, 0, -0.477158, 0.878817, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5505.15, -1743.88, 336.827, -2.98451, 0, 0, 0.996917, -0.078459, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5420.69, -2223.09, 424.955, 1.72788, 0, 0, 0.760406, 0.649448, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5168.54, -2256.53, 419.185, -1.15192, 0, 0, 0.544639, -0.838671, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5941.51, -676.599, 427.108, -1.22173, 0, 0, 0.573576, -0.819152, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5963, -307.491, 455.642, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5965.87, 29.5307, 372.507, -0.069813, 0, 0, 0.034899, -0.999391, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5647.65, -378.015, 370.657, -0.418879, 0, 0, 0.207912, -0.978148, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5286.26, -840.516, 406.846, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5613.39, -1589.05, 403.809, 0.261799, 0, 0, 0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5570.23, -1705.6, 371.53, 2.46091, 0, 0, 0.942641, 0.333808, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5566.91, -1726.08, 342.898, 0.767945, 0, 0, 0.374607, 0.927184, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5456.39, -1757.68, 443.358, 2.09439, 0, 0, 0.866025, 0.5, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5594.91, -1894.3, 398.391, -1.91986, 0, 0, 0.819152, -0.573576, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5828.94, -67.125, 366.152, 2.58309, 0, 0, 0.961262, 0.275637, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5462.9, -321.323, 361.567, 2.79253, 0, 0, 0.984808, 0.173648, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5082.65, -153.88, 442.593, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5281.5, -78.8783, 402.053, -2.63545, 0, 0, 0.968148, -0.25038, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5319.51, -744.61, 392.283, 1.79769, 0, 0, 0.782608, 0.622515, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5000.76, -291.451, 445.826, -0.890118, 0, 0, 0.430511, -0.902585, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4974.16, -233.916, 415.163, -2.53073, 0, 0, 0.953717, -0.300706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5567.07, -1799.28, 360.232, 5.39307, 0, 0, -0.43051, 0.902586, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5520.07, -828.794, 413.923, 1.85005, 0, 0, 0.798635, 0.601815, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5051.87, -585.696, 426.164, 0, 0, 0, 0, 1, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5543.01, -345.275, 359.952, 5.91667, 0, 0, -0.182235, 0.983255, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5504.02, -275.01, 354.252, 6.00393, 0, 0, -0.139173, 0.990268, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5756.99, -1462.85, 407.367, 6.03884, 0, 0, -0.121869, 0.992546, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5481.05, -1555.31, 444.87, 0.436332, 0, 0, 0.216439, 0.976296, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5633.08, -1957.88, 372.963, 2.93214, 0, 0, 0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5552.77, -285.159, 364.495, 5.46288, 0, 0, -0.398748, 0.91706, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5387.72, -282.724, 358.948, 3.54302, 0, 0, -0.979924, 0.19937, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5546.48, -1776.41, 345.431, 2.93214, 0, 0, 0.994521, 0.104535, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5357.22, -421.696, 397.762, 4.34587, 0, 0, -0.824126, 0.566406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5644.32, -2362.95, 424.935, 2.30383, 0, 0, 0.913545, 0.406738, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5828.81, -1682.51, 364.491, 1.43117, 0, 0, 0.656058, 0.75471, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5879.19, -1522.75, 380.578, 4.7473, 0, 0, -0.694658, 0.71934, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5892.93, -1004.22, 411.382, 3.10665, 0, 0, 0.999847, 0.0174693, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6011.58, -114.691, 412.603, 2.56563, 0, 0, 0.958819, 0.284016, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5482.73, -94.8129, 346.902, 3.15906, 0, 0, -0.999962, 0.00873464, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -6099.44, -341.376, 439.957, 0.261798, 0, 0, 0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5763.37, -1213.98, 386.301, 1.0472, 0, 0, 0.5, 0.866025, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5684.73, -1663.59, 360.849, 2.47837, 0, 0, 0.945518, 0.325568, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4855.66, -104.338, 407.57, 0.785397, 0, 0, 0.382683, 0.92388, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5351.83, -309.17, 417.621, 6.02139, 0, 0, -0.130526, 0.991445, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5586.85, -1674.53, 345.407, 1.55334, 0, 0, 0.700909, 0.713251, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5614.62, -1665.83, 351.188, 4.86947, 0, 0, -0.649447, 0.760406, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5382.82, 605.27, 400.618, 3.05433, 0, 0, 0.999048, 0.0436193, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5716.87, -162.426, 374.256, 3.08918, 0, 0, 0.999657, 0.0262017, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5888.64, -318.246, 373.329, 5.32326, 0, 0, -0.461748, 0.887011, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5605.4, -252.373, 367.83, 3.57793, 0, 0, -0.976295, 0.216442, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5247.1, -186.158, 442.975, 2.63544, 0, 0, 0.968147, 0.250381, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5016.47, 5.45768, 395.013, 0.383971, 0, 0, 0.190808, 0.981627, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5587.96, 642.125, 385.477, 3.33359, 0, 0, -0.995396, 0.0958512, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5644.12, 655.195, 385.571, 4.81711, 0, 0, -0.66913, 0.743145, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5341.9, 483.555, 390.603, 5.07891, 0, 0, -0.566406, 0.824126, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -4885.96, 150.787, 404.716, 6.10865, 0, 0, -0.0871553, 0.996195, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5628.65, -1651.85, 365.856, 2.07694, 0, 0, 0.861628, 0.507539, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5432.38, -2023.23, 427.529, 4.50295, 0, 0, -0.777145, 0.629321, 900, 100, 1),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, 1731, 0, -5706.74, -2259.01, 445.328, 6.05629, 0, 0, -0.113203, 0.993572, 900, 100, 1);

SET @FREE_POOL_TEMPLATE = 11660;
SET @FREE_GAMEOBJECT = 93000;

DELETE FROM `pool_gameobject` WHERE `pool_entry`BETWEEN @FREE_POOL_TEMPLATE AND @FREE_POOL_TEMPLATE+17;

INSERT INTO `pool_gameobject` (`guid`, `pool_entry`, `chance`, `description`) VALUES 
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+1, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+2, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+3, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+4, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+5, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+6, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+7, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+8, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+9, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Durotar: Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Durotar: Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+10, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+11, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+12, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+13, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+14, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+15, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+16, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein'),
(@FREE_GAMEOBJECT := @FREE_GAMEOBJECT+1, @FREE_POOL_TEMPLATE+17, 0, 'Copper Vein');

-- pool_pool and pool_template database entries have to be deleted later when these pool_gameobjects are split completely. Otherwise all nodes will spawn on top of each other
-- since there are multiple nodes from the same type in the same pool. Importing this from Vmangos with correct sniffs.
DELETE FROM `pool_gameobject` WHERE `guid` IN (73500,73501,73502,73503,73504,73505,73506,73507,73508,73509,73510,73511,73512,73513,73514,73515,73516,73517,73518,73519,73520,73521,73522,73523,73524,73525,73526,73527,73528,73529,73530,73531,73532,73533,73534,73535,73536,73537,73538,73539,73540,73541,73542,73543,73544,73545,73546,73547,73548,73549,73550,73551,73552,73553,73554,73555,73556,73557,73558,73559,73560,73561,73562,73563,73564,73565,73566,73567,73568,73569,73570,73571,73572,73573,73574,73575,73576,73577,73578,73579,73580,73581,73582,73583,73584,73585,73586,73600,73605,73610,73615,73620,73625,73630,73635,73640,73645,73650,73655,73660,73665,73675,73680,73685,73690,73695,73700,73705,73710,73715,73720,73725,73730,73735,73740,73745,73750,73755,73760,73765,73770,73775,73780,73785,73790,73795,73800,73805,73810,73815,73820,73825,73830,73835,73840,73845,73850,73855,73860,73865,73870,73875,73880,73885,73890,73895,73900,73905,73910,73915,73920,73925,73930,73935,73940,73945,73950,73955,73960,73965,73970,73975,73980,73985,73990,73995,74000,74005,74010,74015,74020,74025,74035,74040,74055,74060,74065,74070,74080,74085,74095,74100,74105,74110,74115,74120,74125,74130,74145,74150,74155,74160,74800,74801,74802,74803,74804,74805,74806,74807,74808,74809,74810,74811,74812,74813,74814,74815,74816,74817,74818,74819,74820,74821,74822,74823,74824,74825,74826,74827,74828,74829,74830,74831,74832,74833,74834,74835,74836,74837,74838,74839,74840,74841,74842,74843,74844,74845,74846,74847,74848,74849,74850,74851,74852,74853,74854,74855,74856,74857,74858,74859,74860,74861,74862,74863,74864,74865,74866,74867,74868,74869,74870,74871,74872,74873,74874,74875,74876,74877,74878,74879,74880,74881,74882,74883,74884,74885,74886,74887,74888,74889,74890,74891,74892,74893,74894,74895,74896,74897,74898,74899,74900,74958,74957,74956,74955,74954,74953,74952,74951,74950,74949,74948,74947,74946,74945,74944,74943,74942,74941,74940,74939,74938,74937,74936,74935,74934,74933,74932,74931,74930,75000,75003,75006,75009,75012,75015,75018,75021,75024,75027,75030,75033,75036,75039,75042,75045,75048,75051,75054,75057,75060,75063,75066,75069,75072,75075,75078,75081,75084,75087,75090,75093,75096,75099,75102,75105,75108,75111,75114,75117,75200,75203,75206,75209,75212,75215,75218,75221,75224,75227,75230,75233,75236,75242,75245,75248,75251,75254,75260,75263,75266,75269,75272,75275,75278,75281,75284,75287,75290,75293,75296,75299,75302,75305,75308,75311,75314,75317,75320,75323,75326,75329,75332,75335,75338,75341,75344,75347,75350,75353,75356,75359,75362,75365,75368,75371,75374,75377,75380,75383,75386,75389,75392,75395,75398,75401,75404,75600,75603,75606,75609,75612,75615,75618,75621,75624,75627,75630,75633,75636,75639,75642,75645,75648,75651,75654,75657,75660,75663,75666,75669,75672,75675,75678,75681,75684,75687,75690,75693,75696,75699,75702,75705,75708,75711,75714,75717,75720,75723,75726,75729,75732,75735,75738,75741,75744,75747,75750,75753,75756,75759,75765,75768,75771,75774,75777,75780,75783,75786,75789,75792,75795,75798,75801,75804,75807,75810,75813,75816,75819,75822,75825,75828,75831,75834,75837,75840,75843,75846,75849,75852,75855,75858,75861,75864,75867,75870,75873,75876,75879,75882,75885,75888,75891,75894,75897,75900,75903,75906,75909,75912,75915,75918,75921,75924,75927,75930,75936,150129,150130,150138,150139,5290,5291,5292,5293,5294,5295,5296,5297,5298,5299,5300,5301,5302,5303,5304,5305,5306,5307,5308,5309,5310,5311,5312,5313,30444,30478,30486,30498,30507,30512,30514,30531,39948,39949,39950,120291,120340,120362,120791,74959,74960,74961,74962,74963,74964,74965,74966,74967,74968,74969,74970,74971,74972,74973,74974,74975,74976,74977,74978,74979,74980,74981,74982,74983,74984,74985,74986,74987,120618);

-- Updating these pools temporarily until we get new pools for Silver, Tin, etc... Chances should remain the same ratio.
UPDATE `pool_gameobject` SET `chance` = 70 WHERE `description` LIKE 'Ghostlands Spawn Point%(Tin)%';
UPDATE `pool_gameobject` SET `chance` = 30 WHERE `description` LIKE 'Ghostlands Spawn Point%(Silver)%';
UPDATE `pool_gameobject` SET `chance` = 70 WHERE `description` LIKE 'Spawn Point%Tin%' AND `chance` = 30;
UPDATE `pool_gameobject` SET `chance` = 30 WHERE `description` LIKE 'Spawn Point%Silver%' AND `chance` = 5;

-- Re-insert the TBC Copper veins in the Database
-- No need to delete these since we deleted gameobject with gameobject_template 1731
INSERT INTO `gameobject` (`guid`, `id`, `map`, `zoneId`, `areaId`, `spawnMask`, `phaseMask`, `position_x`, `position_y`, `position_z`, `orientation`, `rotation0`, `rotation1`, `rotation2`, `rotation3`, `spawntimesecs`, `animprogress`, `state`) VALUES
(74958, 1731, 530, 0, 0, 1, 1, 8133.32, -6785.23, 71.3577, -1.88496, 0, 0, 0.809017, -0.587785, 900, 255, 1),
(74957, 1731, 530, 0, 0, 1, 1, 8620.07, -5806.49, 37.7047, 2.47837, 0, 0, 0, 1, 60, 255, 1),
(74956, 1731, 530, 0, 0, 1, 1, 8924, -7719.83, 198.6, -2.00713, 0, 0, 0, 1, 60, 255, 1),
(74955, 1731, 530, 0, 0, 1, 1, 8707.96, -5766.97, 10.0575, 0.052359, 0, 0, 0, 1, 60, 255, 1),
(74954, 1731, 530, 0, 0, 1, 1, 8744.72, -7365.18, 95.5962, 0.610864, 0, 0, 0, 1, 60, 255, 1),
(74953, 1731, 530, 0, 0, 1, 1, 8842.07, -7222.16, 66.3173, -2.26892, 0, 0, 0, 1, 60, 255, 1),
(74952, 1731, 530, 0, 0, 1, 1, 8479.97, -6171.22, 77.417, -1.51844, 0, 0, 0, 1, 60, 255, 1),
(74951, 1731, 530, 0, 0, 1, 1, 8671.68, -6909.58, 110.265, -1.93732, 0, 0, 0, 1, 60, 255, 1),
(74950, 1731, 530, 0, 0, 1, 1, 8632.77, -6182.24, 55.188, -2.77507, 0, 0, 0, 1, 60, 255, 1),
(74949, 1731, 530, 0, 0, 1, 1, 8113.18, -6211.63, 41.5512, 0.890117, 0, 0, 0, 1, 60, 255, 1),
(74948, 1731, 530, 0, 0, 1, 1, 9045.76, -6455.03, 2.36801, -1.27409, 0, 0, 0, 1, 60, 255, 1),
(74947, 1731, 530, 0, 0, 1, 1, 8840.84, -6578.18, 53.5975, 1.53589, 0, 0, 0, 1, 60, 255, 1),
(74946, 1731, 530, 0, 0, 1, 1, 9113.07, -6119.59, 40.2528, -0.052359, 0, 0, 0, 1, 60, 255, 1),
(74945, 1731, 530, 0, 0, 1, 1, 8576.77, -7342.3, 150.207, 1.44862, 0, 0, 0, 1, 60, 255, 1),
(74944, 1731, 530, 0, 0, 1, 1, 9153.6, -6066.24, 91.2698, 0.349065, 0, 0, 0, 1, 60, 255, 1),
(74943, 1731, 530, 0, 0, 1, 1, 9352.82, -7918.88, 15.0909, -0.925024, 0, 0, 0.446198, -0.894934, 60, 255, 1),
(74942, 1731, 530, 0, 0, 1, 1, 8758.19, -6855.44, 65.6054, -2.82743, 0, 0, 0.987688, -0.156434, 60, 255, 1),
(74941, 1731, 530, 0, 0, 1, 1, 8141.18, -7891.01, 201.926, -2.54818, 0, 0, 0.956305, -0.292372, 60, 255, 1),
(74940, 1731, 530, 0, 0, 1, 1, 8255.45, -6933.31, 88.8827, -2.11185, 0, 0, 0.870356, -0.492423, 60, 255, 1),
(74939, 1731, 530, 0, 0, 1, 1, 8304.13, -6966.73, 93.4246, -2.25148, 0, 0, 0.902585, -0.430511, 60, 255, 1),
(74938, 1731, 530, 0, 0, 1, 1, 8296.24, -7134.22, 126.994, 0.349066, 0, 0, 0.173648, 0.984808, 60, 255, 1),
(74937, 1731, 530, 0, 0, 1, 1, 8620.28, -7021.36, 69.6507, -2.21657, 0, 0, 0.894934, -0.446198, 60, 255, 1),
(74936, 1731, 530, 0, 0, 1, 1, 8772, -6848.48, 61.0593, 1.37881, 0, 0, 0.636078, 0.771625, 60, 255, 1),
(74935, 1731, 530, 0, 0, 1, 1, 9074.31, -6966.63, 18.7819, 1.41372, 0, 0, 0.649448, 0.760406, 60, 255, 1),
(74934, 1731, 530, 0, 0, 1, 1, 9061.54, -7157.18, 79.3397, -2.33874, 0, 0, 0.920505, -0.390731, 60, 255, 1),
(74933, 1731, 530, 0, 0, 1, 1, 8957.36, -6504.22, 18.1698, 2.18166, 0, 0, 0.887011, 0.461749, 60, 255, 1),
(74932, 1731, 530, 0, 0, 1, 1, 8851.35, -6392.48, 32.4433, 3.01942, 0, 0, 0.998135, 0.061049, 60, 255, 1),
(74931, 1731, 530, 0, 0, 1, 1, 9156.25, -5893.79, 4.91682, -2.25148, 0, 0, 0.902585, -0.430511, 60, 255, 1),
(74930, 1731, 530, 0, 0, 1, 1, 8978.16, -7247.94, 111.609, -0.715585, 0, 0, 0, 1, 60, 255, 1),
(5290, 1731, 530, 0, 0, 1, 1, -3198.77, -12358.7, 18.8168, -0.663225, 0, 0, 0, 0, 60, 100, 1),
(5291, 1731, 530, 0, 0, 1, 1, -3205.95, -12416.9, 3.10401, 0.959931, 0, 0, 0, 0, 60, 100, 1),
(5292, 1731, 530, 0, 0, 1, 1, -3127.35, -12482.8, 2.25965, -1.15192, 0, 0, 0, 0, 60, 100, 1),
(5293, 1731, 530, 0, 0, 1, 1, -3356.8, -12177.6, 41.1204, -1.32645, 0, 0, 0, 0, 60, 100, 1),
(5294, 1731, 530, 0, 0, 1, 1, -3875.6, -12775, 25.1323, 2.77507, 0, 0, 0, 0, 60, 100, 1),
(5295, 1731, 530, 0, 0, 1, 1, -3961.57, -12711.4, 82.02, 1.02974, 0, 0, 0, 0, 60, 100, 1),
(5296, 1731, 530, 0, 0, 1, 1, -4295.92, -12735.8, 21.8414, -1.0472, 0, 0, 0, 0, 60, 100, 1),
(5297, 1731, 530, 0, 0, 1, 1, -4302, -12927.6, 9.3698, -2.14675, 0, 0, 0, 0, 60, 100, 1),
(5298, 1731, 530, 0, 0, 1, 1, -4778.81, -11573.7, -2.75845, -0.820305, 0, 0, 0, 0, 60, 100, 1),
(5299, 1731, 530, 0, 0, 1, 1, -4774.76, -11635.7, -36.7166, -1.23918, 0, 0, 0, 1, 60, 255, 1),
(5300, 1731, 530, 0, 0, 1, 1, -4702.54, -11540.2, -24.777, -0.733038, 0, 0, 0, 0, 60, 100, 1),
(5301, 1731, 530, 0, 0, 1, 1, -4773.14, -11467.5, 24.643, -1.91986, 0, 0, 0, 0, 60, 100, 1),
(5302, 1731, 530, 0, 0, 1, 1, -5045.83, -11072.7, 29.4929, 1.8675, 0, 0, 0, 0, 60, 100, 1),
(5303, 1731, 530, 0, 0, 1, 1, -5167.42, -11033.6, 24.5044, 1.6057, 0, 0, 0, 0, 60, 100, 1),
(5304, 1731, 530, 0, 0, 1, 1, -5118.29, -10943.1, 16.0907, 0.174533, 0, 0, 0, 1, 60, 100, 1),
(5305, 1731, 530, 0, 0, 1, 1, -4874.49, -11278.6, 2.6214, -0.488692, 0, 0, 0, 0, 60, 100, 1),
(5306, 1731, 530, 0, 0, 1, 1, -4890.08, -11146.4, 7.22636, 1.0472, 0, 0, 1, 1, 60, 100, 1),
(5307, 1731, 530, 0, 0, 1, 1, -4977.74, -11441.8, -36.9993, 2.75762, 0, 0, 1, 0, 60, 100, 1),
(5308, 1731, 530, 0, 0, 1, 1, -4816.09, -11605.3, -42.3396, -1.0821, 0, 0, 1, 0, 60, 100, 1),
(5309, 1731, 530, 0, 0, 1, 1, -5083.06, -11658.3, -12.9082, 2.82743, 0, 0, 1, 0, 60, 100, 1),
(5310, 1731, 530, 0, 0, 1, 1, -4961.75, -11727.3, 12.8604, 0.541052, 0, 0, 0, 1, 60, 100, 1),
(5311, 1731, 530, 0, 0, 1, 1, -4890.44, -12042.3, 22.4669, 0.733038, 0, 0, 0, 1, 60, 100, 1),
(5312, 1731, 530, 0, 0, 1, 1, -4323.69, -12851.5, 11.673, -1.15192, 0, 0, 1, 0, 60, 100, 1),
(5313, 1731, 530, 0, 0, 1, 1, -4178.29, -12910.6, 6.36836, -1.01229, 0, 0, 0, 0, 60, 100, 1),
(30444, 1731, 530, 0, 0, 1, 1, -4066.04, -12693.8, 14.8971, -0.872665, 0, 0, 0.422618, -0.906308, 60, 100, 1),
(30478, 1731, 530, 0, 0, 1, 1, -4565.29, -12030.2, 42.5364, 0.331613, 0, 0, 0.165048, 0.986286, 60, 100, 1),
(30486, 1731, 530, 0, 0, 1, 1, -5085.58, -11970, -2.40896, 1.8326, 0, 0, 0.793353, 0.608761, 60, 100, 1),
(30498, 1731, 530, 0, 0, 1, 1, -4809.11, -11667.8, -40.5956, -1.43117, 0, 0, 0.656059, -0.75471, 60, 100, 1),
(30507, 1731, 530, 0, 0, 1, 1, -3887.91, -12696.9, 94.0337, -2.70526, 0, 0, 0.976296, -0.21644, 60, 100, 1),
(30512, 1731, 530, 0, 0, 1, 1, -4408.13, -11518.2, 14.1106, 1.46608, 0, 0, 0.669131, 0.743145, 60, 100, 1),
(30514, 1731, 530, 0, 0, 1, 1, -4666.33, -11535.9, 22.4012, -2.11185, 0, 0, 0.870356, -0.492423, 60, 100, 1),
(30531, 1731, 530, 0, 0, 1, 1, -3149.38, -12393.9, 12.1434, 3.00197, 0, 0, 0.997564, 0.069757, 60, 100, 1),
(39948, 1731, 530, 0, 0, 1, 1, -3496.12, -11577, 14.9949, 0, 0, 0, 0, 0, 60, 0, 1),
(39949, 1731, 530, 0, 0, 1, 1, -3703.19, -11466.1, 303.665, 2.53072, 0, 0, 0, 1, 60, 255, 1),
(39950, 1731, 530, 0, 0, 1, 1, -3823.74, -12579.4, 2.17238, 0, 0, 0, 0, 0, 60, 0, 1),
(120291, 1731, 530, 0, 0, 1, 1, -3584.34, -11828.8, 11.2678, 2.30383, 0, 0, 0, 1, 60, 255, 1),
(120340, 1731, 530, 0, 0, 1, 1, -2997.91, -12274.1, 17.2133, 0.191985, 0, 0, 0, 1, 60, 255, 1),
(120362, 1731, 530, 0, 0, 1, 1, -3208.36, -12254.1, 57.0038, -0.680679, 0, 0, 0, 1, 60, 255, 1),
(120791, 1731, 530, 0, 0, 1, 1, -4095.92, -11588.5, 19.9823, -1.78023, 0, 0, 0, 1, 60, 255, 1),
(74959, 1731, 530, 0, 0, 1, 1, 8199.71, -6835.06, 79.741, -0.942478, 0, 0, 0.453991, -0.891006, 60, 255, 1),
(74960, 1731, 530, 0, 0, 1, 1, 8852.93, -6967.63, 30.9926, -0.820305, 0, 0, 0.398749, -0.91706, 60, 255, 1),
(74961, 1731, 530, 0, 0, 1, 1, 8570.85, -7471.04, 139.781, -2.11185, 0, 0, 0.870356, -0.492423, 60, 255, 1),
(74962, 1731, 530, 0, 0, 1, 1, 8522.32, -7738.49, 123.011, -0.15708, 0, 0, 0.078459, -0.996917, 60, 255, 1),
(74963, 1731, 530, 0, 0, 1, 1, 8864.79, -7776.76, 172.942, -0.855212, 0, 0, 0.414693, -0.909961, 60, 255, 1),
(74964, 1731, 530, 0, 0, 1, 1, 9709.15, -7925.85, 9.3747, -0.139626, 0, 0, 0.069756, -0.997564, 60, 255, 1),
(74965, 1731, 530, 0, 0, 1, 1, 9595.45, -7797.68, 41.8183, -1.25664, 0, 0, 0.587785, -0.809017, 60, 255, 1),
(74966, 1731, 530, 0, 0, 1, 1, 9258.71, -6442.46, 18.3215, -3.01942, 0, 0, 0.998135, -0.061048, 60, 255, 1),
(74967, 1731, 530, 0, 0, 1, 1, 9270.79, -6399.6, 9.563, 0.523599, 0, 0, 0.258819, 0.965926, 60, 255, 1),
(74968, 1731, 530, 0, 0, 1, 1, 9227.42, -6113.97, 45.0611, 2.70526, 0, 0, 0.976296, 0.21644, 60, 255, 1),
(74969, 1731, 530, 0, 0, 1, 1, 8915.39, -6179.36, 7.73462, 1.64061, 0, 0, 0.731354, 0.681998, 60, 255, 1),
(74970, 1731, 530, 0, 0, 1, 1, 9018.65, -5971.72, 12.7394, 2.51327, 0, 0, 0.951057, 0.309017, 60, 255, 1),
(74971, 1731, 530, 0, 0, 1, 1, 8999.2, -5967.17, 13.0295, -1.79769, 0, 0, 0.782608, -0.622515, 60, 255, 1),
(74972, 1731, 530, 0, 0, 1, 1, 8933.25, -6974.55, 21.7225, 1.23918, 0, 0, 0.580703, 0.814116, 60, 255, 1),
(74973, 1731, 530, 0, 0, 1, 1, 8923.4, -6822.55, 54.7797, 1.72788, 0, 0, 0.760406, 0.649448, 60, 255, 1),
(74974, 1731, 530, 0, 0, 1, 1, 8796.62, -6693.31, 56.7484, -2.00713, 0, 0, 0.843391, -0.5373, 60, 255, 1),
(74975, 1731, 530, 0, 0, 1, 1, 8265.85, -7446.72, 175.822, -2.77507, 0, 0, 0.983255, -0.182235, 60, 255, 1),
(74976, 1731, 530, 0, 0, 1, 1, 8338.62, -7384.13, 210.359, 0.122173, 0, 0, 0.061049, 0.998135, 60, 255, 1),
(74977, 1731, 530, 0, 0, 1, 1, 8640.4, -7137.19, 86.2068, 2.9147, 0, 0, 0.993572, 0.113203, 60, 255, 1),
(74978, 1731, 530, 0, 0, 1, 1, 8757.04, -7205.74, 56.7255, -0.20944, 0, 0, 0.104528, -0.994522, 60, 255, 1),
(74979, 1731, 530, 0, 0, 1, 1, 8350.22, -6638.37, 114.945, -1.48353, 0, 0, 0.67559, -0.737277, 60, 255, 1),
(74980, 1731, 530, 0, 0, 1, 1, 8391.14, -6303.74, 128.429, 1.50098, 0, 0, 0.681998, 0.731354, 60, 255, 1),
(74981, 1731, 530, 0, 0, 1, 1, 8697.75, -6217.64, 35.9229, 1.6057, 0, 0, 0.71934, 0.694658, 60, 255, 1),
(74982, 1731, 530, 0, 0, 1, 1, 8616.06, -6041.67, 57.8267, 1.67552, 0, 0, 0.743145, 0.669131, 60, 255, 1),
(74983, 1731, 530, 0, 0, 1, 1, 8698.23, -6041.53, 19.0908, 0.855211, 0, 0, 0.414693, 0.909961, 60, 255, 1),
(74984, 1731, 530, 0, 0, 1, 1, 8435.37, -5754.15, 26.823, -2.56563, 0, 0, 0.95882, -0.284015, 60, 255, 1),
(74985, 1731, 530, 0, 0, 1, 1, 8757.86, -6306.09, 57.1405, -1.0821, 0, 0, 0.515038, -0.857167, 60, 255, 1),
(74986, 1731, 530, 0, 0, 1, 1, 8923.64, -6579.72, 40.4971, -0.017453, 0, 0, 0.008727, -0.999962, 60, 255, 1),
(74987, 1731, 530, 0, 0, 1, 1, 9157.8, -6194.33, 28.8475, -0.314159, 0, 0, 0.156434, -0.987688, 60, 255, 1),
(120618, 1731, 530, 0, 0, 1, 1, 8764.94, -7016, 40.6887, 0.890117, 0, 0, 0, 1, 60, 255, 1),
(120325, 1731, 530, 0, 0, 1, 1, -2620.13, -11774.8, 10.9012, 3.18007, 0, 0, -0.999815, 0.01924, 300, 0, 1),
(120324, 1731, 530, 0, 0, 1, 1, -1488.09, -10655.4, 135.281, 4.83145, 0, 0, -0.663785, 0.747923, 300, 0, 1),
(2134521, 1731, 530, 0, 0, 1, 1, -2141.63, -12362, 28.6973, 6.15893, 0, 0, -0.0620857, 0.998071, 300, 0, 1),
(2134623, 1731, 530, 0, 0, 1, 1, -2474.41, -12316.3, 15.1395, 1.79086, 0, 0, -0.780479, -0.625182, 300, 0, 1),
(2134624, 1731, 530, 0, 0, 1, 1, -2424.38, -12134.5, 35.782, 2.10502, 0, 0, -0.86867, -0.495392, 300, 0, 1),
(2134625, 1731, 530, 0, 0, 1, 1, -1921.96, -11650.8, 38.4271, 5.58182, 0, 0, -0.343537, 0.939139, 300, 0, 1),
(2134626, 1731, 530, 0, 0, 1, 1, -1341.23, -11605.1, 8.95622, 5.65439, 0, 0, -0.309242, 0.950984, 300, 0, 1),
(2134627, 1731, 530, 0, 0, 1, 1, -1137.25, -11491.4, -3.61492, 4.48289, 0, 0, -0.783418, 0.621496, 300, 0, 1),
(2134631, 1731, 530, 0, 0, 1, 1, -2109.01, -10874.3, 74.3795, 4.78009, 0, 0, -0.682771, 0.730633, 300, 0, 1),
(2134632, 1731, 530, 0, 0, 1, 1, -1663.13, -11742.1, 36.28, 1.60708, 0, 0, -0.719818, -0.694162, 300, 0, 1),
(2134633, 1731, 530, 0, 0, 1, 1, -1500.01, -11706.2, 35.8202, 3.27684, 0, 0, -0.997715, 0.0675699, 300, 0, 1),
(2134634, 1731, 530, 0, 0, 1, 1, -2041.58, -12329.4, 12.4268, 4.08894, 0, 0, -0.889899, 0.456158, 300, 0, 1),
(2134635, 1731, 530, 0, 0, 1, 1, -1967.73, -11628.9, 48.4035, 3.25264, 0, 0, -0.998459, 0.0554972, 300, 0, 1),
(2134636, 1731, 530, 0, 0, 1, 1, -1878.46, -11569.2, 45.1684, 1.02997, 0, 0, -0.492522, -0.8703, 300, 0, 1),
(2134637, 1731, 530, 0, 0, 1, 1, -2372.18, -11507.2, 25.9138, 1.38591, 0, 0, -0.638815, -0.769361, 300, 0, 1),
(2134638, 1731, 530, 0, 0, 1, 1, -2291.71, -11507.2, 26.5163, 2.19518, 0, 0, -0.890113, -0.45574, 300, 0, 1),
(2134639, 1731, 530, 0, 0, 1, 1, -2204.71, -12381.6, 42.0177, 2.9831, 0, 0, -0.996862, -0.0791652, 300, 0, 1),
(2134640, 1731, 530, 0, 0, 1, 1, -2106.83, -11490.9, 64.849, 1.25805, 0, 0, -0.588357, -0.808602, 300, 0, 1),
(2134643, 1731, 530, 0, 0, 1, 1, -2675.37, -11420.7, 27.6011, 0.789321, 0, 0, -0.384495, -0.923127, 300, 0, 1),
(2134645, 1731, 530, 0, 0, 1, 1, -2109.01, -11164.7, 72.2675, 2.94508, 0, 0, -0.995177, -0.0980966, 300, 0, 1),
(2134647, 1731, 530, 0, 0, 1, 1, -2591.03, -11575.8, 27.5438, 2.61365, 0, 0, -0.965361, -0.260918, 300, 0, 1),
(2134648, 1731, 530, 0, 0, 1, 1, -2353.22, -11458.8, 25.0549, 1.80359, 0, 0, -0.784441, -0.620204, 300, 0, 1),
(2134649, 1731, 530, 0, 0, 1, 1, -1634.86, -11083.1, 70.4121, 3.39433, 0, 0, -0.992026, 0.126033, 300, 0, 1),
(2134651, 1731, 530, 0, 0, 1, 1, -1895.86, -11406.1, 56.617, 6.21768, 0, 0, -0.0327457, 0.999464, 300, 0, 1),
(2134652, 1731, 530, 0, 0, 1, 1, -2050.28, -11057, 60.3768, 0.466683, 0, 0, -0.23123, -0.972899, 300, 0, 1),
(2134653, 1731, 530, 0, 0, 1, 1, -1541.33, -11471.3, 61.391, 0.580722, 0, 0, -0.286298, -0.958141, 300, 0, 1),
(2134654, 1731, 530, 0, 0, 1, 1, -1225.55, -11112.2, -29.8231, 0.349187, 0, 0, -0.173708, -0.984797, 300, 0, 1),
(2134656, 1731, 530, 0, 0, 1, 1, -2286.65, -11245, 38.6148, 1.15438, 0, 0, -0.54567, -0.838, 300, 0, 1),
(2134658, 1731, 530, 0, 0, 1, 1, -1965.31, -11014.4, 61.4692, 1.775, 0, 0, -0.775495, -0.631354, 300, 0, 1),
(2134659, 1731, 530, 0, 0, 1, 1, -1164.71, -11177.3, -53.5757, 4.50788, 0, 0, -0.775591, 0.631235, 300, 0, 1),
(2134661, 1731, 530, 0, 0, 1, 1, -1984.3, -12204.3, 20.6867, 5.03581, 0, 0, -0.584032, 0.811731, 300, 0, 1),
(2134662, 1731, 530, 0, 0, 1, 1, -1695.76, -10809.1, 64.1206, 3.56712, 0, 0, -0.977451, 0.211162, 300, 0, 1),
(2134663, 1731, 530, 0, 0, 1, 1, -1565.26, -11190.8, 67.9502, 1.5587, 0, 0, -0.702817, -0.71137, 300, 0, 1),
(2134664, 1731, 530, 0, 0, 1, 1, -1811.05, -12126.4, 36.0542, 4.79862, 0, 0, -0.675971, 0.736928, 300, 0, 1),
(2134665, 1731, 530, 0, 0, 1, 1, -2678.86, -11474.6, 27.1757, 1.65201, 0, 0, -0.735228, -0.67782, 300, 0, 1),
(2134667, 1731, 530, 0, 0, 1, 1, -1841.48, -11057, 67.1545, 4.26173, 0, 0, -0.84722, 0.531242, 300, 0, 1),
(2134669, 1731, 530, 0, 0, 1, 1, -1702.28, -12052.1, 14.2249, 1.61745, 0, 0, -0.723407, -0.690422, 300, 0, 1),
(2134670, 1731, 530, 0, 0, 1, 1, -2490.87, -11650.9, 23.0702, 0.565012, 0, 0, -0.278763, -0.96036, 300, 0, 1),
(2134673, 1731, 530, 0, 0, 1, 1, -1282.51, -12505.6, 56.1227, 4.43451, 0, 0, -0.798221, 0.602365, 300, 0, 1),
(2134674, 1731, 530, 0, 0, 1, 1, -2176.43, -12287, 53.9191, 2.47164, 0, 0, -0.944419, -0.328745, 300, 0, 1),
(2134675, 1731, 530, 0, 0, 1, 1, -1458.68, -11549.6, 34.0723, 0.33882, 0, 0, -0.168601, -0.985685, 300, 0, 1),
(2134676, 1731, 530, 0, 0, 1, 1, -1878.46, -11474.6, 50.4829, 4.37577, 0, 0, -0.815568, 0.578661, 300, 0, 1),
(2134678, 1731, 530, 0, 0, 1, 1, -2207.48, -11116.8, 53.2854, 6.02824, 0, 0, -0.127126, 0.991887, 300, 0, 1),
(2134680, 1731, 530, 0, 0, 1, 1, -2572.28, -11213.6, 21.2003, 0.231691, 0, 0, -0.115587, -0.993297, 300, 0, 1),
(2134681, 1731, 530, 0, 0, 1, 1, -1174.23, -12542.7, 66.8175, 0.720042, 0, 0, -0.352294, -0.935889, 300, 0, 1),
(2134682, 1731, 530, 0, 0, 1, 1, -2287.36, -12322.9, 51.7417, 1.66583, 0, 0, -0.739895, -0.672722, 300, 0, 1),
(2134683, 1731, 530, 0, 0, 1, 1, -2615.78, -12189.1, 28.3355, 0.356099, 0, 0, -0.17711, -0.984191, 300, 0, 1),
(2134684, 1731, 530, 0, 0, 1, 1, -2539.66, -11915, 21.7351, 5.82373, 0, 0, -0.227714, 0.973728, 300, 0, 1),
(2134685, 1731, 530, 0, 0, 1, 1, -2150.33, -11647.5, 50.494, 3.29066, 0, 0, -0.997224, 0.0744643, 300, 0, 1),
(2134689, 1731, 530, 0, 0, 1, 1, -2291.67, -11118.2, 11.4338, 3.49643, 0, 0, -0.984302, 0.176491, 300, 0, 1),
(2134690, 1731, 530, 0, 0, 1, 1, -2030.71, -10760.1, 93.8289, 4.37577, 0, 0, -0.815568, 0.578661, 300, 0, 1),
(2134691, 1731, 530, 0, 0, 1, 1, -1413.01, -10724.2, 80.2255, 2.41981, 0, 0, -0.935582, -0.353109, 300, 0, 1),
(2134693, 1731, 530, 0, 0, 1, 1, -1517.41, -11239.7, 68.91, 0.580722, 0, 0, -0.286298, -0.958141, 300, 0, 1),
(2134695, 1731, 530, 0, 0, 1, 1, -1882.81, -12094.5, 28.5107, 5.35374, 0, 0, -0.448173, 0.893947, 300, 0, 1),
(2134696, 1731, 530, 0, 0, 1, 1, -1876.28, -11383.3, 56.5906, 6.1693, 0, 0, -0.0569113, 0.998379, 300, 0, 1),
(2134700, 1731, 530, 0, 0, 1, 1, -1995.28, -10558.2, 180.937, 3.5013, 0, 0, -0.98387, 0.178888, 300, 0, 1),
(2134702, 1731, 530, 0, 0, 1, 1, -2543.74, -11213.9, 22.2677, 3.71069, 0, 0, -0.959788, 0.280726, 300, 0, 1),
(2134704, 1731, 530, 0, 0, 1, 1, -1752.31, -11455, 47.9052, 0.570355, 0, 0, -0.281328, -0.959612, 300, 0, 1),
(2134705, 1731, 530, 0, 0, 1, 1, -2206.88, -11109.2, 46.7431, 6.11401, 0, 0, -0.0844871, 0.996425, 300, 0, 1),
(2134706, 1731, 530, 0, 0, 1, 1, -2334.65, -11213.8, 23.1339, 5.00015, 0, 0, -0.598412, 0.801189, 300, 0, 1),
(2134707, 1731, 530, 0, 0, 1, 1, -1721.86, -11709.5, 42.2004, 6.04489, 0, 0, -0.118864, 0.992911, 300, 0, 1),
(2134708, 1731, 530, 0, 0, 1, 1, -2633.48, -10812.1, -17.7529, 0.468565, 0, 0, -0.232145, -0.972681, 300, 0, 1),
(2134711, 1731, 530, 0, 0, 1, 1, -2254.73, -10861.3, 8.6671, 4.66259, 0, 0, -0.724491, 0.689284, 300, 0, 1),
(2134712, 1731, 530, 0, 0, 1, 1, -2111.18, -10906.9, 69.0148, 3.45308, 0, 0, -0.987897, 0.155114, 300, 0, 1),
(2134713, 1731, 530, 0, 0, 1, 1, -1900.21, -11667.1, 42.0804, 5.76843, 0, 0, -0.254544, 0.967061, 300, 0, 1),
(2134714, 1731, 530, 0, 0, 1, 1, -2074.21, -11037.4, 62.5974, 5.24661, 0, 0, -0.495391, 0.86867, 300, 0, 1),
(2134715, 1731, 530, 0, 0, 1, 1, -2287.36, -12052.1, 27.5849, 0.801891, 0, 0, -0.390289, -0.920693, 300, 0, 1),
(2134716, 1731, 530, 0, 0, 1, 1, -2263.43, -11778, 23.1581, 0.349187, 0, 0, -0.173708, -0.984797, 300, 0, 1),
(2134720, 1731, 530, 0, 0, 1, 1, -1911.08, -11291.9, 66.1291, 2.19518, 0, 0, -0.890113, -0.45574, 300, 0, 1),
(2134721, 1731, 530, 0, 0, 1, 1, -2087.26, -11422.4, 65.3849, 4.73171, 0, 0, -0.700244, 0.713904, 300, 0, 1),
(2134722, 1731, 530, 0, 0, 1, 1, -1282.51, -11422.4, 10.0034, 1.89453, 0, 0, -0.811823, -0.583904, 300, 0, 1),
(2134723, 1731, 530, 0, 0, 1, 1, -2642.91, -11894.6, 10.7262, 1.82715, 0, 0, -0.791692, -0.610921, 300, 0, 1),
(2134724, 1731, 530, 0, 0, 1, 1, -1513.06, -11621.4, 23.5941, 3.63623, 0, 0, -0.969572, 0.244807, 300, 0, 1),
(2134725, 1731, 530, 0, 0, 1, 1, -2045.93, -11239.7, 80.7004, 5.13258, 0, 0, -0.544091, 0.839026, 300, 0, 1),
(2134726, 1731, 530, 0, 0, 1, 1, -1519.58, -11125.5, 79.8109, 1.44121, 0, 0, -0.659838, -0.751408, 300, 0, 1),
(2134728, 1731, 530, 0, 0, 1, 1, -2596.21, -11282.1, 35.5212, 2.87597, 0, 0, -0.991194, -0.132422, 300, 0, 1),
(2134729, 1731, 530, 0, 0, 1, 1, -2021.07, -10685.3, 125.446, 3.33323, 0, 0, -0.995413, 0.0956708, 300, 0, 1),
(2134731, 1731, 530, 0, 0, 1, 1, -1774.06, -11510.5, 48.2173, 3.50837, 0, 0, -0.983231, 0.182363, 300, 0, 1),
(2134732, 1731, 530, 0, 0, 1, 1, -1839.31, -10655.7, 146.297, 2.63752, 0, 0, -0.968407, -0.249376, 300, 0, 1),
(2134733, 1731, 530, 0, 0, 1, 1, -2544.01, -11419.1, 41.3461, 2.76884, 0, 0, -0.982682, -0.185299, 300, 0, 1),
(2134734, 1731, 530, 0, 0, 1, 1, -1193.33, -12084.7, 5.45135, 5.12566, 0, 0, -0.546987, 0.837141, 300, 0, 1),
(2134735, 1731, 530, 0, 0, 1, 1, -1908.91, -11216.9, 58.905, 1.27878, 0, 0, -0.596708, -0.802459, 300, 0, 1),
(2134736, 1731, 530, 0, 0, 1, 1, -2413.51, -11970.5, 18.3405, 3.39433, 0, 0, -0.992026, 0.126033, 300, 0, 1),
(2134737, 1731, 530, 0, 0, 1, 1, -2106.45, -11498.1, 59.5798, 5.5369, 0, 0, -0.364544, 0.931186, 300, 0, 1),
(75000, 1731, 530, 0, 0, 1, 1, 7890.42, -6941.48, 79.6466, -0.575959, 0, 0, 0.284015, -0.95882, 900, 255, 1),
(75003, 1731, 530, 0, 0, 1, 1, 6354.8, -6358.58, 71.9724, -0.20944, 0, 0, 0.104528, -0.994522, 900, 255, 1),
(75006, 1731, 530, 0, 0, 1, 1, 6948.06, -7521.55, 49.5686, -2.46091, 0, 0, 0.942641, -0.333807, 900, 255, 1),
(75009, 1731, 530, 0, 0, 1, 1, 7756.59, -6772.16, 50.1252, -2.67035, 0, 0, 0.97237, -0.233445, 900, 255, 1),
(75012, 1731, 530, 0, 0, 1, 1, 7234.2, -6527.35, 12.0761, 2.54818, 0, 0, 0.956305, 0.292372, 900, 255, 1),
(75015, 1731, 530, 0, 0, 1, 1, 7605.64, -6331.32, 16.5091, -1.67552, 0, 0, 0.743145, -0.669131, 900, 255, 1),
(75018, 1731, 530, 0, 0, 1, 1, 7500.13, -6103.76, 2.77201, 1.98968, 0, 0, 0.838671, 0.544639, 900, 255, 1),
(75021, 1731, 530, 0, 0, 1, 1, 6818.22, -5942, 56.7248, 0.698132, 0, 0, 0.34202, 0.939693, 900, 255, 1),
(75024, 181248, 530, 0, 0, 1, 1, 7073.06, -6183.51, 21.7027, 2.44346, 0, 0, 0.939693, 0.34202, 900, 255, 1),
(75027, 1731, 530, 0, 0, 1, 1, 6861.82, -6071.52, 34.0041, 2.3911, 0, 0, 0, 1, 900, 255, 1),
(75030, 1731, 530, 0, 0, 1, 1, 7836.56, -7476.31, 154.909, 1.67551, 0, 0, 0, 1, 900, 255, 1),
(75033, 1731, 530, 0, 0, 1, 1, 7532.1, -5748.37, 4.13592, 0.261798, 0, 0, 0, 1, 900, 255, 1),
(75036, 1731, 530, 0, 0, 1, 1, 7085.96, -5802.34, 30.6811, -3.12412, 0, 0, 0, 1, 900, 255, 1),
(75039, 1731, 530, 0, 0, 1, 1, 8005.6, -7867.93, 192.507, 2.77507, 0, 0, 0, 1, 900, 255, 1),
(75042, 1731, 530, 0, 0, 1, 1, 7430.31, -6559.54, 11.2087, -3.07177, 0, 0, 0, 1, 900, 255, 1),
(75045, 1731, 530, 0, 0, 1, 1, 7084.87, -7166.42, 51.4525, -1.13446, 0, 0, 0, 1, 900, 255, 1),
(75048, 1731, 530, 0, 0, 1, 1, 7213.27, -6609.58, 55.7905, 1.22173, 0, 0, 0.573576, 0.819152, 900, 255, 1),
(75051, 181248, 530, 0, 0, 1, 1, 7235.42, -6315.06, 25.745, -1.11701, 0, 0, 0.529919, -0.848048, 900, 255, 1),
(75054, 1731, 530, 0, 0, 1, 1, 7787.24, -6298.68, 23.9224, -1.37881, 0, 0, 0.636078, -0.771625, 900, 255, 1),
(75057, 1731, 530, 0, 0, 1, 1, 7870.94, -6696.52, 19.3749, 0.785398, 0, 0, 0.382683, 0.92388, 900, 255, 1),
(75060, 1731, 530, 0, 0, 1, 1, 7939.39, -6423.14, 59.7752, -1.65806, 0, 0, 0.737277, -0.67559, 900, 255, 1),
(75063, 1731, 530, 0, 0, 1, 1, 7517.04, -7870.55, 157.273, -2.09439, 0, 0, 0.866025, -0.5, 900, 255, 1),
(75066, 1731, 530, 0, 0, 1, 1, 7970.28, -6264.19, 24.6026, 0.959931, 0, 0, 0.461749, 0.887011, 900, 255, 1),
(75069, 1731, 530, 0, 0, 1, 1, 8048.01, -5940.32, 4.73735, 0.959931, 0, 0, 0.461749, 0.887011, 900, 255, 1),
(75072, 1731, 530, 0, 0, 1, 1, 7479.82, -7592.04, 124.83, 1.8326, 0, 0, 0.793353, 0.608761, 900, 255, 1),
(75075, 1731, 530, 0, 0, 1, 1, 7773.21, -7819.7, 160.805, -0.087267, 0, 0, 0.04362, -0.999048, 900, 255, 1),
(75078, 1731, 530, 0, 0, 1, 1, 7866.6, -7938.18, 176.191, -2.60054, 0, 0, 0.96363, -0.267238, 900, 255, 1),
(75081, 1731, 530, 0, 0, 1, 1, 7834.53, -5983.14, 4.11614, 0.261799, 0, 0, 0.130526, 0.991445, 900, 255, 1),
(75084, 1731, 530, 0, 0, 1, 1, 7209.66, -5844.62, 15.5623, 2.42601, 0, 0, 0.936672, 0.350207, 900, 255, 1),
(75087, 1731, 530, 0, 0, 1, 1, 7683.54, -6087.25, 19.2903, -1.3439, 0, 0, 0.622515, -0.782608, 900, 255, 1),
(75090, 181248, 530, 0, 0, 1, 1, 7081.05, -6264.08, 19.0276, 2.82743, 0, 0, 0.987688, 0.156434, 900, 255, 1),
(75093, 1731, 530, 0, 0, 1, 1, 7795.77, -7253.04, 168.994, 1.55334, 0, 0, 0.700909, 0.71325, 900, 255, 1),
(75096, 1731, 530, 0, 0, 1, 1, 6977.29, -6562.71, 11.2095, -0.366518, 0, 0, 0, 1, 900, 255, 1),
(75099, 181248, 530, 0, 0, 1, 1, 7142.39, -6211.11, 24.2601, -1.55334, 0, 0, 0.700909, -0.71325, 900, 255, 1),
(75102, 1731, 530, 0, 0, 1, 1, 7105.41, -6559.61, 11.4982, 2.23402, 0, 0, 0.898794, 0.438371, 900, 255, 1),
(75105, 181248, 530, 0, 0, 1, 1, 7064.71, -6243.39, 18.7586, 1.3439, 0, 0, 0.622515, 0.782608, 900, 255, 1),
(75108, 1731, 530, 0, 0, 1, 1, 7241.18, -6447.52, 48.1141, 0.453786, 0, 0, 0.224951, 0.97437, 900, 255, 1),
(75111, 1731, 530, 0, 0, 1, 1, 7379.89, -7913.66, 158.573, -0.802851, 0, 0, 0.390731, -0.920505, 900, 255, 1),
(75114, 1731, 530, 0, 0, 1, 1, 6830.76, -6491.14, 18.6179, -1.16937, 0, 0, 0.551937, -0.833886, 900, 255, 1),
(75117, 1731, 530, 0, 0, 1, 1, 6873.19, -6164.61, 35.753, 1.3439, 0, 0, 0, 1, 900, 255, 1);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_11_29_05' WHERE sql_rev = '1637592933273396000';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
