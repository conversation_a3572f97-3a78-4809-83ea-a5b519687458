-- DB update 2021_09_01_27 -> 2021_09_01_28
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_01_27';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_01_27 2021_09_01_28 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1630345537511243600'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1630345537511243600');

DELETE FROM `gameobject` WHERE `guid`=87978;
INSERT INTO `gameobject` VALUES
(87978,188021,0,0,0,1,1,-137.439,-815.147,55.2293,1.36284,-0,-0,-0.629895,-0.77668,300,0,1,'',0);

DELETE FROM `game_event_gameobject` WHERE `guid`=87978;
INSERT INTO `game_event_gameobject` VALUES
(1,87978);

--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_01_28' WHERE sql_rev = '1630345537511243600';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
