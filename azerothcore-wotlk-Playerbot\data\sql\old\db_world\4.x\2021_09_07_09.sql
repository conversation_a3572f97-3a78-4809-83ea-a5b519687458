-- DB update 2021_09_07_08 -> 2021_09_07_09
DROP PROCEDURE IF EXISTS `updateDb`;
DELIMITER //
CREATE PROCEDURE updateDb ()
proc:BEGIN DECLARE OK VARCHAR(100) DEFAULT 'FALSE';
SELECT COUNT(*) INTO @COLEXISTS
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'version_db_world' AND COLUMN_NAME = '2021_09_07_08';
IF @COLEXISTS = 0 THEN LEAVE proc; END IF;
START TRANSACTION;
ALTER TABLE version_db_world CHANGE COLUMN 2021_09_07_08 2021_09_07_09 bit;
SELECT sql_rev INTO OK FROM version_db_world WHERE sql_rev = '1630816536518638817'; IF OK <> 'FALSE' THEN LEAVE proc; END IF;
--
-- START UPDATING QUERIES
--

INSERT INTO `version_db_world` (`sql_rev`) VALUES ('1630816536518638817');

-- Delete Arena Spoils items from arena team loot tables
DELETE FROM `creature_loot_template` WHERE `Entry` IN (16049, 16050, 16051, 16052, 16055, 16058, 16095);

-- Clear loot tables for arena team
UPDATE `creature_template` SET `lootid` = 0 WHERE `Entry` IN (16049, 16050, 16051, 16052, 16055, 16058, 16095);


--
-- END UPDATING QUERIES
--
UPDATE version_db_world SET date = '2021_09_07_09' WHERE sql_rev = '1630816536518638817';
COMMIT;
END //
DELIMITER ;
CALL updateDb();
DROP PROCEDURE IF EXISTS `updateDb`;
